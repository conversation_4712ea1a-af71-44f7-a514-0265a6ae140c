@using UcpmApi.Shared
@model AgentNewsReceiver

<form asp-page-handler="SaveNewContact" method="post" class="SaveNewContact">
    <div class="form-group">
        <label asp-for="ContactName">Contact Name</label>
        <input asp-for="ContactName" class="form-control" />
        <span asp-validation-for="ContactName" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="CompanyName">Company Name</label>
        <input asp-for="CompanyName" class="form-control" />
        <span asp-validation-for="CompanyName" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="Email">Email Address</label>
        <input asp-for="Email" class="form-control" />
        <span asp-validation-for="Email" class="text-danger"></span>
    </div>

    <!-- Hidden fields -->
    @Html.HiddenFor(m => m.AgentGuid)
    @Html.HiddenFor(m => m.AgentNewsReceiverGuid)

    
</form>