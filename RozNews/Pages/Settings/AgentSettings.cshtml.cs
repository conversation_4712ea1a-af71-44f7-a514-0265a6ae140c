using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using PdfSharpCore.Drawing;
using SkiaSharp;
using Sprache;
using System.Drawing;
using System.Security.Claims;
using System.Xml.Linq;
using UcpmApi.Shared;
using UcpmApi.Shared.Api.Crm;
using UcpmApi.Shared.Api.Security;
using UcpmApi.Shared.ApiClientBase.Interfaces;
using UcpmApi.Shared.Apis;
using UcpmApi.Shared.Auth;
using UcpmApi.Shared.Doc;
using UcpmApi.Shared.Enums;

namespace RozNews.Pages.Settings
{
    public class AgentSettingsModel : PageModel
    {
        AgentApi AgentApi { get; set; }
        DocApi DocApi { get; set; }
        CompanyApi CompanyApi { get; set; }
        AgentNewsReceiverApi ContactsApi { get; set; }
        UserApi UserApi { get; set; }
        IErrorComponent ErrorComponent { get; set; }
        private readonly ILogger<AgentSettingsModel> _logger;

        public Agent Agent { get; set; }
        public List<AgentNewsReceiver> NewsContacts { get; set; }
        public AgentNewsReceiver AddNewNewsContacts { get; set; }

        [BindProperty]
        public Guid AgentGuid { get; set; }
        [BindProperty]
        public int NotificationFrequencyDays { get; set; }
        [BindProperty]
        public IFormFile? Logo { get; set; }
        [BindProperty]
        public string SuccessMessage { get; set; }
        [BindProperty]
        public string ErrorMessage { get; set; }

        public AgentSettingsModel(
            DocApi _docApi,
            AgentApi _agentApi,
            AgentNewsReceiverApi _contactsApi,
            CompanyApi _companyApi,
            UserApi _userApi,
            IErrorComponent _errorComponent,
            ILogger<AgentSettingsModel> logger)
        {
            AgentApi = _agentApi;
            UserApi = _userApi;
            DocApi = _docApi;
            ContactsApi = _contactsApi;
            ErrorComponent = _errorComponent;
            _logger = logger;
            Agent = new();
            NewsContacts = [];
            AddNewNewsContacts = new AgentNewsReceiver();
            Logo = null;
            SuccessMessage = "";
            ErrorMessage = "";
            CompanyApi = _companyApi;
        }

        public override PageResult Page()
        {
            OnGet().GetAwaiter().GetResult();
            return new PageResult();//<-This is literally all that the original Page() does
        }

        public async Task OnGet()
        {
            if (AgentApi != null)
            {
                string authToken = User?.Claims?.Where(x => x.Type == "JwtToken")?.FirstOrDefault()?.Value.ToString() ?? "";
                string agentGuid = User?.Claims?.Where(x => x.Type == "AgentGuid")?.FirstOrDefault()?.Value.ToString() ?? "";

                if (Guid.TryParse(agentGuid, out Guid trueAgentGuid))
                {
                    AgentGuid = trueAgentGuid;
                    Agent = await GetAgentForRoz() ?? new();

                    NotificationFrequencyDays = Agent.NotificationFrequencyDays;
                    IEnumerable<AgentNewsReceiver> results = await ContactsApi.GetForAgent(AgentGuid, ErrorComponent, authToken);
                    NewsContacts = results.ToList();
                    AddNewNewsContacts.AgentGuid = AgentGuid;
                    AddNewNewsContacts.AgentNewsReceiverGuid = Guid.Empty;
                }
            }
        }

        private async Task<Agent> GetAgentForRoz()
        {
            string authToken = User?.Claims?.Where(x => x.Type == "JwtToken")?.FirstOrDefault()?.Value.ToString() ?? "";
            Guid agentGuid = Guid.Parse(User?.Claims?.Where(x => x.Type == "AgentGuid")?.FirstOrDefault()?.Value);

            return await AgentApi.GetAgentForRoz(agentGuid, ErrorComponent, authToken) ?? new();
        }

        /*    public IActionResult OnPostEditSelections()
            {
                return RedirectToPage("/Settings/NewsCobs");
            }*/

        public async Task<IActionResult> OnPostChangeNotificationDays()
        {
            string authToken = User?.Claims?.Where(x => x.Type == "JwtToken")?.FirstOrDefault()?.Value.ToString() ?? "";

            if (AgentApi != null)
            {
                Agent = await AgentApi.GetAgentForRoz(AgentGuid, ErrorComponent, authToken) ?? new();
                Agent.NotificationFrequencyDays = NotificationFrequencyDays;
                await AgentApi.UpdateAgentRozNotificationTime(Agent, ErrorComponent, authToken);
            }
            return Page();
        }

        public async Task<IActionResult> OnPostArchiveContact(Guid id)
        {
            string authToken = User?.Claims?.Where(x => x.Type == "JwtToken")?.FirstOrDefault()?.Value.ToString() ?? "";
            await ContactsApi.DeleteAgentNewsReceiver(id, ErrorComponent, authToken);

            return RedirectToPage();
        }

        public async Task<PartialViewResult> OnGetEditContactPartial(Guid id)
        {
            string authToken = User?.Claims?.Where(x => x.Type == "JwtToken")?.FirstOrDefault()?.Value.ToString() ?? "";
            AgentNewsReceiver yo = await ContactsApi.GetAgentNewsReceiver(id, ErrorComponent, authToken);
            AddNewNewsContacts = yo;
            return Partial("_NewsContact", yo);
        }

        public async Task<IActionResult> OnPostSaveNewContact()
        {
            AgentNewsReceiver agentNewsReceiver = new()
            {
                ContactName = Request.Form["ContactName"],
                CompanyName = Request.Form["CompanyName"],
                Email = Request.Form["Email"],
                AgentGuid = Guid.Parse(Request.Form["AgentGuid"]),//Will return an empty string if new -> background code checks for that to determine if it should add or edit
                AgentNewsReceiverGuid = Guid.Parse(Request.Form["AgentNewsReceiverGuid"])
            };
            string authToken = User?.Claims?.Where(x => x.Type == "JwtToken")?.FirstOrDefault()?.Value.ToString() ?? "";
            bool result = await ContactsApi.AddAgentNewsReceiver(agentNewsReceiver, ErrorComponent, authToken);
            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostUploadFile(IFormFile file, string tableName)
        {
            if (file == null)
            {
                ErrorMessage = "Error: File Not Found";
                return Page();
            }

            try
            {
                if (file.Length > 1000000)
                {
                    ErrorMessage = "Error: File Too Large";

                    return Page();
                }

                string fileType = string.Empty;
                using (MemoryStream ms = new())
                {
                    file.CopyTo(ms);
                    //SignatureCheck finds out what a file's type is using it's signature
                    fileType = SignatureCheck.GetFileType(ms.ToArray());
                }

                //string.Empty means it was a file type not on the whitelist
                if (fileType == string.Empty)
                {
                    ErrorMessage = "Error File type not supported";
                    return Page();
                }

                string fileEnd = file.FileName.Split('.')[1];
                //What are they doing if they are lying about the file's extension but it's on the whitelist?
                if (fileEnd != fileType)
                {
                    ErrorMessage = "Error: File Corrupted";
                    return Page();
                }

                string authToken = User?.Claims?.Where(x => x.Type == "JwtToken")?.FirstOrDefault()?.Value.ToString() ?? "";
                string agentGuidAsString = User?.Claims?.Where(x => x.Type == "AgentGuid")?.FirstOrDefault()?.Value ?? "";

                if (Guid.TryParse(agentGuidAsString, out Guid agentGuid))
                {
                    Agent = await AgentApi.GetAgentForRoz(agentGuid, ErrorComponent, authToken) ?? new();
                }

                if (file != null && file.Length > 0)
                {
                    using MemoryStream stream = new();
                    await file.CopyToAsync(stream);

                    ResizePngWithSkiaSharp(stream, 250, 375);

                    if (tableName == "company")
                    {
                        await SetCompanyLogoUrl(fileEnd, stream, authToken);
                    }
                    else if (tableName == "agent")
                    {
                        await SetAgentImageUrl(fileEnd, stream, authToken);
                    }
                }

                Logo = file;
                SuccessMessage = "Success";
                return RedirectToPage();
            }
            catch
            {
                return RedirectToPage();
            }
        }

        private async Task SetCompanyLogoUrl(string fileEnd, MemoryStream stream, string authToken)
        {
            DocModel uploaded = await DocApi.UploadDoc("AgencyRozLogo." + fileEnd, stream, Agent.CompanyGuid, Agent.AgentGuid, NodeTypeEnum.Agency, (int)DocTypeEnum.Other, ErrorComponent, authToken);
            Company company = await CompanyApi.GetCompanyAsync(Agent.CompanyGuid, ErrorComponent, authToken);

            await CompanyApi.SetCompanyLogoUrl(Agent.CompanyGuid, uploaded.CloudUrl, company.RolodexGuid, ErrorComponent, authToken);
        }

        private async Task SetAgentImageUrl(string fileEnd, MemoryStream stream, string authToken)
        {
            DocModel uploaded = await DocApi.UploadDoc($"{Agent.AgentName.Replace(" ", "_")}." + fileEnd, stream, Agent.CompanyGuid, Agent.AgentGuid, NodeTypeEnum.Agent, (int)DocTypeEnum.Other, ErrorComponent, authToken);
            Agent = await GetAgentForRoz();
            Agent.ImageUrl = uploaded.CloudUrl;

            await AgentApi.UpdateAgent(Agent, ErrorComponent, authToken);
        }

        public async Task<IActionResult> OnPostUpdateDirectPhone(string directPhone)
        {
            string authToken = User?.Claims?.Where(x => x.Type == "JwtToken")?.FirstOrDefault()?.Value.ToString() ?? "";
            Agent = await GetAgentForRoz();
            Agent.DirectPhone = directPhone;

            bool success = await AgentApi.UpdateAgent(Agent, ErrorComponent, authToken);

            if (!success)
            {
                return NotFound();
            }

            return RedirectToPage();
        }

        public async Task<IActionResult> OnPostDeleteLogo()
        {
            string authToken = User?.Claims?.Where(x => x.Type == "JwtToken")?.FirstOrDefault()?.Value.ToString() ?? "";
            Guid agentGuid = Guid.Parse(User?.Claims?.Where(x => x.Type == "AgentGuid")?.FirstOrDefault()?.Value);
            Agent = await AgentApi.GetAgentForRoz(agentGuid, ErrorComponent, authToken) ?? new();

            if (!string.IsNullOrEmpty(Agent.CompanyLogo))
            {
                Agent.CompanyLogo = "";

                Company company = await CompanyApi.GetCompanyAsync(Agent.CompanyGuid, ErrorComponent, authToken);
                await CompanyApi.SetCompanyLogoUrl(Agent.CompanyGuid, Agent.CompanyLogo, company.RolodexGuid, ErrorComponent, authToken);

                SuccessMessage = "Success";
            }
            else
            {
                ErrorMessage = "No logo found to delete";
                return RedirectToPage();
            }

            return Page();
        }

        public async Task<IActionResult> OnPostUpdatePassword(string currentPassword, string newPassword, string confirmPassword)
        {
            IEnumerable<Claim>? claims = HttpContext.User.Claims;
            string apiToken = claims.Where(c => c.Type == "JwtToken").Select(c => c.Value).SingleOrDefault() ?? "";

            ApplicationUserChangePasswordRequest model = new()
            {
                ApplicationUserGuid = Guid.Parse(claims.Where(c => c.Type == "AgentGuid").Select(c => c.Value).SingleOrDefault() ?? ""),
                Email = claims.Where(c => c.Type == ClaimTypes.Name).Select(c => c.Value).SingleOrDefault() ?? "",
                CurrentPassword = currentPassword,
                NewPassword = newPassword,
                ConfirmPassword = confirmPassword
            };
            (bool success, string errorMessage) = await UserApi.ChangePassword(model, apiToken, null);

            if (!success)
            {
                throw new Exception(errorMessage);
            }

            return RedirectToPage();
        }


        public void ResizePngWithSkiaSharp(Stream stream, int maxWidth, int maxHeight)
        {
            // The desired maximum dimensions

            // Make sure the input stream is at the beginning
            stream.Seek(0, SeekOrigin.Begin);

            // Decode the image from the stream into an SKBitmap
            // (Note: SkiaSharp can decode directly from the stream if it supports seeking,
            //  otherwise you can buffer it into a MemoryStream first.)
            using SKBitmap original = SKBitmap.Decode(stream);
            if (original == null)
            {
                throw new Exception("Unable to decode image. Make sure the input is a valid image format.");
            }

            // Calculate resize ratio to match the "Max" behavior 
            double ratioX = (double)maxWidth / original.Width;
            double ratioY = (double)maxHeight / original.Height;
            double ratio = Math.Min(ratioX, ratioY);

            // Determine new dimensions
            int newWidth = (int)(original.Width * ratio);
            int newHeight = (int)(original.Height * ratio);

            // Prepare a new bitmap with 32-bit color + alpha (Rgba8888)
            var info = new SKImageInfo(newWidth, newHeight, SKColorType.Rgba8888, SKAlphaType.Premul);
            using var resized = new SKBitmap(info);

            // Draw the original image scaled into the new bitmap
            using (var canvas = new SKCanvas(resized))
            {
                canvas.Clear(SKColors.Transparent); // optional clear
                var destRect = new SKRect(0, 0, newWidth, newHeight);
                canvas.DrawBitmap(original, destRect);
            }

            // Reset the output stream before writing
            stream.Seek(0, SeekOrigin.Begin);
            stream.SetLength(0);

            // Encode the resized image as PNG (with alpha channel preserved)
            using SKImage skImage = SKImage.FromBitmap(resized);
            // Quality=100 → high image quality (PNG uses lossless compression, but 100 means full detail).
            using SKData data = skImage.Encode(SKEncodedImageFormat.Png, 100);

            // Write the PNG bytes back into the stream
            data.SaveTo(stream);

            // The stream now contains the resized PNG image
        }
    }
}