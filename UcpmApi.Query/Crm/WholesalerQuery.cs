using ORMStandard;
using ORMStandard.EntityClasses;
using SD.LLBLGen.Pro.ORMSupportClasses;

namespace UcpmApi.Query.Crm;

public class WholesalerQuery : BaseQuery
{
    private readonly IQuickAdapterAsync _quickAdapterAsync;

    public WholesalerQuery(IQuickAdapterAsync quickAdapterAsync)
    {
        _quickAdapterAsync = quickAdapterAsync;
    }

    public WholesalerEntity GetWholesalerByGuid(Guid keyToFetchInto)
    {
        WholesalerEntity wholesaler = new(keyToFetchInto);
        _quickAdapterAsync.FetchEntity(wholesaler);
        return wholesaler;
    }

    public WholesalerEntity GetWholesalerByGuidWithPrefetch(Guid wholesalerGuid)
    {
        WholesalerEntity wholesaler = new(wholesalerGuid);
        PrefetchPath2 path = new(EntityType.WholesalerEntity)
        {
            WholesalerEntity.PrefetchPathWholesalerProgramFeeOverride,
            WholesalerEntity.PrefetchPathWholesalerProgramStateFeeOverride
        };

        _quickAdapterAsync.FetchEntity(wholesaler, path);
        return wholesaler;
    }
}