using LLBLGen.Linq.Prefetch;
using ORMStandard;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.LinqSupportClasses;
using SD.LLBLGen.Pro.ORMSupportClasses;
using SD.LLBLGen.Pro.QuerySpec;

namespace UcpmApi.Query.Crm
{
    public class ProjectOwnerQuery : BaseQuery﻿
    {
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        private readonly IQuickAdapterAsync _quickAdapterAsync;

        public ProjectOwnerQuery(IQuickAdapterAsync quickAdapterAsync)
        {
            _quickAdapterAsync = quickAdapterAsync;
        }

        public async Task<EntityCollection<ProjectOwnerEntity>> GetAllInStateOrAdjacentStates(string stateCode)
        {
            EntityCollection<ProjectOwnerEntity> coverageIssueEntities = [];

            RelationPredicateBucket predicateBucket = new(StateAdjacencyFields.StateCode == stateCode |
                                                          StateAdjacencyFields.AdjacentCode == stateCode);
            predicateBucket.Relations.Add(ProjectOwnerEntity.Relations.ProjectOwnerStateEntityUsingProjectOwnerGuid);
            predicateBucket.Relations.Add(ProjectOwnerStateEntity.Relations.StateEntityUsingStateCode);
            predicateBucket.Relations.Add(StateEntity.Relations.StateAdjacencyEntityUsingStateCode);
            predicateBucket.Relations.Add(ProjectOwnerEntity.Relations﻿
                .ProjectOwnerCoverageIssueEntityUsingProjectOwnerGuid);
            predicateBucket.Relations.Add(ProjectOwnerCoverageIssueEntity.Relations﻿
                .CoverageIssueEntityUsingCoverageIssueGuid);
            PrefetchPath2 prefetchPath = new(EntityType.ProjectOwnerEntity);
            IPrefetchPathElement2 joinPath = prefetchPath.Add(ProjectOwnerEntity.PrefetchPathProjectOwnerState);
            IPrefetchPathElement2 statePath = joinPath.SubPath.Add(ProjectOwnerStateEntity.PrefetchPathState);
            statePath.SubPath.Add(StateEntity.PrefetchPathStateAdjacency);
            IPrefetchPathElement2 projectOwnerCoverageIssuePath =
                prefetchPath.Add(ProjectOwnerEntity.PrefetchPathProjectOwnerCoverageIssue);
            projectOwnerCoverageIssuePath.SubPath.Add(ProjectOwnerCoverageIssueEntity.PrefetchPathCoverageIssue);
            projectOwnerCoverageIssuePath.SubPath.Add(ProjectOwnerCoverageIssueEntity.PrefetchPathCoverageIssueStatus);
            SortExpression sorter = [];

            QueryParameters parameters = new()
            {
                CollectionToFetch = coverageIssueEntities,
                FilterToUse = predicateBucket.PredicateExpression,
                RelationsToUse = predicateBucket.Relations,
                PrefetchPathToUse = prefetchPath,
                SorterToUse = sorter
            };

            await _quickAdapterAsync.FetchEntityCollectionAsync(parameters);
            return coverageIssueEntities;
        }

        public async Task<ProjectOwnerEntity> GetProjectOwner(Guid projectOwnerGuid)
        {
            return await _linqMetaData.ProjectOwner
                .Where(x => x.ProjectOwnerGuid == projectOwnerGuid)
                .FirstOrDefaultAsync();
        }

        public async Task<EntityCollection<ProjectOwnerEntity>> GetProjectOwners(bool isDeleted = false)
        {
            EntityCollection<ProjectOwnerEntity> projectOwnerEntities = [];

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = projectOwnerEntities,
                FilterToUse = ProjectOwnerFields.IsDeleted == isDeleted
            };

            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return projectOwnerEntities;
        }

        // get all duplicate project owners by project owner name
        public async Task<List<IGrouping<string, ProjectOwnerEntity>>> GetDuplicateProjectOwners()
        {
            List<IGrouping<string, ProjectOwnerEntity>> groupDuplicate = await _linqMetaData.ProjectOwner
                .With(x => x.ProjectOwnerCoverageIssue)
                .With(x => x.PolicyProspect)
                .Where(x => x.IsDeleted == false)
                .GroupBy(x => x.ProjectOwnerName)
                .Where(x => x.Count() > 1)
                .OrderByDescending(x => x.Count())
                .ToListAsync();
            return groupDuplicate;
        }

        public async Task<EntityCollection<ProjectOwnerEntity>> GetProjectOwners(List<Guid> projectOwnerGuids)
        {
            EntityCollection<ProjectOwnerEntity> projectOwnerEntities = [];
            QueryParameters queryParameters = new()
            {
                CollectionToFetch = projectOwnerEntities,
                FilterToUse = ProjectOwnerFields.ProjectOwnerGuid.In(projectOwnerGuids)
            };
            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return projectOwnerEntities;
        }
    }
}