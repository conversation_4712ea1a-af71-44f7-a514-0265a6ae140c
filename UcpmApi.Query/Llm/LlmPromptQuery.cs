using LLBLGenHelper;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.Linq;
using UcpmApi.Logging;

namespace UcpmApi.Query.Llm
{
    public class LlmPromptQuery : BaseQuery
    {
        private readonly IQuickAdapterAsync _quickAdapterAsync;

        public LlmPromptQuery(IQuickAdapterAsync quickAdapterAsync)
        {
            _quickAdapterAsync = quickAdapterAsync;
        }

        public LlmPromptEntity GetPromptById(int promptId)
        {
            LlmPromptEntity promptEntity = new(promptId);
            _quickAdapterAsync.FetchEntity(promptEntity);
            return promptEntity;
        }
    }
}
