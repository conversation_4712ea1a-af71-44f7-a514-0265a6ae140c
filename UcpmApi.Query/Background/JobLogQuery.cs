using ORMStandard;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.LinqSupportClasses;
using SD.LLBLGen.Pro.ORMSupportClasses;
using UcpmApi.Shared.Enums;

namespace UcpmApi.Query.JobQuery;

public class JobLogQuery : BaseQuery
{
    private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
    private readonly IQuickAdapterAsync _quickAdapterAsync;

    public JobLogQuery(IQuickAdapterAsync quickAdapterAsync)
    {
        _quickAdapterAsync = quickAdapterAsync;
    }

    public async Task<EntityCollection<JobLogEntity>> GetJobLogsByJobGuid(Guid jobGuid)
    {
        EntityCollection<JobLogEntity> jobLogs = [];
        PrefetchPath2 path = new(EntityType.JobLogEntity)
        {
            JobLogEntity.PrefetchPathJobStatus
        };
        RelationPredicateBucket filter = new(JobLogFields.JobGuid == jobGuid);
        SortExpression sortExpression = new(JobLogFields.LogDate | SortOperator.Descending);
        QueryParameters queryParameters = new()
        {
            CollectionToFetch = jobLogs,
            FilterToUse = filter.PredicateExpression,
            PrefetchPathToUse = path,
            SorterToUse = sortExpression,
            RowsToTake = 100
        };

        await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
        return jobLogs;
    }

    public async Task<JobLogEntity> GetLatestJobLog(Guid jobGuid)
    {
        PrefetchPath2 path = new(EntityType.JobLogEntity)
        {
            JobLogEntity.PrefetchPathJobStatus
        };

        JobLogEntity earliestLog = await _linqMetaData.JobLog
            .WithPath(path)
            .Where(r => r.JobGuid == jobGuid && r.JobStatusId == (int)JobStatusEnum.Successs)
            .OrderByDescending(x => x.LogDate).FirstOrDefaultAsync();
        return earliestLog;
    }
}