using ORMStandard;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.ORMSupportClasses;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Guids;

namespace UcpmApi.Query.Kato
{
    public class KatoQuery : BaseQuery
    {
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        private readonly IQuickAdapterReader _quickAdapterAsync;

        public KatoQuery(IQuickAdapterReader quickAdapterAsync)
        {
            _quickAdapterAsync = quickAdapterAsync;
        }

        public async Task<EntityCollection<FlexInsuredExposureEntity>> GetByPolicy(Guid policyProspectGuid)
        {
            EntityCollection<FlexInsuredExposureEntity> exposures = [];
            RelationPredicateBucket filter = new(FlexInsuredExposurePolicyProspectFields.PolicyProspectGuid == policyProspectGuid);
            filter.Relations.Add(FlexInsuredExposureEntity.Relations.FlexInsuredExposurePolicyProspectEntityUsingFlexInsuredExposureGuid);

            PrefetchPath2 path = new(EntityType.FlexInsuredExposureEntity);

            IPrefetchPath2 assemblyCoveragePartPath = path.Add(FlexInsuredExposureEntity.PrefetchPathIkeaCoveragePartFlexInsuredExposure).SubPath;
            IPrefetchPath2 coveragePartPath = assemblyCoveragePartPath.Add(IkeaCoveragePartFlexInsuredExposureEntity.PrefetchPathIkeaCoveragePart).SubPath;
            IPrefetchPath2 ikeaCoveragePartExclusionPath = coveragePartPath.Add(IkeaCoveragePartEntity.PrefetchPathIkeaCoveragePartExclusion).SubPath;
            IPrefetchPath2 exclusionPath = ikeaCoveragePartExclusionPath.Add(IkeaCoveragePartExclusionEntity.PrefetchPathIkeaExclusion).SubPath;
            IPrefetchPath2 exclusionDefinedTermPath = exclusionPath.Add(IkeaExclusionEntity.PrefetchPathIkeaExclusionDefinedTerm).SubPath;
            exclusionDefinedTermPath.Add(IkeaExclusionDefinedTermEntity.PrefetchPathIkeaDefinedTerm);

            QueryParameters queryParameters = new()
            {
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                CollectionToFetch = exposures,
                PrefetchPathToUse = path
            };
            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return exposures;
        }

        public PackageEntity GetForErm(Guid packageGuid)
        {
            PackageEntity package = new(packageGuid);

            PrefetchPath2 path = new(EntityType.PackageEntity);

            IPrefetchPath2 agentHistoryPath = path.Add(PackageEntity.PrefetchPathInsuredAgentHistory).SubPath;
            agentHistoryPath.Add(InsuredAgentHistoryEntity.PrefetchPathInsured);

            IPrefetchPath2 agentPath = agentHistoryPath.Add(InsuredAgentHistoryEntity.PrefetchPathAgent).SubPath;
            IPrefetchPath2 locationPath = agentPath.Add(AgentEntity.PrefetchPathLocation).SubPath;
            locationPath.Add(LocationEntity.PrefetchPathCompany);

            IPrefetchPath2 policyPath = path.Add(PackageEntity.PrefetchPathPolicyProspect).SubPath;
            IPrefetchPath2 cobPath = policyPath.Add(PolicyProspectEntity.PrefetchPathClassOfBusiness).SubPath;
            IPrefetchPath2 carrierPath = policyPath.Add(PolicyProspectEntity.PrefetchPathCarrierSubmission).SubPath;
            cobPath.Add(ClassOfBusinessEntity.PrefetchPathPathfinderClassOfBusinessBlurb);
            carrierPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionOption);
            carrierPath.Add(CarrierSubmissionEntity.PrefetchPathPolicyStatus);
            IPrefetchPath2 flexAdditionalServicePolicyProspect = policyPath.Add(PolicyProspectEntity.PrefetchPathFlexAdditionalServicePolicyProspect).SubPath;
            flexAdditionalServicePolicyProspect.Add(FlexAdditionalServicePolicyProspectEntity.PrefetchPathFlexAdditionalService);

            IPrefetchPath2 linking = path.Add(PackageEntity.PrefetchPathFlexResponseLink).SubPath;

            IPrefetchPath2 flexResponsePath = linking.Add(FlexResponseLinkEntity.PrefetchPathFlexResponse, 1, null, null, new SortExpression(FlexResponseFields.FlexDefinitionId | SortOperator.Descending)).SubPath;
            IPrefetchPath2 saveStatePath = flexResponsePath.Add(FlexResponseEntity.PrefetchPathFlexResponseSaveState).SubPath;

            IPrefetchPath2 saveVersionPath = saveStatePath.Add(FlexResponseSaveStateEntity.PrefetchPathFlexVersion).SubPath;
            saveVersionPath.Add(FlexVersionEntity.PrefetchPathFlexVersionCarrierOffer);
            IPrefetchPath2 definitionPath = saveVersionPath.Add(FlexVersionEntity.PrefetchPathFlexDefinition).SubPath;
            IPrefetchPath2 publishedVersionPath = definitionPath.Add(FlexDefinitionEntity.PrefetchPathFlexPublishedVersion).SubPath;
            publishedVersionPath.Add(FlexPublishedVersionEntity.PrefetchPathFlexVersion);

            IPrefetchPath2 defPath = flexResponsePath.Add(FlexResponseEntity.PrefetchPathFlexDefinition).SubPath;
            IPrefetchPathElement2 versionPath = defPath.Add(FlexDefinitionEntity.PrefetchPathFlexPublishedVersion).SubPath.Add(FlexPublishedVersionEntity.PrefetchPathFlexVersion);
            //^^ need version that matches whatever the started response is on, do not know if we are versioning forward yet...

            _quickAdapterAsync.FetchEntity(package, path);
            return package;
        }

        public PackageEntity GetForIndex(Guid packageGuid)
        {
            PackageEntity package = new(packageGuid);

            PrefetchPath2 path = new(EntityType.PackageEntity)
            {
                PackageEntity.PrefetchPathPrimaryRepEmployee,
                PackageEntity.PrefetchPathPolicyProspect
            };
            IPrefetchPath2 agentHistoryPath = path.Add(PackageEntity.PrefetchPathInsuredAgentHistory).SubPath;
            agentHistoryPath.Add(InsuredAgentHistoryEntity.PrefetchPathInsured);

            IPrefetchPath2 agentPath = agentHistoryPath.Add(InsuredAgentHistoryEntity.PrefetchPathAgent).SubPath;
            IPrefetchPath2 locationPath = agentPath.Add(AgentEntity.PrefetchPathLocation).SubPath;
            locationPath.Add(LocationEntity.PrefetchPathCompany);

            _quickAdapterAsync.FetchEntity(package, path);
            return package;
        }

        public PackageEntity GetForKatoRmExposure(Guid packageGuid)
        {
            PackageEntity package = new(packageGuid);
            PrefetchPath2 path = new(EntityType.PackageEntity);

            IPrefetchPath2 policyPath = path.Add(PackageEntity.PrefetchPathPolicyProspect).SubPath;
            IPrefetchPath2 claimAdvocate = policyPath.Add(PolicyProspectEntity.PrefetchPathClaimAdvocateEmployee).SubPath;
            IPrefetchPath2 claimConsultant = policyPath.Add(PolicyProspectEntity.PrefetchPathClaimConsultantEmployee).SubPath;
            IPrefetchPath2 cobPath = policyPath.Add(PolicyProspectEntity.PrefetchPathClassOfBusiness).SubPath;

            RelationPredicateBucket docFilter = new(EnviroRiskOverviewFields.IsArchived == false &
                DocFields.IsDeleted == false & ((DocFields.DocName % "ERO%") | (DocFields.DocName % "EPRO%")));
            docFilter.Relations.Add(EnviroRiskOverviewEntity.Relations.DocEntityUsingDocGuid);

            IPrefetchPathElement2 docs = cobPath.Add(ClassOfBusinessEntity.PrefetchPathEnviroRiskOverviewCollectionViaEnviroRiskOverviewCob, 0, docFilter.PredicateExpression, docFilter.Relations).SubPath
                .Add(EnviroRiskOverviewEntity.PrefetchPathDoc);

            IPrefetchPath2 agentHistoryPath = path.Add(PackageEntity.PrefetchPathInsuredAgentHistory).SubPath;
            agentHistoryPath.Add(InsuredAgentHistoryEntity.PrefetchPathInsured);

            IPrefetchPath2 agentPath = agentHistoryPath.Add(InsuredAgentHistoryEntity.PrefetchPathAgent).SubPath;
            IPrefetchPath2 locationPath = agentPath.Add(AgentEntity.PrefetchPathLocation).SubPath;
            locationPath.Add(LocationEntity.PrefetchPathCompany);
            _quickAdapterAsync.FetchEntity(package, path);
            return package;
        }

        public KatoSelectedContentEntity GetKatoSelectedContent(Guid selectedContentGuid)
        {
            KatoSelectedContentEntity ksc = new(selectedContentGuid);
            PrefetchPath2 path = new(EntityType.KatoSelectedContentEntity)
            {
                KatoSelectedContentEntity.PrefetchPathKatoSelectedContentSelection,
            };
            IPrefetchPath2 agentPath = path.Add(KatoSelectedContentEntity.PrefetchPathAgent).SubPath
                .Add(AgentEntity.PrefetchPathLocation).SubPath
                .Add(LocationEntity.PrefetchPathCompany).SubPath;

            IPrefetchPath2 policyPath = path.Add(KatoSelectedContentEntity.PrefetchPathPolicyProspect).SubPath;
            _ = policyPath.Add(PolicyProspectEntity.PrefetchPathPackage).SubPath
                .Add(PackageEntity.PrefetchPathInsuredAgentHistory).SubPath
                .Add(InsuredAgentHistoryEntity.PrefetchPathInsured);

            _quickAdapterAsync.FetchEntity(ksc, path);
            return ksc;
        }

        public PathfinderSynthesiaPolicyEntity GetSynthesiaVideo(Guid policyProspectGuid)
        {
            PathfinderSynthesiaPolicyEntity pse = new(policyProspectGuid);


            PrefetchPath2 path = new(EntityType.PathfinderSynthesiaPolicyEntity);

            _quickAdapterAsync.FetchEntity(pse, path);
            return pse;
        }

        public CompanyIntroVideoEntity GetCompanyIntroVideo(Guid companyGuid)
        {
            CompanyIntroVideoEntity companyIntroVideoEntity = new(companyGuid);
            _quickAdapterAsync.FetchEntity(companyIntroVideoEntity);
            return companyIntroVideoEntity;
        }

        public FlexInsuredExposureEntity GetWithConnections(Guid flexInsuredExposureGuid)
        {
            FlexInsuredExposureEntity flexInsuredExposureEntity = new(flexInsuredExposureGuid);

            PrefetchPath2 path = new(EntityType.FlexInsuredExposureEntity);

            IPrefetchPath2 assemblyCoveragePartPath = path.Add(FlexInsuredExposureEntity.PrefetchPathFlexInsuredExposurePolicyProspect).SubPath;
            IPrefetchPath2 coveragePartPath = assemblyCoveragePartPath.Add(FlexInsuredExposureEntity.PrefetchPathIkeaCoveragePartFlexInsuredExposure).SubPath;

            _quickAdapterAsync.FetchEntity(flexInsuredExposureEntity, path);

            return flexInsuredExposureEntity;
        }

        public KatoSelectedContentSelectionEntity GetBySelectedContentGuid(Guid katoSelectedContentGuid)
        {
            KatoSelectedContentSelectionEntity selectionEntity = new(katoSelectedContentGuid, (int)KatoShareableContentEnum.BindCoverage);
            _quickAdapterAsync.FetchEntity(selectionEntity);
            return selectionEntity;
        }

        public async Task<EntityCollection<InvoiceEntity>> GetInvoiceForPolicy(Guid packageGuid)
        {
            EntityCollection<InvoiceEntity> invoiceEntity = [];
            RelationPredicateBucket filter = new(PolicyProspectFields.PackageGuid == packageGuid);
            filter.PredicateExpression.Add(CarrierSubmissionFields.IsDeleted == false);
            filter.PredicateExpression.Add(InvoiceFields.IsDeleted == false);
            filter.PredicateExpression.Add(PolicyProspectFields.IsDeleted == false);
            filter.PredicateExpression.Add(CarrierSubmissionOptionFields.IsDeleted == false);
            filter.PredicateExpression.Add(CarrierSubmissionFields.CarrierGuid != CarrierList.UnknownCarrierGuid);
            filter.Relations.Add(InvoiceEntity.Relations.CarrierSubmissionOptionEntityUsingCarrierSubmissionOptionGuid);
            filter.Relations.Add(CarrierSubmissionOptionEntity.Relations.CarrierSubmissionEntityUsingCarrierSubmissionGuid);
            filter.Relations.Add(CarrierSubmissionEntity.Relations.PolicyProspectEntityUsingPolicyProspectGuid);

            PrefetchPath2 path = new(EntityType.InvoiceEntity);
            IPrefetchPath2 itemPath = path.Add(InvoiceEntity.PrefetchPathInvoiceItem).SubPath;
            IPrefetchPath2 carrierSubmissionOptionPath = path.Add(InvoiceEntity.PrefetchPathCarrierSubmissionOption).SubPath;
            IPrefetchPathElement2 transactionTypePath = itemPath.Add(InvoiceItemEntity.PrefetchPathTransactionType);
            IPrefetchPath2 submissionPath = carrierSubmissionOptionPath.Add(CarrierSubmissionOptionEntity.PrefetchPathCarrierSubmission).SubPath;
            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathPolicyProspect);
            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathNxusType);

            QueryParameters queryParameters = new()
            {
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                CollectionToFetch = invoiceEntity,
                PrefetchPathToUse = path
            };


            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return invoiceEntity;
        }

        public FlexResponseLinkEntity GetFlexResponseLink(Guid flexResponseGuid)
        {
            FlexResponseLinkEntity flexResposneLink = new(flexResponseGuid);
            _quickAdapterAsync.FetchEntity(flexResposneLink);
            return flexResposneLink;
        }
    }
}