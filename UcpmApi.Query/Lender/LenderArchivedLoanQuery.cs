using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using SD.LLBLGen.Pro.ORMSupportClasses;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Lender.RequestModels;

namespace UcpmApi.Query.Lender;

public class LenderArchivedLoanQuery : BaseQuery
{
    private readonly IQuickAdapterReader _quickAdapterReader;

    public LenderArchivedLoanQuery(IQuickAdapterReader quickAdapterReader)
    {
        _quickAdapterReader = quickAdapterReader;
    }

    #region Lenders

    public async Task<EntityCollection<ResponseLenderCacheEntity>> GetArchivedLoansByInsuredGuid(
        LoanQuotesRequest request)
    {
        EntityCollection<ResponseLenderCacheEntity> entity = [];

        RelationCollection relationCollection =
        [
            PolicyProspectEntity.Relations.PackageEntityUsingPackageGuid,
            PackageEntity.Relations.InsuredAgentHistoryEntityUsingInsuredAgentHistoryGuid,
            InsuredAgentHistoryEntity.Relations.InsuredEntityUsingInsuredGuid
        ];

        FieldCompareSetPredicate innerSubQuery = _quickAdapterReader.GenerateSubQuery(
            ResponseFields.ResponseLinkingGuid,
            PolicyProspectFields.PolicyProspectGuid,
            SetOperator.In,
            InsuredFields.InsuredGuid == request.InsuredGuid,
            relationCollection,
            false);

        FieldCompareSetPredicate subQuery = _quickAdapterReader.GenerateSubQuery(ResponseLenderCacheFields.ResponseGuid,
            ResponseFields.ResponseGuid,
            SetOperator.In,
            innerSubQuery,
            null,
            false);

        RelationPredicateBucket filter = new(subQuery &
                                             ResponseLenderCacheFields.LoanTransactionTypeId ==
                                             (int)LoanTransactionTypeEnum.Archived &
                                             ResponseLenderCacheFields.BorrowerName != string.Empty);

        SortExpression sorter = new(ResponseLenderCacheFields.BorrowerName | SortOperator.Ascending);

        QueryParameters queryParameters = new QueryParameters
        {
            CollectionToFetch = entity,
            RelationsToUse = filter.Relations,
            FilterToUse = filter.PredicateExpression,
            SorterToUse = sorter,
            /*RowsToTake = request.PageSize,
            RowsToSkip = (request.PageNumber - 1) * request.PageSize,*/
        };

        await _quickAdapterReader.FetchEntityCollectionAsync(queryParameters);

        return entity;
    }

    public async Task<int> GetArchivedLoansCount(Guid insuredGuid)
    {
        EntityCollection<ResponseLenderCacheEntity> entity = [];

        RelationCollection relationCollection =
        [
            PolicyProspectEntity.Relations.PackageEntityUsingPackageGuid,
            PackageEntity.Relations.InsuredAgentHistoryEntityUsingInsuredAgentHistoryGuid,
            InsuredAgentHistoryEntity.Relations.InsuredEntityUsingInsuredGuid
        ];

        FieldCompareSetPredicate innerSubQuery = _quickAdapterReader.GenerateSubQuery(
            ResponseFields.ResponseLinkingGuid,
            PolicyProspectFields.PolicyProspectGuid,
            SetOperator.In,
            InsuredFields.InsuredGuid == insuredGuid,
            relationCollection,
            false);

        FieldCompareSetPredicate subQuery = _quickAdapterReader.GenerateSubQuery(ResponseLenderCacheFields.ResponseGuid,
            ResponseFields.ResponseGuid,
            SetOperator.In,
            innerSubQuery,
            null,
            false);

        RelationPredicateBucket filter = new(subQuery &
                                             ResponseLenderCacheFields.LoanTransactionTypeId ==
                                             (int)LoanTransactionTypeEnum.Archived &
                                             ResponseLenderCacheFields.BorrowerName != string.Empty);

        SortExpression sorter = new(ResponseLenderCacheFields.BorrowerName | SortOperator.Ascending);

        QueryParameters queryParameters = new QueryParameters
        {
            CollectionToFetch = entity,
            RelationsToUse = filter.Relations,
            FilterToUse = filter.PredicateExpression,
            SorterToUse = sorter,
        };

        await _quickAdapterReader.FetchEntityCollectionAsync(queryParameters);

        return entity.Count();
    }

    #endregion
}