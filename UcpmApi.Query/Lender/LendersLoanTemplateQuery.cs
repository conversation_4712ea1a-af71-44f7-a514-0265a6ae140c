using ORMStandard;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using SD.LLBLGen.Pro.ORMSupportClasses;
using UcpmApi.Shared;

namespace UcpmApi.Query.Lender;

public class LendersLoanTemplateQuery : BaseQuery
{
    private readonly IQuickAdapterAsync _quickAdapterReader;
   
    public LendersLoanTemplateQuery(IQuickAdapterAsync quickAdapterReader)
    {
        _quickAdapterReader = quickAdapterReader;
    }

    #region Lenders

    public async Task<EntityCollection<LenderLoanTemplateEntity>> GetUserLoanTemplates(Guid insuredGuid)
    {
        //Entity to fill
        EntityCollection<LenderLoanTemplateEntity> templates = new EntityCollection<LenderLoanTemplateEntity>();

        //Filtering
        RelationPredicateBucket filter = new( 
           LenderLoanTemplateFields.InsuredGuid == insuredGuid);

        QueryParameters queryParameters = new()
        {
            CollectionToFetch = templates,
            FilterToUse = filter.PredicateExpression, 
        };

        await _quickAdapterReader.FetchEntityCollectionAsync(queryParameters);
        return templates;
    }

    #endregion
}