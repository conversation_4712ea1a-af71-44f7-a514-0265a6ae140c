using ORMStandard;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.LinqSupportClasses;
using SD.LLBLGen.Pro.ORMSupportClasses;
using UcpmApi.Shared;

namespace UcpmApi.Query.SurplusLines
{
    public class SurplusLinesEndorsementQuery : BaseQuery
    {
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        private readonly IQuickAdapterAsync _quickAdapterAsync;

        public SurplusLinesEndorsementQuery(IQuickAdapterAsync quickAdapterAsync)
        {
            _quickAdapterAsync = quickAdapterAsync;
        }
        public static SortExpression SortByTaxDate = new(EndorsementFields.TaxesDueByZoned | SortOperator.Ascending);

        public IQueryable<SurplusLinesEndorsementEntity> GetSurplusLinesEndorsements()
        {
            PrefetchPath2 path = new(EntityType.SurplusLinesEndorsementEntity)
            {
                SurplusLinesEndorsementEntity.PrefetchPathSurplusLinesUnderwriter,
                SurplusLinesEndorsementEntity.PrefetchPathSurplusLinesInsured,
                SurplusLinesEndorsementEntity.PrefetchPathEmployee
            };
            IPrefetchPath2 dueDiligencePath = path.Add(SurplusLinesEndorsementEntity.PrefetchPathSurplusLinesDueDiligence).SubPath;
            IPrefetchPath2 endorsementTypePath = path.Add(SurplusLinesEndorsementEntity.PrefetchPathSurplusLinesEndorsementType).SubPath;
            IPrefetchPath2 requrestStatusPath = path.Add(SurplusLinesEndorsementEntity.PrefetchPathSurplusLinesRequestStatus).SubPath;

            return _linqMetaData.SurplusLinesEndorsement
                .WithPath(path);
        }

        public async Task<EntityCollection<EndorsementEntity>> GetEndorsementsForProcessing(Guid employeeGuid, DateTime endDate, SurplusLinesTableStatusEnum surplusLinesTableStatusEnum, string stateCode = "")
        {
            EntityCollection<EndorsementEntity> endorsements = new EntityCollection<EndorsementEntity>();

            // Main predicate filter
            RelationPredicateBucket filter = new RelationPredicateBucket(
                EndorsementFields.IsReceived == true &
                EndorsementFields.IsCancelled == false &
                (SurplusLinesStateFields.AssignedToEmployeeGuid == employeeGuid |
                 SurplusLinesStateFields.SecondaryAssignedEmployeeGuid == employeeGuid)
            );

            // Date filter using a computed DbFunctionCall field
            DbFunctionCall filterDate = new DbFunctionCall("CONVERT(date, {0})", new object[] { EndorsementFields.TaxesDueByZoned });
            EntityField2 filterDateField = new EntityField2("FilterDate", filterDate);

            if (surplusLinesTableStatusEnum == SurplusLinesTableStatusEnum.ReadyToFile)
            {
                filter.PredicateExpression.Add(EndorsementFields.SurplusLinesStatusId == (int)SurplusLinesStatusEnum.Unknown |
                 EndorsementFields.SurplusLinesStatusId == (int)SurplusLinesStatusEnum.PendingUcpm);
                filter.PredicateExpression.Add(filterDateField <= endDate.Date);
            }
            else if (surplusLinesTableStatusEnum == SurplusLinesTableStatusEnum.Completed)
            {
                filter.PredicateExpression.Add(EndorsementFields.SurplusLinesStatusId == (int)SurplusLinesStatusEnum.DoneUcpm);
                filter.PredicateExpression.Add(filterDateField >= new DateTime(DateTime.Now.Year, 1, 1).Date);
            }

            // Optional state filter
            if (!string.IsNullOrWhiteSpace(stateCode))
            {
                filter.PredicateExpression.Add(CarrierSubmissionFields.SurplusLinesTaxState == stateCode);
            }

            // Add entity relationships
            filter.Relations.Add(EndorsementEntity.Relations.PolicyProspectEntityUsingPolicyGuid);
            filter.Relations.Add(PolicyProspectEntity.Relations.FinancialSummaryPolicyEntityUsingPolicyGuid);
            filter.Relations.Add(FinancialSummaryPolicyEntity.Relations.CarrierSubmissionEntityUsingSelectedSubmissionGuid);
            filter.Relations.Add(CarrierSubmissionEntity.Relations.StateEntityUsingSurplusLinesTaxState);
            filter.Relations.Add(StateEntity.Relations.SurplusLinesStateEntityUsingStateCode);
            filter.Relations.Add(EndorsementEntity.Relations.InvoiceEntityUsingEndorsementGuid);
            filter.Relations.Add(InvoiceEntity.Relations.InvoiceItemEntityUsingInvoiceGuid);

            // Subfilter for invoice prefetch
            RelationPredicateBucket invoiceSubFilter = new RelationPredicateBucket(
                InvoiceFields.IsDeleted == false & InvoiceFields.IsVoided == false
            );

            // Build prefetch path
            PrefetchPath2 path = new PrefetchPath2(EntityType.EndorsementEntity);
            path.Add(EndorsementEntity.PrefetchPathEndorsementSurplusLine);

            IPrefetchPath2 invoicePath = path.Add(EndorsementEntity.PrefetchPathInvoice, 0, invoiceSubFilter.PredicateExpression).SubPath;
            invoicePath.Add(InvoiceEntity.PrefetchPathInvoiceItem);
            invoicePath.Add(InvoiceEntity.PrefetchPathCarrierSubmissionOption);

            IPrefetchPath2 policyPath = path.Add(EndorsementEntity.PrefetchPathPolicyProspect).SubPath;
            IPrefetchPath2 finPolicyPath = policyPath.Add(PolicyProspectEntity.PrefetchPathFinancialSummaryPolicy).SubPath;
            IPrefetchPath2 submissionPath = finPolicyPath.Add(FinancialSummaryPolicyEntity.PrefetchPathCarrierSubmission).SubPath;
            IPrefetchPath2 statePath = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathSurplusLinesState).SubPath;
            statePath.Add(StateEntity.PrefetchPathSurplusLinesState).SubPath.Add(SurplusLinesStateEntity.PrefetchPathEmployee);

            IPrefetchPath2 packagePath = policyPath.Add(PolicyProspectEntity.PrefetchPathPackage).SubPath;
            IPrefetchPath2 insuredAgentHistoryPath = packagePath.Add(PackageEntity.PrefetchPathInsuredAgentHistory).SubPath;
            insuredAgentHistoryPath.Add(InsuredAgentHistoryEntity.PrefetchPathInsured);

            // Construct query parameters
            QueryParameters queryParameters = new QueryParameters
            {
                CollectionToFetch = endorsements,
                FilterToUse = filter.PredicateExpression,
                RelationsToUse = filter.Relations,
                PrefetchPathToUse = path,
                SorterToUse = SortByTaxDate
            };

            // Fetch async
            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return endorsements;
        }

        public async Task<EntityCollection<SurplusLinesEndorsementEntity>> GetSurplusLinesEndorsementsForProcessing(Guid employeeGuid, DateTime endDate, SurplusLinesTableStatusEnum surplusLinesTableStatusEnum, string stateCode = "")
        {
            EntityCollection<SurplusLinesEndorsementEntity> endorsements = new EntityCollection<SurplusLinesEndorsementEntity>();

            // Main predicate filter
            RelationPredicateBucket filter = new RelationPredicateBucket(
                SurplusLinesEndorsementFields.SurplusLinesEndorsmentStatusId == SurplusLinesEndorsementStatusEnum.Received & 
                (SurplusLinesStateFields.AssignedToEmployeeGuid == employeeGuid |
                 SurplusLinesStateFields.SecondaryAssignedEmployeeGuid == employeeGuid)
            );

            // Date filter using a computed DbFunctionCall field
            DbFunctionCall filterDate = new DbFunctionCall("CONVERT(date, {0})", new object[] { SurplusLinesStateFilingDateFields.DueDate });
            EntityField2 filterDateField = new EntityField2("FilterDate", filterDate);

            if (surplusLinesTableStatusEnum == SurplusLinesTableStatusEnum.ReadyToFile)
            {
                filter.PredicateExpression.Add(SurplusLinesPolicyFields.SurplusLinesPolicyStatusId == (int)SurplusLinesStatusEnum.Unknown |
                 SurplusLinesPolicyFields.SurplusLinesPolicyStatusId == (int)SurplusLinesStatusEnum.PendingUcpm);
                filter.PredicateExpression.Add(filterDateField <= endDate.Date);
            }
            else if (surplusLinesTableStatusEnum == SurplusLinesTableStatusEnum.Completed)
            {
                filter.PredicateExpression.Add(SurplusLinesPolicyFields.SurplusLinesPolicyStatusId == (int)SurplusLinesStatusEnum.DoneUcpm);
                filter.PredicateExpression.Add(filterDateField >= new DateTime(DateTime.Now.Year, 1, 1).Date);
            }

            // Optional state filter
            if (!string.IsNullOrWhiteSpace(stateCode))
            {
                filter.PredicateExpression.Add(SurplusLinesInsuredFields.StateCode == stateCode);
            }

            // Add entity relationships 
            // SurplusLinesEndorsement -> SurplusLinesPolicy through SurplusLinesPolicyGuid
            filter.Relations.Add(SurplusLinesEndorsementEntity.Relations.SurplusLinesPolicyEntityUsingSourceSurplusLinesPolicyGuid);
            // SurplusLinesPolicy -> SurplusLinesInsured through SurplusLinesInsuredGuid
            filter.Relations.Add(SurplusLinesPolicyEntity.Relations.SurplusLinesInsuredEntityUsingSurplusLinesInsuredGuid);
            // SurplusLinesInsured -> State through StateCode
            filter.Relations.Add(SurplusLinesInsuredEntity.Relations.StateEntityUsingStateCode);
            // SurplusLinesState -> State through StateCode
            filter.Relations.Add(SurplusLinesStateEntity.Relations.StateEntityUsingStateCode);
            // SurplusLinesState -> SurplusLinesStateFilingDate through SurplusLinesStateGuid
            filter.Relations.Add(SurplusLinesStateEntity.Relations.SurplusLinesStateFilingDateEntityUsingSurplusLinesStateGuid);
            // SurplusLinesEndorsement -> SurplusLinesInvoice through SurplusLinesEndorsementGuid
            filter.Relations.Add(SurplusLinesEndorsementEntity.Relations.SurplusLinesInvoiceEntityUsingSurplusLinesEndorsementGuid); 

            // Subfilter for invoice prefetch
            RelationPredicateBucket invoiceSubFilter = new RelationPredicateBucket(SurplusLinesInvoiceFields.IsDeleted == false);

            // Build prefetch path
            // Initialize Endorsement Entity
            PrefetchPath2 surplusLinesEndorsementPath = new PrefetchPath2(EntityType.SurplusLinesEndorsementEntity);
            // Endorsement -> Policy
            IPrefetchPath2 surplusLinesPolicyPath = surplusLinesEndorsementPath.Add(SurplusLinesEndorsementEntity.PrefetchPathSurplusLinesPolicy).SubPath;
            // Endorsement -> Insured
            IPrefetchPath2 surplusLinesInsuredPath = surplusLinesEndorsementPath.Add(SurplusLinesEndorsementEntity.PrefetchPathSurplusLinesInsured).SubPath;
            // Endorsement -> Invoice
            IPrefetchPath2 surplusLinesInvoicePath = surplusLinesEndorsementPath.Add(SurplusLinesEndorsementEntity.PrefetchPathSurplusLinesInvoice).SubPath;
            // Insured -> State
            IPrefetchPath2 lookupStatePath = surplusLinesInsuredPath.Add(SurplusLinesInsuredEntity.PrefetchPathState).SubPath;
            // State -> SurplusLinesState
            IPrefetchPath2 surplusLinesStatePath = lookupStatePath.Add(StateEntity.PrefetchPathSurplusLinesState).SubPath;
            // SurplusLinesState -> SurplusLinesStateFilingDate
            IPrefetchPath2 surplusLinesStateFilingDatePath = surplusLinesStatePath.Add(SurplusLinesStateEntity.PrefetchPathSurplusLinesFilingDate).SubPath;

            // Construct query parameters
            QueryParameters queryParameters = new QueryParameters
            {
                CollectionToFetch = endorsements,
                FilterToUse = filter.PredicateExpression,
                RelationsToUse = filter.Relations,
                PrefetchPathToUse = surplusLinesEndorsementPath,
                //SorterToUse = SortByTaxDate
            };

            // Fetch async
            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return endorsements;
        }
    }
}
