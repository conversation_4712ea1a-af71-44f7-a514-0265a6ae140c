using ORMStandard;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.LinqSupportClasses;
using SD.LLBLGen.Pro.ORMSupportClasses;
using SD.LLBLGen.Pro.QuerySpec;

namespace UcpmApi.Query.SurplusLines;

public class SurplusLinesStateRequirementQuery : BaseQuery
{
    private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
    private readonly IQuickAdapterReader _quickAdapterReader;

    public SurplusLinesStateRequirementQuery(IQuickAdapterReader quickAdapterReader)
    {
        _quickAdapterReader = quickAdapterReader;
    }

    public IQueryable<SurplusLinesStateRequirementEntity> GetSurplusLinesStateRequirements()
    {
        return _linqMetaData.SurplusLinesStateRequirement
            .WithPath(path => path
                .Prefetch(r => r.SurplusLinesState));
    }

    public IQueryable<SurplusLinesStateRequirementEntity> GetAllRequirementsByState(string state)
    {
        return _linqMetaData.SurplusLinesStateRequirement
            .WithPath(path => path
                .Prefetch(r => r.SurplusLinesState))
            .Where(r => r.SurplusLinesState.StateCode == state);
    }

    public async Task<EntityCollection<SurplusLinesStateRequirementEntity>>
        GetAllSurplusLinesStateRequirementsByStateGuid(Guid surplusLinesStateGuid)
    {
        EntityCollection<SurplusLinesStateRequirementEntity> surplusLinesStateRequirements = new();

        PrefetchPath2 path = new(EntityType.SurplusLinesStateRequirementEntity)
        {
            { SurplusLinesStateRequirementEntity.PrefetchPathSurplusLinesStateRequirementYear }
        };
        QueryParameters parameter = new()
        {
            CollectionToFetch = surplusLinesStateRequirements,
            SorterToUse = new SortExpression(
                SurplusLinesStateRequirementFields.SurplusLinesStateRequirementGuid.Ascending()),
            PrefetchPathToUse = path,
            FilterToUse =
                new PredicateExpression(SurplusLinesStateFields.SurplusLinesStateGuid == surplusLinesStateGuid)
        };

        await _quickAdapterReader.FetchEntityCollectionAsync(parameter);

        return surplusLinesStateRequirements;
    }
}