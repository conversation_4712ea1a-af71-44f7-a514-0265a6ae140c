using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.ORMSupportClasses;

namespace UcpmApi.Query.SurplusLines
{
    public class ExternalSurplusLinesQuery : BaseQuery
    {
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        private readonly IQuickAdapterAsync _quickAdapterAsync;

        public ExternalSurplusLinesQuery(IQuickAdapterAsync quickAdapterAsync)
        {
            _quickAdapterAsync = quickAdapterAsync;
        }

        public async Task<IEnumerable<ExternalSurplusLinesEntity>> GetAll()
        {
            EntityCollection<ExternalSurplusLinesEntity> externalSurplusLines = [];
            SortExpression sorter = new(ExternalSurplusLinesFields.CompanyGuid | SortOperator.Ascending);

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = externalSurplusLines,
                SorterToUse = sorter,
            };

            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return externalSurplusLines;
        }

        public async Task<ExternalSurplusLinesEntity> GetExternalSurpluslinesByCompanyGuid(Guid companyGuid)
        {
            ExternalSurplusLinesEntity externalSurplusLines = (await GetAll()).ToList().FirstOrDefault(x => x.CompanyGuid == companyGuid);
            return externalSurplusLines;
        }
    }
}
