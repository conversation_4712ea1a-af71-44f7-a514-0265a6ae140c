using ORMStandard;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.LinqSupportClasses;
using SD.LLBLGen.Pro.ORMSupportClasses;
using SD.LLBLGen.Pro.QuerySpec;

namespace UcpmApi.Query.SurplusLines;

public class SurplusLinesFolderTemplateQuery : BaseQuery
{
    private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
    private readonly IQuickAdapterReader _quickAdapterReader;

    public SurplusLinesFolderTemplateQuery(IQuickAdapterReader quickAdapterReader)
    {
        _quickAdapterReader = quickAdapterReader;
    }

    public IQueryable<SurplusLinesFolderTemplateEntity> GetSurplusLinesFolderTemplates()
    {
        return _linqMetaData.SurplusLinesFolderTemplate
            .WithPath(path => path);
    }

    public async Task<EntityCollection<SurplusLinesFolderTemplateEntity>> GetSurplusLinesFolderTemplatesAsync()
    {
        EntityCollection<SurplusLinesFolderTemplateEntity> surplusLinesFolderTemplates = new();

        PrefetchPath2 path = new(EntityType.SurplusLinesFolderTemplateEntity)
        {
            { SurplusLinesFolderTemplateEntity.PrefetchPathSurplusLinesFolderChildTemplate }
        };
        QueryParameters parameter = new()
        {
            CollectionToFetch = surplusLinesFolderTemplates,
            SorterToUse = new SortExpression(
                SurplusLinesFolderTemplateFields.FolderName.Ascending()),
            PrefetchPathToUse = path
        };

        await _quickAdapterReader.FetchEntityCollectionAsync(parameter);

        return surplusLinesFolderTemplates;
    }
    
    public async Task<SurplusLinesFolderTemplateEntity> GetSurplusLinesFolderTemplateByFolderNameAsync(string folderName)
    {
        var query = _linqMetaData.SurplusLinesFolderTemplate
            .Where(x => x.FolderName == folderName);

        var result = await query.ToListAsync();

        return result.FirstOrDefault();
    }
}