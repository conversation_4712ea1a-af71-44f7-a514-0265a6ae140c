using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.LinqSupportClasses;

namespace UcpmApi.Query.Lookups;

public class EmployeeAssignmentLookupQuery : BaseQuery
{
    private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
    
    public async Task<EmployeeAssignmentLookupEntity>  GetAssignedEmployee(int assignmentLookupId)
    { 
        return await _linqMetaData.EmployeeAssignmentLookup
            .FirstOrDefaultAsync(x => x.EmployeeAssignmentLookupId == assignmentLookupId);
    }
}