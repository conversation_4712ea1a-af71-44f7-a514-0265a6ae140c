using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.ORMSupportClasses;

namespace UcpmApi.Query.Doc
{
    public class StorageFoldersQuery : BaseQuery
    {
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        private readonly IQuickAdapterAsync _quickAdapterAsync;

        public StorageFoldersQuery(IQuickAdapterAsync quickAdapterAsync)
        {
            _quickAdapterAsync = quickAdapterAsync;
        }

        public IQueryable<StorageFolderEntity> GetStorageFolders()
        {
            return _linqMetaData.StorageFolder;
        }
        
        public async Task<IEnumerable<StorageFolderEntity>> GetFolderList()
        {
            EntityCollection<StorageFolderEntity> storageFolders = [];
            QueryParameters queryParameters = new()
            {
                CollectionToFetch = storageFolders
            };
            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return storageFolders;
        }
    }
}
