using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.LinqSupportClasses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UcpmApi.Query.Sync
{
    public class SyncJobQuery : BaseQuery
    {
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());

        public SyncJobEntity GetSyncJob(Guid syncJobGuid)
        {
            throw new NotImplementedException();
        }

        public SyncJobEntity GetSyncJobByGuid(Guid syncJobGuid)
        {
            return _linqMetaData.SyncJob.FirstOrDefault(sj => sj.SyncJobGuid == syncJobGuid);
        }

        public IQueryable<SyncJobEntity> GetSyncJobs()
        {
            return _linqMetaData.SyncJob;
                //.WithPath(path => path
                //.Prefetch<SyncJobEntity>(sj => sj.SyncJobStatus).SubPath(sjs => sjs));
        }
    }
}
