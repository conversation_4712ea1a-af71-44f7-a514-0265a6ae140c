using LLBLGenHelper;
using ORMStandard;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using SD.LLBLGen.Pro.ORMSupportClasses;
using UcpmApi.Logging;

namespace UcpmApi.Query.SurplusSolutions
{
    public class SurplusSolutionsInsuredOverviewQuery : BaseQuery
    {
        private readonly IQuickAdapterAsync _quickAdapterAsync;

        public SurplusSolutionsInsuredOverviewQuery(IQuickAdapterAsync quickAdapterAsync)
        {
            _quickAdapterAsync = quickAdapterAsync;
        }

        public async Task<IEnumerable<SurplusLinesPolicyEntity>> GetAccountsByEmployee(Guid employeeGuid)
        {
            EntityCollection<SurplusLinesPolicyEntity> policies = [];

            PrefetchPath2 path = new(EntityType.SurplusLinesPolicyEntity);
            IPrefetchPath2 invoicePath = path.Add(SurplusLinesPolicyEntity.PrefetchPathSurplusLinesInvoice).SubPath;
            invoicePath.Add(SurplusLinesInvoiceEntity.PrefetchPathSurplusLinesInvoiceItem);
            path.Add(SurplusLinesPolicyEntity.PrefetchPathSurplusLinesInsured).SubPath
                .Add(SurplusLinesInsuredEntity.PrefetchPathSurplusLinesCustomerType);
            path.Add(SurplusLinesPolicyEntity.PrefetchPathSurplusLinesPolicyType);
            path.Add(SurplusLinesPolicyEntity.PrefetchPathSurplusLinesRequestStatus);
            

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = policies,
                PrefetchPathToUse = path
            };
            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return policies;
        }

    }
}
