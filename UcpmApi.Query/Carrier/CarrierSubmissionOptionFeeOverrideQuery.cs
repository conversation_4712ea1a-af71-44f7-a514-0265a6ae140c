using MoreLinq;
using ORMStandard;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.ORMSupportClasses;
using UcpmApi.Shared;

namespace UcpmApi.Query.Carrier;

public class CarrierSubmissionOptionFeeOverrideQuery : BaseQuery
{
    private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
    private readonly IQuickAdapterAsync _quickAdapterAsync;

    public CarrierSubmissionOptionFeeOverrideQuery(IQuickAdapterAsync quickAdapterAsync)
    {
        _quickAdapterAsync = quickAdapterAsync;
    }

    public async Task<EntityCollection<CarrierSubmissionOptionFeeOverrideEntity>> GetByCarrierSubmissionOptionGuid(Guid carrierSubmissionOptionGuid)
    {
        EntityCollection<CarrierSubmissionOptionFeeOverrideEntity> results = [];

        QueryParameters queryParameters = new()
        {
            CollectionToFetch = results,
            FilterToUse = new PredicateExpression(CarrierSubmissionOptionFeeOverrideFields.CarrierSubmissionOptionGuid == carrierSubmissionOptionGuid),
        };

        await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
        return results;
    }


}