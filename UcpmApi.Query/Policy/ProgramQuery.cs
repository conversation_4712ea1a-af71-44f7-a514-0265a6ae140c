using ORMStandard;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.LinqSupportClasses;
using SD.LLBLGen.Pro.ORMSupportClasses;
using System.IO;

namespace UcpmApi.Query.Policy
{
    public class ProgramQuery : BaseQuery
    {
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        private readonly IQuickAdapterReader _quickAdapterAsync;

        public ProgramQuery(IQuickAdapterReader quickAdapterAsync)
        {
            _quickAdapterAsync = quickAdapterAsync;
        }

        public IQueryable<ProgramEntity> GetPrograms()
        {
            return _linqMetaData.Program
                .WithPath(path => path)
                .WithPath(path => path.Prefetch(program => program.DcatTemplateProgramSnippet));
        }
        public ProgramEntity GetProgram(Guid programGuid)
        {
            ProgramEntity program = new(programGuid);
            PrefetchPath2 path = new(EntityType.ProgramEntity);

            _quickAdapterAsync.FetchEntity(program, path);
            return program;
        }

        public async Task<EntityCollection<ProgramEntity>> GetProgramsNotInCarrierProgram(Guid carrierGuid)
        {
            //Select From Clause
            EntityCollection<ProgramEntity> programs = [];

            //Where Clause
            RelationPredicateBucket filter = new(ProgramFields.IsDeleted == false);
            FieldCompareSetPredicate notAlreadyGenerated = _quickAdapterAsync.GenerateSubQuery
                (ProgramFields.ProgramGuid, //Field from the Main Query
                CarrierProgramFields.ProgramGuid, //Field from the Sub Query
                SetOperator.In, //Operator to use for comparing the Sub Query Field and Main Query Field
                (CarrierProgramFields.CarrierGuid == carrierGuid), // Condition of the Sub Query
                true); // boolean to Negate the Operator to use for comparing the Sub Query Field and Main Query Field
            filter.PredicateExpression.Add(notAlreadyGenerated);

            //Consolidate Query Parameters
            QueryParameters queryParameters = new()
            {
                CollectionToFetch = programs,
                FilterToUse = filter.PredicateExpression,
            };

            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);

            //Return Entity 
            return programs;
        }


    }
}
