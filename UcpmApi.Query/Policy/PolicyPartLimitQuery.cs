using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.ORMSupportClasses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UcpmApi.Query.Policy
{
    public class PolicyPartLimitQuery : BaseQuery 
    {
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        private readonly IQuickAdapterAsync _quickAdapterAsync;

        public PolicyPartLimitQuery(IQuickAdapterAsync quickAdapterAsync)
        {
            _quickAdapterAsync = quickAdapterAsync;
        }

        public async Task<IEnumerable<PolicyPartLimitEntity>> GetPolicyPartLimit(Guid carrierSubmissionOptionGuid)
        {
            EntityCollection<PolicyPartLimitEntity> policyPartLimitEntities = new();

            RelationPredicateBucket filter = new(
                PolicyPartLimitFields.CarrierSubmissionOptionGuid == carrierSubmissionOptionGuid);


            QueryParameters queryParameters = new()
            {
                CollectionToFetch = policyPartLimitEntities,
                FilterToUse = filter.PredicateExpression,
            };

            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return policyPartLimitEntities;
        }
    }
}
