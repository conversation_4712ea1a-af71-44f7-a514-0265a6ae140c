using LLBLGenHelper;
using ORMStandard;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.ORMSupportClasses;
using UcpmApi.Logging;
using UcpmApi.Shared;

namespace UcpmApi.Query.Acc;

public class FeeTaxQuery : BaseQuery
{
    private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
    private readonly IQuickAdapterAsync _quickAdapterAsync;

    public FeeTaxQuery(IQuickAdapterAsync quickAdapterAsync)
    {
        _quickAdapterAsync = quickAdapterAsync;
    }

    public IQueryable<FeeTaxEntity> GetFeeTaxes()
    {
        return _linqMetaData.FeeTax;
    }

    public async Task<EntityCollection<FeeTaxEntity>> GetStateErpFeeTax(string stateCode, int transactionTypeId)
    {
        EntityCollection<FeeTaxEntity> feeTax = [];
        RelationPredicateBucket filter = new(
            FeeTaxFields.State == stateCode & FeeTaxFields.TransactionTypeId == transactionTypeId);
        SortExpression sorter = new(FeeTaxFields.EffectiveDate | SortOperator.Descending);
        QueryParameters queryParameters = new()
        {
            CollectionToFetch = feeTax,
            FilterToUse = filter.PredicateExpression,
            SorterToUse = sorter
        };
        await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
        return feeTax;
    }

    public async Task<EntityCollection<FeeTaxEntity>> GetAll()
    {
        EntityCollection<FeeTaxEntity> feeTaxCache = [];
        PrefetchPath2 path = new(EntityType.FeeTaxEntity);
        IPrefetchPath2 transactionTypePath = path.Add(FeeTaxEntity.PrefetchPathTransactionType).SubPath;
        transactionTypePath.Add(TransactionTypeEntity.PrefetchPathTransactionGroup);
        IPrefetchPath2 returnPath = path.Add(FeeTaxEntity.PrefetchPathFeeTaxReturn).SubPath;
        IPrefetchPath2 paidOnPath = path.Add(FeeTaxEntity.PrefetchPathFeeTaxPaidOn).SubPath;
        IPrefetchPath2 programPath = path.Add(FeeTaxEntity.PrefetchPathProgram).SubPath;
        IPrefetchPath2 roundingPath = path.Add(FeeTaxEntity.PrefetchPathRounding).SubPath;

        QueryParameters queryParameters = new()
        {
            PrefetchPathToUse = path,
            CollectionToFetch = feeTaxCache
        };
        await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
        return feeTaxCache;
    }

    public IEnumerable<FeeTaxEntity> GetFeeTaxByProgramAndState(string slState, bool excludeTaxes, DateTime effectiveDate, Guid programGuid, bool isTaxExempt = false)
    {
        if (isTaxExempt)
        {
            return [];
        }
        EntityCollection<FeeTaxEntity> feeTaxCollection = [];
        PrefetchPath2 path = new(EntityType.FeeTaxEntity)
        {
            FeeTaxEntity.PrefetchPathTransactionType
        };
        RelationPredicateBucket filter = new(FeeTaxFields.State == slState &
            FeeTaxFields.EffectiveDate <= effectiveDate &
            (FeeTaxFields.ExpireDate >= effectiveDate.Date | FeeTaxFields.ExpireDate == DBNull.Value) &
            (FeeTaxFields.ProgramGuid == programGuid | FeeTaxFields.ProgramGuid == DBNull.Value | FeeTaxFields.ProgramGuid == Guid.Empty));

        if (excludeTaxes)
        {
            filter.PredicateExpression.Add(TransactionTypeFields.TransactionGroupId != (int)TransactionGroupEnum.SlTax);
            filter.Relations.Add(FeeTaxEntity.Relations.TransactionTypeEntityUsingTransactionTypeId);
        }
        SortExpression sorter = new(FeeTaxFields.EffectiveDate | SortOperator.Descending);
        QuickAdapter quickAdapter = new(new DataAccessAdapter(), new DataLogger());

        quickAdapter.FetchEntityCollection(feeTaxCollection, filter, 0, sorter, path);
        return feeTaxCollection.DistinctBy(x => (x.TransactionTypeId, x.TransactionCompanyGuid));
    }

    public async Task<IEnumerable<FeeTaxEntity>> GetFeeTaxForStateAndTransactionGroup(string stateCode, int transactionGroupId)
    {
        EntityCollection<FeeTaxEntity> feeTax = new();
        RelationPredicateBucket filter = new(
            FeeTaxFields.State == stateCode
            & FeeTaxFields.EffectiveDate <= DateTime.Now
            & FeeTaxFields.ExpireDate == DBNull.Value
            & (FeeTaxFields.ProgramGuid == DBNull.Value | FeeTaxFields.ProgramGuid == Guid.Empty)
            & TransactionTypeFields.TransactionGroupId == transactionGroupId);
        filter.Relations.Add(FeeTaxEntity.Relations.TransactionTypeEntityUsingTransactionTypeId);

        SortExpression sorter = new()
        {
            FeeTaxFields.EffectiveDate | SortOperator.Descending
        };

        QueryParameters queryParameters = new()
        {
            CollectionToFetch = feeTax,
            FilterToUse = filter.PredicateExpression,
            RelationsToUse = filter.Relations,
            SorterToUse = sorter
        };

        await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
        IEnumerable<FeeTaxEntity> distinctFeeTaxes = feeTax.DistinctBy(ft => ft.TransactionTypeId);

        return distinctFeeTaxes;
    }
}