using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using SD.LLBLGen.Pro.ORMSupportClasses;

namespace UcpmApi.Query.Survey;

public class QuestionCarrierOfferDefaultCalcQuery : BaseQuery
{
    private readonly IQuickAdapterAsync _quickAdapterAsync;

    public QuestionCarrierOfferDefaultCalcQuery(IQuickAdapterAsync quickAdapterAsync)
    {
        _quickAdapterAsync = quickAdapterAsync;
    }

    /// <summary>
    /// Retrieves all QuestionCarrierOfferDefaultCalcEntity instances associated with a specific collection of QuestionEntity instances.
    /// </summary>
    /// <param name="questions">The collection of QuestionEntity instances.</param>
    /// <returns>A Task that represents the asynchronous operation. The task result contains a collection of QuestionCarrierOfferDefaultCalcEntity instances associated with the provided collection of QuestionEntity instances.</returns>
    public async Task<EntityCollection<QuestionCarrierOfferDefaultCalcEntity>> GetAllByQuestionCollection(
        EntityCollection<QuestionEntity> questions)
    {
        EntityCollection<QuestionCarrierOfferDefaultCalcEntity> questionCarrierOfferDefaultCalcs = new();
        RelationPredicateBucket filter = new();
        foreach (QuestionEntity question in questions)
        {
            filter.PredicateExpression.AddWithOr(QuestionCarrierOfferDefaultCalcFields.QuestionGuid ==
                                                 question.QuestionGuid);
        }

        QueryParameters queryParameters = new QueryParameters
        {
            CollectionToFetch = questionCarrierOfferDefaultCalcs,
            FilterToUse = filter.PredicateExpression
        };

        await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);

        return questionCarrierOfferDefaultCalcs;
    }
}