using ORMStandard;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.ORMSupportClasses;
using System.ComponentModel;
using System.Globalization;
using UcpmApi.Shared.Enums;

namespace UcpmApi.Query.Security
{
    public class InviteLinkQuery : BaseQuery
    {
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        private readonly IQuickAdapterReader _quickAdapterAsync;

        public InviteLinkQuery(IQuickAdapterReader quickAdapterAsync)
        {
            _quickAdapterAsync = quickAdapterAsync;
        }

        public InviteLinkEntity GetInviteLink(Guid inviteGuid)
        {
            InviteLinkEntity inviteLink = new(inviteGuid);
            PrefetchPath2 path = new(EntityType.InviteLinkEntity)
            {
                InviteLinkEntity.PrefetchPathInviteLinkParam
            };
            _quickAdapterAsync.FetchEntity(inviteLink, path);
            return inviteLink;
        }

        public async Task<EntityCollection<InviteLinkEntity>> GetInviteLinksWithPasscodeOrPackageGuidParams()
        {
            EntityCollection<InviteLinkEntity> inviteLinkEntities = [];
            PrefetchPath2 path = new(EntityType.InviteLinkEntity)
            {
                InviteLinkEntity.PrefetchPathInviteLinkParam,
            };

            RelationPredicateBucket filter = new();
            filter.Relations.Add(InviteLinkEntity.Relations.InviteLinkParamEntityUsingInviteLinkGuid);
            filter.PredicateExpression.Add(InviteLinkParamFields.ParamName == "Passcode" | InviteLinkParamFields.ParamName == "PackageGuid");

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = inviteLinkEntities,
                FilterToUse = filter.PredicateExpression,
                RelationsToUse = filter.Relations,
                PrefetchPathToUse = path,
            };

            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);

            return inviteLinkEntities;
        }

        public async Task<EntityCollection<InviteLinkEntity>> GetRpsInviteLinks()
        {
            EntityCollection<InviteLinkEntity> inviteLinkEntities = [];
            PrefetchPath2 path = new(EntityType.InviteLinkEntity)
            {
                InviteLinkEntity.PrefetchPathInviteLinkParam,
            };

            RelationPredicateBucket filter = new(InviteLinkFields.InviteLinkTypeId == InviteLinkTypeEnum.RpsLink);
            filter.Relations.Add(InviteLinkEntity.Relations.InviteLinkParamEntityUsingInviteLinkGuid);
            filter.PredicateExpression.Add(InviteLinkParamFields.ParamName == "FlexResponseGuid");

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = inviteLinkEntities,
                FilterToUse = filter.PredicateExpression,
                RelationsToUse = filter.Relations,
                PrefetchPathToUse = path,
            };

            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);

            return inviteLinkEntities;
        }

        public async Task<EntityCollection<InviteLinkEntity>> GetInviteLinksByAccountGuid(Guid accountGuid)
        {
            EntityCollection<InviteLinkEntity> inviteLinkEntities = [];
            RelationPredicateBucket filter = new();
            filter.PredicateExpression.Add(InviteLinkFields.AccountGuid == accountGuid);
            QueryParameters queryParameters = new()
            {
                CollectionToFetch = inviteLinkEntities,
                FilterToUse = filter.PredicateExpression,
            };
            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);

            return inviteLinkEntities;
        }

        public async Task<InviteLinkEntity> CheckForExistingInviteLink(Guid paramGuid, int inviteLinkType)
        {
            EntityCollection<InviteLinkEntity> invites = [];
            RelationPredicateBucket filter = new(InviteLinkFields.InviteLinkTypeId == inviteLinkType &
                                                 InviteLinkParamFields.ParamValue == paramGuid.ToString());
            filter.Relations.Add(InviteLinkEntity.Relations.InviteLinkParamEntityUsingInviteLinkGuid);
            PrefetchPath2 path = new(EntityType.InviteLinkEntity)
            {
                InviteLinkEntity.PrefetchPathInviteLinkParam
            };
            QueryParameters queryParameters = new()
            {
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                CollectionToFetch = invites,
                PrefetchPathToUse = path,
                RowsToTake = 1
            };

            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return !invites.Any() ? new InviteLinkEntity() : invites.First();
        }

        public async Task<InviteLinkEntity> CheckForExistingInviteLinkForKato(Guid paramGuid, int inviteLinkType)
        {
            EntityCollection<InviteLinkEntity> invites = [];
            RelationPredicateBucket filter = new(InviteLinkFields.InviteLinkTypeId == inviteLinkType &
                                                 InviteLinkFields.InviteExpireZoned > DateTimeOffset.Now &
                                                 InviteLinkParamFields.ParamValue == paramGuid.ToString());
            filter.Relations.Add(InviteLinkEntity.Relations.InviteLinkParamEntityUsingInviteLinkGuid);

            QueryParameters queryParameters = new()
            {
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                CollectionToFetch = invites,
                RowsToTake = 1
            };

            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return !invites.Any() ? new InviteLinkEntity() : invites.First();
        }

        public async Task<InviteLinkEntity> CheckAccountExistingInviteLinkForKato(Guid paramGuid, int inviteLinkType, Guid loggedInAccountGuid)
        {
            EntityCollection<InviteLinkEntity> invites = [];
            RelationPredicateBucket filter = new(InviteLinkFields.InviteLinkTypeId == inviteLinkType &
                                                 InviteLinkFields.InviteExpireZoned > DateTimeOffset.Now &
                                                 InviteLinkParamFields.ParamValue == paramGuid.ToString() &
                                                 InviteLinkFields.AccountGuid == loggedInAccountGuid);
            filter.Relations.Add(InviteLinkEntity.Relations.InviteLinkParamEntityUsingInviteLinkGuid);

            PrefetchPath2 path = new(EntityType.InviteLinkEntity)
            {
                InviteLinkEntity.PrefetchPathInviteLinkParam
            };

            QueryParameters queryParameters = new()
            {
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                CollectionToFetch = invites,
                RowsToTake = 1
            };

            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return !invites.Any() ? new InviteLinkEntity() : invites.First();
        }

        public InviteLinkEntity GetMatching(Guid ilg)
        {
            InviteLinkEntity inviteLink = new(ilg);

            PrefetchPath2 path = new(EntityType.InviteLinkEntity)
            {
                InviteLinkEntity.PrefetchPathInviteLinkParam
            };
            _quickAdapterAsync.FetchEntity(inviteLink, path);
            return inviteLink;
        }

        public async Task<InviteLinkEntity> CheckAccountExistingInviteLinkForDrat(Guid paramGuid, int inviteLinkType, Guid loggedInAccountGuid)
        {
            EntityCollection<InviteLinkEntity> invites = [];
            RelationPredicateBucket filter = new(InviteLinkFields.InviteLinkTypeId == inviteLinkType &
                                                 InviteLinkFields.InviteExpireZoned > DateTimeOffset.Now &
                                                 InviteLinkParamFields.ParamValue == paramGuid.ToString() &
                                                 InviteLinkFields.AccountGuid == loggedInAccountGuid);
            filter.Relations.Add(InviteLinkEntity.Relations.InviteLinkParamEntityUsingInviteLinkGuid);

            SortExpression sorter = new(InviteLinkFields.InviteExpireZoned | SortOperator.Descending);

            PrefetchPath2 path = new(EntityType.InviteLinkEntity)
            {
                InviteLinkEntity.PrefetchPathInviteLinkParam
            };

            QueryParameters queryParameters = new()
            {
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                CollectionToFetch = invites,
                SorterToUse = sorter,
                RowsToTake = 1
            };

            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return !invites.Any() ? new InviteLinkEntity() : invites.First();
        }
    }

    public class StringToGuidTypeConverter : TypeConverter
    {
        public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType)
        {
            return Guid.Parse(value.ToString());
        }
    }
}
