using Microsoft.AspNetCore.Mvc;
using UcpmApi.Shared;
using UcpmApi.Shared.ApiClientBase.Interfaces;
using UcpmApi.Shared.Apis;
using UcpmComponentLibraryEx.Helpers;

namespace UcpmControllerLibraryEx.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CarrierSubmissionOptionController : ControllerBase
    {
        private readonly CarrierSubmissionOptionApi _carrierSubOptApi;
        private readonly IErrorComponent _errorComponent;
        private readonly IHttpContextAccessor _contextAccessor;

        public CarrierSubmissionOptionController(CarrierSubmissionOptionApi carrierSubOptApi,
            IErrorComponent errorComponent,
            IHttpContextAccessor contextAccessor)
        {
            _carrierSubOptApi = carrierSubOptApi;
            _errorComponent = errorComponent;
            _contextAccessor = contextAccessor;
        }

        [HttpPut("UpdateRevenue")]
        public async Task<IActionResult> UpdateRevenue(Guid carrierSubmissionOptionGuid, decimal revenue)
        {
            string jwtToken = Utilities.GetJwtToken(_contextAccessor);

            // Corrected method call to handle the returned CarrierSubmissionOption object
            MessageResponse result = await _carrierSubOptApi.UpdateRevenue(carrierSubmissionOptionGuid, revenue, _errorComponent, jwtToken);

            // Check if the result is not null to determine success
            if (result != null)
            {
                return Ok(result);
            }

            return StatusCode(StatusCodes.Status500InternalServerError);
        }

        [HttpPut("BoundCarrierSubmissionOption")]
        public async Task<IActionResult> BoundCarrierSubmissionOption(Guid carrierSubmissionOptionGuid, DateTimeOffset expirationDate)
        {
            string jwtToken = Utilities.GetJwtToken(_contextAccessor);
            bool success = await _carrierSubOptApi.BoundCarrierSubmissionOption(carrierSubmissionOptionGuid, expirationDate, _errorComponent, jwtToken);

            return success ? Ok(success) : StatusCode(StatusCodes.Status500InternalServerError);
        }
    }
}
