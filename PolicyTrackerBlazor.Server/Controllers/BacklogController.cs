using Microsoft.AspNetCore.Mvc;
using UcpmApi.Shared;
using UcpmApi.Shared.ApiClientBase.Interfaces;
using UcpmApi.Shared.Apis;
using UcpmApi.Shared.Planning;

namespace PolicyTrackerBlazor.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class BacklogController : ControllerBase
    {
        private readonly IErrorComponent _errorComponent;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly BacklogApi _backlogApi;
        private readonly BacklogEstimateApi _estimateApi;
        public BacklogController(IHttpContextAccessor accessor, IErrorComponent errorComponent, BacklogApi backlogApi, BacklogEstimateApi estimateApi)
        {
            _httpContextAccessor = accessor;
            _errorComponent = errorComponent;
            _backlogApi = backlogApi;
            _estimateApi = estimateApi;
        }

        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAll(bool loadFromDevOps)
        {
            string jwtToken = Utilities.GetJwtToken(_httpContextAccessor);

            IEnumerable<Backlog> dashboardInfo = await _backlogApi.GetAll(loadFromDevOps, _errorComponent, jwtToken);
            return Ok(dashboardInfo);
        }

        [HttpGet("GetAllPlanningPokerAdmin")]
        public async Task<IActionResult> GetAllPlanningPokerAdmin(bool loadFromDevOps)
        {
            string jwtToken = Utilities.GetJwtToken(_httpContextAccessor);

            IEnumerable<Backlog> dashboardInfo = await _backlogApi.GetAllPlanningPokerAdmin(loadFromDevOps, _errorComponent, jwtToken);
            return Ok(dashboardInfo);
        }

        [HttpGet("Get")]
        public async Task<IActionResult> Get(Guid backlogGuid)
        {
            string jwtToken = Utilities.GetJwtToken(_httpContextAccessor);

            Backlog dashboardInfo = await _backlogApi.Get(backlogGuid, _errorComponent, jwtToken);
            return Ok(dashboardInfo);
        }

        [HttpGet("GetSprintList")]
        public async Task<IActionResult> GetSprintList(Guid employeeGuid)
        {
            string jwtToken = Utilities.GetJwtToken(_httpContextAccessor);

            IEnumerable<Backlog> dashboardInfo = await _backlogApi.GetSprintList(employeeGuid, _errorComponent, jwtToken);
            return Ok(dashboardInfo);
        }

        [HttpPost("SubmitEstimate")]
        public async Task<IActionResult> SubmitEstimate(BacklogEstimate estimate)
        {
            string jwtToken = Utilities.GetJwtToken(_httpContextAccessor);
            BacklogEstimate result = await _estimateApi.Save(estimate, _errorComponent, jwtToken);
            return Ok(result);
        }

        [HttpPost("SubmitCards")]
        public async Task<IActionResult> SubmitCards(BacklogListModel backlogList)
        {
            string jwtToken = Utilities.GetJwtToken(_httpContextAccessor);

            BacklogListModel dashboardInfo = await _backlogApi.SubmitCards(backlogList, _errorComponent, jwtToken);
            return Ok(dashboardInfo);
        }

        [HttpPost("ArchiveBacklog")]
        public async Task<IActionResult> ArchiveBacklog(BacklogListModel backlogList)
        {
            string jwtToken = Utilities.GetJwtToken(_httpContextAccessor);

            Backlog backlog = await _backlogApi.ArchiveBacklog(backlogList, _errorComponent, jwtToken);
            return Ok(backlog);
        }

        [HttpPost("AddAnswer")]
        public async Task<IActionResult> AddAnswer(BacklogAnswer answer)
        {
            string jwtToken = Utilities.GetJwtToken(_httpContextAccessor);

            BacklogAnswer backlog = await _backlogApi.AddAnswer(answer, _errorComponent, jwtToken);
            return Ok(backlog);
        }

        [HttpPost("AddQuestion")]
        public async Task<IActionResult> AddQuestion(BacklogQuestion question)
        {
            string jwtToken = Utilities.GetJwtToken(_httpContextAccessor);

            BacklogQuestion backlog = await _backlogApi.AddQuestion(question, _errorComponent, jwtToken);
            return Ok(backlog);
        }

        [HttpPost("SubmitAdjustedScore")]
        public async Task<IActionResult> SubmitAdjustedScore(Backlog backlog)
        {
            string jwtToken = Utilities.GetJwtToken(_httpContextAccessor);

            Backlog result = await _backlogApi.SubmitAdjustedScore(backlog, _errorComponent, jwtToken);
            return Ok(result);
        }
    }
}
