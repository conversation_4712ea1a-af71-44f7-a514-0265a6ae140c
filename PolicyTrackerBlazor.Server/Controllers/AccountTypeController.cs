using Microsoft.AspNetCore.Mvc;
using UcpmApi.Shared;
using UcpmApi.Shared.ApiClientBase.Interfaces;
using UcpmApi.Shared.Apis;

namespace PolicyTrackerBlazor.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AccountTypeController : ControllerBase
    {
        private readonly IErrorComponent _errorComponent;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly AccountTypeApi _AccountTypeApi;
        public AccountTypeController(IHttpContextAccessor accessor, IErrorComponent errorComponent, AccountTypeApi accountTypeApi)
        {
            _httpContextAccessor = accessor;
            _errorComponent = errorComponent;
            _AccountTypeApi = accountTypeApi;
        }

        [HttpGet("GetAccountTypes")]
        public async Task<IActionResult> GetAccountTypes()
        {
            string jwtToken = Utilities.GetJwtToken(_httpContextAccessor);
            IEnumerable<AccountType> accountTypes = await _AccountTypeApi.GetAccountTypes(_errorComponent, jwtToken);
            return Ok(accountTypes);
        }
    }
}
