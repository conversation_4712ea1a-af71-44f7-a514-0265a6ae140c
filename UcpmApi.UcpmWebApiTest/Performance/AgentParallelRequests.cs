using EnvironmentInspector;
using EnvironmentInspector.Enums;
using EnvironmentInspector.Factories;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ORMStandard.DatabaseSpecific;
using OrmStandardHelper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using UcpmApi.NonDbLogging;
using UcpmApi.Shared;
using UcpmApi.Shared.Api.Crm;
using UcpmApi.Shared.Api.Security;
using UcpmApi.Shared.ApiClientBase;
using UcpmApi.Shared.Auth;

namespace UcpmApi.UcpmWebApiTest.Performance
{
    [TestClass]
    public class AgentParallelRequests
    {
        private AgentApi _testApi;
        private UserApi _userApi;
        private HttpClient _httpClient;
        private PermissionApi _permissionApi;

        [TestInitialize]
        public void Initialize()
        {
            AppEnvironment appEnvironment =
                new AppEnvironmentFactory(DataAccessAdapter.ConnectionStringKeyName, ForCompanyEnum.Ucpm)
                    .GetAppEnvironmentFromEnvironmentVariables("UcpmAPI-Tests");
            ConfigurationHelper.ConfigureOrm(appEnvironment);
            IErrorLogger errorLogger = new TestErrorLogger();
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("Cache-Control", "no-cache");
            _httpClient.DefaultRequestHeaders.Add("Access-Control-Allow-Origin", "*");
            //_httpClient.DefaultRequestHeaders.Add("test-key", "test value");
            _httpClient.BaseAddress = new Uri("https://ucpm-api-dev.ucpm.com/api/v1/");

            _testApi = new AgentApi(new HttpWrapper(_httpClient), errorLogger);
            _permissionApi = new PermissionApi(new HttpWrapper(_httpClient), errorLogger);
            _userApi = new UserApi(new HttpWrapper(_httpClient), errorLogger);
        }

        [TestMethod]
        public async Task MyTestMethod1()
        {
            ApplicationUser user = await _userApi.ApiTokenLogin(new ApplicationUserApiTokenLoginRequest() { AccountGuid = Guid.Empty, TokenGuid = Guid.Parse("d3e793f9-5cb7-4c14-8a9b-152238982a53") }, null);
            IEnumerable<Permission> dudeski = await _permissionApi.GetAgentPermissions(Guid.Parse("D5ACA22D-260A-46B5-9DFE-95F336617A28"), null, user.JwtToken);
            Assert.IsTrue(dudeski.Any(p => p.PermissionId.Equals(72)));
        }

        [TestMethod]
        public async Task MyTestMethod()
        {
            ApplicationUser user = await _userApi.ApiTokenLogin(
                new ApplicationUserApiTokenLoginRequest()
                {
                    AccountGuid = Guid.Empty,
                    TokenGuid = Guid.Parse("d3e793f9-5cb7-4c14-8a9b-152238982a53")
                }, null);
            Shared.InviteLink link = await _testApi.GetAgentResetLink(Guid.Parse("c2d0d1a0-ba98-45ac-9e81-e08d770a0bf8"), null, user.JwtToken);
            Console.WriteLine(link.AccountGuid);
            Assert.AreEqual(Guid.Parse("c2d0d1a0-ba98-45ac-9e81-e08d770a0bf8"), link.AccountGuid);
            //https://localhost:44301/api/v1/
            //https://localhost:44301/api/v1/Agent/GetAgentResetLink?agentGuid=c2d0d1a0-ba98-45ac-9e81-e08d770a0bf8

        }

        //[TestMethod]
        //public async Task TestPolicyStatusWithPolicyGuid()
        //{
        //    Guid policyProspect = Guid.Parse("41B7AF2C-FE7E-4F9C-B235-2B6462A23725");
        //    PolicyStatus value = await _policyStatusApi.GetPolicyStatusWithPolicyGuid(policyProspect, null, "");
        //    string value1 = value.StatusForAgent;
        //    Assert.AreEqual("Quoted", value1);
        //}
    }
}
