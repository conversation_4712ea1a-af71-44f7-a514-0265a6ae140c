using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Command.SurplusLines;
using UcpmApi.Converter;
using UcpmApi.Shared.SurplusLines;

namespace UcpmApi.UcpmWebApi.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v1/[controller]")]
    public class SurplusLinesStateUsesFolderTemplateController : ControllerBase
    {
        private readonly SurplusLinesStateUsesFolderTemplateCommand _surplusLinesStateUsesFolderTemplateCommand;
        private readonly SurplusLinesStateUsesFolderTemplateConverter _surplusLinesStateUsesFolderTemplateConverter;

        public SurplusLinesStateUsesFolderTemplateController(
            SurplusLinesStateUsesFolderTemplateCommand surplusLinesStateUsesFolderTemplateCommand,
            SurplusLinesStateUsesFolderTemplateConverter surplusLinesStateUsesFolderTemplateConverter)
        {
            _surplusLinesStateUsesFolderTemplateCommand = surplusLinesStateUsesFolderTemplateCommand;
            _surplusLinesStateUsesFolderTemplateConverter = surplusLinesStateUsesFolderTemplateConverter;
        }

        [HttpGet("GetSurplusLinesStateUsesFolderTemplateById")]
        public async Task<IActionResult> GetSurplusLinesStateUsesFolderTemplateById(int surplusLinesFolderTemplateId)
        {
            SurplusLinesStateUsesFolderTemplate surplusLinesStateUsesFolderTemplate = await _surplusLinesStateUsesFolderTemplateConverter.GetSurplusLinesStateUsesFolderTemplateById(surplusLinesFolderTemplateId);

            return Ok(ApiResponse<SurplusLinesStateUsesFolderTemplate>.GetSuccessResponse(surplusLinesStateUsesFolderTemplate));
        }
    }
}
