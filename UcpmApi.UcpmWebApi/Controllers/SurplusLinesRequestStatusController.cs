using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Command.SurplusLines;
using UcpmApi.Converter.SurplusLines;
using UcpmApi.Shared.SurplusLines;

namespace UcpmApi.UcpmWebApi.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v1/[controller]")]
    public class SurplusLinesRequestStatusController : ControllerBase
    {
        private readonly SurplusLinesRequestStatusCommand _surplusLinesRequestStatusCommand;
        private readonly SurplusLinesRequestStatusConverter _surplusLinesRequestStatusConverter;

        public SurplusLinesRequestStatusController(
            SurplusLinesRequestStatusCommand surplusLinesRequestStatusCommand,
            SurplusLinesRequestStatusConverter surplusLinesRequestStatusConverter)
        {
            _surplusLinesRequestStatusCommand = surplusLinesRequestStatusCommand;
            _surplusLinesRequestStatusConverter = surplusLinesRequestStatusConverter;
        }

        [HttpGet("GetSurplusLinesRequestStatues")]
        public async Task<IActionResult> GetSurplusLinesRequestStatues()
        {
            IEnumerable<SurplusLinesRequestStatus> surplusLinesRequestStatuses = await _surplusLinesRequestStatusConverter.GetSurlusLinesRequestStatuses();

            return Ok(ApiResponse<SurplusLinesRequestStatus>.GetSuccessResponse(surplusLinesRequestStatuses, surplusLinesRequestStatuses.Count()));
        }
    }
}
