using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Sprache;
using Swashbuckle.AspNetCore.Annotations;
using UcpmApi.Command.Vetting;
using UcpmApi.Converter;
using UcpmApi.Shared;
using UcpmApi.Shared.Api;
using UcpmApi.Shared.Api.Vetting;
using UcpmApi.Shared.Vetting;

namespace UcpmApi.UcpmWebApi.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v1/[controller]")]

    public class ProcessInstanceController : ControllerBase
    {
        private readonly ProcessInstanceCommand _processInstanceCommand;
        private readonly ProcessInstanceConverter _processInstanceConverter;

        public ProcessInstanceController(
            ProcessInstanceCommand processInstanceCommand,
            ProcessInstanceConverter processInstanceConverter)
        {
            _processInstanceCommand = processInstanceCommand;
            _processInstanceConverter = processInstanceConverter;
        }

        [HttpGet("GetProcessInstanceByGuid")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Gets a ProcessInstance.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> GetProcessInstanceByGuid(Guid processInstanceGuid)
        {
            ProcessInstance processInstance = await _processInstanceConverter.GetProcessInstanceByGuid(processInstanceGuid);

            return Ok(ApiResponse<ProcessInstance>.GetSuccessResponse(processInstance));
        }

        [HttpGet("GetProcessInstanceByAssessed")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Gets a ProcessInstance by assessed Guid.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> GetProcessInstanceByAssessed(Guid assessedGuid)
        {
            ProcessInstance processInstance = await _processInstanceConverter.GetProcessInstanceByAssessed(assessedGuid);

            return Ok(ApiResponse<ProcessInstance>.GetSuccessResponse(processInstance));
        }

        [HttpGet("GetForUcpmViewDrillDown")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Gets a ProcessInstance for UCPM view drill down.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> GetForUcpmViewDrillDown(Guid processInstanceGuid)
        {
            ProcessInstance processInstance = await _processInstanceConverter.GetForUcpmViewDrillDown(processInstanceGuid);

            return Ok(ApiResponse<ProcessInstance>.GetSuccessResponse(processInstance));
        }

        [HttpGet("GetProcessInstancesByEvaluator")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Gets ProcessInstances by evaluator Guid.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> GetProcessInstancesByEvaluator(Guid evaluatorGuid)
        {
            IEnumerable<ProcessInstance> processInstances = await _processInstanceConverter.GetProcessInstancesByEvaluator(evaluatorGuid);

            return Ok(ApiResponse<ProcessInstance>.GetSuccessResponse(processInstances, processInstances.Count()));
        }

        [HttpGet("GetForUcpmView")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Gets ProcessInstances for UCPM View.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> GetForUcpmView()
        {
            IEnumerable<ProcessInstance> processInstances = await _processInstanceConverter.GetForUcpmView();

            return Ok(ApiResponse<ProcessInstance>.GetSuccessResponse(processInstances, processInstances.Count()));
        }

        [HttpGet("GetUnverifiedPolicies")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Gets Unverified Process Instances.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> GetUnverifiedPolicies()
        {
            IEnumerable<ProcessInstance> processInstances = await _processInstanceConverter.GetUnverifiedPolicies();

            return Ok(ApiResponse<ProcessInstance>.GetSuccessResponse(processInstances, processInstances.Count()));
        }
        
        [HttpGet("GetAiVerifiedPolicies")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Gets Ai Verified Process Instances.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> GetAiVerifiedPolicies()
        {
            IEnumerable<ProcessInstance> processInstances = await _processInstanceConverter.GetAiVerifiedPolicies();

            return Ok(ApiResponse<ProcessInstance>.GetSuccessResponse(processInstances, processInstances.Count()));
        }

        [HttpGet("GetRejectedProcessInstances")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Gets rejected Process Instances.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> GetRejectedProcessInstances()
        {
            List<ProcessInstance> processInstances = await _processInstanceConverter.GetRejectedProcessInstances();

            return Ok(ApiResponse<ProcessInstance>.GetSuccessResponse(processInstances, processInstances.Count));
        }

        [HttpGet("CheckForFullPolicyUploads")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Returns the Collection of Process Instance.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> CheckForFullPolicyUploads()
        {
            IEnumerable<ProcessInstance> processInstances = await _processInstanceConverter.CheckForFullPolicyUploads();

            return Ok(ApiResponse<ProcessInstance>.GetSuccessResponse(processInstances, processInstances.Count()));
        }

        [HttpGet("CheckForDummies")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Returns the Count of Process Instance Field.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> CheckForDummies()
        {
            IEnumerable<ProcessInstance> processInstances = await _processInstanceConverter.CheckForDummies();

            return Ok(ApiResponse<ProcessInstance>.GetSuccessResponse(processInstances, processInstances.Count()));
        }

        [HttpGet("GetForBackgroundProcessToRun")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Get the Background process to run",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> GetForBackgroundProcessToRun()
        {
            IEnumerable<ProcessInstance> processes = await _processInstanceConverter.GetForBackgroundProcessToRun();
            return Ok(ApiResponse<ProcessInstance>.GetSuccessResponse(processes, processes.Count()));
        }

        [HttpGet("GetForDocProgressDirectSend")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Get Process Instance Object using ",
            Tags = new[] { "ProcessInstance" }
        )]
        public IActionResult GetForDocProgressDirectSend(Guid processInstanceGuid)
        {
            ProcessInstance processes = _processInstanceConverter.GetForDocProgressDirectSend(processInstanceGuid);
            return Ok(ApiResponse<ProcessInstance>.GetSuccessResponse(processes));
        }

        [HttpPost("AddProcessInstance")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Adds a ProcessInstance.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> AddProcessInstance(ProcessInstance processInstance)
        {
            bool success = await _processInstanceCommand.AddProcessInstance(processInstance);
            return success ? Ok(ApiResponse<ProcessInstance>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }

        [HttpPost("CreateAllProcessInstancesAndHistory")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Creates all the process instances and history.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> CreateAllProcessInstancesAndHistory(CreateAllProcessInstancesAndHistoryParams createAllProcessInstancesAndHistoryParams)
        {
            CreateAllProcessInstancesAndHistoryParams success = await _processInstanceCommand.CreateAllProcessInstancesAndHistory(
                createAllProcessInstancesAndHistoryParams);

            return Ok(ApiResponse<CreateAllProcessInstancesAndHistoryParams>.GetSuccessResponse(success));
        }

        [HttpPost("CreateInviteToUploadPolicyLink")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<UploadPolicyLinkResult>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<UploadPolicyLinkResult>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<UploadPolicyLinkResult>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Creates the invite to upload policy link for the process instance.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> CreateInviteToUploadPolicyLink(CreateInviteToUploadPolicyLinkParams inviteToUploadPolicyLinkParams)
        {
            InviteToUploadResult result = await _processInstanceCommand.CreateInviteToUploadPolicyLink(inviteToUploadPolicyLinkParams.ProcessInstanceGuid, inviteToUploadPolicyLinkParams.SiteUrl);

            return Ok(ApiResponse<InviteToUploadResult>.GetSuccessResponse(result));
        }

        [HttpPut("UpdateProcessInstance")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Updates an existing ProcessInstance.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> UpdateProcessInstance(ProcessInstance processInstance)
        {
            bool success = await _processInstanceCommand.UpdateProcessInstance(processInstance);
            return success ? Ok(ApiResponse<ProcessInstance>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }

        [HttpPut("UpdateProcessInstanceStatusAndAddHistory")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Updates the status of an existing ProcessInstance and creates a history record.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> UpdateProcessInstanceStatusAndAddHistory(ProcessInstance processInstance)
        {
            bool success = await _processInstanceCommand.UpdateProcessInstanceStatusAndAddHistory(processInstance.ProcessInstanceGuid, processInstance.ProcessStatusId);
            return success ? Ok(ApiResponse<ProcessInstance>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }        

        [HttpPut("UpdateProcessAndDocsAddHistory")]
        public async Task<IActionResult> UpdateProcessAndDocsAddHistory(UpdateProcessAndDocsAddHistoryModel request)
        {
            int updated = await _processInstanceCommand.UpdateProcessAndDocsAddHistory(request.ProcessInstanceGuid, request.DocStatus, request.ProcessStatus);
            ApiBoolResponse response = new()
            {
                Result = updated > 0
            };

            return Ok(ApiResponse<ApiBoolResponse>.GetSuccessResponse(response));
        }

        [HttpDelete("ResetProcessInstanceForPolicyReupload")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Updates the status of an existing ProcessInstance and creates a history record.",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> ResetProcessInstanceForPolicyReupload(Guid processInstanceGuid)
        {
            bool success = await _processInstanceCommand.ResetProcessInstanceForPolicyReupload(processInstanceGuid);
            return success ? Ok(ApiResponse<ProcessInstance>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }

        [HttpDelete("CleanRejectedProcessInstanceForPolicyUpload")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<ProcessInstance>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Cleans a rejected process Instance by deleting any previously upload docs. The status remains as \"DocsRejected\".",
            Tags = new[] { "ProcessInstance" }
        )]
        public async Task<IActionResult> CleanRejectedProcessInstanceForPolicyUpload(Guid processInstanceGuid, string rejectionReason)
        {
            bool success = await _processInstanceCommand.CleanRejectedProcessInstanceForPolicyUpload(processInstanceGuid, rejectionReason);
            return success ? Ok(ApiResponse<ProcessInstance>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }
    }
}
