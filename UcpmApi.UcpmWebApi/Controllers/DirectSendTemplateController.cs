using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Converter;
using UcpmApi.Shared;

namespace UcpmApi.UcpmWebApi.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v1/[controller]")]
    public class DirectSendTemplateController : ControllerBase
    {
        private readonly DirectSendTemplateConverter _directSendTemplateConverter;

        public DirectSendTemplateController(DirectSendTemplateConverter directSendTemplateConverter)
        {
            _directSendTemplateConverter = directSendTemplateConverter;
        }

        [HttpGet]
        public async Task<IActionResult> GetDirectSendTemplates()
        {
            IEnumerable<DirectSendTemplate> directSendTemplates = await _directSendTemplateConverter.GetDirectSendTemplates();

            return Ok(ApiResponse<DirectSendTemplate>.GetSuccessResponse(directSendTemplates, directSendTemplates.Count()));
        }

        [HttpGet("{directSendTemplateId:int}")]
        public async Task<IActionResult> GetDirectSendTemplateByGuid([FromRoute] int directSendTemplateId)
        {
            DirectSendTemplate directSendTemplate = await _directSendTemplateConverter.GetDirectSendTemplateById(directSendTemplateId);

            return Ok(ApiResponse<DirectSendTemplate>.GetSuccessResponse(directSendTemplate));
        }
    }
}
