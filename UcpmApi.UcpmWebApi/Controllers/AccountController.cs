using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using UcpmApi.Command.Security;
using UcpmApi.Converter;
using UcpmApi.Query.Crm;
using UcpmApi.Query.Employee;
using UcpmApi.Query.Security;
using UcpmApi.Shared;
using UcpmApi.Shared.Auth;
using UcpmApi.Shared.Security;

namespace UcpmApi.UcpmWebApi.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v1/[controller]")]
    public class AccountController : ControllerBase
    {
        private readonly AccountCommand _accountCommand;
        private readonly AccountConverter _accountConverter;
        private readonly AgentQuery _agentQuery;
        private readonly AccountQuery _accountQuery;
        private readonly EmployeeQuery _employeeQuery;

        public AccountController(
            AccountCommand accountCommand,
            AccountConverter accountConverter,
            AgentQuery agentQuery,
            AccountQuery accountQuery,
            EmployeeQuery employeeQuery)
        {
            _accountCommand = accountCommand;
            _accountConverter = accountConverter;
            _agentQuery = agentQuery;
            _accountQuery = accountQuery;
            _employeeQuery = employeeQuery;
        }

        [HttpGet]
        public async Task<IActionResult> Get(Guid AccountGuid)
        {
            Account account = await _accountConverter.GetAccountByGuid(AccountGuid);

            return Ok(ApiResponse<Account>.GetSuccessResponse(account));
        }

        [HttpGet("GetAccountByResetToken")]
        [AllowAnonymous]
        public async Task<IActionResult> GetAccountByResetToken(Guid resetToken, int accountTypeId)
        {
            Account account = await _accountConverter.GetAccountByResetToken(resetToken, accountTypeId);

            return Ok(ApiResponse<Account>.GetSuccessResponse(account));
        }

        [HttpPost("DisableOrEnableAccount")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<Account>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<Account>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<Account>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Disable Or Enable Account.",
            Tags = new[] { "Account" }
        )]
        public async Task<IActionResult> DisableOrEnableAccount(Account account)
        {
            bool success = await _accountCommand.DisableOrEnableAccount(account.AccountGuid, account.IsDisabled);
            return success ? Ok(ApiResponse<ApplicationUser>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }

        [HttpPost("CreateAccount")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<Account>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<Account>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<Account>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Create Account.",
            Tags = new[] { "Account" }
        )]
        public async Task<IActionResult> CreateAccount(Account account)
        {
            bool success = await _accountCommand.CreateAccount(account.AccountGuid);
            return success ? Ok(ApiResponse<Account>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }

        [HttpPost("CreateAccountByModel")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<Account>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<Account>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<Account>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Create Account from the given model.",
            Tags = new[] { "Account" }
        )]
        public async Task<IActionResult> CreateAccountByModel(Account account)
        {
            bool success = await _accountCommand.CreateAccountByModel(account);
            return success ? Ok(ApiResponse<Account>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }

        [HttpPut("UpdateAgentPassword")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<AgentPasswordUpdate>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<AgentPasswordUpdate>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<AgentPasswordUpdate>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Update an agent password.",
            Tags = new[] { "Account" }
        )]
        [AllowAnonymous]
        public async Task<IActionResult> UpdateAgentPassword(AgentPasswordUpdate agentPasswordUpdate)
        {
            (bool Success, string Message) = await _accountCommand.SaveAgentPassword(agentPasswordUpdate);
            agentPasswordUpdate.Success = Success;
            agentPasswordUpdate.Message = Message;

            return Ok(ApiResponse<AgentPasswordUpdate>.GetSuccessResponse(agentPasswordUpdate));
        }

        [HttpPut("UpdateLastNewsOffsetDate")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<Account>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<Account>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<Account>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "UpdateLastNewsOffsetDate",
            Tags = new[] { "Account" }
        )]
        public async Task<IActionResult> UpdateLastNewsOffsetDate(Account account)
        {
            bool success = await _accountCommand.UpdateLastNewsOffsetDate(account.AccountGuid, account.LastNewsOffSetDate);
            return success ? Ok(ApiResponse<ApplicationUser>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }

        [HttpPost("CreateAccountLoginAudit")]
        [AllowAnonymous]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<Account>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<Account>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<Account>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Create CreateAccountLoginAudit.",
            Tags = new[] { "Account" }
        )]
        public async Task<IActionResult> CreateAccountLoginAudit(AccountLoginAudit accountLoginAudit)
        {
            bool success = await _accountCommand.CreateAccountLoginAudit(accountLoginAudit);
            return success ? Ok(ApiResponse<ApplicationUser>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }

        [HttpGet("HasUserSignedInBefore")]
        public async Task<IActionResult> HasUserSignedInBefore(Guid accountGuid, string sourceWebsite)
        {
            bool success = _accountConverter.HasUserSignedInBefore(accountGuid, sourceWebsite);
            return success ? Ok(ApiResponse<ApplicationUser>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }

        [HttpGet("GetAccountAsync")]
        public async Task<IActionResult> GetAccount(Guid AccountGuid)
        {
            Account account = await _accountConverter.GetAccountByGuid(AccountGuid);

            return Ok(ApiResponse<Account>.GetSuccessResponse(account));
        }

        [HttpPut("ChangeEmployeePassword")]
        public async Task<IActionResult> ChangeEmployeePassword(EmployeePasswordUpdate passwordUpdateModel)
        {
            (bool Success, string Message) = await _accountCommand.SaveEmployeePassword(passwordUpdateModel);
            passwordUpdateModel.Success = Success;
            passwordUpdateModel.Message = Message;

            return Success ? Ok(ApiResponse<EmployeePasswordUpdate>.GetSuccessResponse(passwordUpdateModel)) : StatusCode(StatusCodes.Status500InternalServerError);
        }

        [HttpPut("SaveAdminPassword")]
        public async Task<IActionResult> SaveAdminPassword(AdminPasswordUpdate adminPasswordUpdate)
        {
            (bool Success, string Message) = await _accountCommand.SaveAdminPassword(adminPasswordUpdate);
            adminPasswordUpdate.Success = Success;
            adminPasswordUpdate.Message = Message;

            return Success ? Ok(ApiResponse<AdminPasswordUpdate>.GetSuccessResponse(adminPasswordUpdate)) : StatusCode(StatusCodes.Status500InternalServerError);
        }

        [HttpPut("UpdateTwoFactorAuthentication")]
        public async Task<IActionResult> UpdateTwoFactorAuthentication(Account account)
        {
            (bool Success, string Message) = await _accountCommand.UpdateTwoFactorAuthentication(account);
            account.Success = Success;
            account.Message = Message;

            return Ok(ApiResponse<Account>.GetSuccessResponse(account));
        }

        [HttpPut("AgentSaveResetPasswordHash")]
        [AllowAnonymous]
        public async Task<IActionResult> AgentSaveResetPasswordHash(AgentPasswordUpdate agentPasswordUpdate)
        {
            (bool Success, string Message) = await _accountCommand.AgentSaveResetPasswordHash(agentPasswordUpdate);
            agentPasswordUpdate.Success = Success;
            agentPasswordUpdate.Message = Message;

            return Ok(ApiResponse<AgentPasswordUpdate>.GetSuccessResponse(agentPasswordUpdate));
        }
    }
}
