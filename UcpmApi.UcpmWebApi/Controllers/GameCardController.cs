using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using UcpmApi.Command.Bingo;
using UcpmApi.Converter;
using UcpmApi.Shared;

namespace UcpmApi.UcpmWebApi.Controllers
{
    [ApiController]
    [AllowAnonymous]
    [Route("api/v1/[controller]")]

    public class GameCardController : ControllerBase
    {
        private readonly GameCardCommand _gameCardCommand;
        private readonly GameCardConverter _gameCardConverter;

        public GameCardController(
            GameCardCommand gameCardCommand,
            GameCardConverter gameCardConverter)
        {
            _gameCardCommand = gameCardCommand;
            _gameCardConverter = gameCardConverter;
        }

        [HttpGet("GetGameCardByGuid")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GameCard>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<GameCard>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<GameCard>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Get a game card by Guid.",
            Tags = new[] { "GameCard" }
        )]
        public async Task<IActionResult> GetGameCardByGuid(Guid gameCardGuid)
        {
            GameCard gameCard = await _gameCardConverter.GetGameCardByGuid(gameCardGuid);

            return Ok(ApiResponse<GameCard>.GetSuccessResponse(gameCard));
        }

        [HttpGet("GetGameCardsByGame")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GameCard>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<GameCard>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<GameCard>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Get game cards by game.",
            Tags = new[] { "GameCard" }
        )]
        public async Task<IActionResult> GetGameCardsByGame(Guid gameGuid)
        {
            List<GameCard> gameCards = await _gameCardConverter.GetGameCardsByGame(gameGuid);

            return Ok(ApiResponse<GameCard>.GetSuccessResponse(gameCards, gameCards.Count));
        }

        [HttpGet("GetGameCardsByPlayer")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GameCard>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<GameCard>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<GameCard>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Get game cards by player.",
            Tags = new[] { "GameCard" }
        )]
        public async Task<IActionResult> GetGameCardsByPlayer(Guid bingoPlayerGuid)
        {
            List<GameCard> gameCards = await _gameCardConverter.GetGameCardsByPlayer(bingoPlayerGuid);

            return Ok(ApiResponse<GameCard>.GetSuccessResponse(gameCards, gameCards.Count));
        }

        [HttpPost("AddGameCard")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GameCard>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<GameCard>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<GameCard>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Adds a game card.",
            Tags = new[] { "GameCard" }
        )]
        public async Task<IActionResult> AddGameCard(GameCard gameCard)
        {
            bool success = await _gameCardCommand.AddGameCard(gameCard);
            return success ? Ok(ApiResponse<Agent>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }

        [HttpPut("UpdateGameCard")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GameCard>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<GameCard>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<GameCard>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Updates a game card.",
            Tags = new[] { "GameCard" }
        )]
        public async Task<IActionResult> UpdateGameCard(GameCard gameCard)
        {
            bool success = await _gameCardCommand.UpdateGameCard(gameCard);
            return success ? Ok(ApiResponse<Agent>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }
    }
}
