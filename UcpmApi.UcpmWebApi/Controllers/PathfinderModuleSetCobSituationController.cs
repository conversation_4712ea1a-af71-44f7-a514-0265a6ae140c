using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Command.Pathfinder;
using UcpmApi.Converter;
using UcpmApi.Shared;

namespace UcpmApi.UcpmWebApi.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v1/[controller]")]
    public class PathfinderModuleSetCobSituationController : ControllerBase
    {
        private readonly PathfinderModuleSetCobSituationCommand _PathfinderModuleSetCobSituationCommand;
        private readonly PathfinderModuleSetCobSituationConverter _PathfinderModuleSetCobSituationConverter;

        public PathfinderModuleSetCobSituationController(
            PathfinderModuleSetCobSituationCommand PathfinderModuleSetCobSituationCommand,
            PathfinderModuleSetCobSituationConverter PathfinderModuleSetCobSituationConverter)
        {
            _PathfinderModuleSetCobSituationCommand = PathfinderModuleSetCobSituationCommand;
            _PathfinderModuleSetCobSituationConverter = PathfinderModuleSetCobSituationConverter;
        }

        [HttpGet("Get")]
        public async Task<IActionResult> Get(Guid classOfBusinessGuid, int pathfinderSituationId)
        {
            PathfinderModuleSetCobSituation pathfinderModuleSetCobSituation = _PathfinderModuleSetCobSituationConverter.GetPathfinderModuleSetCobSituationByGuid(classOfBusinessGuid, pathfinderSituationId);

            return Ok(ApiResponse<PathfinderModuleSetCobSituation>.GetSuccessResponse(pathfinderModuleSetCobSituation));
        }
    }
}
