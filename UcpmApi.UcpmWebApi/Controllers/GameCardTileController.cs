using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using UcpmApi.Command.Bingo;
using UcpmApi.Converter;
using UcpmApi.Shared;

namespace UcpmApi.UcpmWebApi.Controllers
{
    [ApiController]
    [AllowAnonymous]
    [Route("api/v1/[controller]")]

    public class GameCardTileController : ControllerBase
    {
        private readonly GameCardTileCommand _gameCardTileCommand;
        private readonly GameCardTileConverter _gameCardTileConverter;

        public GameCardTileController(
            GameCardTileCommand gameCardTileCommand,
            GameCardTileConverter gameCardTileConverter)
        {
            _gameCardTileCommand = gameCardTileCommand;
            _gameCardTileConverter = gameCardTileConverter;
        }

        [HttpGet("GetGameCardTileByGuid")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GameCardTile>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<GameCardTile>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<GameCardTile>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Gets a game card tile by Guid.",
            Tags = new[] { "GameCardTile" }
        )]
        public async Task<IActionResult> GetGameCardTileByGuid(Guid GameCardTileGuid)
        {
            GameCardTile gameCardTile = await _gameCardTileConverter.GetGameCardTileByGuid(GameCardTileGuid);

            return Ok(ApiResponse<GameCardTile>.GetSuccessResponse(gameCardTile));
        }

        [HttpGet("GetGameCardTilesByGameCard")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GameCardTile>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<GameCardTile>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<GameCardTile>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Gets game card tiles by game card.",
            Tags = new[] { "GameCardTile" }
        )]
        public async Task<IActionResult> GetGameCardTilesByGameCard(Guid gameCardGuid)
        {
            List<GameCardTile> gameCardTiles = await _gameCardTileConverter.GetGameCardTilesByGameCard(gameCardGuid);

            return Ok(ApiResponse<GameCardTile>.GetSuccessResponse(gameCardTiles, gameCardTiles.Count));
        }

        [HttpPost("AddGameCardTile")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GameCardTile>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<GameCardTile>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<GameCardTile>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Adds a game card tile.",
            Tags = new[] { "GameCardTile" }
        )]
        public async Task<IActionResult> AddGameCardTile(GameCardTile gameCardTile)
        {
            bool success = await _gameCardTileCommand.AddGameCardTile(gameCardTile);

            return success ? Ok(ApiResponse<GameCardTile>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }

        [HttpPut("UpdateGameCardTile")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<GameCardTile>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<GameCardTile>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<GameCardTile>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Updates an existing game card tile.",
            Tags = new[] { "GameCardTile" }
        )]
        public async Task<IActionResult> UpdateGameCardTile(GameCardTile gameCardTile)
        {
            bool success = await _gameCardTileCommand.UpdateGameCardTile(gameCardTile);

            return success ? Ok(ApiResponse<GameCardTile>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }
    }
}
