using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using UcpmApi.Command.Crm;
using UcpmApi.Converter.Crm;
using UcpmApi.Shared;

namespace UcpmApi.UcpmWebApi.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v1/[controller]")]
    public class InsuredDescriptionController : ControllerBase
    {
        private readonly InsuredDescriptionConverter _insuredDescriptionConverter;
        private readonly InsuredDescriptionCommand _insuredDescriptionCommand;

        public InsuredDescriptionController(
            InsuredDescriptionConverter insuredDescriptionConverter,
            InsuredDescriptionCommand insuredDescriptionCommand)        
        {
            _insuredDescriptionConverter = insuredDescriptionConverter;
            _insuredDescriptionCommand = insuredDescriptionCommand; 
        }

        [HttpGet("GetInsuredDescriptionByInsuredGuid")]
        public async Task<IActionResult> GetInsuredDescriptionByInsuredGuid(Guid insuredGuid)
        {
            InsuredDescription insuredDescription = await _insuredDescriptionConverter.GetInsuredDescriptionByInsuredGuid(insuredGuid);

            return Ok(ApiResponse<InsuredDescription>.GetSuccessResponse(insuredDescription));
        }

        [HttpPost("SaveInsuredDescriptions")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<InsuredDescription>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<InsuredDescription>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<InsuredDescription>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Save Insured Descriptions.",
            Tags = new[] { "InsuredDescriptions" }
        )]
        public async Task<IActionResult> SaveInsuredDescriptions(List<InsuredDescription> insuredDescriptions)
        {
            int success = await _insuredDescriptionCommand.SaveInsuredDescriptions(insuredDescriptions);
            return success > 0
                ? Ok(ApiResponse<Agent>.GetSuccessResponse())
                : StatusCode(StatusCodes.Status500InternalServerError);
        }
    }
}
