using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using UcpmApi.Converter.Commission;
using UcpmApi.Shared.Commission;

namespace UcpmApi.UcpmWebApi.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v1/[controller]")]
    public class CompanyContingencyController : ControllerBase
    {
        private readonly CompanyContingencyConverter _companyContingencyConverter;

        public CompanyContingencyController(CompanyContingencyConverter companyContingencyConverter)
        {
            _companyContingencyConverter = companyContingencyConverter;
        }

        [HttpGet("GetCompanyContingencyByCompanyAndPolicyType")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<CompanyContingency>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<CompanyContingency>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status403Forbidden, Type = typeof(object))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<CompanyContingency>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Gets the CompanyContingency by the given CompanyGuid and PolicyTypeID.",
            Tags = new[] { "CompanyContingency" }
        )]
        public async Task<IActionResult> GetCompanyContingencyByCompanyAndPolicyType(Guid companyGuid, int policyTypeId)
        {
            CompanyContingency companyContingency = await _companyContingencyConverter.GetCompanyContingencyByCompanyAndPolicyType(companyGuid, policyTypeId);

            return Ok(ApiResponse<CompanyContingency>.GetSuccessResponse(companyContingency));
        }

        [HttpGet("GetPercentageByCompanyAndPolicyType")]
        public async Task<IActionResult> GetPercentageByCompanyAndPolicyType(Guid companyGuid, int policyTypeId, DateTimeOffset effectiveDateZoned, Guid programGuid, int nxusTypeId)
        {
            CompanyContingencyPercentageResult result = new();
            result.Percentage = await _companyContingencyConverter.GetPercentageByCompanyAndPolicyType(companyGuid, policyTypeId, effectiveDateZoned, programGuid, nxusTypeId);

            return Ok(ApiResponse<CompanyContingencyPercentageResult>.GetSuccessResponse(result));
        }
    }
}
