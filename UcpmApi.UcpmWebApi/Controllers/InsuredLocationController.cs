using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using UcpmApi.Command.Crm;
using UcpmApi.Converter.Crm;
using UcpmApi.Shared.Crm;

namespace UcpmApi.UcpmWebApi.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v1/[controller]")]
    public class InsuredLocationController : ControllerBase
    {
        private readonly InsuredLocationConverter _insuredLocationConverter;
        private readonly InsuredLocationCommand _insuredLocationCommand;

        public InsuredLocationController(InsuredLocationConverter insuredLocationConverter, InsuredLocationCommand insuredLocationCommand)
        {
            _insuredLocationConverter = insuredLocationConverter;
            _insuredLocationCommand = insuredLocationCommand;
        }

        [HttpGet("InsuredLocationsByCarrierSubmissionOption")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<InsuredLocation>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [SwaggerOperation(
            Summary = "Get Insured Locations By Carrier Submission Option",
            Tags = new[] { "Policy" }
        )]
        public async Task<IActionResult> InsuredLocationsByCarrierSubmissionOption(Guid carrierSubmissionOptionGuid)
        {
            IEnumerable<InsuredLocation> locations =
                await _insuredLocationConverter.InsuredLocationsByCarrierSubmissionOption(carrierSubmissionOptionGuid);

            return Ok(ApiResponse<InsuredLocation>.GetSuccessResponse(locations, locations.Count()));
        }

        [HttpPost("AddInsuredLocation")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<InsuredLocation>))]
        [SwaggerOperation(Summary = "Add Insured Location", Tags = new[] { "InsuredLocation" })]
        public async Task<IActionResult> AddInsuredLocation(InsuredLocation insuredLocation)
        {
            bool result = await _insuredLocationCommand.AddInsuredLocation(insuredLocation);
            return result ? Ok(ApiResponse<InsuredLocation>.GetSuccessResponse()) : StatusCode(StatusCodes.Status500InternalServerError);
        }
    }
}
