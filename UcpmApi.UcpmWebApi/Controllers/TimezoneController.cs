using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Converter;
using UcpmApi.Converter.Timezone;
using UcpmApi.Shared;

namespace UcpmApi.UcpmWebApi.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v1/[controller]")]
    public class TimezoneController : ControllerBase
    {
        private readonly TimezoneConverter _TimezoneConverter;

        public TimezoneController(TimezoneConverter TimezoneConverter)
        {
            _TimezoneConverter = TimezoneConverter;
        }

        [HttpGet("GetTimezones")]
        public async Task<IActionResult> GetTimezones()
        {
            List<Timezone> Timezones = await _TimezoneConverter.GetTimezone();

            return Ok(ApiResponse<Timezone>.GetSuccessResponse(Timezones, Timezones.Count));
        }
    }
}
