using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Command.Doc;
using UcpmApi.Converter;
using UcpmApi.Shared;

namespace UcpmApi.UcpmWebApi.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v1/[controller]")]
    public class DocTypeController : ControllerBase
    {
        private readonly DocTypeCommand _DocTypeCommand;
        private readonly DocTypeConverter _DocTypeConverter;

        public DocTypeController(
            DocTypeCommand DocTypeCommand,
            DocTypeConverter DocTypeConverter)
        {
            _DocTypeCommand = DocTypeCommand;
            _DocTypeConverter = DocTypeConverter;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int docTypeId)
        {
            DocType docType = await _DocTypeConverter.GetDocTypeById(docTypeId);

            return Ok(ApiResponse<DocType>.GetSuccessResponse(docType));
        }

        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAll()
        {
            List<DocType> docTypes = await _DocTypeConverter.GetAll();
            return Ok(ApiResponse<DocType>.GetSuccessResponse(docTypes, docTypes.Count));
        }
    }
}
