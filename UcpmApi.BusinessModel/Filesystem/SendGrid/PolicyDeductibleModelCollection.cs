using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using UtilityParsersStandard;

namespace UcpmApi.BusinessModel.Filesystem
{
    public class PolicyDeductibleModelCollection : List<PolicyDeductibleModel>
    {
        public PolicyDeductibleModelCollection(EntityCollection<PolicyDeductibleEntity> policyDeductibles)
        {
            Load(policyDeductibles);
        }

        //public PolicyDeductibleModelCollection(Guid policyProspectGuid)
        //{
        //    EntityCollection<PolicyDeductibleEntity> policyDeductibles = PolicyDeductibleManager.Get(policyProspectGuid);
        //    Load(policyDeductibles);
        //}

        private void Load(EntityCollection<PolicyDeductibleEntity> policyDeductibles)
        {
            foreach (PolicyDeductibleEntity deductible in policyDeductibles)
            {
                Add(new PolicyDeductibleModel(deductible));
            }
        }

        public override string ToString()
        {
            return string.Join("<br />", this.Select(s => s.ToString()));
        }
    }

    public class PolicyDeductibleModel
    {
        public Guid PolicyProspectGuid { get; set; }
        public int DeductibleTypeId { get; set; }
        public string DeductibleTypeName { get; set; }
        public decimal Deductible { get; set; }

        public PolicyDeductibleModel(PolicyDeductibleEntity deductible)
        {
            PolicyProspectGuid = deductible.PolicyProspectGuid;
            DeductibleTypeId = deductible.DeductibleTypeId;
            DeductibleTypeName = deductible.DeductibleType.DeductibleTypeDescription;
            Deductible = deductible.Deductible;
        }

        public override string ToString()
        {
            return $"{DeductibleTypeName} {Parse.FormatCurrencyScaled(Deductible)}";
        }

        //public void Delete()
        //{
        //    PolicyDeductibleManager.Delete(PackageGuid, DeductibleTypeId);
        //}
    }
}