using FlexLibrary;
using LLBLGenHelper;
using ORMStandard;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.ORMSupportClasses;
using SD.LLBLGen.Pro.QuerySpec;
using UcpmApi.Logging;
using UtilityParsersStandard;

namespace UcpmApi.BusinessModel.Flex
{
    public static class FlexEntityFetcherModel
    {
        private static LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        public static PolicyProspectEntity GetPrefetchForExpirationDateQuery(PolicyProspectEntity policy)
        {
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            PolicyProspectEntity withPrefetch = new(policy.PolicyProspectGuid);
            PrefetchPath2 path = new(EntityType.PolicyProspectEntity)
            {
                PolicyProspectEntity.PrefetchPathCarrierSubmission
            };
            IPrefetchPath2 finacialSummaryPath = path.Add(PolicyProspectEntity.PrefetchPathFinancialSummaryPolicy).SubPath;
            IPrefetchPath2 submissionPath = finacialSummaryPath.Add(FinancialSummaryPolicyEntity.PrefetchPathCarrierSubmission).SubPath;
            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionOption);
            IPrefetchPath2 financialSumbssionPath = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathFinancialSummarySubmission).SubPath;
            financialSumbssionPath.Add(FinancialSummarySubmissionEntity.PrefetchPathCarrierSubmissionOption);
            quickAdapter.FetchEntity(withPrefetch, path);
            return withPrefetch;
        }

        public static async Task<Guid> GetHistoryByInsuredAndAgent(Guid insuredGuid, Guid agentGuid)
        {
            EntityCollection<InsuredAgentHistoryEntity> history = [];
            RelationPredicateBucket filter = new(InsuredAgentHistoryFields.InsuredGuid == insuredGuid
                & InsuredAgentHistoryFields.AgentGuid == agentGuid);

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = history,
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);

            InsuredAgentHistoryEntity currentHistory = history.SingleOrDefault();
            if (currentHistory == null)
            {
                return AddNewHistory(insuredGuid, agentGuid);
            }
            else
            {
                currentHistory.IsDeleted = false;
                await quickAdapter.SaveEntityAsync(currentHistory, true);

                return currentHistory.InsuredAgentHistoryGuid;
            }
        }

        private static Guid AddNewHistory(Guid insuredGuid, Guid agentGuid)
        {
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            InsuredAgentHistoryEntity newHistory = new()
            {
                InsuredGuid = insuredGuid,
                AgentGuid = agentGuid,
                OriginalInsuredGuid = insuredGuid,
                InsuredAgentHistoryGuid = Guid.NewGuid()
            };
            quickAdapter.SaveEntityAsync(newHistory, true);
            return newHistory.InsuredAgentHistoryGuid;
        }

        public static LicenseStateEntity GetByState(string sLStateCode)
        {
            LicenseStateEntity issuedLicense = new();

            PredicateExpression expression = new(LicenseStateFields.StateCode == sLStateCode);
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            quickAdapter.FetchEntityUsingUniqueConstraint(issuedLicense, expression, null);

            return issuedLicense; //if this doesn't work, let's add a filter
        }

        public static async Task AddNewCobToInsured(Guid insuredGuid, Guid classOfBusinessGuid)
        {
            InsuredClassOfBusinessEntity icob = new(insuredGuid, classOfBusinessGuid);
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            quickAdapter.FetchEntity(icob);
            await quickAdapter.SaveEntityAsync(icob);
        }

        public static InsuredEntity GetInsured(Guid insuredGuid)
        {
            InsuredEntity insured = new(insuredGuid);
            PrefetchPath2 path = new(EntityType.InsuredEntity);
            IPrefetchPath2 insuredAgentHistoryPath = path.Add(InsuredEntity.PrefetchPathInsuredAgentHistory).SubPath;
            IPrefetchPath2 packagePath = insuredAgentHistoryPath.Add(InsuredAgentHistoryEntity.PrefetchPathPackage).SubPath;
            IPrefetchPath2 policyPath = packagePath.Add(PackageEntity.PrefetchPathPolicyProspect).SubPath;
            IPrefetchPath2 submissionPath = policyPath.Add(PolicyProspectEntity.PrefetchPathCarrierSubmission).SubPath;
            path.Add(InsuredEntity.PrefetchPathAdditionalInsured);
            path.Add(InsuredEntity.PrefetchPathAdditionalNamedInsured);
            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathPolicyStatus);
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            quickAdapter.FetchEntity(insured, path);
            return insured;
        }

        public static async Task<EntityCollection<PackageEntity>> GetPackagesByInsuredAgentHistoryGuid(Guid insuredAgentHistoryGuid)
        {
            EntityCollection<PackageEntity> packages = [];
            RelationPredicateBucket filter = new(PackageFields.InsuredAgentHistoryGuid == insuredAgentHistoryGuid);
            filter.Relations.Add(PackageEntity.Relations.ProgramEntityUsingProgramGuid);
            SortExpression sorter = new(PackageFields.PackageDate | SortOperator.Ascending)
            {
                ProgramFields.ProgramName | SortOperator.Ascending
            };
            PrefetchPath2 path = new(EntityType.PackageEntity);
            IPrefetchPath2 policyPath = path.Add(PackageEntity.PrefetchPathPolicyProspect).SubPath;
            IPrefetchPath2 finPolicyPath = policyPath.Add(PolicyProspectEntity.PrefetchPathFinancialSummaryPolicy).SubPath;
            IPrefetchPath2 selectedSubmissionPath = finPolicyPath.Add(FinancialSummaryPolicyEntity.PrefetchPathCarrierSubmission).SubPath;
            IPrefetchPath2 submissionPath = policyPath.Add(PolicyProspectEntity.PrefetchPathCarrierSubmission).SubPath;
            IPrefetchPath2 submissionFormPath = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathSubmissionForm).SubPath;
            submissionFormPath.Add(SubmissionFormEntity.PrefetchPathForm);
            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionSubjectivity);

            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathPolicyStatus);
            IPrefetchPath2 optionPath = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionOption).SubPath;
            IPrefetchPath2 invoicePath = optionPath.Add(CarrierSubmissionOptionEntity.PrefetchPathInvoice).SubPath;
            IPrefetchPath2 itemPath = invoicePath.Add(InvoiceEntity.PrefetchPathInvoiceItem).SubPath;
            itemPath.Add(InvoiceItemEntity.PrefetchPathTransactionType);
            itemPath.Add(InvoiceItemEntity.PrefetchPathTransactionCompany);
            QueryParameters queryParams = new()
            {
                CollectionToFetch = packages,
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                SorterToUse = sorter,
                PrefetchPathToUse = path
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParams);
            return packages;
        }

        public static async Task<IEnumerable<FormEntity>> GetByGuids(IEnumerable<Guid> formGuids)
        {
            EntityCollection<FormEntity> forms = [];
            RelationPredicateBucket filter = new();
            foreach (Guid item in formGuids)
            {
                filter.PredicateExpression.AddWithOr(FormFields.FormGuid == item);
            }

            QueryParameters queryParameters = new()
            {
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                CollectionToFetch = forms
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);
            return forms;

        }

        public static async Task<EntityCollection<CarrierBillingOfficeEntity>> GetOfficeByCarrierAndIssuingCompany(Guid carrierGuid, Guid issuingCompanyGuid)
        {
            SortExpression sort = new(CarrierBillingOfficeFields.NewtonCarrierCode | SortOperator.Ascending);
            EntityCollection<CarrierBillingOfficeEntity> billingOfficeIssuingCompanies = [];
            RelationPredicateBucket joinTableFilter = new(CarrierBillingOfficeIssuingCompanyFields.IssuingCompanyGuid == issuingCompanyGuid);
            joinTableFilter.Relations.Add(CarrierBillingOfficeEntity.Relations.CarrierBillingOfficeIssuingCompanyEntityUsingCarrierBillingOfficeGuid);
            QueryParameters queryParameters = new()
            {
                CollectionToFetch = billingOfficeIssuingCompanies,
                SorterToUse = sort,
                FilterToUse = joinTableFilter.PredicateExpression,
                RelationsToUse = joinTableFilter.Relations
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);
            if (billingOfficeIssuingCompanies.Any())
            {
                return billingOfficeIssuingCompanies;
            }

            EntityCollection<CarrierBillingOfficeEntity> offices = [];
            RelationPredicateBucket filter = new(
                CarrierBillingOfficeFields.CarrierGuid == carrierGuid);

            queryParameters = new()
            {
                CollectionToFetch = offices,
                SorterToUse = sort,
                FilterToUse = filter.PredicateExpression
            };
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);
            return offices;
        }

        public static PolicyProspectEntity GetPolicyAndSubmissionCarriers(Guid policyGuid)
        {
            PolicyProspectEntity policy = new(policyGuid);
            PrefetchPath2 path = new(EntityType.PolicyProspectEntity);
            IPrefetchPath2 financialPath = path.Add(PolicyProspectEntity.PrefetchPathFinancialSummaryPolicy).SubPath;
            financialPath.Add(FinancialSummaryPolicyEntity.PrefetchPathCarrierSubmission);

            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            quickAdapter.FetchEntity(policy, path);
            return policy;
        }

        public static PolicyProspectEntity GetPolicyAndSubmissionCarriersSimple(Guid policyGuid)
        {
            PolicyProspectEntity policy = new(policyGuid);
            PrefetchPath2 path = new(EntityType.PolicyProspectEntity);
            IPrefetchPath2 submissionPath = path.Add(PolicyProspectEntity.PrefetchPathCarrierSubmission).SubPath;

            IPrefetchPath2 optionPath = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionOption).SubPath;
            optionPath.Add(CarrierSubmissionOptionEntity.PrefetchPathFlexMaterPlan);
            optionPath.Add(CarrierSubmissionOptionEntity.PrefetchPathCarrierSubmissionOptionFeeOverride);

            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathNxusType);
            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrier);
            IPrefetchPath2 invoicePath = optionPath.Add(CarrierSubmissionOptionEntity.PrefetchPathInvoice).SubPath;
            IPrefetchPath2 itemPath = invoicePath.Add(InvoiceEntity.PrefetchPathInvoiceItem).SubPath;
            itemPath.Add(InvoiceItemEntity.PrefetchPathTransactionType);

            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionSubjectivity);
            IPrefetchPath2 submissionFormPath = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathSubmissionForm).SubPath;
            submissionFormPath.Add(SubmissionFormEntity.PrefetchPathForm);
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            quickAdapter.FetchEntity(policy, path);
            return policy;
        }

        public static async Task<FlexResponseSaveStateEntity> GetLatestByResponseAsyncForGetPricing(Guid flexResponseGuid, bool submittedOnly = false)
        {
            EntityCollection<FlexResponseSaveStateEntity> collection = [];

            PrefetchPath2 path = new(EntityType.FlexResponseSaveStateEntity)
            {
                FlexResponseSaveStateEntity.PrefetchPathAccount
            };

            IPrefetchPath2 versionPath = path.Add(FlexResponseSaveStateEntity.PrefetchPathFlexVersion).SubPath;
            IPrefetchPath2 versionOfferPath = versionPath.Add(FlexVersionEntity.PrefetchPathFlexVersionCarrierOffer).SubPath;
            IPrefetchPath2 offerPath = versionOfferPath.Add(FlexVersionCarrierOfferEntity.PrefetchPathCarrierMaximumOffer).SubPath;
            IPrefetchPath2 carrierProgramPath = offerPath.Add(CarrierMaximumOfferEntity.PrefetchPathCarrierProgram).SubPath;
            carrierProgramPath.Add(CarrierProgramEntity.PrefetchPathCarrier);

            IPrefetchPath2 flexResponsePath = path.Add(FlexResponseSaveStateEntity.PrefetchPathFlexResponse).SubPath;
            IPrefetchPath2 subPath = flexResponsePath.Add(FlexResponseEntity.PrefetchPathFlexDefinition).SubPath;
            subPath.Add(FlexDefinitionEntity.PrefetchPathProgram);

            flexResponsePath.Add(FlexResponseEntity.PrefetchPathCarrierSubmissionFlexResponse);
            IPrefetchPath2 responseLinkPath = flexResponsePath.Add(FlexResponseEntity.PrefetchPathFlexResponseLink).SubPath;
            IPrefetchPath2 pathRecordingPath = responseLinkPath.Add(FlexResponseLinkEntity.PrefetchPathPathRecording).SubPath;
            IPrefetchPath2 recordingAgentPath = pathRecordingPath.Add(PathRecordingEntity.PrefetchPathAgent).SubPath;
            IPrefetchPath2 recordingLocationPath = recordingAgentPath.Add(AgentEntity.PrefetchPathLocation).SubPath;
            recordingLocationPath.Add(LocationEntity.PrefetchPathCompany);

            IPrefetchPath2 linkAgentPath = responseLinkPath.Add(FlexResponseLinkEntity.PrefetchPathAgent).SubPath;
            IPrefetchPath2 packagePath = responseLinkPath.Add(FlexResponseLinkEntity.PrefetchPathPackage).SubPath;
            packagePath.Add(PackageEntity.PrefetchPathPrimaryRepEmployee);
            packagePath.Add(PackageEntity.PrefetchPathProgram);

            IPrefetchPath2 insuredAgentHistoryPat = packagePath.Add(PackageEntity.PrefetchPathInsuredAgentHistory).SubPath;
            insuredAgentHistoryPat.Add(InsuredAgentHistoryEntity.PrefetchPathInsured);
            IPrefetchPath2 policyPath = packagePath.Add(PackageEntity.PrefetchPathPolicyProspect).SubPath;
            IPrefetchPath2 submissionPath = policyPath.Add(PolicyProspectEntity.PrefetchPathCarrierSubmission).SubPath;
            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathNxusType);
            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrier);
            IPrefetchPath2 submisisonFormPAth = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathSubmissionForm).SubPath;
            submisisonFormPAth.Add(SubmissionFormEntity.PrefetchPathForm);

            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionSubjectivity);

            IPrefetchPath2 coveragePartCarrierSubmission = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathIkeaCarrierSubmissionCoveragePart).SubPath;
            IPrefetchPath2 coveragePartPath = coveragePartCarrierSubmission.Add(IkeaCarrierSubmissionCoveragePartEntity.PrefetchPathIkeaCoveragePart).SubPath;
            coveragePartPath.Add(IkeaCoveragePartEntity.PrefetchPathIkeaCoveragePartVersionFactor);

            IPrefetchPath2 submissionDefinedTermPath = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathIkeaCarrierSubmissionDefinedTerm).SubPath;
            IPrefetchPath2 definedTermPath = submissionDefinedTermPath.Add(IkeaCarrierSubmissionDefinedTermEntity.PrefetchPathIkeaDefinedTerm).SubPath;
            definedTermPath.Add(IkeaDefinedTermEntity.PrefetchPathIkeaDefinedTermVersionFactor);

            IPrefetchPath2 submissionDefinedTermOptionPath = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathIkeaCarrierSubmissionDefinedTermOption).SubPath;
            IPrefetchPath2 definedTermOptionPath = submissionDefinedTermOptionPath.Add(IkeaCarrierSubmissionDefinedTermOptionEntity.PrefetchPathIkeaDefinedTermOption).SubPath;
            definedTermOptionPath.Add(IkeaDefinedTermOptionEntity.PrefetchPathIkeaDefinedTermOptionVersionFactor);
            definedTermOptionPath.Add(IkeaDefinedTermOptionEntity.PrefetchPathIkeaDefinedTerm);

            IPrefetchPath2 submissionOptionPath = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionOption).SubPath;
            IPrefetchPath2 materPlanPath = submissionOptionPath.Add(CarrierSubmissionOptionEntity.PrefetchPathFlexMaterPlan).SubPath;
            materPlanPath.Add(FlexMaterPlanEntity.PrefetchPathCarrierSubmissionOption);


            IPrefetchPath2 invoicePath = submissionOptionPath.Add(CarrierSubmissionOptionEntity.PrefetchPathInvoice).SubPath;
            IPrefetchPath2 invoiceItemPath = invoicePath.Add(InvoiceEntity.PrefetchPathInvoiceItem).SubPath;
            invoiceItemPath.Add(InvoiceItemEntity.PrefetchPathTransactionType);

            IPrefetchPath2 agentPath = insuredAgentHistoryPat.Add(InsuredAgentHistoryEntity.PrefetchPathAgent).SubPath;
            IPrefetchPath2 locationPath = agentPath.Add(AgentEntity.PrefetchPathLocation).SubPath;
            locationPath.Add(LocationEntity.PrefetchPathCompany);

            RelationPredicateBucket filter = new(FlexResponseSaveStateFields.FlexResponseGuid == flexResponseGuid);
            SortExpression sorter = new(FlexResponseSaveStateFields.SavedAt | SortOperator.Descending);
            if (submittedOnly)
            {
                filter.PredicateExpression.Add(FlexResponseSaveStateFields.WasUserSubmit == true);
            }
            QueryParameters queryParameters = new()
            {
                PrefetchPathToUse = path,
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                SorterToUse = sorter,
                RowsToTake = 1,
                CollectionToFetch = collection
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);
            return collection.SingleOrDefault();
        }

        public static async Task<FlexResponseSaveStateEntity> GetLatestResponseWithCarrierInfo(Guid flexResponseGuid, bool submittedOnly = false)
        {
            EntityCollection<FlexResponseSaveStateEntity> collection = [];

            PrefetchPath2 path = new(EntityType.FlexResponseSaveStateEntity);
            IPrefetchPath2 flexResponsePath = path.Add(FlexResponseSaveStateEntity.PrefetchPathFlexResponse).SubPath;
            IPrefetchPath2 responseLinkPath = flexResponsePath.Add(FlexResponseEntity.PrefetchPathFlexResponseLink).SubPath;
            IPrefetchPath2 versionPath = path.Add(FlexResponseSaveStateEntity.PrefetchPathFlexVersion).SubPath;
            IPrefetchPath2 versionOfferPath = versionPath.Add(FlexVersionEntity.PrefetchPathFlexVersionCarrierOffer).SubPath;
            IPrefetchPath2 offerPath = versionOfferPath.Add(FlexVersionCarrierOfferEntity.PrefetchPathCarrierMaximumOffer).SubPath;
            offerPath.Add(CarrierMaximumOfferEntity.PrefetchPathCarrierProgram);
            IPrefetchPath2 packagePath = responseLinkPath.Add(FlexResponseLinkEntity.PrefetchPathPackage).SubPath;

            RelationPredicateBucket filter = new(FlexResponseSaveStateFields.FlexResponseGuid == flexResponseGuid);
            SortExpression sorter = new(FlexResponseSaveStateFields.SavedAt | SortOperator.Descending);
            if (submittedOnly)
            {
                filter.PredicateExpression.Add(FlexResponseSaveStateFields.WasUserSubmit == true);
            }
            QueryParameters queryParameters = new()
            {
                PrefetchPathToUse = path,
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                SorterToUse = sorter,
                RowsToTake = 1,
                CollectionToFetch = collection
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);
            return collection.SingleOrDefault();
        }

        public static async Task<FlexResponseSaveStateEntity> GetLatestByResponseAsyncForFormAnalysis(Guid flexResponseGuid)
        {
            EntityCollection<FlexResponseSaveStateEntity> collection = [];

            PrefetchPath2 path = new(EntityType.FlexResponseSaveStateEntity);

            IPrefetchPath2 versionPath = path.Add(FlexResponseSaveStateEntity.PrefetchPathFlexVersion).SubPath;
            IPrefetchPath2 carrierOfferFormPath = versionPath.Add(FlexVersionEntity.PrefetchPathFlexVersionCarrierOfferForm).SubPath;
            carrierOfferFormPath.Add(FlexVersionCarrierOfferFormEntity.PrefetchPathForm);

            IPrefetchPath2 versionOfferPath = versionPath.Add(FlexVersionEntity.PrefetchPathFlexVersionCarrierOffer).SubPath;
            IPrefetchPath2 offerPath = versionOfferPath.Add(FlexVersionCarrierOfferEntity.PrefetchPathCarrierMaximumOffer).SubPath;
            offerPath.Add(CarrierMaximumOfferEntity.PrefetchPathCarrierMaximumOfferCommission);
            IPrefetchPath2 carrierProgramPath = offerPath.Add(CarrierMaximumOfferEntity.PrefetchPathCarrierProgram).SubPath;
            carrierProgramPath.Add(CarrierProgramEntity.PrefetchPathCarrier);

            IPrefetchPath2 flexResponsePath = path.Add(FlexResponseSaveStateEntity.PrefetchPathFlexResponse).SubPath;
            IPrefetchPath2 submissionFlexResponsePath = flexResponsePath.Add(FlexResponseEntity.PrefetchPathCarrierSubmissionFlexResponse).SubPath;
            submissionFlexResponsePath.Add(CarrierSubmissionFlexResponseEntity.PrefetchPathCarrierSubmission);

            IPrefetchPath2 responseLinkPath = flexResponsePath.Add(FlexResponseEntity.PrefetchPathFlexResponseLink).SubPath;

            IPrefetchPath2 packagePath = responseLinkPath.Add(FlexResponseLinkEntity.PrefetchPathPackage).SubPath;
            IPrefetchPath2 policyPath = packagePath.Add(PackageEntity.PrefetchPathPolicyProspect).SubPath;
            IPrefetchPath2 iahPath = packagePath.Add(PackageEntity.PrefetchPathInsuredAgentHistory).SubPath;
            iahPath.Add(InsuredAgentHistoryEntity.PrefetchPathInsured);

            RelationPredicateBucket filter = new(FlexResponseSaveStateFields.FlexResponseGuid == flexResponseGuid);
            SortExpression sorter = new(FlexResponseSaveStateFields.SavedAt | SortOperator.Descending);
            QueryParameters queryParameters = new()
            {
                PrefetchPathToUse = path,
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                SorterToUse = sorter,
                RowsToTake = 1,
                CollectionToFetch = collection
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);
            return collection.SingleOrDefault();
        }

        public static async Task<FlexResponseSaveStateEntity> GetLatestByResponseAsyncSimple(Guid flexResponseGuid, bool submittedOnly = false)
        {
            EntityCollection<FlexResponseSaveStateEntity> collection = [];
            PrefetchPath2 path = new(EntityType.FlexResponseSaveStateEntity);

            RelationPredicateBucket filter = new(FlexResponseSaveStateFields.FlexResponseGuid == flexResponseGuid);
            SortExpression sorter = new(FlexResponseSaveStateFields.SavedAt | SortOperator.Descending);
            if (submittedOnly)
            {
                filter.PredicateExpression.Add(FlexResponseSaveStateFields.WasUserSubmit == true);
            }
            QueryParameters queryParameters = new()
            {
                PrefetchPathToUse = path,
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                SorterToUse = sorter,
                RowsToTake = 1,
                CollectionToFetch = collection
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);
            return collection.SingleOrDefault();
        }

        public static async Task<FlexResponseSaveStateEntity> GetLatestByResponseAsyncSimpleWithPolicies(Guid flexResponseGuid, bool submittedOnly = false)
        {
            EntityCollection<FlexResponseSaveStateEntity> collection = [];

            PrefetchPath2 path = new(EntityType.FlexResponseSaveStateEntity);
            IPrefetchPath2 flexResponsePath = path.Add(FlexResponseSaveStateEntity.PrefetchPathFlexResponse).SubPath;
            IPrefetchPath2 responseLinkPath = flexResponsePath.Add(FlexResponseEntity.PrefetchPathFlexResponseLink).SubPath;

            IPrefetchPath2 packagePath = responseLinkPath.Add(FlexResponseLinkEntity.PrefetchPathPackage).SubPath;
            IPrefetchPath2 policyPath = packagePath.Add(PackageEntity.PrefetchPathPolicyProspect).SubPath;
            IPrefetchPath2 submissionPath = policyPath.Add(PolicyProspectEntity.PrefetchPathCarrierSubmission).SubPath;
            IPrefetchPath2 optionPath = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionOption).SubPath;
            IPrefetchPath2 invoicePath = optionPath.Add(CarrierSubmissionOptionEntity.PrefetchPathInvoice).SubPath;
            IPrefetchPath2 itemPath = invoicePath.Add(InvoiceEntity.PrefetchPathInvoiceItem).SubPath;
            itemPath.Add(InvoiceItemEntity.PrefetchPathTransactionType);

            RelationPredicateBucket filter = new(FlexResponseSaveStateFields.FlexResponseGuid == flexResponseGuid);
            SortExpression sorter = new(FlexResponseSaveStateFields.SavedAt | SortOperator.Descending);
            if (submittedOnly)
            {
                filter.PredicateExpression.Add(FlexResponseSaveStateFields.WasUserSubmit == true);
            }
            QueryParameters queryParameters = new()
            {
                PrefetchPathToUse = path,
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                SorterToUse = sorter,
                RowsToTake = 1,
                CollectionToFetch = collection
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);
            return collection.SingleOrDefault();
        }
        public static async Task<FlexResponseSaveStateEntity> GetLatestByResponseAsyncWithFlexVersion(Guid flexResponseGuid, bool submittedOnly = false)
        {
            EntityCollection<FlexResponseSaveStateEntity> collection = [];

            PrefetchPath2 path = new(EntityType.FlexResponseSaveStateEntity);
            IPrefetchPath2 versionPath = path.Add(FlexResponseSaveStateEntity.PrefetchPathFlexVersion).SubPath;

            RelationPredicateBucket filter = new(FlexResponseSaveStateFields.FlexResponseGuid == flexResponseGuid);
            SortExpression sorter = new(FlexResponseSaveStateFields.SavedAt | SortOperator.Descending);
            if (submittedOnly)
            {
                filter.PredicateExpression.Add(FlexResponseSaveStateFields.WasUserSubmit == true);
            }
            QueryParameters queryParameters = new()
            {
                PrefetchPathToUse = path,
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                SorterToUse = sorter,
                RowsToTake = 1,
                CollectionToFetch = collection
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);
            return collection.SingleOrDefault();
        }

        public static async Task<FlexResponseSaveStateEntity> GetLatestByResponseAsyncForOfflineSubmission(Guid flexResponseGuid, bool submittedOnly = false)
        {
            EntityCollection<FlexResponseSaveStateEntity> collection = [];

            PrefetchPath2 path = new(EntityType.FlexResponseSaveStateEntity);
            IPrefetchPath2 flexVersionPath = path.Add(FlexResponseSaveStateEntity.PrefetchPathFlexVersion).SubPath;
            IPrefetchPath2 flexVersionCarrierOfferPath = flexVersionPath.Add(FlexVersionEntity.PrefetchPathFlexVersionCarrierOffer).SubPath;
            IPrefetchPath2 carrierMaximumOfferOfferPath = flexVersionCarrierOfferPath.Add(FlexVersionCarrierOfferEntity.PrefetchPathCarrierMaximumOffer).SubPath;
            IPrefetchPath2 carrierProgramPath = carrierMaximumOfferOfferPath.Add(CarrierMaximumOfferEntity.PrefetchPathCarrierProgram).SubPath;
            IPrefetchPath2 flexResponsePath = path.Add(FlexResponseSaveStateEntity.PrefetchPathFlexResponse).SubPath;
            IPrefetchPath2 responseLinkPath = flexResponsePath.Add(FlexResponseEntity.PrefetchPathFlexResponseLink).SubPath;
            IPrefetchPath2 pathRecordingPath = responseLinkPath.Add(FlexResponseLinkEntity.PrefetchPathPathRecording).SubPath;
            IPrefetchPath2 recordingAgentPath = pathRecordingPath.Add(PathRecordingEntity.PrefetchPathAgent).SubPath;
            IPrefetchPath2 recordingLocationPath = recordingAgentPath.Add(AgentEntity.PrefetchPathLocation).SubPath;
            recordingLocationPath.Add(LocationEntity.PrefetchPathCompany);
            IPrefetchPath2 linkAgentPath = responseLinkPath.Add(FlexResponseLinkEntity.PrefetchPathAgent).SubPath;
            IPrefetchPath2 flexDefinitionPath = flexResponsePath.Add(FlexResponseEntity.PrefetchPathFlexDefinition).SubPath;
            IPrefetchPath2 programPath = flexDefinitionPath.Add(FlexDefinitionEntity.PrefetchPathProgram).SubPath;


            RelationPredicateBucket filter = new(FlexResponseSaveStateFields.FlexResponseGuid == flexResponseGuid);
            SortExpression sorter = new(FlexResponseSaveStateFields.SavedAt | SortOperator.Descending);
            if (submittedOnly)
            {
                filter.PredicateExpression.Add(FlexResponseSaveStateFields.WasUserSubmit == true);
            }
            QueryParameters queryParameters = new()
            {
                PrefetchPathToUse = path,
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                SorterToUse = sorter,
                RowsToTake = 1,
                CollectionToFetch = collection
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);
            return collection.SingleOrDefault();
        }

        public static async Task<string> GetTaxState(List<FlexAnswer> flexAnswers, InsuredEntity insured)
        {
            string state = await GetStateFromAnswer("FacilityState", flexAnswers)
                         ?? await GetStateFromAnswer("ProjLocState", flexAnswers)
                         ?? await GetStateFromAnswer("ProjectState", flexAnswers)
                         ?? insured.State;

            return state;
        }

        private static async Task<string> GetStateFromAnswer(string question, List<FlexAnswer> flexAnswers)
        {
            List<FlexAnswer> answers = FlexQuoteModel.FindByQuestion(question, flexAnswers);
            if (answers.Any())
            {
                string state = answers.First().Answer;
                if (state.Length == 2)
                    return state;
                if (state.Length != 2)
                {
                    string fetchedCode = await FlexEntityFetcherModel.GetCodeFromStateName(state);
                    if (!string.IsNullOrEmpty(fetchedCode))
                    {
                        return fetchedCode;
                    }
                    return null;
                }
            }
            return null;
        }


        public static CarrierMaximumOfferEntity GetCarrierGuidFromCarrierMaximumOffer(Guid carrierMaximumOfferGuid)
        {
            CarrierMaximumOfferEntity offer = new(carrierMaximumOfferGuid);
            PrefetchPath2 path = new(EntityType.CarrierMaximumOfferEntity);
            IPrefetchPath2 carrierProgramPath = path.Add(CarrierMaximumOfferEntity.PrefetchPathCarrierProgram).SubPath;
            carrierProgramPath.Add(CarrierProgramEntity.PrefetchPathCarrier);

            path.Add(CarrierMaximumOfferEntity.PrefetchPathCarrierMaximumOfferCommission);

            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            quickAdapter.FetchEntity(offer, path);
            return offer;
        }

        public static async Task<EntityCollection<CarrierMaximumOfferEntity>> GetByGuids(Guid carrierGuid, Guid programGuid, int policyTypeId)
        {
            EntityCollection<CarrierMaximumOfferEntity> offers = [];
            RelationPredicateBucket filter = new(
                CarrierMaximumOfferFields.IsDeleted == false &
                CarrierProgramFields.ProgramGuid == programGuid &
                CarrierProgramFields.CarrierGuid == carrierGuid &
                CarrierMaximumOfferFields.PolicyTypeId == policyTypeId);
            filter.Relations.Add(CarrierMaximumOfferEntity.Relations.CarrierProgramEntityUsingCarrierProgramGuid);

            PrefetchPath2 path = new(EntityType.CarrierMaximumOfferEntity)
            {
                CarrierMaximumOfferEntity.PrefetchPathCarrierProgram,
                CarrierMaximumOfferEntity.PrefetchPathCarrierMaximumOfferCommission
            };

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = offers,
                PrefetchPathToUse = path,
                FilterToUse = filter.PredicateExpression,
                RelationsToUse = filter.Relations
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);
            return offers;
        }

        public static async Task<CarrierMaximumOfferEntity> GetByGuids(int variationNumber, Guid carrierGuid, Guid programGuid, int policyTypeId)
        {
            EntityCollection<CarrierMaximumOfferEntity> offers = [];
            RelationPredicateBucket filter = new(
                CarrierMaximumOfferFields.IsDeleted == false &
                CarrierProgramFields.ProgramGuid == programGuid &
                CarrierProgramFields.CarrierGuid == carrierGuid &
                CarrierMaximumOfferFields.PolicyTypeId == policyTypeId &
                CarrierMaximumOfferFields.VariationNumber == variationNumber);
            filter.Relations.Add(CarrierMaximumOfferEntity.Relations.CarrierProgramEntityUsingCarrierProgramGuid);

            PrefetchPath2 path = new(EntityType.CarrierMaximumOfferEntity)
            {
                CarrierMaximumOfferEntity.PrefetchPathCarrierProgram,
                CarrierMaximumOfferEntity.PrefetchPathCarrierMaximumOfferCommission
            };

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = offers,
                PrefetchPathToUse = path,
                FilterToUse = filter.PredicateExpression,
                RelationsToUse = filter.Relations
            };

            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);
            return offers.SingleOrDefault();
        }

        public static CarrierMaximumOfferEntity GetMaximumOfferByGuids(Guid carrierProgramGuid)
        {
            CarrierMaximumOfferEntity offers = new();
            RelationPredicateBucket filter = new(
                CarrierMaximumOfferFields.CarrierProgramGuid == carrierProgramGuid);

            PrefetchPath2 path = new(EntityType.CarrierMaximumOfferEntity)
            {
                CarrierMaximumOfferEntity.PrefetchPathCarrierProgram,
                CarrierMaximumOfferEntity.PrefetchPathCarrierMaximumOfferCommission
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            quickAdapter.FetchEntityUsingUniqueConstraint(offers, filter.PredicateExpression, path);

            return offers;
        }

        public static CarrierProgramEntity GetCarrierProgramByGuids(Guid carrierGuid, Guid programGuid)
        {
            CarrierProgramEntity program = new();
            PredicateExpression filter = new(CarrierProgramFields.CarrierGuid == carrierGuid & CarrierProgramFields.ProgramGuid == programGuid);
            PrefetchPath2 path = new(EntityType.CarrierProgramEntity)
            {
                CarrierProgramEntity.PrefetchPathUnderwriterAssigned
            };
            IPrefetchPath2 maximumOfferPath = path.Add(CarrierProgramEntity.PrefetchPathCarrierMaximumOffer).SubPath;
            maximumOfferPath.Add(CarrierMaximumOfferEntity.PrefetchPathPolicyType);
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            quickAdapter.FetchEntityUsingUniqueConstraint(program, filter, path);
            return program;
        }

        public static WholesalerEntity GetWholesaler(Guid wholesalerGuid)
        {
            WholesalerEntity wholesaler = new(wholesalerGuid);
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            quickAdapter.FetchEntity(wholesaler);
            return wholesaler;
        }

        public static async Task<string> GetCodeFromStateName(string stateName)
        {
            EntityCollection<StateEntity> states = [];
            RelationPredicateBucket filter = new(StateFields.StateName == stateName);


            QueryParameters queryParameters = new()
            {
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                CollectionToFetch = states,
                RowsToTake = 1
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);
            return states.SingleOrDefault()?.Code ?? "";
        }

        public static async Task GetFormsForSubmission(CarrierSubmissionEntity submission)
        {
            RelationPredicateBucket filter = new(
                SubmissionFormFields.CarrierSubmissionGuid == submission.CarrierSubmissionGuid &
                FormFields.IsDeleted == false
                );
            filter.Relations.Add(SubmissionFormEntity.Relations.FormEntityUsingFormGuid);

            PrefetchPath2 path = new(EntityType.SubmissionFormEntity);

            IPrefetchPath2 formPath = path.Add(SubmissionFormEntity.PrefetchPathForm).SubPath;
            formPath.Add(FormEntity.PrefetchPathCarrier);

            IPrefetchPath2 fciPath = formPath.Add(FormEntity.PrefetchPathFormCoverageIssue).SubPath;
            fciPath.Add(FormCoverageIssueEntity.PrefetchPathCoverageIssueStatus);
            IPrefetchPath2 coverageIssuePath = fciPath.Add(FormCoverageIssueEntity.PrefetchPathCoverageIssue).SubPath;
            coverageIssuePath.Add(CoverageIssueEntity.PrefetchPathProgramIssueGroup);
            IPrefetchPath2 booleanContainerPath = coverageIssuePath.Add(CoverageIssueEntity.PrefetchPathIssueVisibilityBooleanContainer).SubPath;
            IPrefetchPath2 subPath = booleanContainerPath.Add(IssueVisibilityBooleanContainerEntity.PrefetchPathIssueVisibility).SubPath;
            IPrefetchPath2 visibilityPath = subPath.Add(IssueVisibilityEntity.PrefetchPathIssueVisibilityInSet).SubPath;
            visibilityPath.Add(IssueVisibilityInSetEntity.PrefetchPathCoverageIssueStatus);

            SortExpression sorter = new(SubmissionFormFields.SortOrder | SortOperator.Ascending);

            QueryParameters queryParams = new()
            {
                CollectionToFetch = submission.SubmissionForm,
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                SorterToUse = sorter,
                PrefetchPathToUse = path
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParams);
        }

        public static PackageEntity GetForFindARep(Guid packageGuid)
        {
            PackageEntity package = new(packageGuid);
            PrefetchPath2 path = new(EntityType.PackageEntity);
            IPrefetchPath2 insuredAgentHistoryPath = path.Add(PackageEntity.PrefetchPathInsuredAgentHistory).SubPath;
            insuredAgentHistoryPath.Add(InsuredAgentHistoryEntity.PrefetchPathInsured);
            IPrefetchPath2 agentPath = insuredAgentHistoryPath.Add(InsuredAgentHistoryEntity.PrefetchPathAgent).SubPath;
            IPrefetchPath2 locationPath = agentPath.Add(AgentEntity.PrefetchPathLocation).SubPath;
            locationPath.Add(LocationEntity.PrefetchPathCompany);
            locationPath.Add(LocationEntity.PrefetchPathLocationTimeZone);
            IPrefetchPath2 packageProductMappingPath = path.Add(PackageEntity.PrefetchPathPackageProductMapping).SubPath;
            path.Add(PackageEntity.PrefetchPathFinancialSummaryPackage);
            path.Add(PackageEntity.PrefetchPathPrimaryRepEmployee);
            IPrefetchPath2 productPath = packageProductMappingPath.Add(PackageProductMappingEntity.PrefetchPathProduct).SubPath;
            IPrefetchPath2 productEmployeePath = productPath.Add(ProductEntity.PrefetchPathProductEmployee).SubPath;

            IPrefetchPath2 employeePath = productEmployeePath.Add(ProductEmployeeEntity.PrefetchPathEmployee).SubPath;
            employeePath.Add(EmployeeEntity.PrefetchPathTimeZone);
            employeePath.Add(EmployeeEntity.PrefetchPathEmployeePreferredAgent);

            employeePath.Add(EmployeeEntity.PrefetchPathTouchEmployeeAgentRolling);
            IPrefetchPath2 qualPath = employeePath.Add(EmployeeEntity.PrefetchPathEmployeeProductQualification).SubPath;

            RelationPredicateBucket filter = new(
                PolicyProspectSimpleViewFields.PolicyRecordCreated >= DateTime.Now.AddDays(-2).Date &
                PolicyProspectSimpleViewFields.RenewalStatusId == 2);
            filter.Relations.Add(EmployeeEntity.Relations.PolicyProspectSimpleViewEntityUsingPrimaryRepLoginGuid);
            employeePath.Add(EmployeeEntity.PrefetchPathPolicyProspectSimpleView, 0, filter.PredicateExpression, filter.Relations);

            SortExpression workloadSorter = new(EmployeeWorkloadFields.EntryDateZoned | SortOperator.Descending);
            employeePath.Add(EmployeeEntity.PrefetchPathEmployeeWorkload, 1, null, null, workloadSorter).SubPath
                .Add(EmployeeWorkloadEntity.PrefetchPathWorkloadRating);
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            quickAdapter.FetchEntity(package, path);
            return package;
        }

        public static async Task<EntityCollection<ProgramLimitDeductiblePrevalenceEntity>> GetByProgram(Guid programGuid)
        {
            EntityCollection<ProgramLimitDeductiblePrevalenceEntity> programLimits = [];
            RelationPredicateBucket filter = new(ProgramLimitDeductiblePrevalenceFields.ProgramGuid == programGuid);


            QueryParameters queryParameters = new()
            {
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                CollectionToFetch = programLimits
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);
            return programLimits;
        }

        public static FlexVersionEntity GetFlexVersion(Guid flexVersionGuid)
        {
            FlexVersionEntity version = new(flexVersionGuid);
            QuickAdapterAsync adapter = new(new DataAccessAdapter(), new DataLogger());
            adapter.FetchEntity(version);

            return version;
        }

        internal static ClassOfBusinessEntity GetClassOfBusinessByGuid(Guid classOfBusinessGuid)
        {
            ClassOfBusinessEntity cob = new(classOfBusinessGuid);
            QuickAdapterAsync adapter = new(new DataAccessAdapter(), new DataLogger());
            adapter.FetchEntity(cob);

            return cob;
        }

        public static CarrierSubmissionEntity GetSubmission(Guid carrierSubmissionGuid)
        {
            CarrierSubmissionEntity submission = new(carrierSubmissionGuid);
            PrefetchPath2 path = new(EntityType.CarrierSubmissionEntity)
            {
                CarrierSubmissionEntity.PrefetchPathCarrierSubmissionSubjectivity,
                CarrierSubmissionEntity.PrefetchPathNxusType
            };
            IPrefetchPath2 submissionFormPath = path.Add(CarrierSubmissionEntity.PrefetchPathSubmissionForm).SubPath;
            submissionFormPath.Add(SubmissionFormEntity.PrefetchPathForm);

            IPrefetchPath2 policyPath = path.Add(CarrierSubmissionEntity.PrefetchPathPolicyProspect).SubPath;
            IPrefetchPath2 packagePath = policyPath.Add(PolicyProspectEntity.PrefetchPathPackage).SubPath;
            packagePath.Add(PackageEntity.PrefetchPathPrimaryRepEmployee);
            IPrefetchPath2 insuredAgentHistoryPath = packagePath.Add(PackageEntity.PrefetchPathInsuredAgentHistory).SubPath;
            IPrefetchPath2 agentPath = insuredAgentHistoryPath.Add(InsuredAgentHistoryEntity.PrefetchPathAgent).SubPath;
            IPrefetchPath2 locationPath = agentPath.Add(AgentEntity.PrefetchPathLocation).SubPath;
            locationPath.Add(LocationEntity.PrefetchPathCompany);
            IPrefetchPath2 optionPath = path.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionOption).SubPath;
            IPrefetchPath2 invoicePath = optionPath.Add(CarrierSubmissionOptionEntity.PrefetchPathInvoice).SubPath;
            IPrefetchPath2 invoiceItemPath = invoicePath.Add(InvoiceEntity.PrefetchPathInvoiceItem).SubPath;
            invoiceItemPath.Add(InvoiceItemEntity.PrefetchPathTransactionType);
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            quickAdapter.FetchEntity(submission, path);
            return submission;
        }

        public static async Task<EntityCollection<FlexSubmissionDocQueueEntity>> GetQueued()
        {
            EntityCollection<FlexSubmissionDocQueueEntity> queued = [];
            RelationPredicateBucket filter = new(FlexSubmissionDocQueueFields.DocGeneratedStartedAt <= Parse.SentinelDate);
            PrefetchPath2 path = new(EntityType.FlexSubmissionDocQueueEntity);

            IPrefetchPath2 submissionPath = path.Add(FlexSubmissionDocQueueEntity.PrefetchPathCarrierSubmission).SubPath;
            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathNxusType);
            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrier);
            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionSubjectivity);


            IPrefetchPath2 optionPath = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionOption).SubPath;
            IPrefetchPath2 invoicePath = optionPath.Add(CarrierSubmissionOptionEntity.PrefetchPathInvoice).SubPath;
            IPrefetchPath2 invoiceItemPath = invoicePath.Add(InvoiceEntity.PrefetchPathInvoiceItem).SubPath;
            invoiceItemPath.Add(InvoiceItemEntity.PrefetchPathTransactionType);

            IPrefetchPath2 submissionFormPath = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathSubmissionForm).SubPath;
            submissionFormPath.Add(SubmissionFormEntity.PrefetchPathForm);

            IPrefetchPath2 policyPath = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathPolicyProspect).SubPath;
            IPrefetchPath2 packagePath = policyPath.Add(PolicyProspectEntity.PrefetchPathPackage).SubPath;
            packagePath.Add(PackageEntity.PrefetchPathPrimaryRepEmployee);
            IPrefetchPath2 insuredAgentHistoryPath = packagePath.Add(PackageEntity.PrefetchPathInsuredAgentHistory).SubPath;
            insuredAgentHistoryPath.Add(InsuredAgentHistoryEntity.PrefetchPathInsured);
            IPrefetchPath2 agentPath = insuredAgentHistoryPath.Add(InsuredAgentHistoryEntity.PrefetchPathAgent).SubPath;
            IPrefetchPath2 locationPath = agentPath.Add(AgentEntity.PrefetchPathLocation).SubPath;
            locationPath.Add(LocationEntity.PrefetchPathCompany);



            QueryParameters queryParameters = new()
            {
                RelationsToUse = filter.Relations,
                FilterToUse = filter.PredicateExpression,
                CollectionToFetch = queued,
                PrefetchPathToUse = path
            };


            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.FetchEntityCollectionAsync(queryParameters);
            return queued;
        }

        public static CarrierSubmissionEntity GetSubmissionForDocGeneration(Guid carrierSubmissionGuid)
        {
            CarrierSubmissionEntity submission = new(carrierSubmissionGuid);
            PrefetchPath2 path = new(EntityType.CarrierSubmissionEntity)
            {
                CarrierSubmissionEntity.PrefetchPathNxusType,
                CarrierSubmissionEntity.PrefetchPathCarrierSubmissionSubjectivity,
                CarrierSubmissionEntity.PrefetchPathCarrier
            };
            IPrefetchPath2 optionPath = path.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionOption).SubPath;
            IPrefetchPath2 invoicePath = optionPath.Add(CarrierSubmissionOptionEntity.PrefetchPathInvoice).SubPath;
            IPrefetchPath2 invoiceItemPath = invoicePath.Add(InvoiceEntity.PrefetchPathInvoiceItem).SubPath;
            invoiceItemPath.Add(InvoiceItemEntity.PrefetchPathTransactionType);

            IPrefetchPath2 submissionFormPath = path.Add(CarrierSubmissionEntity.PrefetchPathSubmissionForm).SubPath;
            submissionFormPath.Add(SubmissionFormEntity.PrefetchPathForm);

            IPrefetchPath2 policyPath = path.Add(CarrierSubmissionEntity.PrefetchPathPolicyProspect).SubPath;
            IPrefetchPath2 packagePath = policyPath.Add(PolicyProspectEntity.PrefetchPathPackage).SubPath;
            packagePath.Add(PackageEntity.PrefetchPathPrimaryRepEmployee);
            IPrefetchPath2 insuredAgentHistoryPath = packagePath.Add(PackageEntity.PrefetchPathInsuredAgentHistory).SubPath;
            insuredAgentHistoryPath.Add(InsuredAgentHistoryEntity.PrefetchPathInsured);
            IPrefetchPath2 agentPath = insuredAgentHistoryPath.Add(InsuredAgentHistoryEntity.PrefetchPathAgent).SubPath;
            IPrefetchPath2 locationPath = agentPath.Add(AgentEntity.PrefetchPathLocation).SubPath;
            locationPath.Add(LocationEntity.PrefetchPathCompany);
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            quickAdapter.FetchEntity(submission, path);
            return submission;
        }
    }
}