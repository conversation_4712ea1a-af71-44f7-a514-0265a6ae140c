using FlexLibrary;
using FlexLibrary.Enums;
using FlexLibrary.Models;
using LLBLGenHelper;
using ORMStandard;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.LinqSupportClasses;
using SD.LLBLGen.Pro.ORMSupportClasses;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using UcpmApi.BusinessModel.BinderForm;
using UcpmApi.BusinessModel.Filesystem;
using UcpmApi.BusinessModel.Invoice;
using UcpmApi.Command.Doc;
using UcpmApi.Logging;
using UcpmApi.Query.Carrier;
using UcpmApi.Query.Cob;
using UcpmApi.Query.Ikea;
using UcpmApi.Query.Lookups;
using UcpmApi.Query.Policy;
using UcpmApi.Shared;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Flex;
using UcpmApi.Shared.Guids;
using UcpmApi.Shared.Helpers;
using UtilityParsersStandard;

namespace UcpmApi.BusinessModel.Flex
{
    public static class FlexQuoteModel
    {
        private static readonly DataAccessAdapter _dataAccessAdapter = new();
        private static LinqMetaData _linqMetaData = new(_dataAccessAdapter);
        private static QuickAdapterReader _quickAdapterReader = new(_dataAccessAdapter, new DataLogger());
        private static QuickAdapterWriter _quickAdapterWriter = new(_dataAccessAdapter, new DataLogger());
        private static QuickAdapterAsync _quickAdapterAsync = new(_dataAccessAdapter, new DataLogger());

        public static async Task<List<CarrierSubmissionEntity>> GetOrCreateCarrierSubmissions(
            PolicyProspectEntity policy, Guid carrierGuid, CarrierMaximumOfferEntity offer, bool autoGenerateQuotes,
            string taxState, Guid flexVersionGuid, List<int> nxusTypeIds, int policyStatusId = -1)
        {
            if (policy != null)
            {
                PolicyProspectEntity refetchedPolicy =
                    FlexEntityFetcherModel.GetPolicyAndSubmissionCarriersSimple(policy.PolicyProspectGuid);
                List<CarrierSubmissionEntity> submissions = refetchedPolicy.CarrierSubmission
                    .Where(c => c.CarrierGuid == carrierGuid && nxusTypeIds.Contains(c.NxusTypeId)).ToList();

                if (!submissions.Any())
                {
                    WholesalerEntity wholesaler = FlexEntityFetcherModel.GetWholesaler(Guid.Empty);
                    // Most of the time has 1 item which is the nxusTypeId of the carrier submission we want to create.
                    // Sometimes has more than 1 item, in which case we want the first one.
                    int nxusTypeId = nxusTypeIds.FirstOrDefault();
                    CarrierSubmissionEntity submission = await CreateCarrierSubmissionEntity(refetchedPolicy,
                        carrierGuid, wholesaler, offer, taxState, autoGenerateQuotes, nxusTypeId, policyStatusId);
                    if (autoGenerateQuotes && nxusTypeId != 0 && nxusTypeId != (int)NxusTypeEnum.NXUSMatch)
                    {
                        Console.WriteLine($"{policy.ApplicationNumber} {nxusTypeId}");
                        await AddAllPartsDefinedTermsAndOptions(submission, flexVersionGuid, nxusTypeId);
                    }

                    submissions.Add(submission);
                }
                else

                {
                    foreach (CarrierSubmissionEntity submission in submissions)
                    {
                        submission.IsDeleted = false;
                        EntityCollection<CarrierBillingOfficeEntity> offices =
                            await FlexEntityFetcherModel.GetOfficeByCarrierAndIssuingCompany(carrierGuid,
                                offer.IssuingCompanyGuid);
                        if (offices.Any())
                        {
                            CarrierBillingOfficeEntity onlineOffice = offices.FirstOrDefault(o => o.BillingOfficeName.Contains("Online", StringComparison.OrdinalIgnoreCase));
                            submission.CarrierBillingOfficeGuid = onlineOffice?.CarrierBillingOfficeGuid ?? offices.First().CarrierBillingOfficeGuid;
                        }

                        SetPolicyStatus(submission, policyStatusId);
                        submission.SurplusLinesTaxState = taxState;
                        await _quickAdapterAsync.SaveEntityAsync(submission, true);
                    }
                }

                return submissions;
            }

            return null;
        }

        public static AgentEntity GetAgent(FlexResponseSaveStateEntity latestSaveState, PolicyProspectEntity policy)
        {
            return latestSaveState.FlexResponse.FlexResponseLink.PackageGuid != Guid.Empty
                ? policy.Package.InsuredAgentHistory.Agent
                : latestSaveState.FlexResponse.FlexResponseLink.PathRecording.Agent;
        }

        public static AgentEntity GetAgent(FlexResponseSaveStateEntity latestSaveState, PackageEntity package)
        {
            return latestSaveState.FlexResponse.FlexResponseLink.PackageGuid != Guid.Empty
                ? package.InsuredAgentHistory.Agent
                : latestSaveState.FlexResponse.FlexResponseLink.PathRecording.Agent;
        }

        public static async Task<CarrierSubmissionFlexResponseEntity> GetOrCreateCarrierSubmissionFlexResponse(
            FlexResponseSaveStateEntity latestSaveState, CarrierSubmissionEntity submission, bool autoGenerateQuotes)
        {
            CarrierSubmissionFlexResponseEntity carrierSubmissionFlexResponse;
            if (latestSaveState.FlexResponse.CarrierSubmissionFlexResponse.All(c =>
                    c.CarrierSubmissionGuid != submission.CarrierSubmissionGuid))
            {
                carrierSubmissionFlexResponse = await CreateCarrierSubmissionFlexResponse(submission,
                    latestSaveState.FlexResponseGuid, autoGenerateQuotes);
                if (autoGenerateQuotes)
                {
                    await _quickAdapterAsync.SaveEntityAsync(carrierSubmissionFlexResponse, true);
                }
            }
            else

            {
                carrierSubmissionFlexResponse =
                    latestSaveState.FlexResponse.CarrierSubmissionFlexResponse.Single(c =>
                        c.CarrierSubmissionGuid == submission.CarrierSubmissionGuid);
            }

            return carrierSubmissionFlexResponse;
        }

        public static decimal GetHighestExposureBasis(string question, List<FlexAnswer> flexAnswers)
        {
            decimal highestExposureBasis = 0;
            List<FlexAnswer> exposureAnswer = FindByQuestion(question, flexAnswers);
            foreach (FlexAnswer answer in exposureAnswer.Where(r => !string.IsNullOrWhiteSpace(r.Answer)))
            {
                decimal exposureBasis = Parse.CurrencyDecimal(answer.Answer);
                if (exposureBasis > highestExposureBasis)
                {
                    highestExposureBasis = exposureBasis;
                }
            }

            return highestExposureBasis;
        }

        public static List<FlexAnswer> FindByQuestionList(List<string> dataNames, List<FlexAnswer> answers)
        {
            List<FlexAnswer> fullList = [];
            foreach (FlexAnswer item in answers)
            {
                foreach (string dataName in dataNames)
                {
                    if (item.QuestionDataName == dataName)
                    {
                        fullList.Add(item);
                    }

                    if (item.ChildAnswers != null)
                    {
                        foreach (FlexAnswer subItem in item.ChildAnswers)
                        {
                            if (subItem.QuestionDataName == dataName)
                            {
                                fullList.Add(subItem);
                            }

                            if (subItem.ChildAnswers != null)
                            {
                                foreach (FlexAnswer subSubItem in subItem.ChildAnswers)
                                {
                                    if (subSubItem.QuestionDataName == dataName)
                                    {
                                        fullList.Add(subSubItem);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return fullList;
        }

        public static List<FlexAnswer> FindByQuestion(string dataName, List<FlexAnswer> answers)
        {
            List<FlexAnswer> fullList = [];
            foreach (FlexAnswer item in answers)
            {
                if (item.QuestionDataName == dataName)
                {
                    fullList.Add(item);
                }

                if (item.ChildAnswers != null)
                {
                    foreach (FlexAnswer subItem in item.ChildAnswers)
                    {
                        if (subItem.QuestionDataName == dataName)
                        {
                            fullList.Add(subItem);
                        }

                        if (subItem.ChildAnswers != null)
                        {
                            foreach (FlexAnswer subSubItem in subItem.ChildAnswers)
                            {
                                if (subSubItem.QuestionDataName == dataName)
                                {
                                    fullList.Add(subSubItem);
                                }
                            }
                        }
                    }
                }
            }

            return fullList;
        }

        public static bool FindBoolByQuestion(string dataName, List<FlexAnswer> answers)
        {
            bool result = false;
            List<FlexAnswer> fullList = [];
            foreach (FlexAnswer item in answers)
            {
                if (item.QuestionDataName == dataName)
                {
                    fullList.Add(item);
                }

                if (item.ChildAnswers != null)
                {
                    foreach (FlexAnswer subItem in item.ChildAnswers)
                    {
                        if (subItem.QuestionDataName == dataName)
                        {
                            fullList.Add(subItem);
                        }

                        if (subItem.ChildAnswers != null)
                        {
                            foreach (FlexAnswer subSubItem in subItem.ChildAnswers)
                            {
                                if (subSubItem.QuestionDataName == dataName)
                                {
                                    fullList.Add(subSubItem);
                                }
                            }
                        }
                    }
                }
            }

            int count = fullList.Count;
            if (count > 1)
            {
                _ = bool.TryParse(fullList.First().Answer, out result);
                return result;
            }

            if (count == 0)
            {
                return false; //Apparently no answer is false?
            }

            _ = bool.TryParse(fullList.Single().Answer, out result);
            return result;
        }

        public static async Task ProcessAutoGeneratedQuotes(CarrierSubmissionEntity submission,
            PolicyProspectEntity policy,
            FlexOption option,
            EntityCollection<LimitsRequestedEntity> limitList,
            EntityCollection<DeductibleRequestedEntity> deductibleList,
            CarrierMaximumOfferEntity offer,
            FlexRating flexRating,
            bool autoGenerateQuotes,
            AgentEntity agent,
            ProgramEntity program,
            List<FlexAnswer> flexAnswers,
            CommissionCalculationMicroModel commissionCalculationMicroModel,
            InvoiceModel invoiceModel,
            int ucpmRenewableStatusId,
            decimal commToAgency,
            bool payableByInsured = false)
        {
            List<string> retroQuestions = ["RetroActiveDateUST", "RetroactiveDateAst", "RetroactiveDate"];
            List<DateTimeOffset> possibleDates = [];
            List<FlexAnswer> answers = FindByQuestionList(retroQuestions, flexAnswers);
            foreach (FlexAnswer flexAnswer in answers)
            {
                if (!string.IsNullOrWhiteSpace(flexAnswer.Answer))
                {
                    possibleDates.Add(DateTimeOffset.Parse(flexAnswer.Answer));
                }
            }

            (DateTime effectiveDate, DateTime expirationDate) = flexAnswers.GetEffectiveAndExpirationDateFromAnswers();

            possibleDates.Add(effectiveDate);
            DateTimeOffset retroDateZoned = possibleDates.OrderBy(d => d).First();

            CarrierSubmissionOptionEntity submissionOptionEntity = await FindOrCreateCarrierSubmissionOption(submission,
                policy, option, limitList, deductibleList, offer, autoGenerateQuotes, program, flexAnswers,
                commissionCalculationMicroModel, ucpmRenewableStatusId, commToAgency);

            await UpdateCarrierSubmissionOption(submissionOptionEntity, offer, autoGenerateQuotes, option.ExposureBasis,
                retroDateZoned, effectiveDate.AddDays(option.PolicyTermDays), commToAgency);

            InvoiceEntity matchingInvoice =
                FindMatchingInvoice(submissionOptionEntity, option, deductibleList, limitList);

            await ProcessInvoice(option, policy, matchingInvoice, submissionOptionEntity, autoGenerateQuotes, offer,
                agent, program, invoiceModel, submission, payableByInsured, effectiveDate);
            if (!flexRating?.Options.Any(
                    a => a.LimitAggregate == option.LimitAggregate &&
                         a.LimitOccurrence == option.LimitOccurrence &&
                         a.Deductible == option.Deductible &&
                         a.CarrierName == option.CarrierName &&
                         a.InvoiceTotal == option.InvoiceTotal &&
                         a.NxusTypeId == option.NxusTypeId &&
                         a.PolicyTypeId == option.PolicyTypeId) ?? true)
            {
                option.PolicyTerm = effectiveDate.CreatePolicyTermString(option.PolicyTermDays);
                option.ExpirationDate = effectiveDate.AddDays(option.PolicyTermDays);
                option.CarrierSubmissionGuid = submissionOptionEntity.CarrierSubmissionGuid;
                option.CarrierSubmissionOptionGuid = submissionOptionEntity.CarrierSubmissionOptionGuid;
                flexRating?.Options.Add(option);
            }
        }

        public static async Task AddFormsToSubmission(CarrierSubmissionEntity submission,
            FlexResponseEntity flexResponse, Guid carrierMaximumOfferGuid)
        {
            await FlexEntityFetcherModel.GetFormsForSubmission(submission);
            if (!submission.SubmissionForm.Any())
            {
                EntityCollection<SubmissionFormEntity> submissionForms =
                    await GetFormsByResponseForSubmission(submission, flexResponse, carrierMaximumOfferGuid);

                IEnumerable<Guid> newFormGuids = submissionForms.Select(s => s.FormGuid);
                IEnumerable<Guid> oldFormGuids = submission.SubmissionForm.Select(s => s.FormGuid);
                int newFormsThatAreDifferent = newFormGuids.Except(oldFormGuids).Count();
                int oldFormsThatAreDifferent = oldFormGuids.Except(newFormGuids).Count();
                if (newFormsThatAreDifferent > 0 || oldFormsThatAreDifferent > 0)
                {
                    await SaveSubmissionForms(submissionForms, submission.SubmissionForm);
                    await FlexEntityFetcherModel.GetFormsForSubmission(submission);
                }
            }
        }

        public static async Task<PackageEntity> GetPackage(FlexResponseSaveStateEntity latestSaveState,
            InsuredEntity insured, EntityCollection<FlexVersionCarrierOfferEntity> firstOffer,
            List<FlexAnswer> flexAnswers, int nxusTypeId, FlexRating rating)
        {
            bool userWantsProfessional = FindBoolByQuestion("OptionToPurchaseSeparateCPL", flexAnswers);
            return latestSaveState.FlexResponse.FlexResponseLink.PackageGuid == Guid.Empty || latestSaveState.FlexResponse.FlexResponseLink.Package == null
                ? await GetOrCreatePackage(insured, latestSaveState.FlexResponse.FlexResponseLink.PathRecording.Agent,
                    firstOffer, latestSaveState.FlexResponse, flexAnswers, userWantsProfessional, nxusTypeId, false,
                    rating, latestSaveState.FlexVersionGuid)
                : latestSaveState.FlexResponse.FlexResponseLink.Package;
        }

        public static IEnumerable<FlexVersionCarrierOfferEntity> GetFilteredOffersForConstruction(
            bool userWantsProfessional, int nxusTypeId, EntityCollection<FlexVersionCarrierOfferEntity> offers,
            Dictionary<Guid, bool> referralDictionary)
        {
            List<FlexVersionCarrierOfferEntity> toReturn = [];
            if (referralDictionary != null)
            {
                foreach (FlexVersionCarrierOfferEntity item in offers)
                {
                    if (item.CarrierMaximumOffer.PolicyTypeId == (int)PolicyTypeEnum.ProfessionalLiabilityInsuranceEO &&
                        userWantsProfessional)
                    {
                        if (referralDictionary.ContainsKey(item.CarrierMaximumOfferGuid) &&
                            !referralDictionary[item.CarrierMaximumOfferGuid])
                        {
                            toReturn.Add(item);
                        }
                    }
                    else if (item.CarrierMaximumOffer.PolicyTypeId == (int)PolicyTypeEnum.PollutionLiabilityInsurance &&
                             nxusTypeId != 0 && item.CarrierMaximumOffer.VariationNumber == 1)
                    {
                        if (referralDictionary.ContainsKey(item.CarrierMaximumOfferGuid) &&
                            !referralDictionary[item.CarrierMaximumOfferGuid])
                        {
                            toReturn.Add(item);
                        }
                    }
                    else if (item.CarrierMaximumOffer.PolicyTypeId == (int)PolicyTypeEnum.PollutionLiabilityInsurance &&
                             nxusTypeId == 0 && item.CarrierMaximumOffer.VariationNumber == 0)
                    {
                        if (referralDictionary.ContainsKey(item.CarrierMaximumOfferGuid) &&
                            !referralDictionary[item.CarrierMaximumOfferGuid])
                        {
                            toReturn.Add(item);
                        }
                    }
                    else if (item.CarrierMaximumOffer.PolicyTypeId == (int)PolicyTypeEnum.PollutionProfessionalCombined)
                    {
                        toReturn.Add(item);
                    }
                }
            }
            else

            {
                foreach (FlexVersionCarrierOfferEntity item in offers)
                {
                    if (item.CarrierMaximumOffer.PolicyTypeId == (int)PolicyTypeEnum.ProfessionalLiabilityInsuranceEO &&
                        userWantsProfessional)
                    {
                        toReturn.Add(item);
                    }
                    else if (item.CarrierMaximumOffer.PolicyTypeId == (int)PolicyTypeEnum.PollutionLiabilityInsurance &&
                             nxusTypeId != 0 && item.CarrierMaximumOffer.VariationNumber == 1)
                    {
                        toReturn.Add(item);
                    }
                    else if (item.CarrierMaximumOffer.PolicyTypeId == (int)PolicyTypeEnum.PollutionLiabilityInsurance &&
                             nxusTypeId == 0 && item.CarrierMaximumOffer.VariationNumber == 0)
                    {
                        toReturn.Add(item);
                    }
                }
            }

            return toReturn;
        }

        public static async Task<PackageEntity> GetOrCreatePackage(InsuredEntity insured, AgentEntity agent,
            EntityCollection<FlexVersionCarrierOfferEntity> offer, FlexResponseEntity flexResponse,
            List<FlexAnswer> flexAnswers, bool userWantsProfessional, int nxusTypeId, bool isProjectSpecific,
            FlexRating flexRating, Guid flexVersionGuid)
        {
            ProgramEntity program = flexResponse.FlexDefinition.Program;
            if (flexResponse.FlexResponseLink.PackageGuid != Guid.Empty &&
                flexResponse.FlexResponseLink.Package != null && !isProjectSpecific)
            {
                return flexResponse.FlexResponseLink.Package;
            }

            (DateTime effectiveDate, DateTime expirationDate) = flexAnswers.GetEffectiveAndExpirationDateFromAnswers();
            PackageEntity existingPackage;
            if (isProjectSpecific)
            {
                existingPackage = flexResponse.FlexResponseLink.Package;
            }
            else

            {
                existingPackage = insured.InsuredAgentHistory
                    .Where(i => i.AgentGuid == agent.AgentGuid).SelectMany(p => p.Package)
                    .Where(p => p.ProgramGuid == program.ProgramGuid && p.IsDeleted == false &&
                                p.PackageDate.Year == effectiveDate.Year)
                    .OrderByDescending(p => p.PackageDate).FirstOrDefault();
            }

            PackageEntity packageToUse = null;
            PolicyProspectEntity policy = null;
            int renewableStatus = (int)UcpmRenewableStatusEnum.Renewable;
            int nonRenewableStatus = (int)UcpmRenewableStatusEnum.NonRenewable;


            DateTimeOffset effectiveDateZoned = effectiveDate;
            if (agent == null)
            {
                return null;
            }

            Guid newInsuredAgentHistoryPackageGuid =
                await FlexEntityFetcherModel.GetHistoryByInsuredAndAgent(insured.InsuredGuid, agent.AgentGuid);
            if (existingPackage != null)
            {
                packageToUse = existingPackage;
            }
            else

            {
                packageToUse = await CreateNewChildPackage(agent.AgentGuid, program, insured,
                    newInsuredAgentHistoryPackageGuid, effectiveDateZoned);
            }

            List<FlexVersionCarrierOfferEntity> filteredOffers = offer.ToList();
            if (flexResponse.FlexDefinitionId == (int)FlexDefinitionEnum.Construction)
            {
                filteredOffers = GetFilteredOffersForConstruction(userWantsProfessional, nxusTypeId, offer,
                    flexRating.ReferralDictionary).ToList();
            }

            var groupedOffers = filteredOffers
                .GroupBy(o => new

                {
                    o.CarrierMaximumOffer.PolicyTypeId,
                    o.CarrierMaximumOffer.VariationNumber
                })
                .ToList();

            foreach (var group in groupedOffers)
            {
                var key = group.Key;
                int policyTypeId = key.PolicyTypeId;
                int variationNumber = key.VariationNumber;

                if (policyTypeId == (int)PolicyTypeEnum.ProfessionalLiabilityInsuranceEO && !userWantsProfessional)
                {
                    continue;
                }

                IEnumerable<PolicyProspectEntity> eligiblePolicies = packageToUse.PolicyProspect.Where(p =>
                    p.PolicyTypeId == policyTypeId
                    && p.IsDeleted == false
                    && p.CarrierSubmission.All(c => c.PolicyStatus.IsBound != true) &&
                    (p.FlexResponseGuid == flexResponse.FlexResponseGuid || p.FlexResponseGuid == Guid.Empty));
                foreach (PolicyProspectEntity candidatePolicy in eligiblePolicies)
                {
                    policy = candidatePolicy;
                    break;
                }

                if (isProjectSpecific && policyTypeId == (int)PolicyTypeEnum.PollutionLiabilityInsurance)
                {
                    bool hasNonRenewablePolicy = packageToUse.PolicyProspect
                        .Any(pol => pol.UcpmRenewableStatusId == (int)UcpmRenewableStatusEnum.NonRenewable);

                    if (!hasNonRenewablePolicy)
                    {
                        policy = await CreateNewChildPolicy(packageToUse, effectiveDateZoned, nonRenewableStatus,
                            flexResponse.FlexResponseGuid, group.First().CarrierMaximumOffer, flexAnswers, insured,
                            flexVersionGuid, nxusTypeId);
                        policy.UcpmRenewableStatusId = nonRenewableStatus;
                    }
                }
                else

                {
                    if (policy != null)
                    {
                        if (policy.UcpmRenewableStatusId != renewableStatus)
                        {
                            policy.UcpmRenewableStatusId = renewableStatus;
                        }

                        if (policy.FlexResponseGuid == Guid.Empty)
                        {
                            policy.FlexResponseGuid = flexResponse.FlexResponseGuid;
                        }

                        policy.IsTransactional = renewableStatus == (int)UcpmRenewableStatusEnum.NonRenewable;
                        await _quickAdapterAsync.SaveEntityAsync(policy, true);
                    }
                    else

                    {
                        policy = await CreateNewChildPolicy(packageToUse, effectiveDateZoned, renewableStatus,
                            flexResponse.FlexResponseGuid, group.First().CarrierMaximumOffer, flexAnswers, insured,
                            flexVersionGuid, nxusTypeId);

                    }
                    await FindBestRep(program.OnlineRepGuid, packageToUse, flexResponse.FlexDefinition.ProgramGuid, insured.Name);
                }

                int ucpmRenewalStatusId =
                    await DetermineRenewalStatus(packageToUse, policy, offer.First().CarrierMaximumOffer);
                policy.UcpmRenewalStatusId = ucpmRenewalStatusId;


                Guid cobGuid = Guid.Empty;
                if ((flexResponse?.FlexResponseLink?.PathRecordingGuid ?? Guid.Empty) != Guid.Empty)
                {
                    cobGuid = flexResponse.FlexResponseLink.PathRecording.ResolvedClassOfBusinessGuid;
                }
                else if ((flexResponse?.FlexResponseLink?.ProcessInstance?.PreferredClassOfBusinessGuid ??
                          Guid.Empty) != Guid.Empty)
                {
                    cobGuid = flexResponse.FlexResponseLink.ProcessInstance.PreferredClassOfBusinessGuid;
                }
                else if (flexAnswers.Count > 0)
                {
                    cobGuid = await GetClassOfBusinessFromFlexAnswers(flexAnswers);
                }

                if (cobGuid != Guid.Empty)
                {
                    policy.PreferredClassOfBusinessGuid = cobGuid;
                    await FlexEntityFetcherModel.AddNewCobToInsured(insured.InsuredGuid, cobGuid);
                }


                await _quickAdapterAsync.SaveEntityAsync(policy, true);
            }

            flexResponse.FlexResponseLink.PackageGuid = policy.PackageGuid;
            if (flexResponse.FlexResponseLink.AgentGuid != Guid.Empty)
            {
                _quickAdapterAsync.SetNull(flexResponse.FlexResponseLink, "AgentGuid");
            }

            await _quickAdapterAsync.SaveEntityAsync(flexResponse.FlexResponseLink, true);
            return GetPackage(packageToUse.PackageGuid);
        }

        private static async Task AddAllPartsDefinedTermsAndOptions(CarrierSubmissionEntity submission,
            Guid flexVersionGuid, int nxusTypeId)
        {
            UnitOfWork2 work = new();

            IkeaCoveragePartQuery ikeaCoveragePartQuery = new(_quickAdapterAsync);

            //Fetch All Coverage Parts
            EntityCollection<IkeaCoveragePartEntity> coverageParts = await ikeaCoveragePartQuery.GetAllIkeaCoverageParts();

            //Excluding CoverageParts marked as UnavailableOnCustom
            if (nxusTypeId == (int)NxusTypeEnum.NXUSCustom)
            {
                coverageParts = [.. coverageParts.Where(cp => !cp.UnavailableOnCustom)];
            }

            //Populate & Insert All Existing Coverage Parts to the CarrierSubmissionCoveragePart Table
            EntityCollection<IkeaCarrierSubmissionCoveragePartEntity> policyParts = [];

            foreach (IkeaCoveragePartEntity item in coverageParts)
            {
                IkeaCarrierSubmissionCoveragePartEntity IkeaCarrierSubmissionCoveragePartEntity = new()
                {
                    CarrierSubmissionGuid = submission.CarrierSubmissionGuid,
                    IkeaCoveragePartGuid = item.IkeaCoveragePartGuid
                };
                policyParts.Add(IkeaCarrierSubmissionCoveragePartEntity);
            }

            work.AddCollectionForSave(policyParts);


            //Fetch All Defined Terms
            EntityCollection<IkeaDefinedTermEntity> definedTerms = await ikeaCoveragePartQuery.GetAllIkeaDefinedTerms();

            //Populate & Insert All Existing Defined Terms to the IkeaCarrierSubmissionDefinedTermTable
            EntityCollection<IkeaCarrierSubmissionDefinedTermEntity> policyDefinedTerms = [];
            foreach (IkeaDefinedTermEntity item in definedTerms)
            {
                IkeaCarrierSubmissionDefinedTermEntity IkeaCarrierSubmissionCoveragePartEntity = new()
                {
                    CarrierSubmissionGuid = submission.CarrierSubmissionGuid,
                    IkeaDefinedTermGuid = item.IkeaDefinedTermGuid
                };
                policyDefinedTerms.Add(IkeaCarrierSubmissionCoveragePartEntity);
            }

            work.AddCollectionForSave(policyDefinedTerms);


            //Fetch All Defined Term Options
            EntityCollection<IkeaDefinedTermOptionEntity> definedTermOptions = await ikeaCoveragePartQuery.GetAllIkeaDefinedTermOptions();

            //Populate & Insert All Existing Defined Terms to the IkeaCarrierSubmissionDefinedTermTable
            EntityCollection<IkeaCarrierSubmissionDefinedTermOptionEntity> policyDefinedTermOptions = [];
            foreach (IkeaDefinedTermOptionEntity item in definedTermOptions)
            {
                IkeaCarrierSubmissionDefinedTermOptionEntity IkeaCarrierSubmissionCoveragePartEntity = new()
                {
                    CarrierSubmissionGuid = submission.CarrierSubmissionGuid,
                    IkeaDefinedTermOptionGuid = item.IkeaDefinedTermOptionGuid
                };
                policyDefinedTermOptions.Add(IkeaCarrierSubmissionCoveragePartEntity);
            }

            work.AddCollectionForSave(policyDefinedTermOptions);

            await _quickAdapterWriter.CommitWorkAsync(work);
        }

        public static PackageEntity GetPackage(Guid packageGuid)
        {
            PackageEntity package = new(packageGuid);

            PrefetchPath2 path = new(EntityType.PackageEntity);
            IPrefetchPath2 policyPath = path.Add(PackageEntity.PrefetchPathPolicyProspect).SubPath;

            IPrefetchPath2 submissionPath = policyPath.Add(PolicyProspectEntity.PrefetchPathCarrierSubmission).SubPath;
            IPrefetchPath2 submissionFormPath =
                submissionPath.Add(CarrierSubmissionEntity.PrefetchPathSubmissionForm).SubPath;
            submissionFormPath.Add(SubmissionFormEntity.PrefetchPathForm);
            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionSubjectivity);

            IPrefetchPath2 optionPath = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionOption)
                .SubPath;
            IPrefetchPath2 invoicePath = optionPath.Add(CarrierSubmissionOptionEntity.PrefetchPathInvoice).SubPath;
            IPrefetchPath2 itemPath = invoicePath.Add(InvoiceEntity.PrefetchPathInvoiceItem).SubPath;
            itemPath.Add(InvoiceItemEntity.PrefetchPathTransactionType);

            path.Add(PackageEntity.PrefetchPathProgram);
            path.Add(PackageEntity.PrefetchPathPrimaryRepEmployee);
            IPrefetchPath2 iahPath = path.Add(PackageEntity.PrefetchPathInsuredAgentHistory).SubPath;
            iahPath.Add(InsuredAgentHistoryEntity.PrefetchPathInsured);
            IPrefetchPath2 agentPath = iahPath.Add(InsuredAgentHistoryEntity.PrefetchPathAgent).SubPath;
            IPrefetchPath2 locationPath = agentPath.Add(AgentEntity.PrefetchPathLocation).SubPath;
            locationPath.Add(LocationEntity.PrefetchPathCompany);


            _quickAdapterAsync.FetchEntity(package, path);
            return package;
        }

        private static PolicyProspectEntity GetPolicy(Guid policyProspectGuid)
        {
            PolicyProspectEntity policy = new(policyProspectGuid);

            PrefetchPath2 path = new(EntityType.PolicyProspectEntity);
            IPrefetchPath2 submissionPath = path.Add(PolicyProspectEntity.PrefetchPathCarrierSubmission).SubPath;
            IPrefetchPath2 submissionFormPath =
                submissionPath.Add(CarrierSubmissionEntity.PrefetchPathSubmissionForm).SubPath;
            submissionFormPath.Add(SubmissionFormEntity.PrefetchPathForm);
            submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionSubjectivity);

            IPrefetchPath2 optionPath = submissionPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionOption)
                .SubPath;
            IPrefetchPath2 invoicePath = optionPath.Add(CarrierSubmissionOptionEntity.PrefetchPathInvoice).SubPath;
            IPrefetchPath2 itemPath = invoicePath.Add(InvoiceEntity.PrefetchPathInvoiceItem).SubPath;
            itemPath.Add(InvoiceItemEntity.PrefetchPathTransactionType);

            IPrefetchPath2 packagePath = path.Add(PolicyProspectEntity.PrefetchPathPackage).SubPath;
            packagePath.Add(PackageEntity.PrefetchPathProgram);
            packagePath.Add(PackageEntity.PrefetchPathPrimaryRepEmployee);
            IPrefetchPath2 iahPath = packagePath.Add(PackageEntity.PrefetchPathInsuredAgentHistory).SubPath;
            iahPath.Add(InsuredAgentHistoryEntity.PrefetchPathInsured);
            IPrefetchPath2 agentPath = iahPath.Add(InsuredAgentHistoryEntity.PrefetchPathAgent).SubPath;
            IPrefetchPath2 locationPath = agentPath.Add(AgentEntity.PrefetchPathLocation).SubPath;
            locationPath.Add(LocationEntity.PrefetchPathCompany);

            _quickAdapterAsync.FetchEntity(policy, path);
            return policy;
        }

        private static ClassOfBusinessEntity GetClassOfBusinessByName(string cobName)
        {
            EntityCollection<ClassOfBusinessEntity> entities = [];

            RelationPredicateBucket filter = new(ClassOfBusinessFields.ClassOfBusinessName == cobName);
            QueryParameters queryParameters = new()
            {
                CollectionToFetch = entities,
                FilterToUse = filter.PredicateExpression﻿
            };
            _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters).GetAwaiter().GetResult();
            return entities.FirstOrDefault();
        }

        private static (string, decimal) GetRenewalOfPolicyNumber(Guid renewalOfPolicyGuid)
        {
            if (renewalOfPolicyGuid == Guid.Empty)
            {
                return ("", 0);
            }

            PolicyProspectEntity renewalOfPolicy = new(renewalOfPolicyGuid);

            PrefetchPath2 path = new(EntityType.PolicyProspectEntity);
            IPrefetchPath2 policyProspectSimpleView =
                path.Add(PolicyProspectEntity.PrefetchPathPolicyProspectSimpleView).SubPath;
            IPrefetchPath2 submissionPath = policyProspectSimpleView
                .Add(PolicyProspectSimpleViewEntity.PrefetchPathCarrierSubmissionSelected).SubPath;
            IPrefetchPath2 finSubmissionPath =
                submissionPath.Add(CarrierSubmissionEntity.PrefetchPathFinancialSummarySubmission).SubPath;
            IPrefetchPath2 optionPath = finSubmissionPath
                .Add(FinancialSummarySubmissionEntity.PrefetchPathCarrierSubmissionOption).SubPath;

            _quickAdapterAsync.FetchEntity(renewalOfPolicy, path);
            string policyNumber = renewalOfPolicy.PolicyProspectSimpleView?.UcpmPolicyNumber ?? "";
            decimal revenues = renewalOfPolicy.PolicyProspectSimpleView?.CarrierSubmissionSelected
                ?.FinancialSummarySubmission?.CarrierSubmissionOption?.ExposureBasis ?? 0;
            return (policyNumber, revenues);
        }

        public static string BuildDeductibleLabel(FlexOption option,
            EntityCollection<DeductibleRequestedEntity> deductibleList)
        {
            decimal deductible = deductibleList.Single(l => l.DeductibleRequestedGuid == option.DeductibleRequestedGuid)
                .DeductibleRequestedValue;
            return Parse.FormatCurrencyScaled(deductible);
        }

        public static string BuildLimitLabel(FlexOption option, EntityCollection<LimitsRequestedEntity> limits)
        {
            decimal occLimit = limits.Single(l => l.LimitsRequestedGuid == option.OccLimitsRequstedGuid)
                .LimitsRequestedValue;
            decimal aggLimit = limits.Single(l => l.LimitsRequestedGuid == option.AggLimitsRequestedGuid)
                .LimitsRequestedValue;
            return Parse.FormatCurrencyScaled(occLimit, aggLimit);
        }

        public static async
            Task<Guid> ProcessCarrierGroup(
                Guid CarrierMaximumOfferGuid,
                List<FlexOption> options,
                FlexResponseSaveStateEntity latestSaveState,
                NewFlexRatingContainer ratingData,
                FlexVersionCarrierOfferEntity offerInfo,
                PolicyProspectEntity policy,
                InsuredEntity insured,
                EntityCollection<LimitsRequestedEntity> limitList,
                EntityCollection<DeductibleRequestedEntity> deductibleList,
                CarrierMaximumOfferEntity firstOffer,
                AgentEntity agent,
                ProgramEntity program,
                List<FlexAnswer> flexAnswers,
                QuickAdapterAsync quickAdapter,
                FlexAnswerContainer container,
                CommissionCalculationMicroModel commissionCalculationMicroModel,
                InvoiceModel invoiceModel,
                StateRequirementQuery stateRequirement,
                int nxusTypeId,
                List<int> nxusTypeIds,
                int ucpmRenewableStatusId)
        {
            EntityCollection<FlexVersionCarrierOfferEntity>
                offers = latestSaveState.FlexVersion.FlexVersionCarrierOffer;
            PackageEntity package = await GetPackage(latestSaveState, insured, offers, flexAnswers, nxusTypeId, null);
            VariableHolder variableHolder = new();
            ExpressionParser parser = new(variableHolder);
            FlexEntityCreatorModel.GetEffectiveAndExpirationDatePlusPolicyTerms(flexAnswers,
                container.OptionalExpiringDates, out DateTime effectiveDate, out DateTime expirationDate,
                out List<int> policyTerms);
            CarrierMaximumOfferEntity offer =
                FlexEntityFetcherModel.GetCarrierGuidFromCarrierMaximumOffer(CarrierMaximumOfferGuid);
            Guid carrierGuid = offer.CarrierProgram.CarrierGuid;
            Guid carrierMaximumOfferGuid = offer.CarrierMaximumOfferGuid;
            int policyStatusId = (int)PolicyStatusEnum.QuotedRcvd;
            if (flexAnswers.Any(x => x.QuestionDataName == "DeclineReasons"))
            {
                policyStatusId =
                    flexAnswers.FirstOrDefault(x => x.QuestionDataName == "DeclineReasons")?.Answer == "None"
                        ? (int)PolicyStatusEnum.QuotedRcvd
                        : (int)PolicyStatusEnum.Declined;
            }

            if (policy == null)
            {
                policy = await CreateNewChildPolicy(package,
                    effectiveDate,
                    ucpmRenewableStatusId,
                    latestSaveState.FlexResponseGuid,
                    offerInfo.CarrierMaximumOffer,
                    flexAnswers,
                    insured,
                    latestSaveState.FlexVersionGuid,
                    nxusTypeId);
                policy.Package = package;
            }

            CarrierMaximumOfferQuery carrierMaximumOfferQuery = new(quickAdapter);
            CarrierMaximumOfferEntity carrierMaximumOfferEntity =
                carrierMaximumOfferQuery.GetByGuid(carrierMaximumOfferGuid);
            bool isNxus = carrierMaximumOfferEntity?.VariationName == "Nxus";

            string taxState = await FlexEntityFetcherModel.GetTaxState(flexAnswers, insured);

            List<CarrierSubmissionEntity> submissions = await GetOrCreateCarrierSubmissions(policy,
                carrierGuid,
                offer,
                true,
                taxState,
                latestSaveState.FlexVersionGuid,
                isNxus ? nxusTypeIds : [nxusTypeId],
                policyStatusId);

            CarrierSubmissionEntity submission = submissions?.FirstOrDefault();

            CarrierSubmissionFlexResponseEntity carrierSubmissionFlexResponse = await GetOrCreateCarrierSubmissionFlexResponse(latestSaveState, submission, true);
            commissionCalculationMicroModel.SetFields(submission.CarrierSubmissionGuid, DateTimeOffset.Now, false);
            decimal commToAgency = await commissionCalculationMicroModel.CalculateAgentCommission(policy.PolicyProspectGuid, offer.VariationNumber);

            await UpdatePolicyAndSubmissionOptionDates(policy, effectiveDate, expirationDate, options.FirstOrDefault()?.PolicyTermDays ?? 365, true);

            foreach (FlexOption option in options)
            {
                await ProcessAutoGeneratedQuotes(submission, policy, option, limitList, deductibleList, offer, null,
                    true, agent, program, flexAnswers, commissionCalculationMicroModel, invoiceModel,
                    ucpmRenewableStatusId, commToAgency);
            }

            FlexQuestionContainer flex = FlexQuestionContainer.FromJson(latestSaveState.FlexVersion.SurveyJson);
            await AddFormsToSubmission(submission, latestSaveState.FlexResponse, carrierMaximumOfferGuid);

            List<FlexQuestion> flexQuestions = FlexHelper.GetFullQuestionList(flex.FlexSurvey);

            variableHolder.SetUpWithFlexAnswers(container, flexQuestions, policy.EffectiveDateZoned.Date);
            variableHolder.SetVariable("IsRenewal", policy.RenewalOfPolicyGuid != Guid.Empty);
            variableHolder.SetVariable("NxusMatch", submission.NxusTypeId == (int)NxusTypeEnum.NXUSMatch);
            decimal highestDeductible = options.OrderByDescending(d => d.Deductible).FirstOrDefault()?.Deductible ?? 0;
            decimal lowestDeductible = options.OrderBy(d => d.Deductible).FirstOrDefault()?.Deductible ?? 0;
            variableHolder.SetVariable("HighestDeductible", (double)highestDeductible);
            variableHolder.SetVariable("LowestDeductible", (double)lowestDeductible);

            FlexAnswer cobRevenue = flexAnswers.FirstOrDefault(x => x.QuestionDataName == "CobRevenues");
            double revenueVal = 0.00;
            double payrollVal = 0.00;

            variableHolder.SetVariable("RevenueForCob", 1, 1, revenueVal);
            variableHolder.SetVariable("Payroll", 1, 1, payrollVal);

            if (cobRevenue != null && cobRevenue.ChildAnswers.Any(i => i.QuestionDataName == "RevenueForCob"))
            {
                revenueVal = cobRevenue.ChildAnswers
                    .Where(i => i.QuestionDataName == "RevenueForCob")
                    .Sum(i =>
                    {
                        string val = i.Answer ?? "$0";
                        return (double)decimal.Parse(val, NumberStyles.Currency);
                    });
            }

            // Total Payroll calculation
            if (cobRevenue != null && cobRevenue.ChildAnswers.Any(i => i.QuestionDataName == "Payroll"))
            {
                payrollVal = cobRevenue.ChildAnswers
                    .Where(i => i.QuestionDataName == "Payroll")
                    .Sum(i =>
                    {
                        string val = i.Answer ?? "$0";
                        return (double)decimal.Parse(val, NumberStyles.Currency);
                    });
            }

            variableHolder.SetVariable("RevenueForCob", 1, 0, revenueVal);
            variableHolder.SetVariable("Payroll", 1, 0, payrollVal);

            // Remove all existing subjectivities for this submission before adding new ones
            if (submission.CarrierSubmissionSubjectivity.Any())
            {
                await DeleteCarrierSubmissionSubjectivity(submission); // Delete subjectivities from the database
                submission.CarrierSubmissionSubjectivity.Clear();      // Clear the in-memory collection
            }

            // Add new subjectivities from the rating data if present
            if (ratingData.CarrierSubjectivities != null && ratingData.CarrierSubjectivities.Any())
            {
                IEnumerable<FlexSubjectivity> subjectivities =
                    ratingData.CarrierSubjectivities.Where(r => r.Rule != "NONE");
                foreach (FlexSubjectivity item in subjectivities)
                {
                    CompanyEntity companyEntity = agent.Location.Company;
                    DateTimeOffset producerAgreement = await GetProducerAgreement(companyEntity.CompanyGuid);

                    // Add subjectivity if it does not already exist and has no rule
                    if (string.IsNullOrWhiteSpace(item.Rule) &&
                        submission.CarrierSubmissionSubjectivity.All(c =>
                            c.SubjectivityTextOverride != item.Subjectivity))
                    {
                        await AddNewSubmissionSubjectivity(item, submission, quickAdapter);

                        // If no producer agreement, add a "Producer Questionnaire" subjectivity
                        if (producerAgreement == DateTimeOffset.MinValue &&
                             submission.CarrierSubmissionSubjectivity.All(c =>
                                 c.SubjectivityTextOverride != item.Subjectivity))
                        {
                            item.Subjectivity = "Producer Questionnaire";
                            item.Link =
                                "https://ucpmpublic.blob.core.windows.net/ucpmpublic/Production/f4/2e/511f/ada04f35a1036c1f987d794f/Producer_Questionnaire.pdf";
                            await AddNewSubmissionSubjectivity(item, submission, quickAdapter);
                        }
                    }
                    // If there is a rule, evaluate it and add subjectivity if the rule passes
                    else if (!string.IsNullOrEmpty(item.Rule))
                    {
                        try
                        {
                            System.Linq.Expressions.Expression<Func<bool>> condition1 = parser.ParseBool(item.Rule);
                            Func<bool> compiled = condition1.Compile();
                            bool result = compiled();
                            if (result && submission.CarrierSubmissionSubjectivity.All(c =>
                                    c.SubjectivityTextOverride != item.Subjectivity))
                            {
                                await AddNewSubmissionSubjectivity(item, submission, quickAdapter);
                            }

                        }
                        catch (MissingVariableException)
                        {
                            // If a variable is missing, skip adding this subjectivity
                        }
                    }
                }
            }

            // Add state requirement subjectivities if not already present
            EntityCollection<StateRequirementEntity> requirements =
                await stateRequirement.GetAllRequirementsByStateAndPolicy(submission.SurplusLinesTaxState,
                    offer.PolicyTypeId);
            foreach (StateRequirementEntity requirement in requirements)
            {
                string asQuotedExtensionText = requirement.RequirementText;
                string asQuotedUrl = requirement.RequirementUrl;
                if (submission.CarrierSubmissionSubjectivity.All(c =>
                        c.SubjectivityTextOverride != requirement.RequirementText))
                {
                    await AddNewSubmissionSubjectivity(requirement, submission, quickAdapter);
                }
            }

            // Add "Payment must be received prior to coverage being bound" subjectivity if company tag 88 is present
            if (submission.CarrierSubmissionSubjectivity.All(c =>
                    c.SubjectivityTextOverride != "Payment must be received prior to coverage being bound"))
            {
                CompanyEntity company = agent.Location.Company;
                if (company.CompanyTag.Any(a => a.TagId == 88))
                {
                    await AddCreditRisk(submission, quickAdapter);
                }
            }

            return submission.CarrierSubmissionGuid;
        }

        public static async Task<DateTimeOffset> GetProducerAgreement(Guid companyGuid)
        {
            CompanyEntity company = await _linqMetaData.Company.Where(w => w.CompanyGuid == companyGuid).SingleAsync();
            return company.ProducerAgreementDateZoned;
        }

        public static async Task<EntityCollection<IkeaCoveragePartEntity>> GetAllCoverageParts()
        {
            EntityCollection<IkeaCoveragePartEntity> ikeaCoveragePartEntities = [];
            SortExpression sorter = new(IkeaCoveragePartFields.Title | SortOperator.Ascending);

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = ikeaCoveragePartEntities,
                SorterToUse = sorter﻿
            };

            await _quickAdapterReader.FetchEntityCollectionAsync(queryParameters);

            return ikeaCoveragePartEntities;
        }

        public static async Task<EntityCollection<IkeaCarrierSubmissionCoveragePartEntity>>
            GetAllIkeaPolicyCoverageParts(Guid carrierSubmissionGuid)
        {
            EntityCollection<IkeaCarrierSubmissionCoveragePartEntity> ikeaPolicyCoveragePartEntities = [];

            RelationPredicateBucket filter =
                new(IkeaCarrierSubmissionCoveragePartFields.CarrierSubmissionGuid == carrierSubmissionGuid);

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = ikeaPolicyCoveragePartEntities,
                FilterToUse = filter.PredicateExpression﻿
            };

            await _quickAdapterReader.FetchEntityCollectionAsync(queryParameters);

            return ikeaPolicyCoveragePartEntities;
        }


        public static async Task<EntityCollection<IkeaCarrierSubmissionDefinedTermOptionEntity>>
            GetAllIkeaPolicyDefinedTermOptions(Guid carrierSubmissionGuid)
        {
            EntityCollection<IkeaCarrierSubmissionDefinedTermOptionEntity> ikeaPolicyDefinedTermOptionEntities = [];

            RelationPredicateBucket filter = new(IkeaCarrierSubmissionDefinedTermOptionFields.CarrierSubmissionGuid ==
                                                 carrierSubmissionGuid);

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = ikeaPolicyDefinedTermOptionEntities,
                FilterToUse = filter.PredicateExpression﻿
            };


            await _quickAdapterReader.FetchEntityCollectionAsync(queryParameters);

            return ikeaPolicyDefinedTermOptionEntities;
        }

        public static async Task<EntityCollection<IkeaDefinedTermEntity>> GetAllIkeaDefinedTerms()
        {
            EntityCollection<IkeaDefinedTermEntity> ikeaDefinedTermEntities = [];
            SortExpression sorter = new(IkeaDefinedTermFields.Title | SortOperator.Ascending);

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = ikeaDefinedTermEntities,
                SorterToUse = sorter﻿
            };


            await _quickAdapterReader.FetchEntityCollectionAsync(queryParameters);

            return ikeaDefinedTermEntities;
        }

        public static async Task<EntityCollection<IkeaDefinedTermOptionEntity>> GetAllIkeaDefinedTermOptions()
        {
            EntityCollection<IkeaDefinedTermOptionEntity> ikeaDefinedTermEntities = [];
            SortExpression sorter = new(IkeaDefinedTermOptionFields.SortOrder | SortOperator.Ascending);

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = ikeaDefinedTermEntities,
                SorterToUse = sorter﻿
            };


            await _quickAdapterReader.FetchEntityCollectionAsync(queryParameters);

            return ikeaDefinedTermEntities;
        }

        public static async Task<EntityCollection<IkeaCarrierSubmissionCoveragePartEntity>> GetIkeaCoverageParts(
            Guid carrierSubmissionGuid)
        {
            EntityCollection<IkeaCarrierSubmissionCoveragePartEntity> ikeaPolicyCoveragePartEntities = [];

            PrefetchPath2 path = new(EntityType.IkeaCarrierSubmissionCoveragePartEntity);
            IPrefetchPath2 ikeaCoveragePartpath =
                path.Add(IkeaCarrierSubmissionCoveragePartEntity.PrefetchPathIkeaCoveragePart).SubPath;

            RelationPredicateBucket filter =
                new(IkeaCarrierSubmissionCoveragePartFields.CarrierSubmissionGuid == carrierSubmissionGuid);

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = ikeaPolicyCoveragePartEntities,
                PrefetchPathToUse = path,
                FilterToUse = filter.PredicateExpression﻿
            };

            await _quickAdapterReader.FetchEntityCollectionAsync(queryParameters);

            return ikeaPolicyCoveragePartEntities;
        }


        public static int[] ExcludedCoveragesByIndex(
            EntityCollection<IkeaCarrierSubmissionCoveragePartEntity> ikeaPolicyCoveragePartEntities,
            bool isNxusMax = false)
        {
            string[] coveragesLetter = ["A", "B", "C", "D", "E", "F", "G"];

            List<int> excludedCoveragesByIndex = [];

            if (isNxusMax)
            {
                return excludedCoveragesByIndex.ToArray();
            }

            int index = 0;
            foreach (string letter in coveragesLetter)
            {
                List<IkeaCarrierSubmissionCoveragePartEntity> entities = ikeaPolicyCoveragePartEntities
                    .Where(x => x.IkeaCoveragePart.Title.Trim()[0].ToString() == letter).ToList();

                if (!entities.Any())
                {
                    excludedCoveragesByIndex.Add(index);
                }

                index++;
            }

            return excludedCoveragesByIndex.ToArray();
        }

        public static async
            Task<(CarrierMaximumOfferQuoteTemplateEntity, CarrierMaximumOfferAppTemplateEntity,
                CarrierMaximumOfferWarrantyTemplateEntity)> GetTransformedFlexTemplates(Guid carrierMaximumOfferGuid,
                QuickAdapterAsync quickAdapter, FlexAnswerContainer container, InsuredEntity insured,
                PolicyProspectEntity policy, List<CarrierSubmissionEntity> submissions,
                FlexDefinitionEnum flexDefinitionEnum, FlexQuestionContainer flexQuestionContainer,
                Guid flexVersionGuid,
                bool isNxusMax = false, NewFlexRatingContainer newRatingContainer = null,
                List<FlexOption> options = null)
        {
            CarrierSubmissionEntity submission = submissions.FirstOrDefault();
            EntityCollection<IkeaDefinedTermEntity> ikeaDefinedTermEntities = await GetAllIkeaDefinedTerms();
            EntityCollection<IkeaDefinedTermOptionEntity> ikeaDefinedTermOptionEntities =
                await GetAllIkeaDefinedTermOptions();
            EntityCollection<IkeaCoveragePartEntity> ikeaCoveragePartEntities = await GetAllCoverageParts();

            CarrierMaximumOfferEntity carrierMaximumOffer = new(carrierMaximumOfferGuid);
            CarrierProgramEntity carrierProgramEntity = new(carrierMaximumOffer.CarrierProgramGuid);
            CarrierEntity carrierEntity = new(carrierProgramEntity.CarrierGuid);

            // Gets all the subimissions' details for nxus quote﻿
            Dictionary<Guid, EntityCollection<IkeaCarrierSubmissionCoveragePartEntity>>
                ikeaPolicyCoveragePartEntitiesDictionary = [];
            Dictionary<Guid, EntityCollection<IkeaCarrierSubmissionDefinedTermOptionEntity>>
                ikeaPolicyDefinedTermOptionEntitiesDictionary = [];
            Dictionary<Guid, EntityCollection<IkeaCarrierSubmissionCoveragePartEntity>>
                selectedIkeaPolicyCoveragePartEntitiesDictionary = [];
            Dictionary<Guid, int[]> excludedCoveragesListDictionary = [];
            foreach (CarrierSubmissionEntity sub in submissions)
            {
                ikeaPolicyCoveragePartEntitiesDictionary.Add(sub.CarrierSubmissionGuid,
                    await GetIkeaCoverageParts(sub.CarrierSubmissionGuid));
                ikeaPolicyDefinedTermOptionEntitiesDictionary.Add(sub.CarrierSubmissionGuid,
                    await GetAllIkeaPolicyDefinedTermOptions(sub.CarrierSubmissionGuid));
                selectedIkeaPolicyCoveragePartEntitiesDictionary.Add(sub.CarrierSubmissionGuid,
                    await GetAllIkeaPolicyCoverageParts(sub.CarrierSubmissionGuid));
                ikeaPolicyCoveragePartEntitiesDictionary.TryGetValue(sub.CarrierSubmissionGuid,
                    out EntityCollection<IkeaCarrierSubmissionCoveragePartEntity> value);
                if (value != null)
                {
                    excludedCoveragesListDictionary.Add(sub.CarrierSubmissionGuid, ExcludedCoveragesByIndex(value));
                }
            }

            // Gets only the first submission details for non nxus quote
            ikeaPolicyCoveragePartEntitiesDictionary.TryGetValue(submission.CarrierSubmissionGuid,
                out EntityCollection<IkeaCarrierSubmissionCoveragePartEntity> ikeaPolicyCoveragePartEntities);
            ikeaPolicyDefinedTermOptionEntitiesDictionary.TryGetValue(submission.CarrierSubmissionGuid,
                out EntityCollection<IkeaCarrierSubmissionDefinedTermOptionEntity> ikeaPolicyDefinedTermOptionEntities);
            selectedIkeaPolicyCoveragePartEntitiesDictionary.TryGetValue(submission.CarrierSubmissionGuid,
                out EntityCollection<IkeaCarrierSubmissionCoveragePartEntity> selectedIkeaPolicyCoveragePartEntities);
            excludedCoveragesListDictionary.TryGetValue(submission.CarrierSubmissionGuid,
                out int[] excludedCoveragesList);

            CarrierMaximumOfferWarrantyTemplateEntity warrantyStatementTemplate = new(carrierMaximumOfferGuid);
            quickAdapter.FetchEntity(warrantyStatementTemplate);

            CarrierMaximumOfferQuoteTemplateEntity quoteTemplate = new(carrierMaximumOfferGuid);
            quickAdapter.FetchEntity(quoteTemplate);

            CarrierMaximumOfferAppTemplateEntity appTemplate = new(carrierMaximumOfferGuid);
            quickAdapter.FetchEntity(appTemplate);

            ClassOfBusinessEntity preferredCob = new(policy.PreferredClassOfBusinessGuid);
            quickAdapter.FetchEntity(preferredCob);

            List<FacilitySummary> facilityWithTanksTable =
                FlexHtmlModel.GetFacilityTable(container, policy.EffectiveDateZoned);
            List<CoveredLocationSummary> coveredLocations =
                FlexHtmlModel.GetCoveredLocationsList(container, flexDefinitionEnum);

            CarrierMaximumOfferQuery carrierMaximumOfferQuery = new(quickAdapter);
            CarrierMaximumOfferEntity carrierMaximumOfferEntity =
                carrierMaximumOfferQuery.GetByGuid(carrierMaximumOfferGuid);

            bool isNxus = carrierMaximumOfferEntity?.VariationName == "Nxus";

            if (quickAdapter.IsClean(quoteTemplate) && quickAdapter.IsClean(appTemplate))
            {
                string insuredName = insured.Name;
                string address = $"{insured.Address} {insured.Address2}".Trim();
                string suite = insured.Address2;
                string city = insured.City;
                string state = insured.State;
                string zipCode = insured.Zip;
                AgentEntity agent = policy.Package.InsuredAgentHistory.Agent;
                string companyName = agent.Location.Company.CompanyName;
                string companyDba = agent.Location.Company.CompanyDba;
                string agentName = agent.AgentName;
                string projectName, completeLocation = "";
                (string renewalOfPolicyNumber, decimal priorRevenues) =
                    GetRenewalOfPolicyNumber(policy.RenewalOfPolicyGuid);

                EmployeeEntity employee = policy.Package.PrimaryRepEmployee;
                string commission = FlexHtmlModel.GetPremiumList(submission);

                string mailingAddress =
                    $"{insured.MailingAddress} {insured.MailingCity}, {insured.MailingState} {insured.MailingZip}"
                        .Trim();
                if (mailingAddress.Length < 2)
                {
                    mailingAddress = "";
                }

                List<FlexAnswer> flexAnswers = container.FlexAnswers;
                (DateTime effectiveDate, DateTime expirationDate) = flexAnswers.GetEffectiveAndExpirationDateFromAnswers();

                //Might want to consider a function that grabs all Answers in Flex and just automatically make them a replace action based on their name
                string retroactiveDateAsString =
                    GetRetroDateAsString(container.FlexAnswers, flexDefinitionEnum); //Revert Nathan
                string priorYearTotalRevenue =
                    FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "PriorYearTotalRevenues")
                        ?.FirstOrDefault()?.Answer ?? "";
                string retroYesNoString = FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "RetroYesNo")?.FirstOrDefault()?.Answer ?? "false";
                string profServicesDescription = FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "Profile")
                    ?.FirstOrDefault()?.Answer ?? "";
                string workCategorization =
                    FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "WorkCategorization")?.FirstOrDefault()
                        ?.Answer ?? "";
                string isProjectSpecificString =
                    FindByQuestion("IsProjectSpecific", flexAnswers).FirstOrDefault()?.Answer ?? "false";

                List<FlexAnswer> revenueClassifications =
                    FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "RevenueClassification")?.FirstOrDefault()
                        ?.ChildAnswers ?? [];

                List<FlexAnswer> cobRevenues =
                    FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "CobRevenues").FirstOrDefault()
                        ?.ChildAnswers ?? [];

                string selectedCobKey =
                    cobRevenues.Where(x => x.QuestionDataName == "SelectedCob").FirstOrDefault()?.Answer ?? "";
                string selectedCobValue = selectedCobKey;

                selectedCobKey = ReplaceSpecialCharacters(selectedCobKey, string.Empty);

                // Get all RevenueForCob answers and sum their values
                IEnumerable<FlexAnswer> revenueAnswers = cobRevenues.Where(x => x.QuestionDataName == "RevenueForCob");
                decimal totalRevenue = 0;
                foreach (FlexAnswer revenue in revenueAnswers)
                {
                    string revenueAnswer = revenue?.Answer ?? "0";
                    if (Parse.IsCurrency(revenueAnswer))
                    {
                        decimal currentRevenue = Parse.CurrencyDecimal(revenueAnswer);
                        totalRevenue += currentRevenue;
                    }
                }
                string revenueForCob = totalRevenue.ToString("C0");

                string payrollForCob = cobRevenues.Where(x => x.QuestionDataName == "Payroll").FirstOrDefault()?.Answer ?? "";

                string projectRectification =
                    FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "AddRectification")?.FirstOrDefault()
                        ?.Answer ?? "false";
                string projectProfProtective =
                    FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "AddProfessionalProtective")
                        ?.FirstOrDefault()?.Answer ?? "false";
                FlexAnswer? retroactiveDates = FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "RetroactiveDates")?.FirstOrDefault();
                string projectDefenseLimit =
                    FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "AddOneMillionAdditionalDefenseLimit")
                        ?.FirstOrDefault()?.Answer ?? "false";
                string projectContractNumber =
                    FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "ProjectNumber")?.FirstOrDefault()
                        ?.Answer ?? "";
                string projectOwner = FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "Owner")
                    ?.FirstOrDefault()?.Answer ?? "";

                string projectRevenues = FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "ProjectRevenues")
                        ?.FirstOrDefault()?.Answer ?? "";

                string usePayrollString =
                    FindByQuestion("UsePayroll", flexAnswers).FirstOrDefault()?.Answer ?? "false";

                bool isProjectSpecific = bool.Parse(isProjectSpecificString);
                bool usePayroll = bool.Parse(usePayrollString);

                string insuredSuiteDisplayProp = (string.IsNullOrWhiteSpace(suite)) ? "none" : "table-cell";
                string revenueForCobDisplayProp = "none";
                string payrollForCobDisplayProp = "none";
                string projectRevenuesDisplayProp = "none";

                if (policy.UcpmRenewableStatusId == (int)UcpmRenewableStatusEnum.NonRenewable)
                {
                    projectRevenuesDisplayProp = "table-cell";
                }
                else
                {
                    if (usePayroll)
                    {
                        payrollForCobDisplayProp = "table-cell";
                    }
                    else
                    {
                        revenueForCobDisplayProp = "table-cell";
                    }
                }

                string projectContact =
                        FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "ProjectNumber")
                            ?.FirstOrDefault()?.Answer ?? "";

                string projectAddress1 =
                        FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "ProjectAddress1")
                            ?.FirstOrDefault()?.Answer ?? "";
                string projectAddress2 =
                    FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "ProjectAddress2")
                        ?.FirstOrDefault()?.Answer ?? "";
                string projectCity = FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "ProjectCity")
                    ?.FirstOrDefault()?.Answer ?? "";
                string projectState = FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "ProjectState")
                    ?.FirstOrDefault()?.Answer ?? "";
                string projectZip = FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "ProjectZip")
                    ?.FirstOrDefault()?.Answer ?? "";

                string calculateNavigatorsSitePollutionLimit = FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "CalculateNavigatorsSitePollutionLimit")
                    ?.FirstOrDefault()?.Answer ?? "";

                string calculateNavigatorsSitePollutionDeductible = FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "CalculateNavigatorsSitePollutionDeductible")
                    ?.FirstOrDefault()?.Answer ?? "";

                if (flexDefinitionEnum == FlexDefinitionEnum.Construction)
                {
                    projectName = FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "ProjectName")
                        ?.FirstOrDefault()?.Answer ?? "";

                    if (!string.IsNullOrWhiteSpace(projectAddress1) && !string.IsNullOrWhiteSpace(projectCity)
                                                                && !string.IsNullOrWhiteSpace(projectState) &&
                                                                !string.IsNullOrWhiteSpace(projectZip))
                    {
                        completeLocation = projectAddress1 + " " + projectAddress2 + ", " + projectCity + ", " +
                                           projectState + ", " + projectZip + " ";
                    }
                }
                else
                {
                    projectName = FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "Project Name")
                        ?.FirstOrDefault()?.Answer ?? "";
                    List<FlexAnswer> projectLocationDetails = FindByQuestion("ProjectLocations", flexAnswers);
                    if (projectLocationDetails.Any())
                    {
                        StringBuilder stringBuilder = new();

                        foreach (FlexAnswer childAnswer in projectLocationDetails[0].ChildAnswers)
                        {
                            if (!string.IsNullOrWhiteSpace(childAnswer.Answer))
                            {
                                if (stringBuilder.Length > 0)
                                {
                                    stringBuilder.Append(", ");
                                }

                                stringBuilder.Append(childAnswer.Answer);
                            }
                        }

                        completeLocation = stringBuilder.ToString();
                    }
                }

                string projectDescription = FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "ProjectDesc")
                    ?.FirstOrDefault()?.Answer ?? "";
                string projectTermLength =
                    FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "ProjectTermLength")?.FirstOrDefault()
                        ?.Answer ?? "";
                string practicePolicyString =
                    FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "PracticePolicy")?.FirstOrDefault()
                        ?.Answer ?? "false";

                bool isRectification = bool.Parse(projectRectification);
                bool isProfProtective = bool.Parse(projectProfProtective);
                bool isDefenseLimit = bool.Parse(projectDefenseLimit);
                bool isPracticePolicy = bool.Parse(practicePolicyString);
                bool retroYesNo = bool.Parse(retroYesNoString);

                string mepStringText = policy.UcpmRenewableStatusId == (int)UcpmRenewableStatusEnum.NonRenewable ? "100%" : "25%/100%";
                if (!string.IsNullOrWhiteSpace(retroactiveDateAsString))
                {
                    DateTime retro = DateTime.MinValue;
                    if (DateTime.TryParse(retroactiveDateAsString, out retro))
                    {
                        retroactiveDateAsString = retro.Date.ToShortDateString();
                    }
                }

                List<FlexAnswer> retroTable = FindByQuestion("RetroCoverage", flexAnswers);
                string nodsRetroDate = CheckNodsResults(policy, submission, retroTable, flexAnswers);

                string completedOperationsPeriod = FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "CompOps")?.FirstOrDefault()
                    ?.Answer ?? "";


                CarrierSubmissionOptionEntity csoEntity = _linqMetaData.CarrierSubmissionOption
                    .Where(a => a.CarrierSubmissionGuid == submission.CarrierSubmissionGuid && !a.IsDeleted)
                    .FirstOrDefault();
                string expBasis = csoEntity?.ExposureBasis.ToString("c2");
                (string projectRevenueTable, string projectTableTotal) =
                    FlexHtmlModel.GetProjectRevenuesWithTotal(container.FlexAnswers);
                string highestAggregate = string.Empty;
                List<CarrierSubmissionOptionEntity> submissionOptions =
                    submission.CarrierSubmissionOption.Where(o => !o.IsDeleted).ToList();
                if (submissionOptions != null && submissionOptions.Any())
                {
                    highestAggregate = submissionOptions.Max(o => o.LimitAggregate).ToString("c0");
                }

                FlexOption? selectedAplOption = options?.FirstOrDefault();


                int planId = selectedAplOption?.MaterPlanId ?? 1;

                string aplTable = flexDefinitionEnum == FlexDefinitionEnum.Apl
                    ? FlexHtmlModel.GenerateAplLimitTable(submission, (AplPlanEnum)planId, retroactiveDateAsString, csoEntity)
                    : string.Empty;
                string aplTankTable = flexDefinitionEnum == FlexDefinitionEnum.Apl
                    ? FlexHtmlModel.GenerateAplTankTable(container.FlexAnswers)
                    : string.Empty;
                int policyTermYears = GetPolicyTermYears(effectiveDate, expirationDate);
                string policyTermYearsLabel = policyTermYears > 1 ? "Years" : "Year";
                string planName =
                    submission.CarrierSubmissionOption.FirstOrDefault(o => !o.IsDeleted)?.FlexMaterPlan?.PlanName ??
                    string.Empty;

                IQueryable<FlexVersionCarrierOfferFormEntity> flexForms = _linqMetaData.FlexVersionCarrierOfferForm.Where(r => r.CarrierMaximumOfferGuid == carrierMaximumOfferGuid && r.FlexVersionGuid == flexVersionGuid);

                Dictionary<string, string> replaceActions = new()
                {
                    { "Today", DateTime.Today.ToString("d") },
                    { "FormalizedToday", DateTime.Today.ToString("MMM dd, yyyy") },
                    { "InsuredName", insuredName },
                    { "InsuredAddress", address },
                    { "InsuredCity", city },
                    { "InsuredSuite", suite },
                    { "InsuredState", state },
                    { "InsuredZip", zipCode },
                    { "InsuredFullAddress", $"{address} {city}, {state} {zipCode}" },
                    { "InsuredMailedAddress", mailingAddress },
                    { "Company", companyName },
                    { "CompanyDba", companyDba },
                    { "AgentName", agentName },
                    { "Location", $"{agent.Location.City}, {agent.Location.State}" },
                    { "AgentEmail", agent.AgentEmail },
                    { "OtherInsureds", FlexHtmlModel.GenerateOtherInsuredsTable(insured) },
                    { "EffectiveDate", effectiveDate.ToString("d") },
                    { "ExpirationDate", expirationDate.ToString("d") },
                    { "PlanName", planName },
                    { "PolicyTerm", $"{policyTermYears} {policyTermYearsLabel}" },
                    { "ApplicationNumber", policy.ApplicationNumber.ToString() },
                    { "EmployeeName", $"{employee.FirstName} {employee.LastName}" },
                    { "EmployeeEmail", $"{employee.Login}" },
                    { "RenewalOf", renewalOfPolicyNumber },
                    { "NodsRetroDate", nodsRetroDate },
                    { "CompletedOperationsPeriod", policy.UcpmRenewableStatusId == (int)UcpmRenewableStatusEnum.NonRenewable ? completedOperationsPeriod : "N/A" },
                    { "PriorRevenues", priorRevenues.ToString("c0") },
                    { "TotalAggregate", highestAggregate },
                    { "AplLimitTable", aplTable },
                    { "AplTankTable", aplTankTable },
                    {
                        "WarrantyQuestionsTable",
                        FlexHtmlModel.GenerateWarrantyQuestionsTable(flexAnswers,
                            flexQuestionContainer.FlexSurvey.FlexQuestions)
                    },
                    {
                        "LimitTable",
                        FlexHtmlModel.GenerateLimitTable(excludedCoveragesList, submission, flexDefinitionEnum, isNxus)
                    },
                    {
                        "CPLTable",
                        FlexHtmlModel.GenerateCplLimitTable(submission, flexDefinitionEnum, isNxus)
                    },
                    {
                        "DeductibleTable",
                        FlexHtmlModel.GenerateDeductibleTable(excludedCoveragesList, submission, flexDefinitionEnum,
                            isNxus)
                    },
                    { "InvoiceTable", FlexHtmlModel.GenerateInvoiceTable(submission, effectiveDate) },
                    {
                        "CoverageSummary",
                        FlexHtmlModel.GenerateCoverageSummary(excludedCoveragesList, ikeaDefinedTermEntities,
                            ikeaDefinedTermOptionEntities, ikeaPolicyDefinedTermOptionEntities,
                            ikeaPolicyCoveragePartEntities, ikeaCoveragePartEntities)
                    },
                    {
                        "LimitTables",
                        FlexHtmlModel.GenerateLimitTables(excludedCoveragesListDictionary, submissions,
                            flexDefinitionEnum, isNxus)
                    },
                    {
                        "CPLTables",
                        FlexHtmlModel.GenerateCplLimitTables(submissions, flexDefinitionEnum, isNxus)
                    },
                    {
                        "DeductibleTables",
                        FlexHtmlModel.GenerateDeductibleTables(excludedCoveragesListDictionary, submissions,
                            flexDefinitionEnum, isNxus)
                    },
                    { "InvoiceTables", FlexHtmlModel.GenerateInvoiceTables(submissions, isNxus, effectiveDate) },
                    {
                        "CoverageSummaries",
                        FlexHtmlModel.GenerateCoverageSummaries(submissions, excludedCoveragesListDictionary,
                            ikeaDefinedTermEntities, ikeaDefinedTermOptionEntities,
                            ikeaPolicyDefinedTermOptionEntitiesDictionary, ikeaPolicyCoveragePartEntitiesDictionary,
                            ikeaCoveragePartEntities, isNxus)
                    },
                    {
                        "FacilityTable",
                        FlexHtmlModel.GenerateFacilityTables(facilityWithTanksTable, policy.EffectiveDateZoned)
                    },
                    { "FormList", FlexHtmlModel.GenerateFormList(submission, flexForms.ToList()) },
                    { "ContingencyList", FlexHtmlModel.GetContingencyList(submission) },
                    { "Commission", commission },
                    { "CoveredLocations", FlexHtmlModel.GenerateCoveredLocationsTable(coveredLocations) },
                    { "AplCoveredLocations", FlexHtmlModel.GenerateAplCoveredLocationsTable(coveredLocations) },
                    { "FacilityDetails", FlexHtmlModel.GenerateFacilityTablesWithoutTanks(facilityWithTanksTable) },
                    {
                        "UstDetails",
                        FlexHtmlModel.GenerateTankDetailsTable(facilityWithTanksTable, "UST",
                            "Underground Storage Tank (UST) Details")
                    },
                    {
                        "AstDetails",
                        FlexHtmlModel.GenerateTankDetailsTable(facilityWithTanksTable, "AST",
                            "Aboveground Storage Tank (AST) Details")
                    },
                    { "ProjectedRevenueTableData", projectRevenueTable },
                    { "ProjectedRevenueTableTotal", projectTableTotal },
                    { "RevenueForCob", revenueForCob },
                    { "PayrollForCob", payrollForCob },
                    { "RetroactiveDate", retroactiveDateAsString },
                    { "PriorYearRevenues", priorYearTotalRevenue },
                    { "ProfServicesDescription", profServicesDescription },
                    { "WorkCategorization", workCategorization },
                    { "MinimumEarned", mepStringText },
                    { "ContractNumber", projectContractNumber },
                    { "ProjectOwner", projectOwner },
                    { "ProjectName", projectName },
                    { "ProjectTermLength", policy.UcpmRenewableStatusId == (int)UcpmRenewableStatusEnum.NonRenewable ? projectTermLength : "N/A"},
                    { "ProjectDescription", projectDescription },
                    { "ProjectLocations", completeLocation },
                    { "ExposureBasis", expBasis },
                    {
                        "AddtlInsuredsOwnershipTable",
                        FlexHtmlModel.GenerateAddtlInsuredsOwnershipTable(container.FlexAnswers)
                    },
                    { "AddtlInsuredsTable", FlexHtmlModel.GenerateAddtlInsuredsTable(container.FlexAnswers) },
                    { "CoveredLocationsTable", FlexHtmlModel.GenerateCoveredLocationTable(container.FlexAnswers) },
                    { "CurrentLocationsTable", FlexHtmlModel.GenerateCurrentLocationsTable(container.FlexAnswers) },
                    { "TanksGridTable", FlexHtmlModel.GenerateTanksGridTable(container.FlexAnswers) },
                    { "ProximityCheckbox", FlexHtmlModel.GenerateProximityCheckbox(container.FlexAnswers) },
                    { "TankCapacity", FlexHtmlModel.GenerateTankCapacityCheckbox(container.FlexAnswers) },
                    { "EffectiveDateOfCoverage", FlexHtmlModel.GenerateEffectiveDate(container.FlexAnswers)},
                    { "IsProjectSpecific", policy.UcpmRenewableStatusId == (int)UcpmRenewableStatusEnum.NonRenewable ? "Project Specific" : "Annual"},
                    { "RetroYesNo", retroYesNo ? "Yes" : "No" },
                    { "SelectedCob", selectedCobValue },
                    { "NxusCoverageRetroDateTable", FlexHtmlModel.GenerateNxusCoverageRetroDateTable(retroactiveDates) },
                    { "InsuredSuiteDisplayProp", insuredSuiteDisplayProp },
                    { "RevenueForCobDisplayProp", revenueForCobDisplayProp },
                    { "ProjectRevenuesDisplayProp", projectRevenuesDisplayProp },
                    { "PayrollForCobDisplayProp", payrollForCobDisplayProp },
                    { "ProjectRevenues", projectRevenues },
                    { "ProjectAddress", projectAddress1 },
                    { "ProjectSuite", projectAddress2 },
                    { "ProjectCity", projectCity },
                    { "ProjectState", projectState },
                    { "ProjectZip", projectZip },
                    { "ProjectContact", projectContact },
                    { "CalculateNavigatorsSitePollutionLimit", calculateNavigatorsSitePollutionLimit },
                    { "CalculateNavigatorsSitePollutionDeductible", calculateNavigatorsSitePollutionDeductible },
                };

                Dictionary<string, string> cobPlaceholderMap = GetCobPlaceholderMap();
                Dictionary<string, decimal> cobRevenueMap = BuildCobRevenueMap(cobRevenues);
                foreach ((string cobName, string placeholderKey) in cobPlaceholderMap)
                {
                    string cob = ReplaceSpecialCharacters(cobName, string.Empty);
                    string fullPlaceholder = $"Flex.{placeholderKey}";

                    decimal cobRevenue = cobRevenueMap.TryGetValue(cob, out decimal value) ? value : 0;

                    replaceActions[fullPlaceholder] = cobRevenue.ToString("C0");
                }

                Dictionary<string, string> classPlaceholderMap = GetClassificationPlaceholderMap();
                Dictionary<string, decimal> classificationsMap = BuildClassificationsMap(revenueClassifications);
                foreach ((string classificationName, string placeholderKey) in classPlaceholderMap)
                {
                    string classification = ReplaceSpecialCharacters(classificationName, string.Empty);
                    string fullPlaceholder = $"Flex.{placeholderKey}";

                    decimal revenuePercentage = classificationsMap.TryGetValue(classification, out decimal value) ? value : 0;

                    replaceActions[fullPlaceholder] = revenuePercentage.ToString("0");
                }

                string issuingCompanyName = "N/A";
                if (submission.IssuingCompanyGuid != Guid.Empty)
                {
                    IssuingCompanyEntity issuingCompany = new(submission.IssuingCompanyGuid);
                    quickAdapter.FetchEntity(issuingCompany);
                    issuingCompanyName = issuingCompany.IssuingName;

                }

                replaceActions["IssuingCompany"] = issuingCompanyName;

                CarrierSubmissionOptionEntity firstOption = submission.CarrierSubmissionOption.FirstOrDefault(o => !o.IsDeleted);
                if (firstOption != null)
                {
                    replaceActions["UcpmRenewableStatus"] = firstOption?.ProposedUcpmRenewableStatusId == 1 ? "Practice" : "Project";
                    replaceActions["LimitOccurrence"] = firstOption.LimitOccurrence.ToString("C0");
                    replaceActions["LimitAggregate"] = firstOption.LimitAggregate.ToString("C0");
                    replaceActions["Deductible"] = firstOption.Deductible.ToString("C0");

                    InvoiceEntity invoice = firstOption.Invoice?.FirstOrDefault(i => !i.IsDeleted);
                    if (invoice?.InvoiceItem?.Any() == true)
                    {
                        InvoiceItemEntity premiumOnly = invoice.InvoiceItem
                            .FirstOrDefault(i =>
                                !i.IsDeleted &&
                                i.Description != null &&
                                i.Description.Trim().Contains("Premium", StringComparison.OrdinalIgnoreCase));

                        if (premiumOnly != null)
                        {
                            replaceActions["Premium"] = premiumOnly.GrossAmount.ToString("C2");
                        }
                    }

                    if (!replaceActions.ContainsKey("Premium"))
                    {
                        replaceActions["Premium"] = "$0.00";
                    }
                }

                if (flexDefinitionEnum == FlexDefinitionEnum.Apl)
                {
                    Dictionary<string, string> toAdd =
                        await AplSpecifics(csoEntity, newRatingContainer, container);
                    foreach (KeyValuePair<string, string> item in toAdd)
                    {
                        if (replaceActions.ContainsKey(item.Key))
                        {
                            replaceActions[item.Key] = item.Value;
                        }
                        else

                        {
                            replaceActions.Add(item.Key, item.Value);
                        }
                    }
                }

                if (!string.IsNullOrWhiteSpace(selectedCobKey))
                {
                    replaceActions.Add(selectedCobKey, revenueForCob);
                }

                foreach (FlexQuestion item in flexQuestionContainer.FlexSurvey.FlexQuestions)
                {
                    if (!replaceActions.ContainsKey(item.DataName))
                    {
                        FlexAnswer matchingAnswer =
                            flexAnswers.FirstOrDefault(s => s.QuestionDataName == item.DataName);
                        if (matchingAnswer != null)
                        {
                            if (item.DataType == FlexDataTypeEnum.Boolean)
                            {
                                if (matchingAnswer.Answer != null && matchingAnswer.Answer.ToLower() == "true")
                                {
                                    replaceActions.Add(item.DataName,
                                        "<input type=\"checkbox\" checked><label>Yes</label><input type=\"checkbox\"><label> No</label>");
                                }
                                else

                                {
                                    replaceActions.Add(item.DataName,
                                        "<input type=\"checkbox\"><label>Yes</label><input type=\"checkbox\" checked><label> No</label>");
                                }
                            }
                            else

                            {
                                replaceActions.Add(item.DataName, matchingAnswer.Answer);
                            }
                        }
                        else

                        {
                            if (item.DataType == FlexDataTypeEnum.Boolean)
                            {
                                replaceActions.Add(item.DataName,
                                    "<input type=\"checkbox\"><label>Yes</label><input type=\"checkbox\" checked><label> No</label>");
                            }
                            else

                            {
                                replaceActions.Add(item.DataName, "");
                            }
                        }
                    }
                }

                replaceActions = AddProjectDeliveryMethodPercentages(replaceActions,
                    FlexHtmlModel.GetAllAnswersByDataName(container.FlexAnswers, "ProjectDeliveryMethodDetails")
                        .FirstOrDefault());
                TransformFlexTemplate(quoteTemplate, replaceActions);
                TransformFlexTemplate(appTemplate, replaceActions);
                TransformFlexTemplate(warrantyStatementTemplate, replaceActions);
            }

            return (quoteTemplate, appTemplate, warrantyStatementTemplate);
        }

        public static Dictionary<string, string> GetClassificationPlaceholderMap()
        {
            return new Dictionary<string, string>
            {
                { "Commercial/Retail", "Com_Percent" },
                { "Industrial", "Ind_Percent" },
                { "Single-Family Residential", "Sin_Percent" },
                { "Government", "Gov_Percent" },
                { "Manufacturing", "Man_Percent" },
                { "Multi-Family Residential", "Mul_Percent" },
                { "Hospitals", "Hos_Percent" },
                { "Schools", "Sch_Percent" },
                { "Other", "Oth_Percent" }
            };
        }

        public static Dictionary<string, string> GetCobPlaceholderMap()
        {
            return new Dictionary<string, string>
            {
                { "Appliance Installation", "AI_Revenue" },
                { "Asbestos / Lead Abatement", "AL_Revenue" },
                { "Barrier / Liner Construction", "BC_Revenue" },
                { "Carpentry / Framing", "CF_Revenue" },
                { "Carpet / Upholstery Cleaning", "CFCI_Revenue" },
                { "Concrete / Masonry", "CO_Revenue" },
                { "Construction Management", "CM_Revenue" },
                { "Crime Scene Cleanup", "CSI_Revenue" },
                { "Demolition - Non-Structural", "DNS_Revenue" },
                { "Demolition - Structural (over 3 stories)", "DS3S_Revenue" },
                { "Demolition - Structural (under 3 stories)", "DS3U_Revenue" },
                { "Drilling - Non-Environmental", "DI_Revenue" },
                { "Drilling - Petroleum Based", "DRO_Revenue" },
                { "Drywall", "DW_Revenue" },
                { "Electrical", "EL_Revenue" },
                { "Excavation / Grading", "EG_Revenue" },
                { "Exterior Insulation (EIFS)", "EIFS_Revenue" },
                { "Fire Suppression / Sprinklers", "FSS_Revenue" },
                { "Flooring", "FL_Revenue" },
                { "General Contracting", "GC_Revenue" },
                { "Glazier / Glass / Window", "GL_Revenue" },
                { "HazMat Clean-Up", "HZ_Revenue" },
                { "Home Building", "HB_Revenue" },
                { "Home Heating Oil/Gas Services", "Home_oil_Revenue" },
                { "HVAC / Mechanical / Refrigeration", "HV_Revenue" },
                { "Industrial Cleaning", "IC_Revenue" },
                { "Insulation", "IN_Revenue" },
                { "Landscaping", "LA_Revenue" },
                { "Maintenance / Janitorial", "MA_Revenue" },
                { "Mold Abatement", "MD_Revenue" },
                { "Painting", "PA_Revenue" },
                { "Paving / Street / Road", "PV_Revenue" },
                { "PCB Removal / Remediation", "PCB_Revenue" },
                { "Pesticide, Herbicide and Fertilizer (no aerial)", "PE_Revenue" },
                { "Pipeline Construction / Repair - Non Oil/Gas", "PI_Revenue" },
                { "Pipeline Construction / Repair - Oil/Gas", "Pipe_oil_Revenue" },
                { "Plastering / Stucco", "PL_Revenue" },
                { "Plumbing", "PU_Revenue" },
                { "Restoration - Build Back", "RBB_Revenue" },
                { "Restoration - Fire / Water", "RS_Revenue" },
                { "Roofing", "RO_Revenue" },
                { "Sandblasting", "SB_Revenue" },
                { "Sewer / Water Main", "SE_Revenue" },
                { "Soil Remediation", "SO_Revenue" },
                { "Steel / Metal Erection", "SERS_Revenue" },
                { "Storage Tank Installation / Removal", "STIR_Revenue" },
                { "Swimming Pool Services", "SWPS_Revenue" },
                { "Tank Cleaning", "TC_Revenue" },
                { "Trucking - Hazardous", "trk_haz_Revenue" },
                { "Trucking - Non-Hazardous", "NHT_Revenue" },
                { "Trucking - Petroleum Based", "TPB_Revenue" },
                { "Utilities", "UT_Revenue" },
                { "Waste Hauling", "WH_Revenue" },
                { "Waterproofing", "WA_Revenue" },
                { "Weatherization", "WE_Revenue" },
                { "Other Services (Please Describe Below)", "Other_Revenue" }
            };
        }

        private static Dictionary<string, decimal> BuildClassificationsMap(List<FlexAnswer> classifications)
        {
            Dictionary<string, decimal> classificationsMap = [];

            string currentClassification = string.Empty;
            foreach (IGrouping<int, FlexAnswer> answer in classifications.GroupBy(c => c.RowNumber))
            {
                FlexAnswer classificationAnswers = answer.FirstOrDefault(f => f.QuestionDataName == "Classifications");
                if (classificationAnswers != null)
                {
                    currentClassification = ReplaceSpecialCharacters(classificationAnswers.Answer, string.Empty);
                    if (!string.IsNullOrWhiteSpace(currentClassification) && !classificationsMap.ContainsKey(currentClassification))
                    {
                        classificationsMap[currentClassification] = 0;
                    }
                    FlexAnswer percentageAnswer = answer.FirstOrDefault(f => f.QuestionDataName == "ClassificationPercentage");
                    if (percentageAnswer != null && currentClassification != null)
                    {
                        string percentage = percentageAnswer.Answer ?? "0";
                        if (Parse.IsPercentage(percentage))
                        {
                            decimal parsedPercentage = Parse.ParseStringPercentage(percentage);
                            if (classificationsMap.ContainsKey(currentClassification))
                            {
                                classificationsMap[currentClassification] += parsedPercentage;
                            }
                        }
                    }
                }
            }

            return classificationsMap;
        }

        private static Dictionary<string, decimal> BuildCobRevenueMap(List<FlexAnswer> cobRevenues)
        {
            Dictionary<string, decimal> cobRevenueMap = [];

            string currentCob = string.Empty;
            foreach (FlexAnswer answer in cobRevenues)
            {
                if (answer.QuestionDataName == "SelectedCob")
                {
                    currentCob = ReplaceSpecialCharacters(answer.Answer, string.Empty);
                    if (!string.IsNullOrWhiteSpace(currentCob) && !cobRevenueMap.ContainsKey(currentCob))
                    {
                        cobRevenueMap[currentCob] = 0;
                    }
                }
                else if (answer.QuestionDataName == "RevenueForCob" && currentCob != null)
                {
                    string revenueAnswer = answer.Answer ?? "0";
                    if (Parse.IsCurrency(revenueAnswer))
                    {
                        decimal revenue = Parse.CurrencyDecimal(revenueAnswer);
                        cobRevenueMap[currentCob] += revenue;
                    }
                }
            }

            return cobRevenueMap;
        }

        private static Dictionary<string, string> AddProjectDeliveryMethodPercentages(
            Dictionary<string, string> actions, FlexAnswer projectDeliveryAnswer)
        {
            if (projectDeliveryAnswer != null && projectDeliveryAnswer.ChildAnswers.Count > 0)
            {
                List<FlexAnswer> childs = projectDeliveryAnswer.ChildAnswers;
                List<string> answersToCheck =
                [
                    "Construction with No Design",
                    "Design for 3rd Parties Only",
                    "Construction Management - Agency",
                    "Construction Management - At Risk",
                    "Design/Build with In-House Design",
                    "Design/Build with Subcontracted Design",
                    "Consulting and Inspections",
                ];

                for (int i = 0; i < answersToCheck.Count; i++)
                {
                    FlexAnswer answer = childs.FirstOrDefault(x => x.Answer == answersToCheck[i]);
                    int address = i + 1;
                    if (answer != null)
                    {
                        string percent = childs
                            .FirstOrDefault(x =>
                                x.RowNumber == answer.RowNumber && x.QuestionDataName == "MethodPercent")?.Answer
                            .Replace("%", "");
                        decimal parsed = decimal.Parse(percent) / 100;
                        actions.Add($"PercentRevenue{address}", parsed.ToString("p0"));
                    }
                    else

                    {
                        actions.Add($"PercentRevenue{address}", "0%");
                    }
                }
            }

            return actions;
        }

        private static async Task AddNewSubmissionSubjectivity(StateRequirementEntity requirement,
            CarrierSubmissionEntity submission, QuickAdapterAsync quickAdapter)
        {
            CarrierSubmissionSubjectivityEntity subjectivityEntity = new()
            {
                CarrierSubmissionGuid = submission.CarrierSubmissionGuid,
                SubjectivityStatusId = 0,
                RecordCreatedZoned = DateTimeOffset.Now,
                CreatedByEmployeeGuid = Guid.Empty,
                CarrierSubmissionSubjectivityGuid = Guid.NewGuid(),
                SubjectivityTextOverride = requirement.RequirementText,
                SubjectivityLinkOverride = requirement.RequirementUrl﻿
            };
            await quickAdapter.SaveEntityAsync(subjectivityEntity);
        }

        public static async Task AddCreditRisk(CarrierSubmissionEntity submission, QuickAdapterAsync quickAdapter)
        {
            CarrierSubmissionSubjectivityEntity subjectivityEntity = new()
            {
                CarrierSubmissionGuid = submission.CarrierSubmissionGuid,
                SubjectivityStatusId = 0,
                RecordCreatedZoned = DateTimeOffset.Now,
                CreatedByEmployeeGuid = Guid.Empty,
                CarrierSubmissionSubjectivityGuid = Guid.NewGuid(),
                SubjectivityTextOverride = "Payment must be received prior to coverage being bound",
                SubjectivityLinkOverride = ""
            };
            await quickAdapter.SaveEntityAsync(subjectivityEntity);
        }

        private static async Task AddNewSubmissionSubjectivity(FlexSubjectivity item,
            CarrierSubmissionEntity submission, QuickAdapterAsync quickAdapter)
        {
            CarrierSubmissionSubjectivityEntity subjectivityEntity = new()
            {
                CarrierSubmissionGuid = submission.CarrierSubmissionGuid,
                SubjectivityStatusId = item.AutoComplete
                    ? (int)SubjectivityStatusEnum.Received
                    : (int)SubjectivityStatusEnum.NotReceived,
                RecordCreatedZoned = DateTimeOffset.Now,
                CreatedByEmployeeGuid = Guid.Empty,
                CarrierSubmissionSubjectivityGuid = Guid.NewGuid(),
                SubjectivityTextOverride = item.Subjectivity,
                SubjectivityLinkOverride = item.Link,
                IsRequired = item.Required,
                RequiresSignature = item.RequiresSignature﻿
            };
            await quickAdapter.SaveEntityAsync(subjectivityEntity);
        }

        private static async Task<CarrierSubmissionEntity> CreateCarrierSubmissionEntity(PolicyProspectEntity policy,
            Guid carrierGuid, WholesalerEntity wholesaler, CarrierMaximumOfferEntity offer, string taxState,
            bool autoGenerateQuotes, int nxusTypeId, int policyStatusId = -1)
        {
            CarrierSubmissionEntity submission = new()
            {
                CarrierSubmissionGuid = Guid.NewGuid(),
                PolicyProspectGuid = policy.PolicyProspectGuid,
                BoundMethodId = 1,
                PolicyStatusId = (int)PolicyStatusEnum.QuotedRcvd,
                CarrierRenewalStatusId = policy.RenewalOfPolicyGuid == Guid.Empty
                    ? (int)CarrierRenewalStatusEnum.NewToCarrierNewPolicy
                    : (int)CarrierRenewalStatusEnum.Unknown,
                SurplusLinesTaxState = taxState,
                CommGross = wholesaler.DefaultCommGross,

                SurplusLinesStatusId = (int)SurplusLinesStatusEnum.Unknown,
                NxusTypeId = nxusTypeId,
                CarrierGuid = carrierGuid﻿
            };
            if (offer.IssuingCompanyGuid != Guid.Empty)
            {
                submission.IssuingCompanyGuid = offer.IssuingCompanyGuid;
            }

            submission.PolicyStatusId = policyStatusId > 0 ? policyStatusId : (int)PolicyStatusEnum.QuotedRcvd;

            PolicyProspectEntity oldPolicy =
                FlexEntityFetcherModel.GetPolicyAndSubmissionCarriers(policy.RenewalOfPolicyGuid);
            CarrierSubmissionEntity preferredSubmission = oldPolicy?.FinancialSummaryPolicy?.CarrierSubmission ?? null;
            EntityCollection<CarrierBillingOfficeEntity> offices =
                await FlexEntityFetcherModel.GetOfficeByCarrierAndIssuingCompany(carrierGuid, offer.IssuingCompanyGuid);
            if (offices.Any())
            {
                CarrierBillingOfficeEntity onlineOffice = offices.FirstOrDefault(o => o.BillingOfficeName.Contains("Online", StringComparison.OrdinalIgnoreCase));
                submission.CarrierBillingOfficeGuid = onlineOffice?.CarrierBillingOfficeGuid ?? offices.First().CarrierBillingOfficeGuid;
            }

            submission.CarrierRenewalStatusId = GetByPolicy(policy, carrierGuid, preferredSubmission);
            submission.ClearedStatusId = 2;
            submission.CommGross = 0m;
            submission.CommUnderwriter =
                CalculateDefaultUnderwriterCommission(submission, offer.CarrierProgram.ProgramGuid);

            if (submission.CarrierGuid == CarrierList.Lloyds)
            {
                FlexResponseSaveStateEntity latestSaveState =
                    await FlexEntityFetcherModel.GetLatestByResponseAsyncSimple(policy.FlexResponseGuid);
                FlexAnswerContainer answers = FlexAnswerContainer.FromJson(latestSaveState.SavedData);
                bool isProjectSpecific = FindBoolByQuestion("IsProjectSpecific", answers.FlexAnswers);

                if (!isProjectSpecific)
                {
                    policy.UcpmRenewableStatusId = (int)UcpmRenewableStatusEnum.Renewable;
                    await _quickAdapterAsync.SaveEntityAsync(policy, true);
                }
                submission.PolicyStatusId = (int)PolicyStatusEnum.QuotedRcvd;
                submission.SelectedUnderwriterGuid = Guid.Parse("26BE13AC-6CC5-4C02-ABBF-C795341B20E1");
                submission.SurplusLinesStatusId = (int)SurplusLinesStatusEnum.PendingUcpm;
            }

            if (autoGenerateQuotes)
            {
                await _quickAdapterAsync.SaveEntityAsync(submission, true);
            }

            if (submission.CarrierGuid == CarrierList.UnknownCarrierGuid)
            {
                CarrierSubmissionOptionEntity submissionOptionEntity = new()
                {
                    CarrierSubmissionGuid = submission.CarrierSubmissionGuid,
                    QuotedPremium = 0,
                    CarrierFee = 0,
                    ClaimsHandlingId = offer.CarrierProgram.DefaultClaimsHandlingId,
                    ExpirationDateZoned = DateTime.MinValue,
                    Tria = 0,
                    IsFlatRated = offer.IsDefaultFlatRate,
                    HasAudit = false,
                    RetroDateOptionId = 1,
                    RetroDateZoned = DateTime.MinValue,
                    CarrierSubmissionOptionGuid = Guid.NewGuid(),
                    LimitAggregate = 0,
                    LimitOccurrence = 0,
                    Deductible = 0,
                    CommToAgency = 0,
                    ExposureBasis = 0
                };
                if (autoGenerateQuotes && submissionOptionEntity != null)
                {
                    await _quickAdapterAsync.SaveEntityAsync(submissionOptionEntity, true);
                }

                InvoiceEntity invoiceEntity = new()
                {
                    InvoiceGuid = Guid.NewGuid(),
                    IsDeleted = false,
                    IsInvoiced = false,
                    IsVoided = false,
                    QuoteSortPriority = 0,
                    IsTaxExempt = false,
                    RecordCreatedZoned = DateTimeOffset.Now,
                    CarrierSubmissionOptionGuid = submissionOptionEntity.CarrierSubmissionOptionGuid,
                    PaymentEmailSent = false,
                    InvoiceTypeId = 1,
                    UsedMailingAddress = false,
                    NoDcat = true,
                    ExcludeContingency = false,
                    ZeroCommission = false,
                    PayableByInsured = false,
                };

                if (autoGenerateQuotes && invoiceEntity != null)
                {
                    await _quickAdapterAsync.SaveEntityAsync(invoiceEntity, true);
                }
            }

            return submission;
        }

        private static string ReplaceSpecialCharacters(string str, string valueToReplace)
        {
            string[] specialChars = ["-", " ", "/", ","];
            string newString = str;
            foreach (string specialChar in specialChars)
            {
                newString = newString.Replace(specialChar, valueToReplace);
            }

            return newString;
        }

        private static decimal CalculateDefaultGrossCommission(WholesalerEntity wholesaler, PolicyProspectEntity policy,
            CarrierMaximumOfferEntity offer)
        {
            int commissionComponentId = policy.UcpmRenewalStatusId == 1 || !(offer?.UseRenewalSplit ?? false)
                ? 0 /* Use New Business if we are new business or we don't use renewal split*/
                : 1 /* Use Renewal rate*/;
            CarrierMaximumOfferCommissionEntity commission = offer﻿
                ?.CarrierMaximumOfferCommission
                .SingleOrDefault(cmoc => cmoc.CarrierCommissionComponentId == commissionComponentId);

            return commission != null ? commission.DefaultCommission : wholesaler.DefaultCommGross;
        }

        private static decimal CalculateDefaultUnderwriterCommission(CarrierSubmissionEntity submission,
            Guid programGuid)
        {
            CarrierProgramEntity carrierProgram =
                FlexEntityFetcherModel.GetCarrierProgramByGuids(submission.CarrierGuid, programGuid);

            return !carrierProgram.IsNew ? carrierProgram.DefaultCommUnderwriter : 0.00m;
        }

        private static int GetByPolicy(PolicyProspectEntity policy, Guid carrierGuid,
            CarrierSubmissionEntity preferredSubmission)
        {
            if (policy.RenewalOfPolicyGuid == Guid.Empty)
            {
                return (int)CarrierRenewalStatusEnum.NewToCarrierNewPolicy;
            }

            if (preferredSubmission?.CarrierGuid == carrierGuid)
            {
                return (int)CarrierRenewalStatusEnum.RenewalWithCarrier;
            }

            return (int)CarrierRenewalStatusEnum.NewToCarrierExistingPolicy;
        }

        private static (DateTime effectiveDate, DateTime expirationDate) GetEffectiveAndExpirationDateFromAnswers(this List<FlexAnswer> flexAnswers)
        {
            List<string> effectiveDateQuestions = ["EffectiveDate", "ProposedEffectiveDate"];
            List<string> expirationDateQuestions = ["ExpirationDate", "ProposedExpirationDate"];

            string effectiveDateAsString =
                FlexQuoteModel.FindByQuestionList(effectiveDateQuestions, flexAnswers).FirstOrDefault()?.Answer ??
                DateTime.Now.ToShortDateString();

            string expirationDateAsString =
                FlexQuoteModel.FindByQuestionList(expirationDateQuestions, flexAnswers).FirstOrDefault()?.Answer ??
                DateTimeOffset.Parse(effectiveDateAsString).AddYears(1).ToShortDateString();
            DateTime effectiveDate = DateTime.Parse(effectiveDateAsString).Date;
            DateTime expirationDate = DateTime.Parse(expirationDateAsString).Date;

            return (effectiveDate, expirationDate);
        }

        private static async Task<CarrierSubmissionFlexResponseEntity> CreateCarrierSubmissionFlexResponse(
            CarrierSubmissionEntity submission, Guid flexResponseGuid, bool autoGenerateQuotes)
        {
            if (submission == null)
                return null;
            CarrierSubmissionFlexResponseEntity carrierSubmissionFlexResponse = new()
            {
                CarrierSubmissionGuid = submission.CarrierSubmissionGuid,
                FlexResponseGuid = flexResponseGuid,
                IsReferral = false,
                ReferralReaspon = "",
                OverrideReason = "",
            };
            if (autoGenerateQuotes)
            {
                await _quickAdapterAsync.SaveEntityAsync(carrierSubmissionFlexResponse, true);
            }

            return carrierSubmissionFlexResponse;
        }

        private static int? GetPolicyTermDays(CarrierSubmissionOptionEntity submissionOption,
            PolicyProspectEntity policyProspect)
        {
            if (submissionOption == null)
            {
                throw new ArgumentNullException(nameof(submissionOption));
            }

            int? policyTermDays = null;

            if (policyProspect == null)
            {
                return policyTermDays;
            }

            if (policyProspect.EffectiveDateZoned > Parse.SentinelDateZoned)
            {
                DateTimeOffset expirationDate = submissionOption.ExpirationDateZoned;
                if (expirationDate > Parse.SentinelDateZoned)
                {
                    policyTermDays = expirationDate.Subtract(policyProspect.EffectiveDateZoned).Days;
                }
            }

            return policyTermDays;
        }

        private static async Task UpdatePolicyAndSubmissionOptionDates(PolicyProspectEntity policy,
            DateTime effectiveDate,
            DateTime expirationDate,
            int policyTermDays,
            bool autoGenerateQuotes)
        {
            int policyTermLowDays = policyTermDays - 1;
            int policyTermHighDays = policyTermDays + 1;

            bool hasExceedTermDay = policy.CarrierSubmission.Any(submission => submission.CarrierSubmissionOption.Any(x => (GetPolicyTermDays(x, policy) ?? 365) < policyTermLowDays ||
                                                                                                                           (GetPolicyTermDays(x, policy) ?? 365) > policyTermHighDays));

            // Update Policy and Submission Option
            UnitOfWork2 work2 = new();
            if (hasExceedTermDay && autoGenerateQuotes)
            {
                policy.EffectiveDateZoned = effectiveDate;
                work2.AddForSave(policy, true);
                foreach (CarrierSubmissionEntity submission in policy.CarrierSubmission)
                {
                    foreach (CarrierSubmissionOptionEntity submissionOption in submission.CarrierSubmissionOption)
                    {
                        submissionOption.ExpirationDateZoned = expirationDate;
                        work2.AddForSave(submissionOption, true);
                    }

                    await _quickAdapterAsync.CommitWorkAsync(work2);
                }
            }
        }

        private static async Task<CarrierSubmissionOptionEntity> FindOrCreateCarrierSubmissionOption(
            CarrierSubmissionEntity submission, PolicyProspectEntity policy, FlexOption option,
            EntityCollection<LimitsRequestedEntity> limitList,
            EntityCollection<DeductibleRequestedEntity> deductibleList, CarrierMaximumOfferEntity offer,
            bool autoGenerateQuotes, ProgramEntity program, List<FlexAnswer> flexAnswers,
            CommissionCalculationMicroModel commissionCalculationMicroModel, int ucpmRenewableStatusId, decimal commToAgency)
        {

            int policyTermLowDays = option.PolicyTermDays - 1;
            int policyTermHighDays = option.PolicyTermDays + 1;

            CarrierSubmissionOptionEntity submissionOptionEntity = null;
            if (submission != null)
            {
                List<CarrierSubmissionOptionEntity> options = submission.CarrierSubmissionOption
                     .Where(c => c.IsDeleted == false)
                     .ToList();

                List<CarrierSubmissionOptionEntity> matchingAggAndOcc = options
                    .Where(c => c.LimitAggregate == option.LimitAggregate && c.LimitOccurrence == option.LimitOccurrence)
                    .ToList();

                List<CarrierSubmissionOptionEntity> matchingDeductible = matchingAggAndOcc
                    .Where(c => c.Deductible == option.Deductible)
                    .ToList();

                List<CarrierSubmissionOptionEntity> matchingMasterPlan = matchingDeductible
                    .Where(c => c.MaterPlanId == option.MaterPlanId)
                    .ToList();

                List<CarrierSubmissionOptionEntity> matchingNxusType = matchingMasterPlan
                    .Where(c => c.CarrierSubmission?.NxusTypeId == option.NxusTypeId)
                    .ToList();

                List<CarrierSubmissionOptionEntity> matchingPolicyTerm = matchingNxusType
                    .Where(c =>
                    {
                        int termDays = GetPolicyTermDays(c, policy) ?? 365;
                        return termDays >= policyTermLowDays && termDays <= policyTermHighDays;
                    })
                    .ToList();

                // Final selection
                submissionOptionEntity = matchingPolicyTerm.FirstOrDefault();
            }

            (DateTime effectiveDate, DateTime expirationDate) = flexAnswers.GetEffectiveAndExpirationDateFromAnswers();

            commissionCalculationMicroModel.SetFields(submission, effectiveDate, false, program, policy?.Package?.InsuredAgentHistory?.Agent?.Location);
            if (submissionOptionEntity == null)
            {

                List<FlexAnswer> retroAst = FindByQuestion("RetroactiveDateAst", flexAnswers);
                List<FlexAnswer> retroUst = FindByQuestion("RetroActiveDateUST", flexAnswers);

                DateTimeOffset retroDateToUse = Parse.SentinelDateZoned;
                if (retroAst.Any() || retroUst.Any())
                {
                    DateTimeOffset oldestDate = DateTimeOffset.Now;
                    foreach (FlexAnswer retro in retroAst)
                    {
                        if (!string.IsNullOrWhiteSpace(retro.Answer))
                        {
                            DateTimeOffset retroDateParsed = DateTimeOffset.Parse(retro.Answer);
                            if (retroDateParsed < oldestDate)
                            {
                                oldestDate = retroDateParsed;
                            }
                        }
                    }

                    foreach (FlexAnswer retro in retroUst)
                    {
                        if (!string.IsNullOrWhiteSpace(retro.Answer))
                        {
                            DateTimeOffset retroDateParsed = DateTimeOffset.Parse(retro.Answer);
                            if (retroDateParsed < oldestDate)
                            {
                                oldestDate = retroDateParsed;
                            }
                        }
                    }

                    retroDateToUse = oldestDate;
                }


                if (submission != null)
                {
                    submissionOptionEntity = new CarrierSubmissionOptionEntity﻿
                    {
                        CarrierSubmissionGuid = submission.CarrierSubmissionGuid,
                        QuotedPremium = 0,
                        CarrierFee = 0,
                        ClaimsHandlingId = offer.CarrierProgram.DefaultClaimsHandlingId,
                        ExpirationDateZoned = effectiveDate.AddDays(option.PolicyTermDays),
                        Tria = 0,
                        IsFlatRated = offer.IsDefaultFlatRate,
                        HasAudit = false,
                        RetroDateOptionId = 1,
                        RetroDateZoned = retroDateToUse,
                        CarrierSubmissionOptionGuid = Guid.NewGuid(),
                        LimitAggregate = option.LimitAggregate,
                        LimitOccurrence = option.LimitOccurrence,
                        Deductible = option.Deductible,
                        CommToAgency = commToAgency,
                        ExposureBasis = option.ExposureBasis,
                        ProposedUcpmRenewableStatusId = ucpmRenewableStatusId,
                    };
                    if (option.MaterPlanId != 0)
                    {
                        submissionOptionEntity.MaterPlanId = option.MaterPlanId;
                    }

                    if (autoGenerateQuotes)
                    {
                        await _quickAdapterAsync.SaveEntityAsync(submissionOptionEntity, true);
                    }
                }
            }

            return submissionOptionEntity;
        }

        private static async Task UpdateCarrierSubmissionOption(CarrierSubmissionOptionEntity submissionOptionEntity,
            CarrierMaximumOfferEntity offer, bool autoGenerateQuote, decimal exposureBasis,
            DateTimeOffset retroDateZoned, DateTimeOffset expirationDate, decimal commToAgency)
        {
            if (submissionOptionEntity != null)
            {
                submissionOptionEntity.CommToAgency = commToAgency;
                submissionOptionEntity.ExposureBasis = exposureBasis;
                submissionOptionEntity.ClassOfBusinessGuid = Guid.Parse("F14BBFCA-961E-47A6-99F3-9BAF6B8FC114");
                submissionOptionEntity.IsFlatRated = offer.IsDefaultFlatRate;
                submissionOptionEntity.RetroDateZoned = retroDateZoned;
                submissionOptionEntity.IsDeleted = false;
                submissionOptionEntity.ExpirationDateZoned = expirationDate;
                if (autoGenerateQuote)
                {
                    await _quickAdapterAsync.SaveEntityAsync(submissionOptionEntity, true, false);
                }
            }
        }

        private static InvoiceEntity FindMatchingInvoice(CarrierSubmissionOptionEntity submissionOptionEntity,
            FlexOption option, EntityCollection<DeductibleRequestedEntity> deductibleList,
            EntityCollection<LimitsRequestedEntity> limitList)
        {
            decimal occLimit = limitList.Single(l => l.LimitsRequestedGuid == option.OccLimitsRequstedGuid)
                .LimitsRequestedValue;
            decimal aggLimit = limitList.Single(l => l.LimitsRequestedGuid == option.AggLimitsRequestedGuid)
                .LimitsRequestedValue;

            decimal deductibleEntity = deductibleList
                .Single(l => l.DeductibleRequestedGuid == option.DeductibleRequestedGuid).DeductibleRequestedValue;
            if (submissionOptionEntity != null)
            {
                InvoiceEntity matchingInvoice = submissionOptionEntity.Invoice.FirstOrDefault(i =>
                    !i.IsVoided &&
                    !i.IsInvoiced &&
                    i.CarrierSubmissionOption.Deductible == deductibleEntity﻿
                    && i.CarrierSubmissionOption.LimitAggregate == aggLimit﻿
                    && i.CarrierSubmissionOption.LimitOccurrence == occLimit﻿
                    && i.CarrierSubmissionOption.CarrierSubmission?.NxusTypeId == option.NxusTypeId);

                if (matchingInvoice != null)
                {
                    matchingInvoice.IsDeleted = false;
                    return matchingInvoice;
                }
                else
                {
                    return null;
                }
            }

            return null;
        }

        private static async Task ProcessInvoice(FlexOption option,
             PolicyProspectEntity policy,
             InvoiceEntity matchingInvoice,
             CarrierSubmissionOptionEntity submissionOptionEntity,
             bool autoGenerateQuotes,
             CarrierMaximumOfferEntity carrierMaximumOffer,
             AgentEntity agent,
             ProgramEntity program,
             InvoiceModel invoiceModel,
             CarrierSubmissionEntity submission,
             bool payableByInsured,
             DateTimeOffset effectiveDate)
        {
            VirtualInvoiceModelParams virtualParms = new()
            {
                optionTotal = option.OptionPremium,
                ucpmFee = 0,
                ucpmSlFee = 0,
                slState = submission?.SurplusLinesTaxState ?? "AZ",
                isRenewal = (policy?.RenewalOfPolicyGuid ?? Guid.Empty) != Guid.Empty,
                effectiveDate = effectiveDate,
                policy = policy,
                agent = agent,
                location = agent?.Location,
                company = agent?.Location?.Company,
                policyTermId = option?.PolicyTermDays ?? 1,
                CarrierSubmissionOption = submissionOptionEntity,
                surplusLineStatusId = (int)SurplusLinesStatusEnum.PendingUcpm,
                Program = program,
                CarrierMaximumOffer = carrierMaximumOffer
            };
            await invoiceModel.CreateVirtualInvoice(virtualParms);

            option.InvoiceTotal = invoiceModel.Invoice.InvoiceItem.Sum(i => i.GrossAmount);
            option.FeeTotal = invoiceModel.Invoice.InvoiceItem
                .Where(i => i.TransactionCompanyGuid == Guid.Parse("C5F86BD1-D6E3-4C22-AAF7-41B84D88CDA3"))//Nathan Company = UCPM, Inc.
                .Sum(c => c.GrossAmount);
            option.TaxTotal = option.InvoiceTotal - option.FeeTotal - option.OptionPremium;
            invoiceModel.Invoice.QuoteExpireDateZoned = DateTimeOffset.Now.AddDays(30);
            invoiceModel.Invoice.DueDate = effectiveDate.AddDays(15).Date;
            invoiceModel.Invoice.PayableByInsured = payableByInsured;

            if (matchingInvoice == null)
            {
                await CreateNewInvoice(autoGenerateQuotes, invoiceModel);
            }
            else

            {
                await UpdateInvoiceValues(autoGenerateQuotes, matchingInvoice, invoiceModel);
            }
        }

        private static async Task UpdateInvoiceValues(bool autoGenerateQuotes, InvoiceEntity matchingInvoice,
            InvoiceModel invoiceModel)
        {
            EntityCollection<InvoiceItemEntity> addItems = [];
            foreach (InvoiceItemEntity item in invoiceModel.Invoice.InvoiceItem)
            {
                if (matchingInvoice.InvoiceItem.Any())
                {
                    InvoiceItemEntity matchingInvoiceItem = matchingInvoice.InvoiceItem.FirstOrDefault(
                        trans => trans.TransactionTypeId == item.TransactionTypeId &&
                                 trans.TransactionCompanyGuid == item.TransactionCompanyGuid);

                    if (matchingInvoiceItem == null)
                    {
                        addItems.Add(item);
                    }
                }
            }

            await UpdateInvoiceItems(matchingInvoice, invoiceModel, autoGenerateQuotes);

            foreach (InvoiceItemEntity item in addItems)
            {
                matchingInvoice.InvoiceItem.Add(item);
            }
        }

        private static async Task UpdateInvoiceItems(InvoiceEntity invoice, InvoiceModel virtualInvoice, bool autoGenerateQuotes)
        {
            Dictionary<Guid, decimal> invoiceItemsUpdated = [];
            foreach (InvoiceItemEntity item in invoice.InvoiceItem)
            {
                InvoiceItemEntity matchingInvoiceItem = virtualInvoice.Invoice.InvoiceItem.SingleOrDefault(
                    trans => trans.TransactionTypeId == item.TransactionTypeId &&
                             trans.TransactionCompanyGuid == item.TransactionCompanyGuid);
                decimal newItemAmount;
                if (matchingInvoiceItem != null)
                {
                    newItemAmount = matchingInvoiceItem.GrossAmount;
                    if (Math.Abs(item.GrossAmount - newItemAmount) >= 0.01m)
                    {
                        item.GrossAmount = newItemAmount;
                        invoiceItemsUpdated.Add(item.InvoiceItemGuid, matchingInvoiceItem.GrossAmount);
                    }

                }
                else
                {
                    if (autoGenerateQuotes)
                    {
                        await _quickAdapterAsync.DeleteEntityAsync(item);
                    }

                }
            }

            if (autoGenerateQuotes)
            {
                EntityCollection<InvoiceItemEntity> newItems = [];
                RelationPredicateBucket filter = new();
                List<Guid> updatedInvoiceItemGuids = invoiceItemsUpdated.Keys.ToList();
                filter.PredicateExpression.Add(InvoiceItemFields.InvoiceGuid == invoice.InvoiceGuid);
                filter.PredicateExpression.AddWithAnd(InvoiceItemFields.InvoiceItemGuid == updatedInvoiceItemGuids);
                QueryParameters queryParameters = new()
                {
                    RelationsToUse = filter.Relations,
                    FilterToUse = filter.PredicateExpression,
                    CollectionToFetch = newItems
                };

                await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);

                foreach (InvoiceItemEntity item in newItems)
                {
                    if (invoiceItemsUpdated.ContainsKey(item.InvoiceItemGuid))
                    {
                        decimal newGrossAmount = invoiceItemsUpdated[item.InvoiceItemGuid];
                        item.GrossAmount = newGrossAmount;
                    }
                }
                await _quickAdapterAsync.SaveEntityCollectionAsync(newItems);
            }
        }

        private static async Task CreateNewInvoice(bool autoGenerateQuotes, InvoiceModel invoiceModel)
        {
            if (autoGenerateQuotes)
            {
                await invoiceModel.Save();
            }
        }

        public static async Task<int> SaveSubmissionForms(EntityCollection<SubmissionFormEntity> stays,
            EntityCollection<SubmissionFormEntity> removed)
        {
            List<UnitOfWorkBlockType> order =
            [
                UnitOfWorkBlockType.Deletes,
                UnitOfWorkBlockType.Updates,
                UnitOfWorkBlockType.Inserts﻿
            ];

            UnitOfWork2 work = new(order);
            if (removed != null)
            {
                work.AddCollectionForDelete(removed);
            }

            if (stays != null)
            {
                work.AddCollectionForSave(stays);
            }

            return await _quickAdapterAsync.CommitWorkAsync(work);
        }

        public static async Task<EntityCollection<SubmissionFormEntity>> GetFormsByResponseForSubmission(
            CarrierSubmissionEntity submission, FlexResponseEntity response, Guid offer)
        {
            EntityCollection<SubmissionFormEntity> submissionForms = [];
            EntityCollection<FormEntity> forms = await GetFormsByResponse(response.FlexResponseGuid, offer);
            foreach (FormEntity form in forms)
            {
                SubmissionFormEntity newSubmissionForm = new()
                {
                    FormGuid = form.FormGuid,
                    CarrierSubmissionGuid = submission.CarrierSubmissionGuid,
                    SortOrder = form.PolicyIssuanceOrder﻿
                };
                submissionForms.Add(newSubmissionForm);
            }

            return submissionForms;
        }

        public static async Task<EntityCollection<SubmissionFormEntity>>
            GetFormsByResponseForSubmissionForLegacyPrograms(CarrierSubmissionEntity submission,
                ResponseEntity response, Guid offer)
        {
            EntityCollection<SubmissionFormEntity> submissionForms = [];
            EntityCollection<FormEntity> forms =
                await GetFormsByResponseForLegacyProgram(submission.CarrierGuid, response, offer);
            foreach (FormEntity form in forms)
            {
                SubmissionFormEntity newSubmissionForm = new()
                {
                    FormGuid = form.FormGuid,
                    CarrierSubmissionGuid = submission.CarrierSubmissionGuid,
                    SortOrder = form.PolicyIssuanceOrder﻿
                };
                submissionForms.Add(newSubmissionForm);
            }

            return submissionForms;
        }



        public static async Task<EntityCollection<FormEntity>> GetFormsByResponse(Guid flexResponseGuid, Guid offerGuid)
        {
            CarrierMaximumOfferEntity offer = FlexEntityFetcherModel.GetCarrierGuidFromCarrierMaximumOffer(offerGuid);
            FlexResponseSaveStateEntity latestSaveState =
                await FlexEntityFetcherModel.GetLatestByResponseAsyncForFormAnalysis(flexResponseGuid);

            Guid carrierGuid = offer.CarrierProgram.CarrierGuid;

            Guid programGuid = offer.CarrierProgram.ProgramGuid;
            int variationNumber = offer.VariationNumber;
            Guid carrierMaximumOfferGuid = offer.CarrierMaximumOfferGuid;

            FlexVersionCarrierOfferEntity matchingOffer =
                latestSaveState.FlexVersion.FlexVersionCarrierOffer.Single(s => s.CarrierMaximumOfferGuid == offerGuid);

            IEnumerable<FlexVersionCarrierOfferFormEntity> flexForms =
                latestSaveState.FlexVersion.FlexVersionCarrierOfferForm.Where(f =>
                    f.CarrierMaximumOfferGuid == carrierMaximumOfferGuid);

            IEnumerable<FormEntity> forms = flexForms.Select(f => f.Form);
            forms = forms
                .Where(f => f.CarrierGuid == carrierGuid && f.ProgramGuid == programGuid && f.IsDeleted == false)
                .OrderBy(f => f.PolicyIssuanceOrder);

            EntityCollection<FormEntity> formsToAdd = [];

            string facilityStateCode = await GetStateCodeFromResponse(latestSaveState.FlexResponse);
            string insuredStateCode = GetAnswerByDataName("StateCode", latestSaveState.FlexResponse);
            string projectStateCode = GetAnswerByDataName("ProjLocState", latestSaveState.FlexResponse);
            if (!string.IsNullOrWhiteSpace(projectStateCode))
            {
                projectStateCode = GetAnswerByDataName("ProjecState", latestSaveState.FlexResponse);
            }
            StateQuery stateQuery = new(_quickAdapterReader);

            insuredStateCode = await CheckConvertToStateCode(insuredStateCode, latestSaveState, stateQuery);
            facilityStateCode = await CheckConvertToStateCode(facilityStateCode, latestSaveState, stateQuery);
            projectStateCode = await CheckConvertToStateCode(projectStateCode, latestSaveState, stateQuery);

            foreach (FormEntity form in forms)
            {
                bool includeForm;
                string reason;
                FlexVersionCarrierOfferFormEntity
                    flexForm = flexForms.SingleOrDefault(s => s.FormGuid == form.FormGuid);
                if (flexForm != null)
                {
                    FlexForm binderForm = FlexForm.FromJson(flexForm.RuleJson);
                    if (binderForm == null)
                    {
                        (includeForm, reason) = (false, "No rules found");
                    }
                    else

                    {
                        (includeForm, reason) = CheckToIncludeForm(binderForm, facilityStateCode, insuredStateCode,
                            projectStateCode, latestSaveState.FlexResponse);
                    }

                    if (includeForm)
                    {
                        formsToAdd.Add(form);
                    }
                }
            }

            return formsToAdd;
        }

        private static async Task<string> CheckConvertToStateCode(string stateIdentifier, FlexResponseSaveStateEntity latestSaveState, StateQuery stateQuery)
        {
            string result = stateIdentifier;
            if (string.IsNullOrWhiteSpace(stateIdentifier))
            {
                result = latestSaveState.FlexResponse.FlexResponseLink.Package.InsuredAgentHistory.Insured.State;
                result = result.Length == 2
                    ? result
                    : await FlexEntityFetcherModel.GetCodeFromStateName(result);
            }
            else if (result.Length > 2)
            {
                StateEntity state = await stateQuery.GetStateByName(result);
                result = state.Code;
            }
            return result;
        }

        private static async Task<EntityCollection<FormEntity>> GetFormsByResponseForLegacyProgram(Guid carrierGuid,
            ResponseEntity response, Guid offerGuid)
        {
            CarrierMaximumOfferEntity offer = FlexEntityFetcherModel.GetCarrierGuidFromCarrierMaximumOffer(offerGuid);
            Guid programGuid = offer.CarrierProgram.ProgramGuid;
            int variationNumber = offer.VariationNumber;
            Guid carrierMaximumOfferGuid = offer.CarrierMaximumOfferGuid;
            EntityCollection<BinderFormInclusionEntity> binderForms =
                await BinderFormInclusion.GetBinderFormsByResponse(response);
            IEnumerable<FormEntity> forms = binderForms.Select(b => b.Form).Distinct();
            forms = forms
                .Where(f => f.CarrierGuid == carrierGuid && f.ProgramGuid == programGuid && f.IsDeleted == false)
                .OrderBy(f => f.PolicyIssuanceOrder);


            UnitOfWork2 work = new();
            EntityCollection<FormEntity> formsToAdd = [];

            BinderResultEntity result = new()
            {
                BinderResultGuid = Guid.NewGuid(),
                ResponseGuid = response.ResponseGuid,
                GeneratedZoned = DateTimeOffset.Now,
                CarrierGuid = carrierGuid﻿
            };
            work.AddForSave(result);
            IEnumerable<SurveyDataBooleanEntity> booleanAnswers =
                response.SurveyData.Where(s => s.SurveyDataBoolean != null).Select(s => s.SurveyDataBoolean);
            IEnumerable<string> textAnswers = response.SurveyData.Where(s => s.SurveyDataText != null)
                .Select(s => s.SurveyDataText.Value);
            List<Guid> textAnswersGuids = textAnswers.Where(g => { return Guid.TryParse(g, out Guid value); })
                .Select(g => Guid.Parse(g)).ToList();
            string facilityStateCode = GetStateCodeFromResponse(response);
            string insuredStateCode = response.StateCode;
            foreach (FormEntity form in forms)
            {
                bool includeForm;
                string reason;
                BinderFormInclusionEntity binderForm = binderForms.FirstOrDefault(r => r.FormGuid == form.FormGuid);
                if (binderForm == null)
                {
                    (includeForm, reason) = (false, "No rules found");
                }
                else

                {
                    (includeForm, reason) = CheckToIncludeForm(offer.CarrierMaximumOfferGuid, booleanAnswers,
                        textAnswersGuids, binderForm, facilityStateCode, insuredStateCode);
                }

                BinderResultFormEntity resultForm = new()
                {
                    BinderResultGuid = result.BinderResultGuid,
                    FormGuid = form.FormGuid,
                    WasIncluded = includeForm,
                    Reason = reason
                };
                work.AddForSave(resultForm);

                if (includeForm)
                {
                    formsToAdd.Add(form);
                }
            }

            await _quickAdapterAsync.CommitWorkAsync(work);
            return formsToAdd;
        }

        private static (bool includeForm, string reason) CheckToIncludeForm(FlexForm binderForm,
            string facilityStateCode, string insuredStateCode, string projectStateCode, FlexResponseEntity response)
        {
            if (binderForm.OpCode.ToLower() == "none")
            {
                return (false, "Op code none");
            }

            bool noRules = NoRules(binderForm);
            if (noRules)
            {
                return (true, "No Response Rules, No State Rules, No COB rules, No factor rules");
            }

            List<FlexAnswer> flexAnswerList = ExtractFlexAnswerList(response);

            bool stateCheck = PerformStateCheck(binderForm, facilityStateCode, insuredStateCode, projectStateCode);

            (bool includeForm, string reason) result = binderForm.OpCode.ToLower() == "and"
                ? PerformAndCheck(binderForm, flexAnswerList, stateCheck)
                : PerformOrCheck(binderForm, flexAnswerList, stateCheck);
            if (binderForm.InvertLogic)
            {
                result.includeForm = !result.includeForm;
            }

            return result;
        }

        private static bool NoRules(FlexForm binderForm)
        {
            return binderForm.QuestionRules.Count == 0
                   && binderForm.StateRules.Count == 0
                   && binderForm.CobRules.Count == 0
                   && binderForm.FactorRules.Count == 0;
        }

        private static (bool includeForm, string reason) PerformAndCheck(FlexForm binderForm,
            List<FlexAnswer> flexAnswerList, bool stateCheck)
        {
            bool binderByResponseRuleCheck = CheckResponseRules(binderForm, flexAnswerList, true);
            bool binderFormByCategoryCheck = CheckCategoryRules(binderForm, flexAnswerList);
            bool binderFormByFactorCheck = CheckFactorRules(binderForm, flexAnswerList);

            return (stateCheck && binderByResponseRuleCheck && binderFormByCategoryCheck && binderFormByFactorCheck,
                $"{binderByResponseRuleCheck} (Response Rule) {binderFormByCategoryCheck} (Cob Rule) {binderFormByFactorCheck} (Factor Rule) {stateCheck} State Check");
        }

        private static (bool includeForm, string reason) PerformOrCheck(FlexForm binderForm,
            List<FlexAnswer> flexAnswerList, bool stateCheck)
        {
            if (!binderForm.QuestionRules.Any())
            {
                bool responseRuleCheck = CheckResponseRules(binderForm, flexAnswerList, false);
                if (responseRuleCheck)
                {
                    return (true, "Matching Answers Found for true (Top Level Answer)");
                }
            }

            bool categoryRuleCheck = false;
            if (binderForm.CobRules.Any())
            {
                categoryRuleCheck = CheckCategoryRules(binderForm, flexAnswerList);
            }

            if (categoryRuleCheck)
            {
                return (true, "COB matching");
            }

            bool factorRuleCheck = false;
            if (binderForm.FactorRules.Any())
            {
                factorRuleCheck = CheckFactorRules(binderForm, flexAnswerList);
            }

            if (factorRuleCheck)
            {
                return (true, "Factor Matching");
            }

            if (binderForm.StateRules.Any())
            {
                return (stateCheck, "State Matching");
            }

            return (false, "No matches");
        }

        private static bool CheckResponseRules(FlexForm binderForm, List<FlexAnswer> flexAnswerList, bool andCondition)
        {
            if (andCondition)
            {
                return binderForm.QuestionRules.All(
                    r => flexAnswerList.Any(
                        a => r.Question != null && a.QuestionDataName == r.Question.DataName﻿
                             && a.Answer.ToLower() == "true"));
            }
            else
            {
                IEnumerable<FormRuleQuestion> rulesForThisForm =
                    binderForm.QuestionRules.Where(r =>
                        flexAnswerList.Any(a => a.QuestionDataName == r.Question.DataName));
                return rulesForThisForm.Any(rule =>
                    flexAnswerList.Any(a => a.QuestionDataName == rule.Question.DataName && a.Answer == "true"));
            }
        }

        private static bool CheckCategoryRules(FlexForm binderForm, List<FlexAnswer> flexAnswerList)
        {
            if (!binderForm.CobRules.Any())
            {
                return true;
            }
            else
            {
                foreach (FormRuleClassOfBusiness item in binderForm.CobRules)
                {
                    ClassOfBusinessEntity classOfBusinessEntity =
                        FlexEntityFetcherModel.GetClassOfBusinessByGuid(item.ClassOfBusinessGuid);
                    foreach (FlexAnswer answer in flexAnswerList)
                    {
                        if (answer.Answer == classOfBusinessEntity.ClassOfBusinessName)
                            return true;
                        if (answer.ChildAnswers != null)
                        {
                            foreach (FlexAnswer childAnswer in answer.ChildAnswers)
                            {
                                if (childAnswer.Answer == classOfBusinessEntity.ClassOfBusinessName)
                                    return true;
                            }
                        }
                    }
                }
            }

            return false;
        }

        private static bool CheckFactorRules(FlexForm binderForm, List<FlexAnswer> flexAnswerList)
        {
            return !binderForm.FactorRules.Any() || binderForm.FactorRules.All(
                r => flexAnswerList.Any(
                    a => a.Answer == r.Factor.FactorDisplayName));
        }

        private static bool PerformStateCheck(FlexForm binderForm, string facilityStateCode, string insuredStateCode,
            string projectStateCode)
        {
            bool facilityStateCheck = binderForm.StateRules.Any(s =>
                s.StateCode == facilityStateCode && s.RuleType == FormStateRuleTypeEnum.FacilityQuestion);
            bool insuredStateCheck = binderForm.StateRules.Any(s =>
                s.StateCode == insuredStateCode && s.RuleType == FormStateRuleTypeEnum.InsuredState);
            bool projectStateCheck = binderForm.StateRules.Any(s =>
                s.StateCode == projectStateCode && s.RuleType == FormStateRuleTypeEnum.ProjectState);
            return facilityStateCheck || insuredStateCheck || projectStateCheck || binderForm.StateRules.Count == 0;
        }

        private static List<FlexAnswer> ExtractFlexAnswerList(FlexResponseEntity response)
        {
            FlexAnswerContainer flexAnswerContainer = FlexAnswerContainer.FromJson(response.FlexResponseSaveState
                .OrderByDescending(f => f.SavedAt).First().SavedData);
            return flexAnswerContainer.FlexAnswers;
        }

        private static string GetAnswerByDataName(string dataName, FlexResponseEntity response)
        {
            FlexResponseSaveStateEntity latestSave =
                response.FlexResponseSaveState.OrderByDescending(f => f.SavedAt).First();
            string json = latestSave.SavedData;
            FlexAnswerContainer container = FlexAnswerContainer.FromJson(json);

            List<FlexAnswer> flexAnswers = container.FlexAnswers;
            foreach (FlexAnswer item in flexAnswers)
            {
                if (item.QuestionDataName == dataName)
                {
                    return item.Answer;
                }

                if (item.ChildAnswers != null)
                {
                    foreach (FlexAnswer childAnswer in item.ChildAnswers)
                    {
                        if (childAnswer.QuestionDataName == dataName)
                        {
                            return childAnswer.Answer;
                        }
                    }
                }
            }

            return "";
        }

        private static async Task<string> GetStateCodeFromResponse(FlexResponseEntity response)
        {
            string dataName = GetAnswerByDataName("FacilityState", response);
            return string.IsNullOrWhiteSpace(dataName) ? GetAnswerByDataName("StateCode", response) : dataName;
        }

        private static async Task FindBestRep(Guid programOnlineRepGuid, PackageEntity packageToUse,
            Guid responseProgramGuid, string insuredName)
        {
            if (packageToUse.ProgramGuid == Guid.Empty && responseProgramGuid != Guid.Empty)
            {
                packageToUse.ProgramGuid = responseProgramGuid;
            }

            PackageEntity packageWithPrefetch = FlexEntityFetcherModel.GetForFindARep(packageToUse.PackageGuid);
            LicenseStateEntity license =
                FlexEntityFetcherModel.GetByState(packageWithPrefetch.InsuredAgentHistory.Insured.State);
            RepSuggesterFactorDataCollection collectionData = [];
            RepSuggesterFactorDataCollection factorData =
                await collectionData.GetByProgram(packageToUse.ProgramGuid);
            RepSuggesterViewModelCollection repsToSort = new(packageWithPrefetch, license, factorData, false);
            List<RepSuggesterViewModel> bestRep = await repsToSort.FindBestRep();

            packageToUse.PrimaryRepLoginGuid = bestRep?.FirstOrDefault()?.EmployeeGuid ?? programOnlineRepGuid;
            packageToUse.SecondaryRepLoginGuid = bestRep?.FirstOrDefault()?.EmployeeGuid ?? programOnlineRepGuid;



            MessageEntity messageEntity = new()
            {
                MessageData = $"New NXUS online account {insuredName}"
            };
            await _quickAdapterAsync.SaveEntityAsync(messageEntity, true);

            MessageTargetDirectEntity messageTargetDirectEntity = new()
            {
                AccountGuid = packageToUse.PrimaryRepLoginGuid,
                MessageId = messageEntity.MessageId,
                ServerId = (int)ServerEnum.Glados,
                SuspentSendUntilZoned = DateTimeOffset.Now,
            };
            await _quickAdapterAsync.SaveEntityAsync(messageTargetDirectEntity);

            Guid primaryRepLoginGuid = packageToUse.PrimaryRepLoginGuid;
            await _quickAdapterAsync.SaveEntityAsync(packageToUse, true);
        }


        private static async Task<int> DetermineRenewalStatus(PackageEntity package, PolicyProspectEntity policy,
            CarrierMaximumOfferEntity offer)
        {
            Guid currentCarrierGuid =
                offer?.CarrierProgram?.CarrierGuid ?? Guid.Parse("CBB6E817-6621-47B3-845C-A6E989053151");
            if (policy.RenewalOfPolicyGuid != Guid.Empty)
            {
                PolicyProspectEntity oldPolicy =
                    FlexEntityFetcherModel.GetPolicyAndSubmissionCarriers(policy.RenewalOfPolicyGuid);
                CarrierSubmissionEntity winningSubmission = oldPolicy.FinancialSummaryPolicy.CarrierSubmission;

                return winningSubmission.CarrierGuid == currentCarrierGuid
                    ? (int)UcpmRenewalStatusEnum.Renewal
                    : (int)UcpmRenewalStatusEnum.Renewal;
            }
            else

            {
                Guid programGuid = package.ProgramGuid;
                EntityCollection<PackageEntity> oldPackages =
                    await FlexEntityFetcherModel.GetPackagesByInsuredAgentHistoryGuid(package.InsuredAgentHistoryGuid);
                IEnumerable<PackageEntity> matchingOldPackages = oldPackages
                    .Where(p => p.ProgramGuid == programGuid && p.PackageGuid != package.PackageGuid);
                if (matchingOldPackages.Any())
                {
                    PackageEntity lastPackage = oldPackages.OrderBy(p => p.PackageDate).Last();
                    if (!lastPackage.PolicyProspect.Any())
                    {
                        return (int)UcpmRenewalStatusEnum.NewBusiness;
                    }

                    PolicyProspectEntity latestPolicy = lastPackage.PolicyProspect
                        .Where(p => p.PolicyTypeId == policy.PolicyTypeId).OrderBy(d => GetExpirationDateForPolicy(d))
                        .LastOrDefault();
                    if (latestPolicy == null)
                    {
                        return (int)UcpmRenewalStatusEnum.NewBusiness;
                    }

                    CarrierSubmissionEntity winningSubmission = latestPolicy?.FinancialSummaryPolicy?.CarrierSubmission;
                    DateTimeOffset lastDayForRenewal = GetExpirationDateForPolicy(latestPolicy).AddDays(30);
                    TimeSpan differenceInTime = latestPolicy.EffectiveDateZoned - lastDayForRenewal;

                    if (differenceInTime.TotalDays > 30)
                    {
                        return (int)UcpmRenewalStatusEnum.NewBusiness; //new business
                    }
                    else
                    {
                        return winningSubmission != null && winningSubmission.CarrierGuid == currentCarrierGuid
                            ? (int)UcpmRenewalStatusEnum.NewBusiness
                            : (int)UcpmRenewalStatusEnum.NewBusiness;
                    }
                }
                else
                {
                    return (int)UcpmRenewalStatusEnum.NewBusiness; //new business
                }
            }
        }

        private static DateTimeOffset GetFromNewPrefetch(PolicyProspectEntity policy)
        {
            policy = FlexEntityFetcherModel.GetPrefetchForExpirationDateQuery(policy);
            return policy.CarrierSubmission.Count == 0 ||
                   !policy.CarrierSubmission.Select(c => c.CarrierSubmissionOption).Any() ||
                   policy.FinancialSummaryPolicy == null ||
                   policy.FinancialSummaryPolicy.CarrierSubmission.FinancialSummarySubmission == null
                ? policy.EffectiveDateZoned.AddYears(1)
                : policy.FinancialSummaryPolicy.CarrierSubmission.FinancialSummarySubmission.CarrierSubmissionOption
                    .ExpirationDateZoned;
        }

        private static DateTimeOffset GetExpirationDateForPolicy(PolicyProspectEntity policy)
        {
            return policy.FinancialSummaryPolicy?.CarrierSubmission?.FinancialSummarySubmission?.CarrierSubmissionOption
                       ?.ExpirationDateZoned
                   ?? GetFromNewPrefetch(policy);
        }

        private static async Task<PackageEntity> CreateNewChildPackage(Guid agentGuid, ProgramEntity program,
            InsuredEntity insured, Guid newInsuredAgentHistoryPackageGuid, DateTimeOffset effectiveDateZoned)
        {
            PackageEntity newPackage = await CreateChildPackage(insured, agentGuid);
            newPackage.InsuredAgentHistoryGuid = newInsuredAgentHistoryPackageGuid;
            newPackage.ProgramGuid = program.ProgramGuid;
            newPackage.PackageDate = new DateTime(effectiveDateZoned.DateTime.Year, 1, 1);
            newPackage.PrimaryRepLoginGuid = Guid.Empty;
            newPackage.SourceOfContactId = 0;
            await _quickAdapterAsync.SaveEntityAsync(newPackage, true);
            return newPackage;
        }

        private static async Task<PackageEntity> CreateChildPackage(InsuredEntity insured, Guid agentGuid)
        {
            return new PackageEntity
            {
                PackageGuid = Guid.NewGuid(),
                PackageDate = new DateTime(DateTime.Now.Year, 1, 1),
                InsuredAgentHistoryGuid =
                    await FlexEntityFetcherModel.GetHistoryByInsuredAndAgent(insured.InsuredGuid, agentGuid)
            };
        }

        public static async Task<PolicyProspectEntity> CreateNewChildPolicy(PackageEntity packageToUse,
            DateTimeOffset effectiveDateZoned, int renewableStatusId, Guid flexResponseGuid,
            CarrierMaximumOfferEntity offer, List<FlexAnswer> flexAnswers, InsuredEntity insured, Guid flexVersionGuid,
            int nxusTypeId)
        {
            PolicyProspectEntity policy =
                CreateChildPolicy(packageToUse.PackageGuid, renewableStatusId, flexResponseGuid);
            policy.EffectiveDateZoned = effectiveDateZoned;
            policy.QuoteDueDateZoned = DateTimeOffset.Now;
            policy.WasStartedOnline = true;
            policy.PolicyTypeId = offer.PolicyTypeId;
            policy.IsTransactional = renewableStatusId == (int)UcpmRenewableStatusEnum.NonRenewable;
            policy.UcpmRenewableStatusId = renewableStatusId;
            await _quickAdapterAsync.SaveEntityAsync(policy, true);

            await GetOrCreateCarrierSubmissions(policy, CarrierList.UnknownCarrierGuid, offer, true,
                await FlexEntityFetcherModel.GetTaxState(flexAnswers, insured), flexVersionGuid,
                policyStatusId: (int)PolicyStatusEnum.QuotedRcvd, nxusTypeIds: [nxusTypeId]);

            return policy;
        }

        private static PolicyProspectEntity CreateChildPolicy(Guid packageGuid, int renewableStatus,
            Guid flexResponseGuid)
        {
            PolicyProspectEntity policy = new()
            {
                PolicyProspectGuid = Guid.NewGuid(),
                PackageGuid = packageGuid,
                EffectiveDateZoned = DateTimeOffset.Now,
                UcpmRenewableStatusId = renewableStatus,
                UcpmRenewalStatusId = (int)UcpmRenewalStatusEnum.NewBusiness,
                LastMeaningfulTouchTypeId = 1,
                LastMeaningfulTouchZoned = DateTimeOffset.Now,
                FlexResponseGuid = flexResponseGuid
            };

            return policy;
        }

        public static void TransformFlexTemplate(EntityBase2 entity, Dictionary<string, string> replaceActions)
        {
            if (entity is CarrierMaximumOfferQuoteTemplateEntity quoteTemplateEntity)
            {
                quoteTemplateEntity.QuoteTemplate =
                    ReplaceFlexTemplateText(quoteTemplateEntity.QuoteTemplate, replaceActions);

                if (!string.IsNullOrWhiteSpace(quoteTemplateEntity.Header))
                {
                    quoteTemplateEntity.Header = ReplaceFlexTemplateText(quoteTemplateEntity.Header, replaceActions);
                }

                if (!string.IsNullOrWhiteSpace(quoteTemplateEntity.Footer))
                {
                    quoteTemplateEntity.Footer = ReplaceFlexTemplateText(quoteTemplateEntity.Footer, replaceActions);
                }
            }
            else if (entity is CarrierMaximumOfferAppTemplateEntity appTemplateEntity)
            {
                appTemplateEntity.AppTemplate = ReplaceFlexTemplateText(appTemplateEntity.AppTemplate, replaceActions);

                if (!string.IsNullOrWhiteSpace(appTemplateEntity.Header))
                {
                    appTemplateEntity.Header = ReplaceFlexTemplateText(appTemplateEntity.Header, replaceActions);
                }

                if (!string.IsNullOrWhiteSpace(appTemplateEntity.Footer))
                {
                    appTemplateEntity.Footer = ReplaceFlexTemplateText(appTemplateEntity.Footer, replaceActions);
                }
            }
            else if (entity is CarrierMaximumOfferWarrantyTemplateEntity warrantyTemplateEntity &&
                     _quickAdapterReader.IsClean(warrantyTemplateEntity))
            {
                warrantyTemplateEntity.WarrantyTemplate =
                    ReplaceFlexTemplateText(warrantyTemplateEntity.WarrantyTemplate, replaceActions);

                if (!string.IsNullOrWhiteSpace(warrantyTemplateEntity.Header))
                {
                    warrantyTemplateEntity.Header =
                        ReplaceFlexTemplateText(warrantyTemplateEntity.Header, replaceActions);
                }

                if (!string.IsNullOrWhiteSpace(warrantyTemplateEntity.Footer))
                {
                    warrantyTemplateEntity.Footer =
                        ReplaceFlexTemplateText(warrantyTemplateEntity.Footer, replaceActions);
                }
            }
            else if (entity is DcatTemplateEntity newDcatTemplateEntity && _quickAdapterReader.IsClean(newDcatTemplateEntity))
            {
                newDcatTemplateEntity.HtmlTemplate = ReplaceFlexTemplateText(newDcatTemplateEntity.HtmlTemplate, replaceActions);
            }
        }

        public static string ReplaceFlexTemplateText(string templateText, Dictionary<string, string> replaceActions)
        {
            MatchCollection matches = Regex.Matches(templateText, @"\{Flex\.[^}]*\}");
            foreach (Match match in matches.Cast<Match>())
            {
                string matchKey = match.Value;
                string actionKey = replaceActions.Keys.OrderByDescending(key => key.Length)
                    .FirstOrDefault(key => matchKey.Contains(key));
                if (actionKey != null)
                {
                    string replaceAction = replaceActions[actionKey];
                    templateText = templateText.Replace(matchKey, replaceAction);
                }
                else
                {
                    templateText = templateText.Replace(matchKey, "");
                }
            }

            return templateText;
        }

        public static PolicyProspectEntity GetLastYearsPolicy(Guid renewalOfPolicyGuid)
        {
            PolicyProspectEntity policy = new(renewalOfPolicyGuid);

            PrefetchPath2 path = new(EntityType.PolicyProspectEntity);
            IPrefetchPath2 subPath = path.Add(PolicyProspectEntity.PrefetchPathCarrierSubmission).SubPath;
            IPrefetchPath2 optionPath =
                subPath.Add(CarrierSubmissionEntity.PrefetchPathCarrierSubmissionOption).SubPath;
            optionPath.Add(CarrierSubmissionOptionEntity.PrefetchPathFinancialSummaryOption);
            _quickAdapterAsync.FetchEntity(policy, path);
            return policy;
        }

        public static async Task<EntityCollection<FlexVersionCarrierOfferEntity>> GetFlexVersionCarrierSubmissionOffline()
        {
            EntityCollection<FlexVersionCarrierOfferEntity> offer = [];
            PrefetchPath2 path = new(EntityType.FlexVersionCarrierOfferEntity);
            IPrefetchPath2 carrierMaximumOfferOfferPath = path.Add(FlexVersionCarrierOfferEntity.PrefetchPathCarrierMaximumOffer).SubPath;
            IPrefetchPath2 carrierProgramPath = carrierMaximumOfferOfferPath.Add(CarrierMaximumOfferEntity.PrefetchPathCarrierProgram).SubPath;

            RelationPredicateBucket filter = new(FlexVersionCarrierOfferFields.CarrierMaximumOfferGuid == Guid.Parse("E04BE7CD-3C26-4A66-84D3-BEE1C55A0413"));

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = offer,
                FilterToUse = filter.PredicateExpression,
                PrefetchPathToUse = path,
            };

            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return offer;
        }

        public static string GetProjectNameFromSaveState(FlexResponseSaveStateEntity saveState)
        {
            FlexAnswerContainer container = FlexAnswerContainer.FromJson(saveState.SavedData);
            List<FlexAnswer> flexAnswers = container.FlexAnswers;
            List<FlexAnswer> projectAnswer = FindByQuestion("Project Name", flexAnswers);
            string projectName = projectAnswer.Any() ? projectAnswer.SingleOrDefault().Answer : "";
            return projectName;
        }

        public static async Task<FlexVersionCarrierOfferEntity> GetFlexVersionCarrierMaximumOffer(Guid flexResponseGuid)
        {
            EntityCollection<FlexVersionCarrierOfferEntity> collection = [];

            PrefetchPath2 path = new(EntityType.FlexVersionCarrierOfferEntity);
            IPrefetchPath2 flexVersion = path.Add(FlexVersionCarrierOfferEntity.PrefetchPathFlexVersion).SubPath;
            IPrefetchPath2 flexPublishedVersion =
                flexVersion.Add(FlexVersionEntity.PrefetchPathFlexPublishedVersion).SubPath;
            IPrefetchPath2 flexDefinitionPath =
                flexPublishedVersion.Add(FlexPublishedVersionEntity.PrefetchPathFlexDefinition).SubPath;
            flexDefinitionPath.Add(FlexDefinitionEntity.PrefetchPathFlexResponse);

            RelationPredicateBucket filter = new(FlexResponseFields.FlexResponseGuid == flexResponseGuid);
            filter.Relations.Add(FlexVersionCarrierOfferEntity.Relations.FlexVersionEntityUsingFlexVersionGuid);
            filter.Relations.Add(FlexVersionEntity.Relations.FlexPublishedVersionEntityUsingPublishedFlexVersionGuid);
            filter.Relations.Add(FlexPublishedVersionEntity.Relations.FlexDefinitionEntityUsingFlexDefinitionId);
            filter.Relations.Add(FlexDefinitionEntity.Relations.FlexResponseEntityUsingFlexDefinitionId);

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = collection,
                FilterToUse = filter.PredicateExpression,
                RelationsToUse = filter.Relations,
                PrefetchPathToUse = path,
            };

            await _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters);
            return collection.FirstOrDefault();
        }

        private static void SetPolicyStatus(CarrierSubmissionEntity submission, int proposedStatus)
        {
            if (submission.PolicyStatusId == (int)PolicyStatusEnum.SelectStatus)
            {
                submission.PolicyStatusId = proposedStatus == -1 ? (int)PolicyStatusEnum.QuotedRcvd : proposedStatus;
            }
        }

        private static string CheckNodsResults(PolicyProspectEntity policy, CarrierSubmissionEntity submission,
            List<FlexAnswer> retroTable, List<FlexAnswer> flexAnswers)
        {
            string nodsRetroDate = string.Empty;
            if (retroTable.Any(r => r.Answer == "Non-Owned Disposal Sites"))
            {
                FlexAnswer singleNods = retroTable.Single(r => r.Answer == "Non-Owned Disposal Sites");
                List<FlexAnswer> retroDates = FindByQuestion("RetroactiveDate", flexAnswers);
                FlexAnswer match = retroDates.SingleOrDefault(r => r.RowNumber == singleNods.RowNumber);
                if (match != null)
                {
                    nodsRetroDate = match.Answer;
                }
            }

            if (submission.CarrierGuid == CarrierList.Lloyds &&
                policy.Package.ProgramGuid == ProgramList.ConstructionProgram && nodsRetroDate == string.Empty)
            {
                nodsRetroDate = policy.EffectiveDateZoned.ToShortDateString();
            }

            return nodsRetroDate;
        }

        public static async Task<Guid> GetClassOfBusinessFromFlexAnswers(IEnumerable<FlexAnswer> answers)
        {
            FlexAnswer cobAnswer = answers.First(i => i.QuestionDataName == "CobRevenues");
            IGrouping<int, FlexAnswer> winner = null;
            foreach (IGrouping<int, FlexAnswer> answer in cobAnswer.ChildAnswers.GroupBy(i => i.RowNumber))
            {
                if (winner == null)
                {
                    winner = answer;
                }

                if (winner != null)
                {
                    decimal currentWinnerAmount =
                        decimal.Parse(winner.First(i => i.QuestionDataName == "RevenueForCob").Answer,
                            System.Globalization.NumberStyles.Currency);
                    decimal answerWinnerAmount =
                        decimal.Parse(answer.First(i => i.QuestionDataName == "RevenueForCob").Answer,
                            System.Globalization.NumberStyles.Currency);
                    if (currentWinnerAmount < answerWinnerAmount)
                    {
                        winner = answer;
                    }
                }
            }

            string cobName = winner.Where(i => i.QuestionDataName == "SelectedCob")?.First()?.Answer ?? string.Empty;
            ClassOfBusinessQuery classOfBusinessQuery = new(_quickAdapterReader, new(_quickAdapterReader));
            return (await classOfBusinessQuery.GetByName(cobName)).ClassOfBusinessGuid;
        }

        public static async Task<QuestionEntity> GetLatestQuestionByName(Guid surveyDefinitionVersionGuid,
            string dataName)
        {
            EntityCollection<QuestionEntity> latestQuestions = [];

            RelationPredicateBucket filter = new(
                QuestionFields.SurveyDefinitionVersionGuid == surveyDefinitionVersionGuid﻿
                & QuestionFields.DataName == dataName);

            QueryParameters parameters = new()
            {
                CollectionToFetch = latestQuestions,
                FilterToUse = filter.PredicateExpression,
            };
            await _quickAdapterAsync.FetchEntityCollectionAsync(parameters);
            return latestQuestions.FirstOrDefault();
        }

        private static string GetStateCodeFromResponse(ResponseEntity response)
        {
            IEnumerable<QuestionEntity> questions = GetQuestionsByResponse(response);
            QuestionEntity surplusLinesQuestion =
                questions.FirstOrDefault(q => q.DataName == "SurplusLinesTaxState");
            if (surplusLinesQuestion != null)
            {
                SurveyDataEntity surveyData = response.SurveyData.FirstOrDefault(s => s.QuestionGuid == surplusLinesQuestion.QuestionGuid);
                if (surveyData != null)
                {
                    return surveyData.SurveyDataText.Value;
                }
            }

            return response.StateCode;
        }

        private static IEnumerable<QuestionEntity> GetQuestionsByResponse(ResponseEntity response)
        {
            int questionCount = response?.SurveyDefinitionVersion?.Question?.Count ?? 0;
            if (questionCount > 0)
            {
                return response.SurveyDefinitionVersion.Question;
            }

            return [];
        }

        private static (bool includeForm, string reason) CheckToIncludeForm(Guid carMaxOfferGuid,
            IEnumerable<SurveyDataBooleanEntity> booleanAnswers, List<Guid> textAnswersGuids,
            BinderFormInclusionEntity binderForm, string facilityStateCode, string insuredStateCode)
        {
            EntityCollection<BinderFormByResponseEntity> binderResponses = binderForm.BinderFormByResponse;
            EntityCollection<BinderFormForStateEntity> binderStates = binderForm.BinderFormForState;
            EntityCollection<BinderFormByCategoryEntity> binderCategories = binderForm.BinderFormByCategory;
            EntityCollection<BinderFormByFactorEntity> binderFactors = binderForm.BinderFormByFactor;
            bool noResponseRules = binderResponses.Count == 0;
            bool noStateRules = binderStates.Count == 0;
            bool noCategoriesRules = binderCategories.Count == 0;
            bool noFactorRules = binderFactors.Count == 0;
            if (noResponseRules && noStateRules && noCategoriesRules && noFactorRules)
            {
                return (true, "No Response Rules, No State Rules, No COB rules, No factor rules");
            }

            bool facilityStateCheck = binderStates.Any(s =>
                s.StateCode == facilityStateCode &&
                s.FormStateRuleTypeId == (int)FormStateRuleTypeEnum.FacilityQuestion);

            bool insuredStateCheck = binderStates.Any(s =>
                s.StateCode == insuredStateCode && s.FormStateRuleTypeId == (int)FormStateRuleTypeEnum.InsuredState);

            bool stateCheck = facilityStateCheck || insuredStateCheck || noStateRules;

            bool binderByResponseRuleCheck = false;
            bool binderFormByCategoryCheck = false;
            bool binderFormByFactorCheck = false;
            if (binderForm.SurveyDataOpCode == "AND")
            {
                binderByResponseRuleCheck = binderResponses.All(
                    r => booleanAnswers.Any(
                        a => a.SurveyData.QuestionGuid == r.QuestionGuid
                             && a.SurveyData.SurveyDataBoolean.Value == r.RequiredValue
                             && (a.SurveyData.CarrierMaximumOfferGuid == Guid.Empty ||
                                 a.SurveyData.CarrierMaximumOfferGuid == carMaxOfferGuid)));

                binderFormByCategoryCheck = binderCategories.Count <= 0
                                            || binderCategories.Any(r =>
                                                textAnswersGuids.Any(a => a == r.ClassOfBusinessGuid));

                binderFormByFactorCheck = !binderFactors.Any()
                                          || binderFactors.All(r => textAnswersGuids.Any(a => a == r.FactorGuid));

                string reason =
                    $"{binderByResponseRuleCheck} (Response Rule) {binderFormByCategoryCheck} (Cob Rule) {binderFormByFactorCheck} (Factor Rule)";

                if (noStateRules)
                {
                    return (binderByResponseRuleCheck && binderFormByCategoryCheck && binderFormByFactorCheck, reason);
                }
                else
                {
                    return (
                        stateCheck && binderByResponseRuleCheck && binderFormByCategoryCheck && binderFormByFactorCheck,
                        reason);
                }
            }
            else
            {
                if (!noResponseRules)
                {
                    IEnumerable<BinderFormByResponseEntity> rulesForThisForm = binderResponses.Where(r =>
                        booleanAnswers.Any(a => a.SurveyData.QuestionGuid == r.QuestionGuid));
                    foreach (BinderFormByResponseEntity rule in rulesForThisForm)
                    {
                        IEnumerable<SurveyDataBooleanEntity> allMatches =
                            booleanAnswers.Where(r => r.SurveyData.QuestionGuid == rule.QuestionGuid);
                        IEnumerable<SurveyDataBooleanEntity> carrierMatches =
                            allMatches.Where(c => c.SurveyData.CarrierMaximumOfferGuid == carMaxOfferGuid);
                        if (carrierMatches.Any())
                        {
                            if (carrierMatches.Any(c => c.Value == rule.RequiredValue))
                            {
                                return (carrierMatches.Any(c => c.Value == rule.RequiredValue),
                                    $"Matching Answers Found for {rule.QuestionGuid} {rule.RequiredValue} (Carrier Specific Answer)");
                            }
                        }
                        else

                        {
                            IEnumerable<SurveyDataBooleanEntity> genericAnswers =
                                allMatches.Where(c => c.SurveyData.CarrierMaximumOfferGuid == Guid.Empty);
                            if (genericAnswers.Any(c => c.Value == rule.RequiredValue))
                            {
                                return (genericAnswers.Any(c => c.Value == rule.RequiredValue),
                                    $"Matching Answers Found for {rule.QuestionGuid} {rule.RequiredValue} (Top Level Answer)");
                            }
                        }
                    }
                }

                if (!noCategoriesRules)
                {
                    return (binderCategories.Any(r => textAnswersGuids.Any(a => a == r.ClassOfBusinessGuid)),
                        $"COB matching");
                }
                else if (!noFactorRules)
                {
                    return (binderFactors.Any(r => textAnswersGuids.Any(a => a == r.FactorGuid)), $"Factor matching");
                }
                else

                {
                    return (facilityStateCheck || insuredStateCheck, "State Matching");
                }
            }
        }

        private static int GetPolicyTermYears(DateTime effectiveDate, DateTime expirationDate)
        {
            if (expirationDate <= effectiveDate)
            {
                return 0;
            }

            DateTime zeroTime = new(1, 1, 1);
            TimeSpan span = expirationDate - effectiveDate;

            // Because we start at year 1 for the Gregorian calendar, we must subtract a year here.
            int years = (zeroTime + span).Year - 1;

            // 1, where my other algorithm resulted in 0.
            return years;
        }

        private static string GetRetroDateAsString(List<FlexAnswer> flexAnswers, FlexDefinitionEnum flexDefinitionEnum)
        {
            switch (flexDefinitionEnum)
            {
                case FlexDefinitionEnum.Apl:
                    return FlexHtmlModel.GetAplRetroDateAnswer(flexAnswers);
                default:
                    return FlexHtmlModel.GetAllAnswersByDataName(flexAnswers, "RetroactiveDate")?.FirstOrDefault()
                        ?.Answer ?? "";
            }
        }

        private async static Task<Dictionary<string, string>> AplSpecifics(CarrierSubmissionOptionEntity option,
            NewFlexRatingContainer newRatingContainer, FlexAnswerContainer container)
        {
            Dictionary<string, string> toReturn = [];
            Dictionary<string, string> limitsAndDeductible =
                await GetLimitsAndDeductibleWithSelection(option, newRatingContainer);
            FlexAnswer astNoCert =
                container.FlexAnswers.FirstOrDefault(fd => fd.QuestionDataName.Contains("ASTNoCertificate"));

            toReturn.Add("TankCapacity",
                string.Concat(FlexHtmlModel.GetCheckboxWithSelectionFlex(astNoCert, newRatingContainer,
                    "If there are no tanks, check here: ", true)));
            foreach (KeyValuePair<string, string> element in limitsAndDeductible)
            {
                toReturn.Add(element.Key, element.Value);
            }

            return toReturn;
        }

        private async static Task<Dictionary<string, string>> GetLimitsAndDeductibleWithSelection(
            CarrierSubmissionOptionEntity option, NewFlexRatingContainer newRatingContainer)
        {
            Dictionary<string, string> toReturn = [];
            LimitsRequestedQuery limitsQuery = new(_quickAdapterAsync);
            DeductibleRequestedQuery deductibleQuery = new(_quickAdapterAsync);

            List<FlexCarrierOfferLimit> carrierOfferLimits = newRatingContainer.CarrierLimits.ToList();
            List<FlexCarrierOfferDeductible> deductibles = newRatingContainer.CarrierDeductibles.ToList();
            List<Guid> occLimitRequestedGuids = carrierOfferLimits
                .Select(s => Guid.Parse(s.OccLimitsRequestedGuid.ToString())).Distinct().ToList();
            List<Guid> aggLimitRequestedGuids = carrierOfferLimits
                .Select(s => Guid.Parse(s.AggLimitsRequestedGuid.ToString())).Distinct().ToList();
            List<Guid> deductibleRequestedGuid =
                deductibles.Select(s => Guid.Parse(s.DeductibleRequestedGuid.ToString())).Distinct().ToList();

            IEnumerable<LimitsRequestedEntity> occurrenceLimits =
                await limitsQuery.GetAllMatching(occLimitRequestedGuids);
            IEnumerable<LimitsRequestedEntity> aggregateLimits =
                await limitsQuery.GetAllMatching(aggLimitRequestedGuids);
            IEnumerable<DeductibleRequestedEntity> deductibleLimits =
                await deductibleQuery.GetAllMatching(deductibleRequestedGuid);

            string occurrenceHtml = FlexHtmlModel.BuildLimitOption("Limit each Claim: ", option.LimitOccurrence,
                occurrenceLimits, false);
            toReturn.Add("CarrierLimitsOcc", occurrenceHtml);
            string aggregateHtml =
                FlexHtmlModel.BuildLimitOption("Aggregate Limit: ", option.LimitAggregate, aggregateLimits, false);
            toReturn.Add("CarrierLimitsAgg", aggregateHtml);
            string deductibleHtml = FlexHtmlModel.BuildDeductibleOptions("Deductible Each Pollution Condition: ",
                option.Deductible, deductibleLimits, false);
            toReturn.Add("CarrierDeductibles", deductibleHtml);

            return toReturn;
        }

        public static async Task<DcatTemplateEntity> GetDcatTemplateEntity(PolicyProspectEntity policy, InsuredEntity insured, DcatCommand dcatCommand, DocumentFileSystemManager documentFileSystemManager,
                QuickAdapterAsync quickAdapter)
        {
            DcatViewModel dcatViewModel = new(policy.PolicyProspectGuid, DcatTypeEnum.ProgramOnly, dcatCommand, documentFileSystemManager);
            await dcatViewModel.ConstructModel();

            DcatTemplateEntity dcatTemplate = new(1);
            quickAdapter.FetchEntity(dcatTemplate);

            DcatTemplateProgramSnippetEntity programSnippet = new(dcatViewModel.ProgramGuid);
            quickAdapter.FetchEntity(programSnippet);

            string programSnippetTemplate = "";
            if (quickAdapter.IsClean(programSnippet))
            {
                programSnippetTemplate = programSnippet.HtmlTemplate;
            }


            EntityCollection<PolicyDcatNoteEntity> policyDcatNotes = GetByPolicyGuid(policy.PolicyProspectGuid);

            string insuredName = insured.Name;
            AgentEntity agent = policy.Package.InsuredAgentHistory.Agent;
            string companyName = agent.Location.Company.CompanyName;
            string companyDba = agent.Location.Company.CompanyDba;
            string copyRight = "© " + DateTime.Today.Year + " " + "UCPM, Inc";

            Dictionary<string, string> replaceActions = new()
            {
                    { "FormalizedToday", DateTime.Today.ToString("MMM dd, yyyy") },
                    { "InsuredName", insuredName },
                    { "Company", companyName },
                    { "CompanyDba", companyDba },
                    { "CopyRight", copyRight },
                    { "CobName", dcatViewModel.CobName },
                    { "CostOfPlacementTable", FlexHtmlModel.GenerateCostOfPlacementTable(dcatViewModel) },
                    { "CoverageOverviewIssueTable", FlexHtmlModel.GenerateCoverageOverviewTable(dcatViewModel) },
                    { "DetailedAnalysisSection", FlexHtmlModel.GenerateDetailedAnalysisSection(dcatViewModel) },
                    { "FinancialConsiderationTable", FlexHtmlModel.GenerateProposedPolicyTermTable(dcatViewModel) },
                    { "RatingBasisTable", FlexHtmlModel.GenerateRatingBasis(dcatViewModel) },
                    { "SurplusLinesOrAdmittedTable", FlexHtmlModel.GenerateSurplusLinesOrAdmittedTable(dcatViewModel) },
                    { "LimitsAndDeductiblesTable", FlexHtmlModel.GenerateLimitsAndDeductiblesTable(dcatViewModel) },
                    { "MultipleLocationRetroTable", FlexHtmlModel.GenerateMultipleLocationRetro(dcatViewModel) },
                    { "CarrierRatingsTable", FlexHtmlModel.GenerateCarrierRatingsTable(dcatViewModel) },
                    { "RetroDatesTable", FlexHtmlModel.GenerateRetroDatesTable(dcatViewModel) },
                    { "Forms", FlexHtmlModel.GenerateForms(dcatViewModel) },
                    { "DCATSelectedCobs", FlexHtmlModel.GenerateDcatSelectedCobsTable(dcatViewModel) },
                    { "DcatHeaderNoteSection", FlexHtmlModel.GenerateDcatHeaderSection(policyDcatNotes) },
                    { "DcatEndNoteSection",FlexHtmlModel.GenerateDcatEndNoteSection(policyDcatNotes) },
                    { "ProgramSnippet", programSnippetTemplate},
            };

            TransformFlexTemplate(dcatTemplate, replaceActions);

            return dcatTemplate;
        }

        public static async Task<FlexVersionEntity> GetLatestUnpublishedFlexVersionByDefinition(int flexDefinitionId)
        {
            EntityCollection<FlexVersionEntity> flexVersions = [];
            RelationPredicateBucket filter = new(FlexVersionFields.FlexDefinitionId == flexDefinitionId);
            filter.PredicateExpression.Add(_quickAdapterAsync.GenerateSubQuery(FlexVersionFields.FlexVersionGuid, FlexPublishedVersionFields.PublishedFlexVersionGuid, SetOperator.In, null, true));
            SortExpression sort = new(FlexVersionFields.FlexVersionNumber | SortOperator.Descending);

            PrefetchPath2 path = new(EntityType.FlexVersionEntity);
            IPrefetchPath2 flexVersionCarrierOfferFormPath = path.Add(FlexVersionEntity.PrefetchPathFlexVersionCarrierOfferForm).SubPath;
            flexVersionCarrierOfferFormPath.Add(FlexVersionCarrierOfferFormEntity.PrefetchPathForm);

            IPrefetchPath2 flexVersionOfferPath = path.Add(FlexVersionEntity.PrefetchPathFlexVersionCarrierOffer).SubPath;
            IPrefetchPath2 offerPath = flexVersionOfferPath.Add(FlexVersionCarrierOfferEntity.PrefetchPathCarrierMaximumOffer).SubPath;
            IPrefetchPath2 carrierProgramPath = offerPath.Add(CarrierMaximumOfferEntity.PrefetchPathCarrierProgram).SubPath;
            IPrefetchPath2 carrierPath = carrierProgramPath.Add(CarrierProgramEntity.PrefetchPathCarrier).SubPath;

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = flexVersions,
                PrefetchPathToUse = path,
                SorterToUse = sort,
                RowsToTake = 1
            };

            await _quickAdapterReader.FetchEntityCollectionAsync(queryParameters);

            return flexVersions.SingleOrDefault();
        }

        private static EntityCollection<PolicyDcatNoteEntity> GetByPolicyGuid(Guid policyProspectGuid)
        {
            EntityCollection<PolicyDcatNoteEntity> policyNotes = [];
            RelationPredicateBucket filter = new(PolicyDcatNoteFields.PolicyProspectGuid == policyProspectGuid);

            QueryParameters queryParameters = new()
            {
                CollectionToFetch = policyNotes,
                FilterToUse = filter.PredicateExpression﻿
            };
            _quickAdapterAsync.FetchEntityCollectionAsync(queryParameters).GetAwaiter().GetResult();

            return policyNotes;
        }

        /// <summary>
        /// Deletes all subjectivity records associated with the specified carrier submission.
        /// This method queries for all CarrierSubmissionSubjectivity entities linked to the given submission,
        /// and then deletes them from the database.
        /// </summary>
        /// <param name="submission">The carrier submission whose subjectivities should be deleted.</param>
        private static async Task DeleteCarrierSubmissionSubjectivity(CarrierSubmissionEntity submission)
        {
            // Create a query object to fetch subjectivities for the given submission
            CarrierSubmissionSubjectivityQuery subjectivityQuery = new(_quickAdapterReader);

            // Retrieve all subjectivity entities associated with the carrier submission
            EntityCollection<CarrierSubmissionSubjectivityEntity> toDel = await subjectivityQuery.GetAllCarrierSubmissionSubjectivityByCarrierSubmissionGuid(submission.CarrierSubmissionGuid);

            // Delete the retrieved subjectivity entities from the database
            await _quickAdapterWriter.DeleteEntityCollectionAsync(toDel);
        }
    }
}