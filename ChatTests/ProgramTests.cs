using Microsoft.VisualStudio.TestTools.UnitTesting;
using Chat;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Moq;

namespace Chat.Tests
{
    [TestClass]
    public class ProgramTests
    {
        [TestMethod]
        public void ReturnsStage_WhenHostPathContainsStageChat()
        {
            string hostPath = "some/path/stage-chat";
            string environment = DetermineEnvironment(hostPath);
            Assert.AreEqual("Stage", environment);
        }

        [TestMethod]
        public void ReturnsDemo_WhenHostPathContainsDemoChat()
        {
            string hostPath = "some/path/demo-chat";
            string environment = DetermineEnvironment(hostPath);
            Assert.AreEqual("Demo", environment);
        }

        [TestMethod]
        public void ReturnsProduction_WhenHostPathContainsProdChat()
        {
            string hostPath = "some/path/prod-chat";
            string environment = DetermineEnvironment(hostPath);
            Assert.AreEqual("Production", environment);
        }

        [TestMethod]
        public void ReturnsDevelopment_WhenHostPathDoesNotContainAnyEnvironment()
        {
            string hostPath = "some/path/other-chat";
            string environment = DetermineEnvironment(hostPath);
            Assert.AreEqual("Development", environment);
        }

        [TestMethod]
        public void ReturnsDevelopment_DefaultEnvironment()
        {
            string hostPath = "some/path/chat";
            string environment = DetermineEnvironment(hostPath);
            Assert.AreEqual("Development", environment);
        }

        private string DetermineEnvironment(string hostPath)
        {
            return hostPath switch
            {
                _ when hostPath.Contains("stage-chat") => "Stage",
                _ when hostPath.Contains("demo-chat") => "Demo",
                _ when hostPath.Contains("prod-chat") => "Production",
                _ => "Development"
            };
        }

        [TestMethod]
        public void ReturnsCorrectApiUri_ForStageEnvironment()
        {
            var configurationMock = new Mock<IConfiguration>();
            configurationMock.Setup(c => c[$"AppSettings:ApiUris:Stage"]).Returns("https://stage.api.com");

            string environment = "Stage";
            Uri apiUri = new(configurationMock.Object[$"AppSettings:ApiUris:{environment}"]!);

            Assert.AreEqual(new Uri("https://stage.api.com"), apiUri);
        }

        [TestMethod]
        public void ReturnsCorrectHubName_ForDemoEnvironment()
        {
            var configurationMock = new Mock<IConfiguration>();
            configurationMock.Setup(c => c[$"AppSettings:HubNames:Demo"]).Returns("chathubdemo");

            string environment = "Demo";
            string hubName = configurationMock.Object[$"AppSettings:HubNames:{environment}"];

            Assert.AreEqual("chathubdemo", hubName);
        }


        [TestMethod]
        public void ReturnsCorrectOriginList_ForProductionEnvironment()
        {
            var configurationMock = new Mock<IConfiguration>();
            var originList = new List<string> { "https://pathfinder.ucpm.com", "https://ext.ucpm.com" };

            var sectionMock = new Mock<IConfigurationSection>();
            sectionMock.Setup(s => s.Value).Returns(System.Text.Json.JsonSerializer.Serialize(originList));
            configurationMock.Setup(c => c.GetSection($"AppSettings:Origins:Production")).Returns(sectionMock.Object);

            string environment = "Production";
            List<string> origins = System.Text.Json.JsonSerializer.Deserialize<List<string>>(configurationMock.Object
                .GetSection($"AppSettings:Origins:{environment}").Value);

            CollectionAssert.AreEqual(originList, origins);
        }

        [TestMethod]
        public void ThrowsException_WhenApiUriIsNull()
        {
            var configurationMock = new Mock<IConfiguration>();
            configurationMock.Setup(c => c[$"AppSettings:ApiUris:Development"]).Returns((string)null);

            string environment = "Development";

            Assert.ThrowsException<ArgumentNullException>(() =>
                new Uri(configurationMock.Object[$"AppSettings:ApiUris:{environment}"]!));
        }
    }
}