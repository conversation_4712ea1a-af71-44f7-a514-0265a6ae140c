using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using SD.LLBLGen.Pro.LinqSupportClasses;
using UcpmApi.Query.Mail;
using UcpmApi.Shared;
using UcpmApi.Shared.Mail;
using UcpmTools;

namespace UcpmApi.Converter
{
    public class DirectSendTemplateVersionConverter : BaseConverter
    {
        private readonly DirectSendTemplateVersionQuery _directSendTemplateVersionQuery;

        public DirectSendTemplateVersionConverter(DirectSendTemplateVersionQuery directSendTemplateVersionQuery)
        {
            _directSendTemplateVersionQuery = directSendTemplateVersionQuery;
        }

        public async Task<List<DirectSendTemplateVersion>> GetAll()
        {
            EntityCollection<DirectSendTemplateVersionEntity> directSendTemplateVersion = await _directSendTemplateVersionQuery.GetAll();

            return ToViewList(directSendTemplateVersion);
        }

        public async Task<DirectSendTemplateVersion> GetDirectSendTemplateVersionByGuid(Guid directSendTemplateVersionGuid)
        {
            DirectSendTemplateVersionEntity directSendTemplateVersion = await _directSendTemplateVersionQuery.GetDirectSendTemplateVersions()
                .Where(c => c.DirectSendTemplateVersionGuid == directSendTemplateVersionGuid)
                .SingleOrDefaultAsync();

            return ToView(directSendTemplateVersion);
        }

        public async Task<DirectSendTemplateVersion> GetByTemplate(int templateId, Guid progrmaGuid)
        {
            EntityCollection<DirectSendTemplateVersionEntity> directSendTemplateVersion = await _directSendTemplateVersionQuery.GetByTemplateId(templateId, progrmaGuid);

            return ToView(directSendTemplateVersion.FirstOrDefault());
        }

        public async Task<DirectSendTemplateVersion> GetSimpleNoVariantsNoRules(int templateId, int versionId)
        {
            DirectSendTemplateVersionEntity directSendTemplateVersion = await _directSendTemplateVersionQuery.GetSimpleNoVariantsNoRules(templateId, versionId);

            return ToView(directSendTemplateVersion);
        }

        private static DirectSendTemplateVersion ToView(DirectSendTemplateVersionEntity entity)
        {
            DirectSendTemplateVersion directSendTemplateVersion = new();
            if (entity != null)
            {
                QuickReflection.CopyProps(entity, directSendTemplateVersion);
            }

            return directSendTemplateVersion;
        }

        private List<DirectSendTemplateVersion> ToViewList(EntityCollection<DirectSendTemplateVersionEntity> collection)
        {
            List<DirectSendTemplateVersion> list = [];

            foreach (DirectSendTemplateVersionEntity entity in collection)
            {
                DirectSendTemplateVersion directSendTemplateVersion = new();
                QuickReflection.CopyProps(entity, directSendTemplateVersion);
                list.Add(directSendTemplateVersion);
            }

            return list;
        }

        public async Task<IEnumerable<DirectSendTemplateVersion>> GetAllVariants(int templateId, int versionId)
        {
            EntityCollection<DirectSendTemplateVersionEntity> DirectSendTemplateVersionEntities = await _directSendTemplateVersionQuery.GetAllVariants(templateId, versionId);
            return DirectSendTemplateVersionEntities.Select(ToView); ;
        }

        public async Task<DirectSendTemplateVersion> GetLatestVersionByTemplateId(int templateId, Guid programGuid)
        {
            EntityCollection<DirectSendTemplateVersionEntity> directSendTemplateVersion = await _directSendTemplateVersionQuery.GetLatestVersionByTemplateId(templateId, programGuid);

            return ToView(directSendTemplateVersion.FirstOrDefault());
        }

        public async Task<DirectSendTemplateVersion> GetRandomVersionByTemplateId(int templateId, Guid programGuid)
        {
            DirectSendTemplateVersionEntity directSendTemplateVersionEntity = await _directSendTemplateVersionQuery.GetRandomVersionByTemplateId(templateId, programGuid);

            return ToView(directSendTemplateVersionEntity);
        }
    }
}