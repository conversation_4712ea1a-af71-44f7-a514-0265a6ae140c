using ORMStandard.EntityClasses;
using UcpmApi.Query.Lender;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Lender.RequestModels;
using UcpmApi.Shared.Lender.ResponseModels;

namespace UcpmApi.Converter.Lender;

public class LendersPolicyLoansConverter : BaseConverter
{
    private readonly LenderPolicyLoansQuery _lenderPolicyLoansQuery;

    public LendersPolicyLoansConverter(LenderPolicyLoansQuery lenderPolicyLoansQuery)
    {
        _lenderPolicyLoansQuery = lenderPolicyLoansQuery;
    }

    public async Task<List<LoanQuoteResponse>> GetForManageLoans(LoanQuotesRequest request)
    {
        if (request == null || request.InsuredGuid == Guid.Empty)
            return [];

        var loans = await _lenderPolicyLoansQuery.GetForManageLoans(request);
        if (loans == null || loans.Count == 0)
            return [];

        return loans.Select(ToLoanResponse).ToList();
    }
    
    public async Task<List<LoanQuoteResponse>> GetLenderResponsesForPolicy(LoanQuotesRequest request)
    {
        if (request == null || request.InsuredGuid == Guid.Empty)
            return [];
        
        var loans = await _lenderPolicyLoansQuery.GetLenderResponsesForPolicy(request);
        if (loans == null || loans.Count == 0)
            return [];

        return loans.Select(lenderResponseEntity => ToLoanResponse(lenderResponseEntity, request.InsuredGuid)).ToList();
    }
    
    private static LoanQuoteResponse ToLoanResponse(LenderResponseEntity lenderResponseEntity, Guid insuredGuid)
    {
        var loanQuoteResponse = new LoanQuoteResponse();
        if (lenderResponseEntity == null) return loanQuoteResponse;
        loanQuoteResponse.LoanNumber = lenderResponseEntity.LoanNumber;
        loanQuoteResponse.BorrowerName = lenderResponseEntity.BorrowerName;
        loanQuoteResponse.LoanAmount = lenderResponseEntity.LoanAmount;
        loanQuoteResponse.LoanTransactionType = (LoanTransactionTypeEnum)lenderResponseEntity.LoanTransactionTypeId;
        loanQuoteResponse.AddressSummary = lenderResponseEntity.AddressSummary;
        loanQuoteResponse.AnticipatedLoanClosingDate = lenderResponseEntity.AnticipatedClosingDate;
        loanQuoteResponse.ResponseGuid = lenderResponseEntity.FlexResponseGuid;
        loanQuoteResponse.PolicyProspectGuid = lenderResponseEntity.PolicyProspectGuid;
        loanQuoteResponse.ResponseIsSubmitted = true;
        loanQuoteResponse.ResponseOnLatest = true;
        loanQuoteResponse.InsuredGuid = insuredGuid;
        loanQuoteResponse.ResponseLinkingGuid = lenderResponseEntity.PolicyProspectGuid;
        loanQuoteResponse.LastUpdated = lenderResponseEntity.LastUpdated;
        loanQuoteResponse.SubmittedBy = (SurveySubmittedByEnum)lenderResponseEntity.SubmittedBy;
        loanQuoteResponse.FromNewSurvey = true;
        return loanQuoteResponse;
    }

    private static LoanQuoteResponse ToLoanResponse(ResponseEntity responseEntity)
    {
        var loanQuoteResponse = new LoanQuoteResponse();
        LoanQuoteHistoryEntity loanQuote = responseEntity.LoanQuoteHistory.SingleOrDefault();
        ResponseLenderCacheEntity lenderCache = responseEntity.ResponseLenderCache;

        Guid policyProspectGuid = responseEntity.ResponseLinkingGuid;
        LoanTransactionTypeEnum loanTransactionType = (LoanTransactionTypeEnum)lenderCache.LoanTransactionTypeId;
        decimal premium = loanQuote?.Premium ?? 0;
        decimal taxes = loanQuote?.Taxes ?? 0;
        bool responseIsSubmitted = responseEntity.ResponseEditStateId == (int)ResponseEditStateEnum.Submitted;
       // bool responseOnLatest = responseEntity.SurveyDefinitionVersionGuid == _latestLoanAppVersionGuid;

       //if (loanQuote == null) return loanQuoteResponse;
        //loanQuoteResponse.PolicyProspectGuid = loanQuote.PolicyProspectGuid;
        loanQuoteResponse.LoanNumber = lenderCache.LoanNumber;
        loanQuoteResponse.BorrowerName = lenderCache.BorrowerName;
        loanQuoteResponse.LoanAmount = lenderCache.LoanAmount;
        loanQuoteResponse.LoanTransactionType = loanTransactionType;
        loanQuoteResponse.Premium = premium;
        loanQuoteResponse.Taxes = taxes;
        loanQuoteResponse.AddressSummary = lenderCache.AddressSummary;
        loanQuoteResponse.AnticipatedLoanClosingDate = lenderCache.AnticipatedClosingDate;
        loanQuoteResponse.ResponseGuid = responseEntity.ResponseGuid;
        loanQuoteResponse.ResponseIsSubmitted = responseIsSubmitted;
        loanQuoteResponse.ResponseOnLatest = true;//responseEntity.SurveyDefinitionVersionGuid == _latestLoanAppVersionGuid;

        return loanQuoteResponse;
    }
}