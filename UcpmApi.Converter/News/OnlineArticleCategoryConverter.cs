using ORMStandard.EntityClasses;
using SD.LLBLGen.Pro.LinqSupportClasses;
using UcpmApi.Query.News;
using UcpmApi.Shared.News;
using UcpmTools;

namespace UcpmApi.Converter.News
{
    public class OnlineArticleCategoryConverter : BaseConverter
    {
        private readonly OnlineArticleCategoryQuery _onlineArticleCategoryQuery;

        public OnlineArticleCategoryConverter(OnlineArticleCategoryQuery onlineArticleCategoryQuery)
        {
            _onlineArticleCategoryQuery = onlineArticleCategoryQuery;
        }

        public async Task<List<OnlineArticleCategory>> GetOnlineArticleCategories()
        {
            IEnumerable<OnlineArticleCategoryEntity> onlineArticleCategories = await _onlineArticleCategoryQuery.GetOnlineArticleCategories()
                .OrderBy(oac => oac.CategoryName)
                .ToListAsync();

            return onlineArticleCategories.Select(ToView).ToList();
        }

        public async Task<OnlineArticleCategory> GetOnlineArticleCategoryByGuid(Guid onlineArticleCategoryGuid)
        {
            OnlineArticleCategoryEntity onlineArticleCategoryEntity = await _onlineArticleCategoryQuery.GetOnlineArticleCategories()
                .Where(oac => oac.OnlineArticleCategoryGuid == onlineArticleCategoryGuid)
                .SingleAsync();

            return ToView(onlineArticleCategoryEntity);
        }

        private OnlineArticleCategory ToView(OnlineArticleCategoryEntity entity)
        {
            OnlineArticleCategory onlineArticleCategory = new();

            if (entity != null)
            {
                QuickReflection.CopyProps(entity, onlineArticleCategory);
            }

            return onlineArticleCategory;
        }
    }
}
