using LLBLGenHelper;
using MoreLinq;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using System.IO;
using System.Text.RegularExpressions;
using UcpmApi.Command.News;
using UcpmApi.Converter.Cob;
using UcpmApi.Logging;
using UcpmApi.Query.News;
using UcpmApi.Query.Security;
using UcpmApi.Shared;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Lookups;
using UcpmApi.Shared.News;
using UcpmTools;

namespace UcpmApi.Converter.News
{
    public class OnlineArticleConverter : BaseConverter
    {
        private readonly OnlineArticleQuery _onlineArticleQuery;
        private readonly OnlineArticleCommand _onlineArticleCommand;
        private readonly AccountQuery _accountQuery;

        public OnlineArticleConverter(OnlineArticleQuery articleQuery, AccountQuery accountQuery, OnlineArticleCommand onlineArticleCommand)
        {
            _onlineArticleQuery = articleQuery;
            _accountQuery = accountQuery;
            _onlineArticleCommand = onlineArticleCommand;
        }

        public OnlineArticle GetArticle(Guid articleGuid)
        {
            OnlineArticleEntity rozArticle = _onlineArticleQuery.GetArticle(articleGuid);
            return ToView(rozArticle);
        }

        public async Task<List<OnlineArticle>> GetAllForRoz()
        {
            EntityCollection<OnlineArticleEntity> articles = await _onlineArticleQuery.GetForRoz();
            return articles.Select(ToView).ToList();
        }

        public async Task<List<OnlineArticle>> OnlineArticleSearchForRoz(string searchTerm, List<Guid> onlineArticleCategoryGuids)
        {
            EntityCollection<OnlineArticleEntity> articles = await _onlineArticleQuery.OnlineArticleSearchForRoz(searchTerm, onlineArticleCategoryGuids);
            return articles.Select(ToView).ToList();
        }

        public async Task<List<OnlineArticle>> GetNonViewedForRoz(Guid agentGuid)
        {
            EntityCollection<OnlineArticleEntity> articles = await _onlineArticleQuery.GetNonViewedForRoz(agentGuid);
            return articles.Select(ToView).ToList();
        }

        public async Task<List<OnlineArticle>> GetSearchedForRoz(Guid agentGuid, String searchQuery)
        {
            EntityCollection<OnlineArticleEntity> articles = await _onlineArticleQuery.GetSearchedForRoz(agentGuid, searchQuery);
            return articles.Select(ToView).ToList();
        }

        public async Task<List<OnlineArticle>> GetForRozAnonymous(int maxArticles)
        {
            EntityCollection<OnlineArticleEntity> articles = await _onlineArticleQuery.GetForRozAnonymous(maxArticles);
            return articles.Select(a => ToView(a)).ToList();
        }

        public async Task<List<OnlineArticle>> GetRozArticlesForNotifications()
        {
            EntityCollection<OnlineArticleEntity> articles = await _onlineArticleQuery.GetRozArticlesForNotifications();
            return articles.Select(a => ToView(a)).ToList();
        }

        public async Task<List<OnlineArticle>> GetRozArticlesForKato(Guid cobGuid)
        {
            EnvironmentEnumModel model = new EnvironmentConverter().GetEnvironment();
            QuickAdapterAsync quickAdapterAsync = new(new DataAccessAdapter(), new DataLogger());
            SiteUrlConverter siteUrlConverter = new(new Query.Lookups.SiteUrlQuery(quickAdapterAsync));
            SiteUrl siteUrl = await siteUrlConverter.GetSiteUrlByEnvironmentNameAndSiteProjectId(model.EnvironmentName, 15);
            EntityCollection<OnlineArticleEntity> articles = await _onlineArticleQuery.GetRozArticlesForKato(cobGuid);
            List<OnlineArticle> yo = articles.Select(a => ToView(a)).ToList();

            foreach (OnlineArticle article in yo)
            {
                article.ArticleUrl = $"{siteUrl.BaseUrl}/article/{article.OnlineArticleGuid}";
            }

            return yo;
        }

        public async Task<List<OnlineArticle>> GetForPathfinder(Guid agentGuid)
        {
            EntityCollection<OnlineArticleEntity> articles = await _onlineArticleQuery.GetForPathfinder();
            AccountLoginAuditEntity accountLoginAuditEntity = _accountQuery.GetLastAccountLoginForAccountAndSite(agentGuid, "Pathfinder");

            return accountLoginAuditEntity == null
                ? articles.Select(a => ToView(a)).ToList()
                : articles.Where(a => a.PublishedDateZoned >= accountLoginAuditEntity.AttemptDateTimeZoned).Select(a => ToView(a)).ToList();
        }

        public async Task<List<OnlineArticle>> GetUnreadOnlineNewsArticles(Guid accountGuid, PublishLocationEnum publishLocation)
        {
            IEnumerable<OnlineArticleEntity> articleCollection = await _onlineArticleQuery.GetUnReadOnlineArticles(accountGuid, publishLocation);
            List<OnlineArticle> onlineArticles = [];
            articleCollection.ForEach(fe => onlineArticles.Add(ToView(fe)));

            return onlineArticles;
        }

        public async Task<List<OnlineArticle>> GetOnlineArticlesByPublishType(PublishLocationEnum publishLocationEnum)
        {
            EntityCollection<OnlineArticleEntity> collection = await _onlineArticleQuery.GetOnlineArticlesByWebSite(publishLocationEnum);
            List<OnlineArticle> onlineArticles = [];
            collection.ForEach(fe => onlineArticles.Add(ToView(fe)));

            return onlineArticles;
        }

        public async Task<bool> CreateOnlineArticle(OnlineArticle onlineArticle)
        {
            OnlineArticleEntity onlineArticleEntity = ToEntity(onlineArticle);
            bool result = await _onlineArticleCommand.CreateOnlineArticle(onlineArticleEntity);

            return result;
        }
        
        public async Task<List<OnlineArticle>> GetBookmarkedArticles(Guid agentGuid)
        {
            EntityCollection<OnlineArticleEntity> articles = await _onlineArticleQuery.GetBookmarkedArticles(agentGuid);
            return articles.Select(ToView).ToList();
        }

        private OnlineArticleEntity ToEntity(OnlineArticle onlineArticle)
        {
            OnlineArticleEntity onlineArticleEntity = new();
            QuickReflection.CopyProps(onlineArticle, onlineArticleEntity);
            onlineArticleEntity.ArticleHtml = CleanExcessWhitespace(onlineArticle.ArticleHtml);

            return onlineArticleEntity;
        }

        private OnlineArticle ToView(OnlineArticleEntity articleEntity)
        {
            OnlineArticle article = new();
            QuickReflection.CopyProps(articleEntity, article);
            article.ArticleHtml = CleanExcessWhitespace(articleEntity.ArticleHtml);
            article.AbbreviatedArticleHtml = SetAbbrevText(1, article.ArticleHtml);
            article.ArticleCobs = ClassOfBusinessConverter.ConvertCobs(articleEntity.ClassOfBusinessCollectionViaOnlineArticleClassOfBusiness).OrderBy(c => c.CobName).ToList();
            article.ArticleStates = StateConverter.ConvertStates(articleEntity.StateCollectionViaOnlineArticleState).ToList();
            article.ArticleCobGroups = [];

            article.ArticleUrl = (string.IsNullOrWhiteSpace(articleEntity?.ArticleUrl))
                                 ? articleEntity?.OnlineArticleMedia?.FirstOrDefault(x => x.MediaTypeId == (int)MediaTypeEnum.Link)?.MediaUrl ?? ""
                                 : articleEntity.ArticleUrl;
            article.HeroImageUrl = (string.IsNullOrWhiteSpace(articleEntity?.HeroImageUrl))
                                 ? articleEntity?.OnlineArticleMedia?.FirstOrDefault(x => x.MediaTypeId == (int)MediaTypeEnum.HeroImage)?.MediaUrl ?? ""
                                 : articleEntity.HeroImageUrl;
            article.VideoUrl = (string.IsNullOrWhiteSpace(articleEntity?.VideoUrl))
                                 ? articleEntity?.OnlineArticleMedia?.FirstOrDefault(x => x.MediaTypeId == (int)MediaTypeEnum.Video)?.MediaUrl ?? ""
                                 : string.Empty;
            article.AudioUrl = articleEntity?.OnlineArticleMedia?.FirstOrDefault(x => x.MediaTypeId == (int)MediaTypeEnum.Audio)?.MediaUrl
                               ?? "";

            article.AudioFileName = (string.IsNullOrWhiteSpace(article.AudioUrl)) ? "" : SetAudioFileName(article.AudioUrl);
            article.AudioFileType = (string.IsNullOrWhiteSpace(article.AudioUrl)) ? "" : Path.GetExtension(article
                .AudioUrl)[1..];

            foreach (OnlineArticleClassOfBusinessEntity item in articleEntity.OnlineArticleClassOfBusiness)
            {
                if (item.ClassOfBusiness.OnlineArticleClassOfBusinessGroupToCob == null)
                {
                    break;
                }
                foreach (OnlineArticleClassOfBusinessGroupToCobEntity subItem in item.ClassOfBusiness.OnlineArticleClassOfBusinessGroupToCob)
                {
                    article.ArticleCobGroups.Add(new OnlineArticleClassOfBusinessGroup()
                    {
                        OnlineArticleClassOfBusinessGroupGuid = subItem.OnlineArticleClassOfBusinessGroupGuid,
                        GroupName = subItem?.OnlineArticleClassOfBusinessGroup?.GroupName ?? "",
                        Cobs = []
                    });
                }
            }

            return article;
        }

        private string CleanExcessWhitespace(string articleHtml)
        {
            string body = articleHtml.Replace("<p>&nbsp;</p>", "")
                                     .Replace("<p><br></p>", "")
                                     .Replace("<div><br></div>", "")
                                     .Replace("<p></p>", "")
                                     .Replace("&nbsp;", " ");

            string minusInlineStyles = Regex.Replace(body, @"style="".*?""", "");

            return minusInlineStyles;
        }

        private string SetAbbrevText(int numberOfParagraphs, string articleHtml)
        {
            string[] paragraphs = Regex.Split(articleHtml, @"</p>", RegexOptions.IgnoreCase);
            string ps = string.Join(" ", paragraphs.Take(numberOfParagraphs));

            return SetAbbrevText1(250, ps);
        }

        private string SetAbbrevText1(int length, string articleHtml)
        {
            string body = Regex.Replace(articleHtml, "<.*?>", "");
            string truncated = body.Trim();

            if (body.Length > length)
            {
                int endOfWord = body.IndexOf(" ", length);
                if (endOfWord != -1)
                {
                    truncated = $"{body[..endOfWord]}";
                }
            }

            //remove ':' if there is one at the end, :... is awkward
            string[] split = truncated.Split(':', StringSplitOptions.RemoveEmptyEntries);
            return $"{string.Join(':', split)}...";
        }

        private string SetAudioFileName(string audioUrl)
        {
            string[] split = audioUrl.Split('/');
            string title = split[^1];
            int toFileType = title.IndexOf('.');
            string subString = title[..toFileType];

            return subString.Replace("_", " ");
        }
    }
}
