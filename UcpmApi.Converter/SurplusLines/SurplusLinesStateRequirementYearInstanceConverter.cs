using ORMStandard.EntityClasses;
using SD.LLBLGen.Pro.LinqSupportClasses;
using UcpmApi.Query.SurplusLines;
using UcpmApi.Shared;
using UcpmTools;

namespace UcpmApi.Converter
{
    public class SurplusLinesStateRequirementYearInstanceConverter : BaseConverter
    {
        private readonly SurplusLinesStateRequirementYearInstanceQuery _surplusLinesStateRequirementYearInstanceQuery;

        public SurplusLinesStateRequirementYearInstanceConverter(SurplusLinesStateRequirementYearInstanceQuery surplusLinesStateRequirementYearInstanceQuery)
        {
            _surplusLinesStateRequirementYearInstanceQuery = surplusLinesStateRequirementYearInstanceQuery;
        }

        public async Task<SurplusLinesStateRequirementYearInstance> GetSurplusLinesStateRequirementYearInstanceByGuid(Guid surplusLinesStateRequirementYearInstanceGuid)
        {
            SurplusLinesStateRequirementYearInstanceEntity surplusLinesStateRequirementYearInstance = await _surplusLinesStateRequirementYearInstanceQuery.GetSurplusLinesStateRequirementYearInstances()
                .Where(c => c.SurplusLinesStateRequirementYearInstanceGuid == surplusLinesStateRequirementYearInstanceGuid)
                .SingleOrDefaultAsync();

            return ToView(surplusLinesStateRequirementYearInstance);
        }

        public async Task<IEnumerable<SurplusLinesStateRequirementYearInstance>> GetSurplusLinesStateRequirementYearInstancesByRequirementYearGuid(Guid surplusLinesStateRequirementYearGuid)
        {
            IEnumerable<SurplusLinesStateRequirementYearInstanceEntity> surplusLinesStateRequirementYearInstanceEntities = await _surplusLinesStateRequirementYearInstanceQuery.GetSurplusLinesStateRequirementYearInstances()
                .Where(i => i.SurplusLinesStateRequirementYearGuid == surplusLinesStateRequirementYearGuid)
                .ToListAsync();

            return surplusLinesStateRequirementYearInstanceEntities.Select(ToView);
        }

        private static SurplusLinesStateRequirementYearInstance ToView(SurplusLinesStateRequirementYearInstanceEntity entity)
        {
            SurplusLinesStateRequirementYearInstance SurplusLinesStateRequirementYearInstance = new();
            QuickReflection.CopyProps(entity, SurplusLinesStateRequirementYearInstance);

            return SurplusLinesStateRequirementYearInstance;
        }
    }
}
