using ORMStandard.EntityClasses;
using SD.LLBLGen.Pro.LinqSupportClasses;
using UcpmApi.Query.Policy;
using UcpmApi.Shared.EPay;

namespace UcpmApi.Converter
{
    public class EPayConverter : BaseConverter
    {
        private readonly InvoiceQuery _invoiceQuery;

        public EPayConverter(InvoiceQuery invoiceQuery)
        {
            _invoiceQuery = invoiceQuery;
        }

        public async Task<EPayInvoicesModel> GetInvoicesAgency(string accountIdentifier)
        {
            DateTimeOffset now = DateTimeOffset.Now;
            DateTimeOffset yearAgo = now.AddYears(-1);

            List<InvoiceEntity> invoices = await _invoiceQuery
                .GetInvoicesForEPayAgency(accountIdentifier)
                .ToListAsync();

            return await ToView(invoices);
        }

        public async Task<EPayInvoicesModel> GetInvoicesInsured(string accountIdentifier)
        {
            List<InvoiceEntity> invoices = await _invoiceQuery
                .GetInvoicesForEPayInsured(accountIdentifier)
                .ToListAsync();

            return await ToView(invoices, true);
        }

        private async Task<EPayInvoicesModel> ToView(List<InvoiceEntity> invoices, bool insured = false)
        {
            EPayInvoicesModel ePayInvoicesModel = new();
            ePayInvoicesModel.Invoices = new List<EPayInvoiceModel>();

            if (invoices.Count > 0)
            {
                if (insured)
                {
                    ePayInvoicesModel.PayerName = invoices[0].InvoiceToCompany.Insured.Name;
                    ePayInvoicesModel.EmailAddresses = new List<string> { invoices[0].InvoiceToCompany.Insured.Email };
                }
                else
                {
                    ePayInvoicesModel.PayerName = invoices[0].InvoiceToCompany.Company.CompanyName;
                    ePayInvoicesModel.EmailAddresses = new List<string> { invoices[0].InvoiceToCompany.Company.AccountingEmail };
                }
            }

            foreach (InvoiceEntity invoice in invoices.Where(c => c.InvoiceItem.SelectMany(sm => sm.ReceivableLine.SelectMany(atsm => atsm.AccountTransaction)).Sum(s => s.IsDebit ? s.Amount : -s.Amount) > 0))
            {
                EPayInvoiceModel ePayInvoiceModel = new();
                ePayInvoiceModel.Id = invoice.InvoiceNumber.ToString();
                ePayInvoiceModel.Name = ePayInvoicesModel.PayerName;
                ePayInvoiceModel.DueDate = invoice.DueDate;
                ePayInvoiceModel.Amount = invoice.InvoiceItem.SelectMany(sm => sm.ReceivableLine.SelectMany(atsm => atsm.AccountTransaction)).Sum(s => s.IsDebit ? s.Amount : -s.Amount);
                ePayInvoiceModel.MaximumAmount = ePayInvoiceModel.Amount.Value;
                ePayInvoiceModel.AllowPartialPayment = false;

                ePayInvoiceModel.InvoiceUnderwriting = new(); //Needed for full financing integration
                ePayInvoiceModel.AttributeValues = []; //Need to request more information on what this hits
                ePayInvoiceModel.InvoiceItems = []; //Not neccesary for current implementation

                ePayInvoicesModel.Invoices.Add(ePayInvoiceModel);
            }

            return ePayInvoicesModel;
        }
    }
}