using Microsoft.TeamFoundation.Policy.WebApi;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using UcpmApi.Query.Lookups;
using UcpmApi.Shared;

namespace UcpmApi.Converter
{
    public class StateRequirementConverter : BaseConverter
    {
        private readonly StateRequirementQuery _stateRequirementQuery;

        public StateRequirementConverter(StateRequirementQuery stateRequirementQuery)
        {
            _stateRequirementQuery = stateRequirementQuery;
        }

        public async Task<IEnumerable<StateRequirement>> Get(string state)
        {
            EntityCollection<StateRequirementEntity> entities = await _stateRequirementQuery.GetAllFlexOptionsRequirementsByState(state);

            return entities.Select(ToView);
        }

        private static StateRequirement ToView(StateRequirementEntity entity)
        {
            StateRequirement stateRequirement = new()
            {
                RequirementName = entity.RequirementText,
                RequirementUrl = entity.RequirementUrl,
                PolicyTypeId = entity.PolicyTypeId,
            };

            return stateRequirement;
        }
    }
}
