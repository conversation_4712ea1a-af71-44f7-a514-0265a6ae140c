using ORMStandard.EntityClasses;
using SD.LLBLGen.Pro.LinqSupportClasses;
using UcpmApi.Query.Lookups;
using UcpmApi.Shared;
using UcpmTools;

namespace UcpmApi.Converter
{
    public class CountryConverter : BaseConverter
    {
        private readonly CountryQuery _CountryQuery;

        public CountryConverter(CountryQuery countryQuery)
        {
            _CountryQuery = countryQuery;
        }

        public async Task<Country> GetCountryByCode(string code)
        {
            CountryEntity country = await _CountryQuery.GetCountries()
                                                       .Where(c => c.CountryCodeIsoAlpha2 == code)
                                                       .SingleOrDefaultAsync();

            return ToView(country);
        }

        private static Country ToView(CountryEntity entity)
        {
            Country country = new();
            QuickReflection.CopyProps(entity, country);
            return country;
        }

        public async Task<List<Country>> GetAll()
        {
            List<Country> returnList = [];
            IEnumerable<CountryEntity> countries = await _CountryQuery.GetAll();

            foreach (CountryEntity country in countries)
            {
                returnList.Add(ToView(country));
            }

            return returnList;
        }
    }
}