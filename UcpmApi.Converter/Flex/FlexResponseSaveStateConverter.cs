using FlexLibrary;
using FlexLibrary.Models;
using LLBLGenHelper;
using ORMStandard;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using SD.LLBLGen.Pro.ORMSupportClasses;
using Ucpm.SourceGenerator.Abstraction;
using UcpmApi.BusinessModel.Flex.Validators;
using UcpmApi.Logging;
using UcpmApi.Query.Flex;
using UcpmApi.Shared;
using UcpmApi.Shared.Flex;
using UcpmApi.Shared.ValidationAbstraction;

namespace UcpmApi.Converter
{
    [CopyMethod<FlexInsuredExposurePolicyProspectEntity, FlexInsuredExposurePolicyProspect>]
    public partial class FlexResponseSaveStateConverter : BaseConverter
    {
        private readonly FlexResponseSaveStateQuery _flexResponseSaveStateQuery;

        public FlexResponseSaveStateConverter(FlexResponseSaveStateQuery flexResponseSaveStateQuery)
        {
            _flexResponseSaveStateQuery = flexResponseSaveStateQuery;
        }

        public async Task<IEnumerable<FlexResponseSaveState>> GetFlexResponseSaveStatesByInsuredUserGuid(
            Guid insuredUserGuid)
        {
            IEnumerable<FlexResponseSaveStateEntity> flexResponseSaveStates =
                await _flexResponseSaveStateQuery.GetFlexResponseSaveStatesByInsuredUserGuid(insuredUserGuid);
            return flexResponseSaveStates.Select(x => ToView(x));
        }

        public async Task<IEnumerable<FlexResponseSaveState>> GetFlexResponseSaveStatesByAgentGuid(Guid agentGuid)
        {
            IEnumerable<FlexResponseSaveStateEntity> flexResponseSaveStates =
                await _flexResponseSaveStateQuery.GetFlexResponseSaveStatesByAgentGuid(agentGuid);
            return flexResponseSaveStates.Select(ToView);
        }

        public async Task<IEnumerable<FlexResponseSaveState>> GetAllSaveStatesByAgentGuidAndFlexDefinition(
            Guid agentGuid, int flexDefinitionId)
        {
            IEnumerable<FlexResponseSaveStateEntity> flexResponseSaveStates =
                await _flexResponseSaveStateQuery.GetAllSaveStatesByAgentGuidAndFlexDefinition(agentGuid,
                    flexDefinitionId);
            return flexResponseSaveStates.Select(ToView);
        }

        public async Task<FlexResponseSaveState> GetFlexResponseSaveStateByGuid(Guid flexResponseGuid)
        {
            FlexResponseSaveStateEntity flexResponseSaveState =
                await _flexResponseSaveStateQuery.GetLatestByResponse(flexResponseGuid);

            if (flexResponseSaveState == null)
            {
                FlexResponseEntity flexResponse = new(flexResponseGuid);
                QuickAdapterAsync adapter = new(new DataAccessAdapter(), new DataLogger());
                PrefetchPath2 path = new(EntityType.FlexResponseEntity);
                IPrefetchPath2 linkPath = path.Add(FlexResponseEntity.PrefetchPathFlexResponseLink).SubPath;
                IPrefetchPath2 policyPath = linkPath.Add(FlexResponseLinkEntity.PrefetchPathPackage).SubPath;
                IPrefetchPath2 definitionPath = path.Add(FlexResponseEntity.PrefetchPathFlexDefinition).SubPath;
                IPrefetchPath2 publishedPath =
                    definitionPath.Add(FlexDefinitionEntity.PrefetchPathFlexPublishedVersion).SubPath;
                adapter.FetchEntity(flexResponse, path);

                FlexAnswerContainer container = new()
                {
                    FlexAnswers = []
                };

                if (flexResponse.FlexDefinition != null)
                {
                    FlexResponseSaveStateEntity newSaveState = new()
                    {
                        FlexResponseSaveStateGuid = Guid.NewGuid(),
                        FlexResponseGuid = flexResponseGuid,
                        FlexVersionGuid = flexResponse.FlexDefinition.FlexPublishedVersion.PublishedFlexVersionGuid,
                        SavedAt = DateTimeOffset.Now,
                        WasUserSubmit = false,
                        SavedData = AnswerSerialize.ToJson(container),
                        SavedBySecurityAccountGuid = Guid.Empty,
                    };

                    await adapter.SaveEntityAsync(newSaveState);
                    flexResponseSaveState = await _flexResponseSaveStateQuery.GetLatestByResponse(flexResponseGuid);

                    return ToView(flexResponseSaveState);
                }

                return null;
            }

            return ToView(flexResponseSaveState);
        }

        public async Task<FlexResponseSaveState> GetFlexResponseSaveStateByGuid(FlexResponseEntity flex)
        {
            FlexResponseSaveStateEntity flexResponseSaveState =
                flex.FlexResponseSaveState.OrderByDescending(d => d.SavedAt).FirstOrDefault();

            FlexAnswerContainer container = new()
            {
                FlexAnswers = []
            };

            if (flexResponseSaveState == null)
            {
                QuickAdapterAsync adapter = new(new DataAccessAdapter(), new DataLogger());
                FlexResponseSaveStateEntity newSaveState = new()
                {
                    FlexResponseSaveStateGuid = Guid.NewGuid(),
                    FlexResponseGuid = flex.FlexResponseGuid,
                    FlexVersionGuid = flex.FlexDefinition.FlexPublishedVersion.PublishedFlexVersionGuid,
                    SavedAt = DateTimeOffset.Now,
                    WasUserSubmit = false,
                    SavedData = AnswerSerialize.ToJson(container),
                    SavedBySecurityAccountGuid = Guid.Empty,
                };

                await adapter.SaveEntityAsync(newSaveState);
                flexResponseSaveState = await _flexResponseSaveStateQuery.GetLatestByResponse(flex.FlexResponseGuid);

                return ToView(flexResponseSaveState);
            }

            return ToView(flexResponseSaveState);
        }

        public async Task<FlexResponseSaveState> GetFlexResponseSaveStateByPackageGuid(Guid packageGuid,
            int flexDefinitionId)
        {
            FlexResponseSaveStateEntity flexResponseSaveState =
                await _flexResponseSaveStateQuery.GetFlexResponseSaveStateByPackageGuid(packageGuid);

            //if there is no flex response save state, we need to create one with the appropriate FKs
            if (flexResponseSaveState == null)
            {
                UnitOfWork2 work = new();
                QuickAdapterAsync adapter = new(new DataAccessAdapter(), new DataLogger());

                //Create new flex response guid
                Guid flexResponseGuid = Guid.NewGuid();
                FlexResponseEntity flexResponse = new()
                {
                    FlexResponseGuid = flexResponseGuid,
                    FlexDefinitionId = flexDefinitionId
                };

                //Save and refetch to stay in sync, refetch to include published version pathing
                await adapter.SaveEntityAsync(flexResponse);

                EntityCollection<FlexResponseEntity> collection = [];
                PrefetchPath2 path = new(EntityType.FlexResponseEntity);
                IPrefetchPath2 definitionPath = path.Add(FlexResponseEntity.PrefetchPathFlexDefinition).SubPath;
                IPrefetchPath2 publishedPath =
                    definitionPath.Add(FlexDefinitionEntity.PrefetchPathFlexPublishedVersion).SubPath;
                RelationPredicateBucket filter = new(FlexResponseFields.FlexResponseGuid == flexResponseGuid);

                QueryParameters queryParameters = new()
                {
                    CollectionToFetch = collection,
                    PrefetchPathToUse = path,
                    RelationsToUse = filter.Relations,
                    FilterToUse = filter.PredicateExpression,
                };

                await adapter.FetchEntityCollectionAsync(queryParameters);

                FlexResponseLinkEntity flexResponseLink = new()
                {
                    FlexResponseGuid = flexResponseGuid,
                    PackageGuid = packageGuid
                };

                work.AddForSave(flexResponseLink);

                FlexAnswerContainer container = new()
                {
                    FlexAnswers = []
                };

                FlexResponseSaveStateEntity newSaveState = new()
                {
                    FlexResponseSaveStateGuid = Guid.NewGuid(),
                    FlexResponseGuid = flexResponseGuid,
                    FlexVersionGuid = collection.FirstOrDefault().FlexDefinition.FlexPublishedVersion
                        .PublishedFlexVersionGuid,
                    SavedAt = DateTimeOffset.Now,
                    WasUserSubmit = false,
                    SavedData = AnswerSerialize.ToJson(container),
                    SavedBySecurityAccountGuid = Guid.Empty,
                };

                work.AddForSave(newSaveState);

                await adapter.CommitWorkAsync(work);

                flexResponseSaveState = await _flexResponseSaveStateQuery.GetLatestByResponse(flexResponseGuid);
            }

            return ToView(flexResponseSaveState);
        }

        public async Task<List<FlexResponseSaveState>> GetAllFlexResponseSaveStateByPackageGuid(Guid packageGuid)
        {
            EntityCollection<FlexResponseSaveStateEntity> flexResponseSaveStates =
                await _flexResponseSaveStateQuery.GetAllFlexResponseSaveStateByPackageGuid(packageGuid);

            List<FlexResponseSaveState> listFlexReponseSaveStates = [];

            if (flexResponseSaveStates != null)
            {
                foreach (FlexResponseSaveStateEntity entity in flexResponseSaveStates)
                {
                    listFlexReponseSaveStates.Add(ToView(entity));
                }
            }

            return listFlexReponseSaveStates;
        }

        public async Task<ValidationResult<FlexResponseSaveState>> SaveFlexResponseSaveState(
            FlexResponseSaveState flexResponseSaveState)
        {
            FlexResponseSaveState getFlexResponseSaveState =
                await GetFlexResponseSaveStateByGuid(flexResponseSaveState.FlexResponseGuid);

            //combine the getFlexResponseSaveState flex answers with the new flexResponseSaveState flex answers
            FlexAnswerContainer combinedAnswers = new()
            {
                FlexAnswers = getFlexResponseSaveState.Answers.FlexAnswers,
                InsuredGuid = getFlexResponseSaveState.Answers.InsuredGuid,
                IsCleared = getFlexResponseSaveState.Answers.IsCleared,
                SelectedDeductibles = getFlexResponseSaveState.Answers.SelectedDeductibles,
                SelectedLimits = getFlexResponseSaveState.Answers.SelectedLimits,
            };

            if (getFlexResponseSaveState.Answers.InsuredGuid == Guid.Empty)
            {
                combinedAnswers.InsuredGuid = flexResponseSaveState.Answers.InsuredGuid;
            }

            foreach (FlexAnswer answer in flexResponseSaveState.Answers.FlexAnswers)
            {
                FlexAnswer existingAnswer =
                    combinedAnswers.FlexAnswers.Find(x => x.QuestionDataName == answer.QuestionDataName);
                if (existingAnswer != null)
                {
                    combinedAnswers.FlexAnswers.Remove(existingAnswer);
                }

                if ((answer.Answer != null && answer.Answer.Length > 0) ||
                    (answer.ChildAnswers != null && answer.ChildAnswers.Count > 0))
                {
                    combinedAnswers.FlexAnswers.Add(answer);
                }
            }

            combinedAnswers.IsCleared = flexResponseSaveState.Answers.IsCleared;
            //assign the combined answers to the new flexResponseSaveState
            flexResponseSaveState.Answers = combinedAnswers;

            return await FlexResponseSaveStateValidator.ValidateThenSave(flexResponseSaveState);
        }

        public async Task<IEnumerable<FlexInsuredExposurePolicyProspect>> GetFlexInsuredExposurePolicyProspect(
            Guid flexResponseGuid)
        {
            IEnumerable<FlexInsuredExposurePolicyProspectEntity> flexInsuredExposurePolicyProspectEntities =
                await _flexResponseSaveStateQuery.GetFlexInsuredExposurePolicyProspect(flexResponseGuid);
            return flexInsuredExposurePolicyProspectEntities.Select(ToViewInsuredExposure);
        }

        public async Task<FlexResponseSaveState> GetLatestByFlexResponseGuid(Guid flexResponseGuid)
        {
            FlexResponseSaveStateEntity flexResponseSaveState =
                await _flexResponseSaveStateQuery.GetLatestByResponseSimple(flexResponseGuid);

            // Handle null entity
            return flexResponseSaveState == null
                ? CreateEmptyFlexResponseSaveState()
                : ToViewSimple(flexResponseSaveState);
        }

        private static FlexResponseSaveState ToViewSimple(FlexResponseSaveStateEntity entity)
        {
            // Add null check for the entity itself
            if (entity == null)
            {
                return CreateEmptyFlexResponseSaveState();
            }

            FlexResponseSaveState flexResponseSaveState = new()
            {
                FlexResponseGuid = entity.FlexResponseGuid,
                FlexResponseSaveStateGuid = entity.FlexResponseSaveStateGuid,
                // Add null safety for SavedData - this is likely the issue at line 333
                Answers = !string.IsNullOrEmpty(entity.SavedData)
                    ? FlexAnswerContainer.FromJson(entity.SavedData)
                    : new FlexAnswerContainer { FlexAnswers = [] },
                WasUserSubmit = entity.WasUserSubmit,
                SavedAt = entity.SavedAt,
                SavedBySecurityAccountGuid = entity.SavedBySecurityAccountGuid,
            };

            return flexResponseSaveState;
        }

        private static FlexResponseSaveState CreateEmptyFlexResponseSaveState()
        {
            return new FlexResponseSaveState
            {
                FlexResponseGuid = Guid.Empty,
                Answers = new FlexAnswerContainer { FlexAnswers = [] },
                WasUserSubmit = false,
                SavedAt = DateTimeOffset.MinValue,
                SavedBySecurityAccountGuid = Guid.Empty,
            };
        }

        private static FlexInsuredExposurePolicyProspect ToViewInsuredExposure(
            FlexInsuredExposurePolicyProspectEntity entity)
        {
            FlexInsuredExposurePolicyProspect prospect = new();
            CopyProps(entity, prospect);
            return prospect;
        }

        private static FlexResponseSaveState ToView(FlexResponseSaveStateEntity entity)
        {
            List<NewFlexRatingContainer> newFlexRatings = [];

            foreach (FlexVersionCarrierOfferEntity item in entity.FlexVersion.FlexVersionCarrierOffer)
            {
                newFlexRatings.Add(NewFlexRatingContainer.FromJson(item.RatingJson));
            }

            FlexVersionEntity publishedVersion = entity.FlexVersion.FlexDefinition.FlexPublishedVersion.FlexVersion;

            FlexResponseSaveState FlexResponseSaveState = new()
            {
                FlexResponseGuid = entity.FlexResponseGuid,
                FlexVersionGuid = publishedVersion.FlexVersionGuid,
                Answers = FlexAnswerContainer.FromJson(entity.SavedData),
                Questions = FlexQuestionContainer.FromJson(publishedVersion.SurveyJson),
                Ratings = newFlexRatings,
                WasUserSubmit = entity.WasUserSubmit,
                SavedAt = entity.SavedAt,
                SavedBySecurityAccountGuid = entity.SavedBySecurityAccountGuid,
            };

            if (entity.FlexResponse.FlexResponseLink.PackageGuid != Guid.Empty)
            {
                FlexResponseSaveState.IsRenewal =
                    entity.FlexResponse.FlexResponseLink.Package.PolicyProspect.Any(p =>
                        p.UcpmRenewalStatusId == (int)UcpmRenewalStatusEnum.Renewal);
                FlexResponseSaveState.PolicyProspectGuid = entity.FlexResponse.FlexResponseLink.Package.PolicyProspect
                    .OrderByDescending(d => d.RecordCreatedZoned).FirstOrDefault()?.PolicyProspectGuid ?? Guid.Empty;
                FlexResponseSaveState.Status = entity.FlexResponse.FlexResponseLink.Package.PolicyProspect
                    .OrderByDescending(d => d.RecordCreatedZoned).FirstOrDefault()?.CarrierSubmission
                    ?.FirstOrDefault(c => c.IsDeleted == false)?.PolicyStatus?.StatusForAgent ?? "Open";
            }

            return FlexResponseSaveState;
        }

        public async Task<FlexResponseSaveState> GetFlexResponseSaveStateWithDataOnly(Guid flexResponseGuid)
        {
            FlexResponseSaveStateEntity flexResponseSaveState =
                await _flexResponseSaveStateQuery.GetFlexResponseSaveStateWithDataOnly(flexResponseGuid);

            if (flexResponseSaveState == null)
            {
                FlexResponseEntity flexResponse = new(flexResponseGuid);
                QuickAdapterAsync adapter = new(new DataAccessAdapter(), new DataLogger());
                PrefetchPath2 path = new(EntityType.FlexResponseEntity);
                IPrefetchPath2 definitionPath = path.Add(FlexResponseEntity.PrefetchPathFlexDefinition).SubPath;
                IPrefetchPath2 publishedPath =
                    definitionPath.Add(FlexDefinitionEntity.PrefetchPathFlexPublishedVersion).SubPath;
                adapter.FetchEntity(flexResponse, path);

                FlexAnswerContainer container = new()
                {
                    FlexAnswers = []
                };

                if (flexResponse.FlexDefinition != null)
                {
                    FlexResponseSaveStateEntity newSaveState = new()
                    {
                        FlexResponseSaveStateGuid = Guid.NewGuid(),
                        FlexResponseGuid = flexResponseGuid,
                        FlexVersionGuid = flexResponse.FlexDefinition.FlexPublishedVersion.PublishedFlexVersionGuid,
                        SavedAt = DateTimeOffset.Now,
                        WasUserSubmit = false,
                        SavedData = AnswerSerialize.ToJson(container),
                        SavedBySecurityAccountGuid = Guid.Empty,
                    };

                    await adapter.SaveEntityAsync(newSaveState);
                    flexResponseSaveState = await _flexResponseSaveStateQuery.GetLatestByResponse(flexResponseGuid);

                    return ToViewSimple(flexResponseSaveState);
                }

                return null;
            }

            return ToViewSimple(flexResponseSaveState);
        }
    }
}