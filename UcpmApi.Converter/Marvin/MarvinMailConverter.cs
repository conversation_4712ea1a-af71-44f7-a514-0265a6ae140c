using HtmlAgilityPack;
using LLBLGenHelper;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using UcpmApi.BusinessModel.Filesystem;
using UcpmApi.Logging;
using UcpmApi.Query.Marvin;
using UcpmApi.Shared;

namespace UcpmApi.Converter
{
    public class MarvinMailConverter : BaseConverter
    {
        private readonly MarvinMailQuery _marvinMailQuery;
        private readonly DocInfoConverter _docInfoConverter;

        private readonly FileViewModelCollection _fileViewModelCollection;

        public MarvinMailConverter(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> marvinMailQuery, DocInfoConverter docInfoConverter, FileViewModelCollection fileViewModelCollection)
        {
            _marvinMailQuery = marvinMailQuery;
            _docInfoConverter = docInfoConverter;

            _fileViewModelCollection = fileViewModelCollection;
        }

        public MarvinMail ToView(MarvinMailPolicyEntity entity)
        {
            bool isEmpty = string.IsNullOrWhiteSpace(entity.FilledBody);
            string body = ReplaceDataTags(string.IsNullOrWhiteSpace(entity.FilledBody) ? entity.MarvinMailTemplate.TemplateBody : entity.FilledBody, entity);
            if (isEmpty)
            {
                body = AddFooter(body, entity);
            }

            string subject = ReplaceDataTags(string.IsNullOrWhiteSpace(entity.FilledSubject) ? entity.MarvinMailTemplate.TemplateSubject : entity.FilledSubject, entity);
            string bodyInnerText = GetInnerBodyText(body);
            return new MarvinMail
            {
                ApprovedForSend = entity.IsApprovedForSend,
                Attachments = _docInfoConverter?.GetDocInfoItemsBySource(entity.MarvinMailPolicyGuid).GetAwaiter().GetResult() ?? [],
                Body = body,
                BodyBeginning = bodyInnerText[..Math.Min(bodyInnerText.Length, 50)],
                DateToSend = entity.SendAtZoned,
                IsDeleted = entity.IsDeleted,
                MarvinMailPolicyGuid = entity.MarvinMailPolicyGuid,
                PolicyGuid = entity.PolicyProspectGuid,
                SendSuccessDate = entity.SendSuccessAtZoned,
                Subject = subject,
                Status = SetStatus(entity.SendSuccessAtZoned, entity.IsApprovedForSend, entity.IsDeleted),
                BodyInnerText = bodyInnerText,
                SourceMarvinMailTemplateGuid = entity.SourceMarvinMailTemplateGuid,
                TemplateName = entity.MarvinMailTemplate.TemplateName,
                TemplateCadenceDays = entity.MarvinMailTemplate.MarvinCadenceDays,
                AgentGuid = entity.PolicyProspect.Package.InsuredAgentHistory.AgentGuid,
                ProgramGuid = entity.PolicyProspect.Package.ProgramGuid,
                PackageGuid = entity.PolicyProspect.PackageGuid,
                PolicyTypeId = entity.MarvinMailTemplate.PolicyTypeId == 0 ? null : entity.MarvinMailTemplate.PolicyTypeId,
                PinDate = entity.PinDate
            };
        }

        private string AddFooter(string body, MarvinMailPolicyEntity entity)
        {
            string footer = entity.PolicyProspect.Package.PrimaryRepEmployee?.EmployeeMailSetting?.MailFooter;
            return $"{body}<br/>{footer}";
        }

        public async Task<MarvinMail> UpdateMarvinMailAsync(MarvinMail marvinMail)
        {
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            if (marvinMail.MarvinMailPolicyGuid == Guid.Empty)
            {
                Guid mailGuid = Guid.NewGuid();

                MarvinMailPolicyEntity mailEntity = new()
                {
                    MarvinMailPolicyGuid = mailGuid,
                    PolicyProspectGuid = marvinMail.PolicyGuid,
                    SendAtZoned = marvinMail.DateToSend,
                    IsDeleted = marvinMail.IsDeleted,
                    IsApprovedForSend = marvinMail.ApprovedForSend,
                    SourceMarvinMailTemplateGuid = marvinMail.SourceMarvinMailTemplateGuid,
                };

                if (marvinMail.SendSuccessDate == DateTimeOffset.MinValue)
                {
                    quickAdapter.SetNull(mailEntity, nameof(mailEntity.SendSuccessAtZoned));
                }
                else
                {
                    mailEntity.SendSuccessAtZoned = marvinMail.SendSuccessDate;
                }
                if (marvinMail.PinDate == DateTime.MinValue)
                {
                    quickAdapter.SetNull(mailEntity, nameof(mailEntity.PinDate));
                }
                else
                {
                    mailEntity.PinDate = marvinMail.PinDate;
                }

                if (marvinMail.SourceMarvinMailTemplateGuid == Guid.Empty)
                {
                    mailEntity.FilledSubject = marvinMail.Subject.Trim();
                    mailEntity.FilledBody = marvinMail.Body.Trim();
                }

                await quickAdapter.SaveEntityAsync(mailEntity);

                marvinMail.MarvinMailPolicyGuid = mailGuid;
            }
            else
            {
                MarvinMailPolicyEntity marvinMailEntity = marvinMail.MarvinMailPolicyGuid == Guid.Empty ? new() : await _marvinMailQuery.GetMarvinPolicyMailById(marvinMail.MarvinMailPolicyGuid);
                marvinMailEntity.FilledBody = marvinMail.Body.Trim();
                marvinMailEntity.FilledSubject = marvinMail.Subject.Trim();
                marvinMailEntity.SendAtZoned = marvinMail.DateToSend;
                marvinMailEntity.IsDeleted = marvinMail.IsDeleted;
                marvinMailEntity.IsApprovedForSend = marvinMail.ApprovedForSend;
                marvinMailEntity.SourceMarvinMailTemplateGuid = marvinMail.SourceMarvinMailTemplateGuid;
                if (marvinMail.SendSuccessDate == DateTimeOffset.MinValue)
                {
                    quickAdapter.SetNull(marvinMailEntity, nameof(marvinMailEntity.SendSuccessAtZoned));
                }
                else
                {
                    marvinMailEntity.SendSuccessAtZoned = marvinMail.SendSuccessDate;
                }

                await quickAdapter.SaveEntityAsync(marvinMailEntity);
            }

            if (marvinMail.Attachments.Any())
            {
                foreach (DocInfo docInfo in marvinMail.Attachments)
                {
                    await _fileViewModelCollection.AddStreamToFilesystem(docInfo.SourceGuid == Guid.Empty ? marvinMail.MarvinMailPolicyGuid : docInfo.SourceGuid, docInfo.StreamData, docInfo.DocName, docInfo.SavedBy);
                }
            }

            return await GetMarvinMailByIdAsync(marvinMail.MarvinMailPolicyGuid);
        }

        public async Task<MarvinMail> GetMarvinMailByIdAsync(Guid marvinMailGuid)
        {
            return ToView(await _marvinMailQuery.GetMarvinPolicyMailById(marvinMailGuid));

        }

        public async Task<List<MarvinMail>> GetAllPendingMarvinEmailsAsync(Guid employeeGuid)
        {
            return (await _marvinMailQuery.GetAllPendingMarvinEmails(employeeGuid)).Select(s => ToView(s))
            .OrderBy(o => o.DateToSend)
            .ToList();
        }


        public async Task<List<MarvinMail>> GetMarvinMailByPolicyAsync(Guid policyGuid)
        {
            return (await _marvinMailQuery.GetMarvinMailByPolicy(policyGuid)).Select(s => ToView(s)).ToList();
        }



        #region static private
        private static string GetInnerBodyText(string htmlBody)
        {
            HtmlDocument htmlDocument = new();
            htmlDocument.LoadHtml(htmlBody.Replace("&nbsp;", " "));
            return htmlDocument.DocumentNode.InnerText;
        }

        private static string ReplaceDataTags(string text, MarvinMailPolicyEntity marvinMailPolicy)
        {
            return text.Replace("Agent.AgentName", marvinMailPolicy.PolicyProspect.Package.InsuredAgentHistory.Agent.AgentFirstName)
                .Replace("Carrier.CarrierName", marvinMailPolicy.PolicyProspect.CarrierSubmission.FirstOrDefault(f => !string.IsNullOrWhiteSpace(f.IssuingCompany?.IssuingName))?.IssuingCompany.IssuingName)
                .Replace("Insured.Name", marvinMailPolicy.PolicyProspect.Package.InsuredAgentHistory.Insured.Name)
                .Replace("Policy.EffectiveDate", marvinMailPolicy.PolicyProspect.EffectiveDateZoned.ToString("d"));
        }
        private static string SetStatus(DateTimeOffset sendDate, bool isApproved, bool isDeleted)
        {
            return sendDate > DateTimeOffset.MinValue ? "Sent" : isApproved ? "Approved" : isDeleted ? "Deleted" : "Pending";
        }

        #endregion
    }
}