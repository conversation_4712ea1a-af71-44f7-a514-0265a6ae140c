using MoreLinq;
using ORMStandard.EntityClasses;
using UcpmApi.Query.Policy;
using UcpmApi.Query.SurplusLines;
using UcpmApi.Query.SurplusSolutions;
using UcpmApi.Shared;

namespace UcpmApi.Converter
{
    public class SurplusSolutionsInsuredOverviewConverter : BaseConverter
    {
        private readonly SurplusSolutionsInsuredOverviewQuery _surplusSolutionsInsuredOverviewQuery;
        private readonly SurplusLinesPolicyQuery _surplusLinesPolicyQuery;
        private readonly SurplusLinesStateQuery _surplusLinesStateQuery;

        public SurplusSolutionsInsuredOverviewConverter(SurplusSolutionsInsuredOverviewQuery surplusSolutionsInsuredOverviewQuery,
            SurplusLinesPolicyQuery surplusLinesPolicyquery, SurplusLinesStateQuery surplusLinesStateQuery)
        {
            _surplusSolutionsInsuredOverviewQuery = surplusSolutionsInsuredOverviewQuery;
            _surplusLinesPolicyQuery = surplusLinesPolicyquery;
            _surplusLinesStateQuery = surplusLinesStateQuery;
        }

        public async Task<IEnumerable<SurplusSolutionsInsuredOverview>> GetAccountsByEmployee(Guid employeeGuid)
        {
            IEnumerable<SurplusLinesPolicyEntity> options = await _surplusLinesPolicyQuery.GetSurplusLinesPoliciesByEmployeeGuid(employeeGuid);
            return ToUcpmSideView(options);      
        }

        [Obsolete] //SLS used this in the past when its using carriersubmission
        public async Task<IEnumerable<SurplusLinesFilingBaseModel>> GetSurplusLinesFiling(Guid employeeGuid, DateTime endDate, SurplusLinesTableStatusEnum surplusLinesTableStatusEnum, string stateCode = "")
        {
            IEnumerable<CarrierSubmissionEntity> options = await _surplusLinesPolicyQuery.GetSurplusLinesFiling(employeeGuid, endDate, surplusLinesTableStatusEnum, stateCode);
            return ToSurplusLinesFilingPolicyCollection(options, employeeGuid);
        }

        //SLS is now using the SurplusLines schema
        public async Task<IEnumerable<SurplusLinesFilingBaseModel>> GetSurplusLinesPolicyFiling(Guid employeeGuid, DateTime endDate, SurplusLinesTableStatusEnum surplusLinesTableStatusEnum, string stateCode = "")
        {
            IEnumerable<SurplusLinesPolicyEntity> options = await _surplusLinesPolicyQuery.GetSurplusLinesPolicyFiling(employeeGuid, endDate, surplusLinesTableStatusEnum, stateCode);
            IEnumerable<SurplusLinesStateEntity> state = _surplusLinesStateQuery.GetSurplusLinesStates();
            return ToSurplusLinesFilingPolicyCollection(options, state, employeeGuid);
        }

        public async Task<IEnumerable<SurplusSolutionsInsuredOverview>> GetSurplusLinesPoliciesWithDocs(Guid employeeGuid)
        {
            IEnumerable<SurplusLinesPolicyEntity> options = await _surplusLinesPolicyQuery.GetSurplusLinesPoliciesWithDocs(employeeGuid);
            return ToUcpmSideView(options);
        }

        public async Task<IEnumerable<SurplusSolutionsInsuredOverview>> GetSurplusSolutionsInsuredOverviewsByUnderwriterGuid(Guid agentGuid)
        {
            IEnumerable<SurplusLinesPolicyEntity> policies = await _surplusLinesPolicyQuery.GetSurplusLinesPoliciesByUnderwriterGuid(agentGuid);
            return ToSurplusLinesView(policies);
        }

        private IEnumerable<SurplusSolutionsInsuredOverview> ToUcpmSideView(IEnumerable<SurplusLinesPolicyEntity> policies)
        {
            List<SurplusSolutionsInsuredOverview> overviewList = [];
            foreach (SurplusLinesPolicyEntity policy in policies.OrderBy(o => o.SurplusLinesInvoice.First().RecordCreatedZoned))
            {
                foreach (var invoice in policy.SurplusLinesInvoice)
                {                   
                    SurplusLinesInvoiceItemEntity invoiceItem = invoice.SurplusLinesInvoiceItem
                        .Where(i => i.TransactionTypeId == (int)TransactionTypeEnum.PremiumNew || 
                                    i.TransactionTypeId == (int)TransactionTypeEnum.Endorsement).FirstOrDefault();
                    SurplusSolutionsInsuredOverview surplusSolutionsInsuredOverview = new()
                    {
                        ProcessRepName = $"{policy.Employee.FirstName} {policy.Employee.LastName}",
                        EndorsementGuid = invoice.SurplusLinesEndorsementGuid,
                        InsuredName = policy.SurplusLinesInsured.InsuredName,
                        InsuredGuid = policy.SurplusLinesInsuredGuid,
                        EffectiveDate = policy.EffectiveDate,
                        PolicyTypeName = policy.SurplusLinesPolicyType.PolicyType,
                        Premium = invoiceItem?.GrossAmount ?? 0,
                        TotalDue = invoice.SurplusLinesInvoiceItem.Sum(i => i.GrossAmount),
                        PolicyStatus = policy.SurplusLinesPolicyStatus.StatusName,
                        RequestStatus = policy.SurplusLinesRequestStatus.RequestStatus,
                        InvoiceGuid = invoice.SurplusLinesInvoiceGuid,
                        ReceivedDate = invoice.RecordCreatedZoned,
                        SurplusLinesPolicyGuid = policy.SurplusLinesPolicyGuid,
                        SurplusLinesCustomerType =  new()
                        {
                            CustomerTypeId = policy.SurplusLinesInsured.SurplusLinesCustomerType?.SurplusLinesCustomerTypeId ?? 0,
                            CustomerTypeName = policy.SurplusLinesInsured.SurplusLinesCustomerType?.SurplusLinesCustomerTypeName ?? ""
                        },  
                    };
                    overviewList.Add(surplusSolutionsInsuredOverview);
                }
            }
            return overviewList;
        }

        private IEnumerable<SurplusSolutionsInsuredOverview> ToSurplusLinesView(IEnumerable<SurplusLinesPolicyEntity> policies)
        {
            List<SurplusSolutionsInsuredOverview> overviewList = [];

            foreach (SurplusLinesPolicyEntity policy in policies.OrderBy(o => o.SurplusLinesInvoice.First().RecordCreatedZoned))
            {
                foreach (var invoice in policy.SurplusLinesInvoice)
                {
                    SurplusLinesInvoiceItemEntity invoiceItem = invoice.SurplusLinesInvoiceItem
                        .Where(i => i.TransactionTypeId == (int)TransactionTypeEnum.PremiumNew ||
                                    i.TransactionTypeId == (int)TransactionTypeEnum.Endorsement).FirstOrDefault();

                    SurplusSolutionsInsuredOverview surplusSolutionsInsuredOverview = new()
                    {
                        ProcessRepName = policy.SurplusLinesUnderwriter.UserName,
                        EndorsementGuid = invoice.SurplusLinesEndorsementGuid,
                        InsuredName = policy.SurplusLinesInsured.InsuredName,
                        InsuredGuid = policy.SurplusLinesInsuredGuid,
                        EffectiveDate = policy.EffectiveDate,
                        PolicyTypeName = policy.SurplusLinesPolicyType.PolicyType,
                        Premium = invoiceItem?.GrossAmount ?? 0,
                        TotalDue = invoice.SurplusLinesInvoiceItem.Sum(i => i.GrossAmount),
                        PolicyStatus = policy.SurplusLinesPolicyStatus.StatusName,
                        RequestStatus = policy.SurplusLinesRequestStatus.RequestStatus,
                        InvoiceGuid = invoice.SurplusLinesInvoiceGuid,
                        ReceivedDate = invoice.RecordCreatedZoned,
                        SurplusLinesPolicyGuid = policy.SurplusLinesPolicyGuid,
                        NeedByDate = policy.NeedByDate,
                    };

                    overviewList.Add(surplusSolutionsInsuredOverview);
                }

                foreach (var endorsement in policy.SurplusLinesEndorsement)
                { 
                    foreach (var invoice in endorsement.SurplusLinesInvoice)
                    {
                        SurplusLinesInvoiceItemEntity invoiceItem = invoice.SurplusLinesInvoiceItem
                            .Where(i => i.TransactionTypeId == (int)TransactionTypeEnum.PremiumNew ||
                                        i.TransactionTypeId == (int)TransactionTypeEnum.Endorsement).FirstOrDefault();

                        SurplusSolutionsInsuredOverview surplusSolutionsInsuredOverview = new()
                        {
                            ProcessRepName = policy.SurplusLinesUnderwriter.UserName,
                            EndorsementGuid = invoice.SurplusLinesEndorsementGuid,
                            InsuredName = policy.SurplusLinesInsured.InsuredName,
                            InsuredGuid = policy.SurplusLinesInsuredGuid,
                            EffectiveDate = policy.EffectiveDate,
                            PolicyTypeName = policy.SurplusLinesPolicyType.PolicyType,
                            Premium = invoiceItem?.GrossAmount ?? 0,
                            TotalDue = invoice.SurplusLinesInvoiceItem.Sum(i => i.GrossAmount),
                            PolicyStatus = policy.SurplusLinesPolicyStatus.StatusName,
                            RequestStatus = endorsement.SurplusLinesRequestStatus.RequestStatus,
                            InvoiceGuid = invoice.SurplusLinesInvoiceGuid,
                            ReceivedDate = invoice.RecordCreatedZoned,
                            SurplusLinesPolicyGuid = policy.SurplusLinesPolicyGuid
                        };

                        overviewList.Add(surplusSolutionsInsuredOverview);
                    }
                }
            }

            return overviewList;
        }

        [Obsolete]
        private IEnumerable<SurplusLinesFilingBaseModel> ToSurplusLinesFilingPolicyCollection(IEnumerable<CarrierSubmissionEntity> upcomingSurplusLinesFiling, Guid employeeGuid = default)
        {
            List<SurplusLinesFilingBaseModel> result = [];
            foreach (CarrierSubmissionEntity submission in upcomingSurplusLinesFiling)
            {
                SurplusLinesFilingBaseModel surplusLinesFilingBaseModel = new();
                InsuredEntity insured = submission.PolicyProspect.Package.InsuredAgentHistory.Insured;
                surplusLinesFilingBaseModel.InsuredGuid = insured.InsuredGuid;
                surplusLinesFilingBaseModel.InsuredName = insured.Name;
                surplusLinesFilingBaseModel.SurplusLinesPolicyGuid = submission.PolicyProspectGuid;
                surplusLinesFilingBaseModel.CarrierSubmissionGuid = submission.CarrierSubmissionGuid;
                surplusLinesFilingBaseModel.DateSettingPk = surplusLinesFilingBaseModel.CarrierSubmissionGuid;

                surplusLinesFilingBaseModel.PolicyNumber = string.IsNullOrWhiteSpace(submission.UcpmPolicyNumber) ? "Unknown" : submission.UcpmPolicyNumber;
                surplusLinesFilingBaseModel.StateCode = submission.SurplusLinesTaxState;
                surplusLinesFilingBaseModel.FilingDate = submission.SldueDateZoned;
                surplusLinesFilingBaseModel.PolicyEffectiveDate = submission.PolicyProspect.EffectiveDateZoned.Date;
                surplusLinesFilingBaseModel.EmployeeName = submission.SurplusLinesState.SurplusLinesState.Employee.GetFullName();
                SurplusLinesStateEntity surplusLinesState = submission.SurplusLinesState?.SurplusLinesState;
                if (surplusLinesState != null && employeeGuid != default)
                {
                    if (surplusLinesState.AssignedToEmployeeGuid == employeeGuid)
                    {
                        surplusLinesFilingBaseModel.Representative = "Primary";
                    }
                    else if (surplusLinesState.SecondaryAssignedEmployeeGuid == employeeGuid)
                    {
                        surplusLinesFilingBaseModel.Representative = "Secondary";
                    }
                    else
                    {
                        surplusLinesFilingBaseModel.Representative = string.Empty;
                    }
                }
                else
                {
                    surplusLinesFilingBaseModel.Representative = string.Empty;
                }
                surplusLinesFilingBaseModel.EndorsementEffectiveDate = "N/A";

                surplusLinesFilingBaseModel.MinSlFilingDate = surplusLinesFilingBaseModel.PolicyEffectiveDate.AddDays(-15).Date.ToString("yyyy-MM-dd");
                surplusLinesFilingBaseModel.Type = "Policy";
                IEnumerable<InvoiceEntity> invoices = submission.CarrierSubmissionOption.SelectMany(i => i.Invoice).Where(r => r.EndorsementGuid == Guid.Empty && r.IsInvoiced);
                surplusLinesFilingBaseModel.Premium = invoices.SelectMany(i => i.InvoiceItem).Where(i => i.TransactionType.TransactionGroupId == (int)TransactionGroupEnum.Premium).Sum(i => i.GrossAmount);
                surplusLinesFilingBaseModel.InvoiceGuid = invoices.FirstOrDefault()?.InvoiceGuid ?? Guid.Empty;
                surplusLinesFilingBaseModel.IsPolicy = true;
                if (submission.CarrierSubmissionSurplusLine != null && !submission.CarrierSubmissionSurplusLine.IsNew)
                {
                    surplusLinesFilingBaseModel.RealFilingDate = submission.CarrierSubmissionSurplusLine.ActualFilingDate;
                }
                else
                {
                    surplusLinesFilingBaseModel.RealFilingDate = surplusLinesFilingBaseModel.FilingDate.Date;
                }

                result.Add(surplusLinesFilingBaseModel);
            }
            return result;
        }

        private IEnumerable<SurplusLinesFilingBaseModel> ToSurplusLinesFilingPolicyCollection(IEnumerable<SurplusLinesPolicyEntity> surplusLinesPolicies, IEnumerable<SurplusLinesStateEntity> state, Guid employeeGuid = default)
        {
            List<SurplusLinesFilingBaseModel> result = [];
            foreach (SurplusLinesPolicyEntity policy in surplusLinesPolicies)
            {
                SurplusLinesFilingBaseModel surplusLinesFilingBaseModel = new();
                SurplusLinesInsuredEntity insured = policy.SurplusLinesInsured;
                surplusLinesFilingBaseModel.InsuredGuid = insured.SurplusLinesInsuredGuid;
                surplusLinesFilingBaseModel.InsuredName = insured.InsuredName;
                surplusLinesFilingBaseModel.SurplusLinesPolicyGuid = policy.SurplusLinesPolicyGuid; 

                surplusLinesFilingBaseModel.PolicyNumber = string.IsNullOrWhiteSpace(policy.PolicyNumber) ? "Unknown" : policy.PolicyNumber;
                surplusLinesFilingBaseModel.StateCode = insured.StateCode;
                surplusLinesFilingBaseModel.FilingDate = policy.NeedByDate;
                surplusLinesFilingBaseModel.PolicyEffectiveDate = policy.EffectiveDate;
                surplusLinesFilingBaseModel.EmployeeName = policy.Employee.GetFullName();         
                SurplusLinesStateEntity surplusLinesState =  state.FirstOrDefault(s => s.StateCode == insured.StateCode); 
                //SurplusLinesStateEntity surplusLinesState = insured.State.SurplusLinesState; prefetch is not working here 

                if (surplusLinesState != null && employeeGuid != default)
                {
                    if (surplusLinesState.AssignedToEmployeeGuid == employeeGuid)
                    {
                        surplusLinesFilingBaseModel.Representative = "Primary";
                    }
                    else if (surplusLinesState.SecondaryAssignedEmployeeGuid == employeeGuid)
                    {
                        surplusLinesFilingBaseModel.Representative = "Secondary";
                    }
                    else
                    {
                        surplusLinesFilingBaseModel.Representative = string.Empty;
                    }
                }
                else
                {
                    surplusLinesFilingBaseModel.Representative = string.Empty;
                }
                surplusLinesFilingBaseModel.EndorsementEffectiveDate = "N/A";

                surplusLinesFilingBaseModel.MinSlFilingDate = surplusLinesFilingBaseModel.PolicyEffectiveDate.AddDays(-15).Date.ToString("yyyy-MM-dd");
                surplusLinesFilingBaseModel.Type = "Policy";

                IEnumerable<SurplusLinesInvoiceEntity> invoices = policy.SurplusLinesInvoice;
                surplusLinesFilingBaseModel.Premium = invoices.SelectMany(i => i.SurplusLinesInvoiceItem).Where(i => i.TransactionType.TransactionGroupId == (int)TransactionGroupEnum.Premium).Sum(i => i.GrossAmount);
                surplusLinesFilingBaseModel.InvoiceGuid = invoices.FirstOrDefault()?.SurplusLinesInvoiceGuid ?? Guid.Empty;
                surplusLinesFilingBaseModel.IsPolicy = true;
                if (policy != null && !policy.IsNew)
                {
                    surplusLinesFilingBaseModel.RealFilingDate = policy.EffectiveDate;
                }
                else
                {
                    surplusLinesFilingBaseModel.RealFilingDate = surplusLinesFilingBaseModel.FilingDate.Date;
                }

                result.Add(surplusLinesFilingBaseModel);
            }
            return result;
        }
    }
}
