using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using UcpmApi.Query.Dcat;
using UcpmApi.Shared.Coverage;
using UcpmApi.Shared.Policy;
using UcpmTools;

namespace UcpmApi.Converter.Dcat
{
    public class DcatCoverageConverter : BaseConverter
    {
        private readonly DcatCoverageQuery _dcatCoverageQuery;

        public DcatCoverageConverter(DcatCoverageQuery dcatCoverageQuery) 
        { 
            _dcatCoverageQuery = dcatCoverageQuery;
        }

        public async Task<List<CoveragePart>> GetAllCoverageParts()
        {
            EntityCollection<CoveragePartEntity> entities = await _dcatCoverageQuery.GetAllCoverageParts();

            return entities.Select(ToViewCoveragePart).ToList();
        }

        public async Task<List<PolicyDcatNote>> GetNotesByPolicy(Guid policyProspectGuid) 
        { 
            EntityCollection<PolicyDcatNoteEntity> entites = await _dcatCoverageQuery.GetNotesByPolicy(policyProspectGuid);

            return entites.Select(ToViewDcatNote).ToList();
        }

        private PolicyDcatNote ToViewDcatNote(PolicyDcatNoteEntity entity) 
        { 
            PolicyDcatNote note = new PolicyDcatNote();

            if (entity != null)
            {
                QuickReflection.CopyProps(entity, note);
            }
            return note;
        }
        private CoveragePart ToViewCoveragePart(CoveragePartEntity entity)
        {
            CoveragePart coverage = new();

            if (entity != null)
            {
                QuickReflection.CopyProps(entity, coverage);
            }
            SetCoveragePartComponent(entity, coverage);
            return coverage;
        }

        private void SetCoveragePartComponent(CoveragePartEntity entity, CoveragePart coveragePart)
        {
            if (entity.CoveragePartComponent != null && entity.CoveragePartComponent.Count > 0)
            {
                coveragePart.CoveragePartComponent ??= [];
                foreach (CoveragePartComponentEntity coveragePartComponentEntity in entity.CoveragePartComponent)
                {
                    CoveragePartComponent coveragePartComponent = new();
                    QuickReflection.CopyProps(coveragePartComponentEntity, coveragePartComponent);
                    coveragePart.CoveragePartComponent.Add(coveragePartComponent);
                }
            }
        }
    }
}
