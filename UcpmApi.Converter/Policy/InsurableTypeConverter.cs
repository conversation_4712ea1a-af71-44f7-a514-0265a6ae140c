using ORMStandard.EntityClasses;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UcpmApi.Query.Policy;
using UcpmApi.Shared.Policy;
using UcpmTools;

namespace UcpmApi.Converter.Policy
{
    public class InsurableTypeConverter : BaseConverter
    {
        private readonly InsurableTypeQuery _insurableTypeQuery;

        public InsurableTypeConverter(InsurableTypeQuery insurableTypeQuery)
        {
            _insurableTypeQuery = insurableTypeQuery;
        }

        public async Task<List<InsurableType>> GetInsurableTypes()
        {
            IEnumerable<InsurableTypeEntity> entities =
                await _insurableTypeQuery.GetInsurableTypes();

            return entities.Select(ToView).ToList();
        }

        public InsurableType ToView(InsurableTypeEntity entity)
        {
            InsurableType type = new InsurableType();
            QuickReflection.CopyProps(entity, type);
            return type;
        }
    }
}
