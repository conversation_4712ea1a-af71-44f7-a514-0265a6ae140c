using ORMStandard.EntityClasses;
using UcpmApi.Query.Coverage;
using UcpmApi.Shared.Coverage;
using UcpmTools;

namespace UcpmApi.Converter.Coverage
{
    public class DcatTemplateConverter : BaseConverter
    {
        private readonly DcatTemplateQuery _DcatTemplateQuery;

        public DcatTemplateConverter(DcatTemplateQuery dcatTemplateQuery)
        {
            _DcatTemplateQuery = dcatTemplateQuery;
        }

        public async Task<IEnumerable<DcatTemplate>> GetAllDcatTemplates()
        {
            IEnumerable<DcatTemplateEntity> dcatTemplateEntities = await _DcatTemplateQuery.GetDcatTemplates();

            return dcatTemplateEntities.Select(ToView);
        }

        public async Task<DcatTemplate> GetAllDcatTemplate(int dcatTemplateId)
        {
            DcatTemplateEntity dcatTemplateEntity = await _DcatTemplateQuery.GetDcatTemplate(dcatTemplateId);

            return ToView(dcatTemplateEntity);
        }

        public async Task<DcatTemplate> GetLastDcatTemplate()
        {
            DcatTemplateEntity dcatTemplateEntity = await _DcatTemplateQuery.GetLastDcatTemplate();

            return ToView(dcatTemplateEntity);
        }

        private static DcatTemplate ToView(DcatTemplateEntity dcatTemplateEntity)
        {
            DcatTemplate dcatTemplate = new();

            if (dcatTemplateEntity != null)
            { 
                QuickReflection.CopyProps(dcatTemplateEntity, dcatTemplate);
            }

            return dcatTemplate;
        }

    }
}