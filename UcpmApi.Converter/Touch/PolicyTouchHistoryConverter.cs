using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using UcpmApi.Query.Touch;
using UcpmApi.Shared;
using UcpmApi.Shared.Task;
using UcpmTools;

namespace UcpmApi.Converter
{
    public class PolicyTouchHistoryConverter : BaseConverter
    {
        private readonly PolicyTouchHistoryQuery _policyTouchHistoryQuery;

        public PolicyTouchHistoryConverter(PolicyTouchHistoryQuery policytouchhistoryQuery)
        {
            _policyTouchHistoryQuery = policytouchhistoryQuery;
        }

        public async Task<List<PolicyTouchHistory>> GetPendingPolicyTouchHistoryForDailyMinutes(DateTimeOffset dateToRun)
        {
            IEnumerable<PolicyTouchHistoryEntity> policyTouchHistory = await _policyTouchHistoryQuery.GetPendingPolicyTouchHistoryForDailyMinutes(dateToRun);
            return policyTouchHistory.Select(ToView).ToList();
        }

        private static PolicyTouchHistory ToView(PolicyTouchHistoryEntity entity)
        {
            PolicyTouchHistory policyTouchHistory = new();
            QuickReflection.CopyProps(entity, policyTouchHistory);
            return policyTouchHistory;
        }
    }
}
