using ORMStandard.EntityClasses;
using UcpmApi.Query.Employee;
using UcpmApi.Shared;
using UcpmTools;

namespace UcpmApi.Converter
{
    public class PersonalGoalConverter : BaseConverter
    {
        private readonly PersonalGoalQuery _personalGoalQuery;

        public PersonalGoalConverter(PersonalGoalQuery personalGoalQuery)
        {
            _personalGoalQuery = personalGoalQuery;
        }

        private IEnumerable<PersonalGoal> ToView(IEnumerable<PersonalGoalEntity> goals)
        {
            List<PersonalGoal> views = [];
            foreach (PersonalGoalEntity goalEntity in goals)
            {
                PersonalGoal goal = new();
                QuickReflection.CopyProps(goalEntity, goal);
                goal.GoalName = goalEntity.PersonalGoalType.PersonalGoalTypeName;
                views.Add(goal);
            }
            return views;
        }

        public async Task<IEnumerable<PersonalGoal>> GetPersonalGoalsByEmployee(Guid employeeGuid)
        {
            IEnumerable<PersonalGoalEntity> goals = await _personalGoalQuery.GetPersonalGoalsByEmployee(employeeGuid);
            return ToView(goals);
        }
    }
}
