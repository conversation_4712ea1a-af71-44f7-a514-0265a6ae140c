using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using SD.LLBLGen.Pro.LinqSupportClasses;
using UcpmApi.Query.Vetting;
using UcpmApi.Shared;
using UcpmApi.Shared.Vetting;
using UcpmTools;

namespace UcpmApi.Converter
{
    public class VettingProcessConverter : BaseConverter
    {
        private readonly VettingProcessQuery _processQuery;

        public VettingProcessConverter(VettingProcessQuery processQuery)
        {
            _processQuery = processQuery;
        }

        public async Task<VettingProcess> GetProcessById(int processId)
        {
            VettingProcessEntity process = await _processQuery.GetProcesses()
                .Where(p => p.ProcessId == processId)
                .SingleOrDefaultAsync();

            return ToView(process);
        }

        public async Task<IEnumerable<VerificationProfile>> GetAllVeriforceVerificationProfiles()
        {
            EntityCollection<VettingProcessEntity> vettingProcessEntities = await _processQuery.GetAllVeriforce();

            return vettingProcessEntities.Select(ToVerificationProfileView).ToList();
        }
        
        public async Task<IEnumerable<VerificationProfile>> GetAllVeriforceWithPrefetchByEvaluator(Guid evaluatorGuid)
        {
            EntityCollection<VettingProcessEntity> vettingProcessEntities = await _processQuery.GetAllVeriforceWithPrefetchByEvaluator(evaluatorGuid);

            return vettingProcessEntities.Select(ToVerificationProfileView).ToList();
        }

        public static VettingProcess ToView(VettingProcessEntity entity)
        {
            VettingProcess process = new();
            if (entity != null)
            {
                QuickReflection.CopyProps(entity, process);
            }

            return process;
        }

        private static VerificationProfile ToVerificationProfileView(VettingProcessEntity entity)
        {
            VerificationProfile verificationProfile = new();
            QuickReflection.CopyProps(entity, verificationProfile);
            verificationProfile.BulletedDescription = verificationProfile.Description.Split('|');
            verificationProfile.IsUserEditable = verificationProfile.ProcessId != 1;

            if(entity.ProcessInstance != null)
            {
                List<ProcessInstance> processInstance = new();
                QuickReflection.CopyProps(entity.ProcessInstance, processInstance);
            }

            if(entity.EvaluatorProcess != null)
            {
                List<EvaluatorProcess> evaluatorProcess = new();
                QuickReflection.CopyProps(entity.EvaluatorProcess, evaluatorProcess);
            }
            return verificationProfile;
        }
    }
}
