using ORMStandard.EntityClasses;
using UcpmApi.Command.Message;
using UcpmApi.Command.Messaging;
using UcpmApi.Query.Messaging;
using UcpmApi.Shared.Api;
using UcpmApi.Shared.Messaging;
using UcpmTools;
namespace UcpmApi.Converter;

public class ChannelConverter : BaseConverter
{
    private readonly ChannelQuery _ChannelQuery;

    public ChannelConverter(ChannelQuery channelQuery)
    {
        _ChannelQuery = channelQuery;
    }

    public async Task<Channel> GetChannelByName(string channelName)
    {
        ChannelEntity channelEntity = _ChannelQuery.GetChannelByChannelName(channelName);
        return ToView(channelEntity);
    }

    private Channel ToView(ChannelEntity entity)
    {
        Channel channel = new();

        if (entity != null)
        {
            QuickReflection.CopyProps(entity, channel);
        }

        return channel;
    }
}