using ORMStandard.EntityClasses;
using SD.LLBLGen.Pro.LinqSupportClasses;
using UcpmApi.Query.Doc;
using UcpmApi.Shared;
using UcpmTools;

namespace UcpmApi.Converter
{
    public class DocHistoryConverter : BaseConverter
    {
        private readonly DocHistoryQuery _DocHistoryQuery;

        public DocHistoryConverter(DocHistoryQuery docHistoryQuery)
        {
            _DocHistoryQuery = docHistoryQuery;
        }

        public async Task<DocHistory> GetDocHistoryByGuid(Guid docHistoryGuid)
        {
            DocHistoryEntity docHistory = await _DocHistoryQuery.GetDocHistories()
                                                                .Where(c => c.DocHistoryGuid == docHistoryGuid)
                                                                .SingleOrDefaultAsync();

            return ToView(docHistory);
        }

        private static DocHistory ToView(DocHistoryEntity entity)
        {
            DocHistory docHistory = new();
            QuickReflection.CopyProps(entity, docHistory);
            return docHistory;
        }
    }
}