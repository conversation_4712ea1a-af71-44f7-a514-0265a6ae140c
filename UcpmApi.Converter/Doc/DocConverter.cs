using Azure.Storage.Blobs;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using PuppeteerSharp;
using SD.LLBLGen.Pro.LinqSupportClasses;
using System.IO;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Text.RegularExpressions;
using Ucpm.SourceGenerator.Abstraction;
using UcpmApi.BusinessModel;
using UcpmApi.BusinessModel.Filesystem;
using UcpmApi.Command.Doc;
using UcpmApi.Command.History;
using UcpmApi.Query.Carrier;
using UcpmApi.Query.Doc;
using UcpmApi.Query.Flex;
using UcpmApi.Query.Policy;
using UcpmApi.Shared;
using UcpmApi.Shared.Adobe;
using UcpmApi.Shared.Doc;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Request;
using UcpmApi.Shared.Security;
using UcpmApi.Shared.Survey;
using UcpmTools;
using UtilityParsersStandard;

namespace UcpmApi.Converter
{
    [CopyMethod<DocEntity, DocModel>]
    public partial class DocConverter : BaseConverter
    {
        private readonly DocQuery _docQuery;
        private readonly DocumentFileSystemManager _docFileSystemManager;
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        private readonly Guid _chromeDownloadFolder = Guid.Parse("b1814366-f1cb-4978-8265-e5a0f6cbb157");
        private readonly DocCommand _docCommand;
        private readonly PolicyProspectQuery _policyProspectQuery;
        private readonly CarrierSubmissionQuery _carrierSubmissionQuery;
        private readonly CarrierSubmissionFlexResponseQuery _carrierSubmissionFlexResponseQuery;
        private readonly ExternalApiTokenConverter _externalApiTokenConverter;
        private readonly LogCommand _logCommand;
        private object _docConverter;
        private IEnumerable<object> finalDocList;

        public DocConverter(
            DocQuery docQuery,
            DocumentFileSystemManager documentFileSystemManager,
            DocCommand docCommand,
            PolicyProspectQuery policyProspectQuery,
            CarrierSubmissionQuery carrierSubmissionQuery,
            CarrierSubmissionFlexResponseQuery carrierSubmissionFlexResponseQuery,
            ExternalApiTokenConverter externalApiTokenConverter,
            LogCommand logCommand
            )
        {
            _carrierSubmissionQuery = carrierSubmissionQuery;
            _docQuery = docQuery;
            _docFileSystemManager = documentFileSystemManager;
            _docCommand = docCommand;
            _policyProspectQuery = policyProspectQuery;
            _carrierSubmissionFlexResponseQuery = carrierSubmissionFlexResponseQuery;
            _externalApiTokenConverter = externalApiTokenConverter;
            _logCommand = logCommand;
        }

        public async Task<DocModel> GetDocByGuid(Guid docGuid)
        {
            DocEntity doc = await _docQuery.GetDocs()
                .Where(c => c.DocGuid == docGuid)
                .SingleOrDefaultAsync();

            return await ToView(doc);
        }

        public async Task<DocModel> GetDocBySourceGuid(Guid docGuid, string fileName)
        {
            DocEntity doc = await _docQuery.GetDocs()
                .Where(c => c.SourceGuid == docGuid && c.DocName.EndsWith(fileName))
                .SingleOrDefaultAsync();

            return await ToView(doc);
        }

        public async Task<AdobeAgreementInfo> RunAdobeESign(AdobeRequest adobeRequest)
        {
            using TempPath tempPath = new(Path.GetTempPath());

            string path = Path.Combine(tempPath.Path, adobeRequest.FileName);

            HttpClient httpClient = new();
            HttpResponseMessage result = await httpClient.GetAsync(adobeRequest.CloudUrl);
            using (FileStream fs = new(path, FileMode.CreateNew))
            {
                await result.Content.CopyToAsync(fs);
            }

            ExternalApiToken apiToken =
                await _externalApiTokenConverter.GetExternalApiToken("-", "AdobeEsign", "AdobeESign") ?? new();

            LogModel logModel = new()
            {
                LogTypeId = LogTypeEnum.ApiCalls,
                LoggedAtOffset = DateTimeOffset.Now,
                MachineName = Environment.MachineName,
                AllocatedBytes = GC.GetTotalAllocatedBytes(),
                LinkingGuid = new Guid(),
                LoggedData = $"Adobe API Token: {apiToken.AccessToken}",
            };

            string clientSecret = Environment.GetEnvironmentVariable("AdobeESign_Client_Secret") ?? "";

            LogModel clientSecretlogModel = new()
            {
                LogTypeId = LogTypeEnum.ApiCalls,
                LoggedAtOffset = DateTimeOffset.Now,
                MachineName = Environment.MachineName,
                AllocatedBytes = GC.GetTotalAllocatedBytes(),
                LinkingGuid = new Guid(),
                LoggedData = $"Adobe Client Secret: {clientSecret}",
            };

            string clientId = Environment.GetEnvironmentVariable("AdobeESign_Client_Id") ?? "";

            LogModel clientIdlogModel = new()
            {
                LogTypeId = LogTypeEnum.ApiCalls,
                LoggedAtOffset = DateTimeOffset.Now,
                MachineName = Environment.MachineName,
                AllocatedBytes = GC.GetTotalAllocatedBytes(),
                LinkingGuid = new Guid(),
                LoggedData = $"Adobe Client ID: {clientId}",
            };

            await _logCommand.AddLog(logModel);
            await _logCommand.AddLog(clientSecretlogModel);
            await _logCommand.AddLog(clientIdlogModel);

            apiToken.AccessToken = await AdobeModel.RefreshToken(apiToken);
            AdobeAgreementInfo agreement = await AdobeModel.RunAdobeESign(apiToken, path, adobeRequest.InsuredName, adobeRequest.Email);
            return agreement == null ? new(true) : agreement;
        }

        public async Task<DocModel> GetDocByUrl(string url)
        {
            DocEntity doc = await _docQuery.GetDocByUrl(url);

            return await ToView(doc);
        }

        public async Task<DocModel> GenerateRiskAssessmentDocument(DocInfo docInfo)
        {
            return await GetDocBySourceGuid(docInfo.SourceGuid, docInfo.DocName);
        }

        public async Task<GetRiskAssessmentNotificationsDocPathsResponse> GetRiskAssessmentNotificationsDocPathsByPackageGuid(Guid packageGuid)
        {
            GetRiskAssessmentNotificationsDocPathsResponse response = new();
            EntityCollection<DocEntity> finalDocList = [];

            IQueryable<DocEntity> docsByPackageGuid = _docQuery.GetActiveDocsBySourceGuidAndDocName(packageGuid);
            finalDocList.AddRange(docsByPackageGuid);

            EntityCollection<PolicyProspectEntity> policyProspect = await _policyProspectQuery.GetPolicyProspectsByPackageGuid(packageGuid);
            foreach (PolicyProspectEntity ppe in policyProspect)
            {
                foreach (CarrierSubmissionEntity cse in ppe.CarrierSubmission.Where(r => r.NxusTypeId == (int)NxusTypeEnum.NXUSCustom))
                {
                    Guid carrierSubmissionGuid = cse.CarrierSubmissionGuid;
                    IQueryable<DocEntity> docsByCarrierSubmissionGuid = _docQuery.GetActiveDocsBySourceGuidAndDocName(carrierSubmissionGuid);
                    finalDocList.AddRange(docsByCarrierSubmissionGuid);
                }
            }

            response.DcatDocPath = GetDocPathByByDocNameFromDoctList(finalDocList, "DCAT.pdf");
            response.DcatDocCloudUrl = GetCloudUrlByDocNameFromDocList(finalDocList, "DCAT.pdf");
            response.DratDocPath = GetDocPathByByDocNameFromDoctList(finalDocList, "DRAT.pdf");
            response.DratDocCloudUrl = GetCloudUrlByDocNameFromDocList(finalDocList, "DRAT.pdf");
            response.ApplicationDocPath = GetDocPathByByDocNameFromDoctList(finalDocList, "App.pdf");
            response.ApplicationCloudUrl = GetCloudUrlByDocNameFromDocList(finalDocList, "App.pdf");
            response.QuoteDocPath = GetDocPathByByDocNameFromDoctList(finalDocList, "Quote.pdf");
            response.QuoteDocCloudUrl = GetCloudUrlByDocNameFromDocList(finalDocList, "Quote.pdf");

            return response;
        }

        public async Task<DocModel> GetDocCheck(Guid sourceGuid)
        {
            DocEntity doc = await _docQuery.GetDocs().Where(c => c.SourceGuid == sourceGuid && c.IsDeleted == false).SingleOrDefaultAsync();

            return await ToView(doc);
        }

        public async Task<DocModel> GetAgencyLogo(Guid agencyGuid)
        {
            DocEntity doc = await _docQuery.GetAgencyLogo(agencyGuid);

            return await ToView(doc);
        }

        public async Task<List<DocModel>> GetDocsBySourceGuid(Guid sourceGuid, NodeTypeEnum nodeType = NodeTypeEnum.Folder)
        {
            List<DocModel> returnList = [];
            IEnumerable<DocEntity> docs = await _docQuery.GetBySourceGuid(sourceGuid);
            foreach (DocEntity doc in docs)
            {
                DocModel model = await ToView(doc);
                if (model != null)
                {
                    model.NodeType = nodeType;
                    returnList.Add(model);
                }
            }

            return returnList;
        }


        private async Task<DocModel> ToView(DocEntity entity)
        {
            DocModel doc = new();
            if (entity != null)
            {
                CopyProps(entity, doc);
                doc.FileName = entity.DocName;
                doc.OriginalFileName = entity.DocName;
                doc.ExternalFileName = entity.ExternalDocName;
                doc.DocPath = GetDocPath(entity.SourceGuid, entity.DocName);
                doc.DocTypeName = entity?.DocType?.DocTypeName ?? "None";
                doc.DocTypeTargetEmail = entity?.DocType?.DocTypeFederatedAgent?.SingleOrDefault()?.Agent?.AgentEmail ??
                                         "<EMAIL>";

                if (!File.Exists(doc.DocPath))
                {
                    return null;
                }

                if (entity.IsDeleted)
                {
                    doc.LastWrite = entity.LastWriteTime.Date;
                    return doc;
                }

                if (ShouldUploadToCloud(entity, doc.DocPath))
                {
                    await UploadToCloudAsync(entity, doc);
                }
                else
                {
                    doc.CloudUrl = entity.CloudUrl;
                }
                doc.LastWrite = File.GetLastWriteTime(doc.DocPath);
            }

            return doc;
        }

        private bool ShouldUploadToCloud(DocEntity entity, string docPath)
        {
            return string.IsNullOrWhiteSpace(entity.CloudUrl) ||
                   (entity.CloudUrl != null && EndsWithGuid(entity.CloudUrl)) ||
                   NewVersionAvailable(entity, File.GetLastAccessTime(docPath)) ||
                   DocIsNotPublic(entity);
        }

        private async Task UploadToCloudAsync(DocEntity entity, DocModel doc)
        {
            string dateTimeFormat = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string apiKeyName = "LenderStorageAccount";

            string decryptedConnectionString = AesEncoder.Decrypt(GetLenderBlobStorage(apiKeyName), apiKeyName);
            if (string.IsNullOrWhiteSpace(decryptedConnectionString))
            {
                throw new InvalidOperationException("Decrypted connection string is null or empty.");
            }

            BlobServiceClient blobServiceClient = new(decryptedConnectionString);
            BlobContainerClient blobContainerClient = blobServiceClient.GetBlobContainerClient("documents");
            await blobContainerClient.CreateIfNotExistsAsync();

            if (blobContainerClient == null)
            {
                throw new InvalidOperationException("Blob container client is null.");
            }

            BlobClient blobClient =
                blobContainerClient.GetBlobClient($"{dateTimeFormat}-{entity.DocGuid}-{entity.DocName}");

            if (blobClient == null)
            {
                throw new InvalidOperationException("Blob client is null.");
            }

            await blobClient.UploadAsync(doc.DocPath, true);

            doc.CloudUrl = blobClient.Uri.ToString();
            entity.LastUploadOffset = DateTimeOffset.Now;
            entity.CloudUrl = doc.CloudUrl;
            await _docCommand.SaveDoc(entity);
        }

        private string GetDocPathByByDocNameFromDoctList(EntityCollection<DocEntity> docList, string docName)
        {
            if (docList.Any(a => a.DocName.EndsWith(docName)))
            {
                DocEntity doc = docList.Single(a => a.DocName.EndsWith(docName));
                return GetDocPath(doc.SourceGuid, doc.DocName);
            }

            return string.Empty;
        }

        private string GetCloudUrlByDocNameFromDocList(EntityCollection<DocEntity> docList, string docName)
        {
            if (docList.Any(a => a.DocName.EndsWith(docName)))
            {
                DocEntity doc = docList.Where(a => a.DocName.EndsWith(docName)).Single();
                return doc.CloudUrl;
            }

            return string.Empty;
        }

        private string GetDocPath(Guid sourceGuid, string docName)
        {
            return Path.Combine(
                        _docFileSystemManager.ExtendPathFromRoot(GuidMangler.GuidToFolderSet(sourceGuid)),
                        docName);
        }

        private static bool DocIsNotPublic(DocEntity entity)
        {
            return entity.CloudUrl.StartsWith("https://ucpmcloudfiles");
        }

        private static bool NewVersionAvailable(DocEntity entity, DateTimeOffset lastWriteTime)
        {
            if (entity.LastUploadOffset <= Parse.SentinelDateZoned)
            {
                return true;
            }

            DocHistoryEntity lastUpdate = entity.DocHistory.OrderByDescending(d => d.SavedAtZoned).FirstOrDefault();
            if (lastUpdate != null)
            {
                if (lastUpdate.SavedAtZoned > entity.LastUploadOffset)
                {
                    return true;
                }
            }

            return lastWriteTime > entity.LastUploadOffset;
        }

        public static bool EndsWithGuid(string filename)
        {
            string guidPattern = @"[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$";
            return Regex.IsMatch(filename, guidPattern);
        }

        public string GetLenderBlobStorage(string apiKeyName)
        {
            return _linqMetaData.LookupsApiKey.SingleOrDefault(s => s.ApiKeyName == apiKeyName)?.ApiKey ?? "";
        }

        public async Task<ReportLinkModel> CreateLoanReportLink(Loan loan, LoanQuoteHistory loanQuoteHistory)
        {
            ReportLinkModel reportLinkModel = new();
            string pdfBody = $@"<p>Loan Number: {loan.LoanNumber}<br>
                Loan Amount: {loan.AnticipatedLoanAmount:C}<br>
                Borrower Name: {loan.BorrowerName}<br>
                Collateral Address: {loan.CollateralPropertyAddress}, {loan.CollateralPropertyCity}, {loan.CollateralPropertyState} {loan.CollateralPropertyZip}<br>
                Date Approved: {loan.BorrowerSignedDate:d}<br>
                Insurance Premium: {loanQuoteHistory.Premium:C}<br>
                Surplus Line Taxes: {loanQuoteHistory.Taxes:C}<br>
                Total Cost: {loanQuoteHistory.Premium + loanQuoteHistory.Taxes:C}<br></P>
                <p>
                {loan.EmailContents}
                </p>
                <p><b>LEIS Customer Service</b><br>
                Telephone: ************<br>
                Email: <EMAIL><br></p>";

            string tempFolderPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());

            string chromeFolder = Path.Combine(@"C:\\UCPM-TEMP\chrome", _chromeDownloadFolder.ToString());
            using BrowserFetcher browserFetcher = new(new BrowserFetcherOptions
            {
                Path = chromeFolder
            });

            await browserFetcher.DownloadAsync(BrowserFetcher.DefaultChromiumRevision);
            Directory.CreateDirectory(tempFolderPath);
            string fullPath = Path.Combine(tempFolderPath, $"{loanQuoteHistory.ResponseGuid}.pdf");

            IBrowser browser = await Puppeteer.LaunchAsync(new LaunchOptions
            {
                Headless = true,
                ExecutablePath = browserFetcher.RevisionInfo(BrowserFetcher.DefaultChromiumRevision).ExecutablePath,
            });

            Console.WriteLine("Browser Launched Successfully");

            using (IPage page = await browser.NewPageAsync())
            {
                await page.SetContentAsync(pdfBody);
                string result = await page.GetContentAsync();
                await page.PdfAsync(fullPath);
            }

            await browser.CloseAsync();

            string apiKeyName = "LenderStorageAccount";
            BlobServiceClient blobServiceClient = new(AesEncoder.Decrypt(GetLenderBlobStorage(apiKeyName), apiKeyName));
            BlobContainerClient blobContainerClient = blobServiceClient.GetBlobContainerClient("approval");
            blobContainerClient.CreateIfNotExists();
            BlobClient blobClient = blobContainerClient.GetBlobClient($"{loanQuoteHistory.ResponseGuid}.pdf");

            try
            {
                await blobClient.UploadAsync(fullPath);
                reportLinkModel.Link = blobClient.Uri.ToString();
                return reportLinkModel;
            }
            catch
            {
                //Upload failed continue and leave url blank
            }

            return reportLinkModel;
        }

        public async Task<GatewayDocTaggerResponse> ProcessGatewayDocTagger()
        {
            EntityCollection<DocEntity> docsTaggedCollection = await _docQuery.GetDocsTagged();
            int tagDocs = await _docCommand.ShareUnderwriterDocs(docsTaggedCollection);

            EntityCollection<DocEntity> otherDocsCollection = await _docQuery.GetOtherDocsTagged();
            int otherTagDocs = await _docCommand.ShareUnderwriterDocs(otherDocsCollection);

            int docsTagged = tagDocs;
            docsTagged += otherTagDocs;

            return new GatewayDocTaggerResponse
            {
                TotalDocsTagged = docsTagged,
                Message = $"Docs Tagged: {docsTagged}"
            };
        }

        public async Task<List<DocModel>> GetActiveDocsBySourceGuidAndDocName(Guid carrierSubmissionGuid)
        {
            List<DocModel> modelsToReturn = [];
            IQueryable<DocEntity> docs = _docQuery.GetActiveDocsBySourceGuidAndDocName(carrierSubmissionGuid);
            foreach (DocEntity doc in docs)
            {
                DocModel model = await ToView(doc);
                if (model != null)
                {
                    modelsToReturn.Add(model);
                }
            }
            return modelsToReturn;
        }

        private DocModel GetDocsBySourceGuidForSubjectivityToView(DocEntity entity)
        {
            DocModel Doc = new();
            if (entity != null)
            {
                QuickReflection.CopyProps(entity, Doc);

                Doc.FileName = entity.DocName;
                Doc.OriginalFileName = entity.DocName;
                Doc.SourceGuid = entity.SourceGuid;
                Doc.ExternalFileName = entity.ExternalDocName;
                Doc.DocPath = GetDocPath(entity.SourceGuid, entity.DocName);
                Doc.DocTypeName = entity?.DocType?.DocTypeName ?? "None";
                Doc.CloudUrl = entity.CloudUrl;
                Doc.LastWrite = File.GetLastWriteTime(Doc.DocPath);
            }

            return Doc;
        }

        public async Task<List<DocModel>> GetDocsBySourceGuidForSubjectivity(Guid sourceGuid, string fileLabel)
        {
            List<DocModel> returnList = [];
            IEnumerable<DocEntity> docs = await _docQuery.GetBySourceGuid(sourceGuid);
            foreach (DocEntity doc in docs)
            {
                DocModel model = GetDocsBySourceGuidForSubjectivityToView(doc);
                if (model != null)
                {
                    model.FileName = $"{fileLabel} : {model.FileName}";
                    returnList.Add(model);
                }
            }

            return returnList;
        }

        public async Task<List<DocModel>> GetAllDocsForSubjectivityByCarrierSubmissionGuid(Guid carrierSubmissionGuid)
        {
            List<DocModel> modelsToReturn = [];

            CarrierSubmissionEntity carrierSubmission = _carrierSubmissionQuery.GetByGuid(carrierSubmissionGuid);

            Guid policyProspectGuid = carrierSubmission.PolicyProspectGuid;
            Guid insuredGuid = carrierSubmission.PolicyProspect.Package.InsuredAgentHistory.InsuredGuid;

            List<DocModel> docs = await GetDocsBySourceGuidForSubjectivity(carrierSubmissionGuid, "Sub");
            List<DocModel> policyProspectDocs = await GetDocsBySourceGuidForSubjectivity(policyProspectGuid, "Pcy");
            List<DocModel> insuredDocs = await GetDocsBySourceGuidForSubjectivity(insuredGuid, "Ins");

            docs.AddRange(policyProspectDocs);
            docs.AddRange(insuredDocs);

            return docs;
        }

        public async Task<DocModel> GetPolicyDocByFlexResponseGuid(Guid flexResponseGuid)
        {
            CarrierSubmissionFlexResponseEntity carrierSubmissionFlexResponseEntity = _carrierSubmissionFlexResponseQuery.GetByFlexResponseGuid(flexResponseGuid);
            Guid carrierSubmissionGuid = carrierSubmissionFlexResponseEntity?.CarrierSubmission?.CarrierSubmissionGuid ?? Guid.Empty;

            if (carrierSubmissionGuid == Guid.Empty)
            {
                return new();
            }

            List<DocModel> allDocs = await GetActiveDocsBySourceGuidAndDocName(carrierSubmissionGuid);
            Guid policyDocGuid = Guid.Empty;

            if (allDocs.Any(a => a.FileName == "Policy.pdf"))
            {
                policyDocGuid = allDocs.Where(a => a.FileName == "Policy.pdf").Single().DocGuid;
            }

            return await GetDocByGuid(policyDocGuid);
        }

        public async Task<List<DocModel>> GetAllIsSharedAgentDocsBySourceGuid(Guid sourceGuid)
        {
            List<DocModel> returnList = [];
            IEnumerable<DocEntity> docs = await _docQuery.GetAllIsSharedAgentDocsBySourceGuid(sourceGuid);
            foreach (DocEntity doc in docs)
            {
                DocModel model = await ToView(doc);
                if (model != null)
                {
                    returnList.Add(model);
                }
            }

            return returnList;
        }

        public async Task<DocModel> GetDocsByCOBGuid(Guid cobGuid)
        {
            EntityCollection<DocEntity> docs = await _docQuery.GetDocsByCOBGuid(cobGuid);
            DocEntity doc = docs.SingleOrDefault(c => !c.IsDeleted);

            return await ToView(doc);
        }


        public async Task<List<DocModel>> GetAllDocsBySourceGuid(Guid sourceGuid, bool includeDeleted)
        {
            List<DocModel> returnList = [];
            IEnumerable<DocEntity> docs = await _docQuery.GetDocsBySourceGuid(sourceGuid, includeDeleted);
            foreach (DocEntity doc in docs)
            {
                DocModel model = await ToView(doc);
                if (model != null)
                {
                    model.NodeType = NodeTypeEnum.Folder;
                    returnList.Add(model);
                }
            }

            return returnList;
        }

        public async Task<DocModel> GetDocSimple(Guid sourceGuid, string fileName, [CallerMemberName] string callerName = "")
        {
            DocEntity doc = await _docQuery.GetDocSimple(sourceGuid, fileName, callerName);

            return await ToView(doc);
        }
    }
}