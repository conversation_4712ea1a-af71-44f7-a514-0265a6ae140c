using FlexLibrary;
using FlexLibrary.Models;
using LLBLGenHelper;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using UcpmApi.Logging;
using UcpmApi.Query.Employee;
using UcpmApi.Query.Policy;
using UcpmApi.Shared;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Pulse;

namespace UcpmApi.Converter.Pulse
{
    public class PulseScoreConverter : BaseConverter
    {

        public async Task PulsePackageConversion(PulseScoringRequestModel request)
        {
            request.ScoringModels = [];

            QuickAdapterAsync adapter = new(new DataAccessAdapter(), new DataLogger());
            DocTouchBucketQuery bucketQuery = new(adapter);
            PackageQuery packageQuery = new(adapter);

            EntityCollection<PolicyProspectEntity> policies;
            if (request.PolicyProspectGuid == Guid.Empty && request.ApplicationNumber == 0)
            {
                policies = await packageQuery.GetPackagesContentForPolicyScoring(request.ProgramType);
            }
            else
            {
                policies = [packageQuery.GetPackageContentForPolicyScoring(request.PolicyProspectGuid, request.ApplicationNumber)];
            }

            EntityCollection<DocTouchBucketEntity> touchBucketCollection = await bucketQuery.GetAll();

            ConvertPackagesToPulseScoring(policies, request, touchBucketCollection);
        }

        private void ConvertPackagesToPulseScoring(EntityCollection<PolicyProspectEntity> policies, PulseScoringRequestModel request, EntityCollection<DocTouchBucketEntity> touchBucketCollection)
        {
            foreach (PolicyProspectEntity policy in policies)
            {
                int maxSubmission = policy.CarrierSubmission.Count();
                int difficulty = policy.Package.DifficultyId;

                int touchHistory = policy.PolicyTouchHistory.Count(w => w.TouchTypeId == (int)PolicyTouchTypeEnum.DocumentAdded);

                int docValue = touchBucketCollection.Where(w => w.DocCountFloor <= touchHistory).OrderByDescending(od => od.PointsToAdd).FirstOrDefault()?.PointsToAdd ?? 0;

                PulseTankFacilityModel facility = new();
                FlexResponseSaveStateEntity hasSaveState = policy.FlexResponse?.FlexResponseSaveState?.OrderByDescending(od => od.SavedAt).FirstOrDefault() ?? null;
                if (hasSaveState != null)
                {
                    facility = ExtractTankFromFlex(hasSaveState);
                }

                PulseScoringModel toAdd = new()
                {
                    PolicyRecordCreatedZoned = policy.RecordCreatedZoned,
                    PolicyNumber = policy.ApplicationNumber,
                    PackageGuid = policy.PackageGuid,
                    PolicyProspectGuid = policy.PolicyProspectGuid,
                    ClaimCount = policy.Claim.Count(),
                    DocCount = docValue,
                    HighSubmissionCount = maxSubmission,
                    DeclinedSubmissionCount = policy.CarrierSubmission.Count(w => w.PolicyStatusId == (int)PolicyStatusEnum.Declined),
                    DifficultyLevel = policy.Package.DifficultyId,
                    PolicyCount = policy.Package.PolicyProspect.Count(),
                    AgencyName = policy.Package.InsuredAgentHistory.Agent.Location.Company.CompanyName,
                    Facility = facility,
                };

                request.ScoringModels.Add(toAdd);
            }
        }

        private PulseTankFacilityModel ExtractTankFromFlex(FlexResponseSaveStateEntity saveState)
        {
            if (saveState != null)
            {
                FlexAnswerContainer answerContainer = FlexAnswerContainer.FromJson(saveState.SavedData);
                FlexAnswer facilityDetail = answerContainer.FlexAnswers.FirstOrDefault(fd => fd.QuestionDataName == "FacilityDetails");
                if (facilityDetail == null)
                {
                    return new();
                }

                List<FlexAnswer> detailChildren = facilityDetail.ChildAnswers;

                PulseTankFacilityModel facility = new()
                {
                    Tanks = [],
                    Name = detailChildren.FirstOrDefault(fd => fd.QuestionDataName.Contains(nameof(PulseTankFacilityModel.Name), StringComparison.OrdinalIgnoreCase))?.Answer ?? "",
                    Address = detailChildren.FirstOrDefault(fd => fd.QuestionDataName.Contains(nameof(PulseTankFacilityModel.Address), StringComparison.OrdinalIgnoreCase))?.Answer ?? "",
                    City = detailChildren.FirstOrDefault(fd => fd.QuestionDataName.Contains(nameof(PulseTankFacilityModel.City), StringComparison.OrdinalIgnoreCase))?.Answer ?? "",
                    State = detailChildren.FirstOrDefault(fd => fd.QuestionDataName.Contains(nameof(PulseTankFacilityModel.State), StringComparison.OrdinalIgnoreCase))?.Answer ?? "",
                };

                foreach (FlexAnswer item in detailChildren.Where(w => w.QuestionDataName.Contains("TankDetails", StringComparison.OrdinalIgnoreCase)))
                {
                    facility.Tanks.AddRange(GetTanks(item));
                }

                return facility;
            }
            else
            {
                return new();
            }
        }

        private List<PulseTankModel> GetTanks(FlexAnswer facilityDetail)
        {
            List<PulseTankModel> toReturn = [];
            foreach (IGrouping<int, FlexAnswer> item in facilityDetail.ChildAnswers.GroupBy(gb => gb.RowNumber))
            {
                PulseTankModel toAdd = new()
                {
                    IsUst = facilityDetail.QuestionDataName.Contains("Ust", StringComparison.OrdinalIgnoreCase),
                    TankId = item.FirstOrDefault(fd => fd.QuestionDataName.Contains(nameof(PulseTankModel.TankId), StringComparison.OrdinalIgnoreCase))?.Answer ?? "",
                    InstallationYear = DateTimeOffset.Parse(item.FirstOrDefault(fd => fd.QuestionDataName.Contains(nameof(PulseTankModel.InstallationYear), StringComparison.OrdinalIgnoreCase))?.Answer ?? DateTimeOffset.MinValue.ToString()),
                    TankSize = decimal.Parse(item.FirstOrDefault(fd => fd.QuestionDataName.Contains(nameof(PulseTankModel.TankSize), StringComparison.OrdinalIgnoreCase))?.Answer ?? "0.0"),
                    Contents = item.FirstOrDefault(fd => fd.QuestionDataName.Contains(nameof(PulseTankModel.Contents), StringComparison.OrdinalIgnoreCase))?.Answer ?? "",
                    RetroactiveDate = DateTimeOffset.Parse(item.FirstOrDefault(fd => fd.QuestionDataName.Contains(nameof(PulseTankModel.RetroactiveDate), StringComparison.OrdinalIgnoreCase))?.Answer ?? DateTimeOffset.MinValue.ToString()),
                    Construction = item.FirstOrDefault(fd => fd.QuestionDataName.Contains(nameof(PulseTankModel.Construction), StringComparison.OrdinalIgnoreCase))?.Answer ?? "",
                };
                toReturn.Add(toAdd);
            }
            return toReturn;
        }
    }
}
