using System.Transactions;
using BusinessLogic.Validation;
using LLBLGenHelper.Interfaces;
using Microsoft.VisualStudio.Services.Common;
using MoreLinq;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using UcpmApi.BusinessModel;
using UcpmApi.Command.Mail;
using UcpmApi.Command.Rating;
using UcpmApi.Command.Survey;
using UcpmApi.Query.Carrier;
using UcpmApi.Query.Cob;
using UcpmApi.Query.Survey;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Survey;
using UcpmTools;

namespace UcpmApi.Converter.Survey;

public class SurveyDefinitionVersionConverter : BaseConverter
{
    private readonly IQuickAdapterAsync _quickAdapterAsync;
    private readonly SurveyDefinitionVersionQuery _surveyDefinitionVersionQuery;
    private readonly SurveyDefinitionVersionCommand _surveyDefinitionVersionCommand;
    private readonly QuestionCommand _questionCommand;
    private readonly QuestionQuery _questionQuery;
    private readonly QuestionRuleContainerQuery _questionRuleContainerQuery;
    private readonly QuestionRequiresCategoryQuery _questionRequiresCategoryQuery;
    private readonly ClassOfBusinessQuery _classOfBusinessQuery;
    private readonly QuestionCarrierOfferDefaultCalcQuery _questionCarrierOfferDefaultCalcQuery;
    private readonly InsuredRiskCommand _insuredRiskCommand;
    private readonly RecommendationCommand _recommendationCommand;
    private readonly CarriersQuery _carriersQuery;
    private readonly SurveyDefinitionVersionUsesContextCommand _surveyDefinitionVersionUsesContextCommand;
    private readonly QuoteContingencyCommand _quoteContingencyCommand;
    private readonly RatingCarrierVolumeDiscountCommand _ratingCarrierVolumeDiscountCommand;
    private readonly SurveyDefinitionQuery _surveyDefinitionQuery;
    private readonly EmailBlastLogCommand _emailBlastLogCommand;
    private readonly Guid _bart = Guid.Parse("E952BE93-DEC4-413F-B989-87469BC2F66D");

    public SurveyDefinitionVersionConverter(IQuickAdapterAsync quickAdapterAsync,
        SurveyDefinitionVersionQuery surveyDefinitionVersionQuery,
        SurveyDefinitionVersionCommand surveyDefinitionVersionCommand, QuestionCommand questionCommand,
        QuestionQuery questionQuery, QuestionRuleContainerQuery questionRuleContainerQuery,
        QuestionRequiresCategoryQuery questionRequiresCategoryQuery, ClassOfBusinessQuery classOfBusinessQuery,
        QuestionCarrierOfferDefaultCalcQuery questionCarrierOfferDefaultCalcQuery,
        InsuredRiskCommand insuredRiskCommand, RecommendationCommand recommendationCommand, CarriersQuery carriersQuery,
        SurveyDefinitionVersionUsesContextCommand surveyDefinitionVersionUsesContextCommand,
        QuoteContingencyCommand quoteContingencyCommand,
        RatingCarrierVolumeDiscountCommand ratingCarrierVolumeDiscountCommand,
        SurveyDefinitionQuery surveyDefinitionQuery, EmailBlastLogCommand emailBlastLogCommand)
    {
        _quickAdapterAsync = quickAdapterAsync;
        _surveyDefinitionVersionQuery = surveyDefinitionVersionQuery;
        _surveyDefinitionVersionCommand = surveyDefinitionVersionCommand;
        _questionCommand = questionCommand;
        _questionQuery = questionQuery;
        _questionRuleContainerQuery = questionRuleContainerQuery;
        _questionRequiresCategoryQuery = questionRequiresCategoryQuery;
        _classOfBusinessQuery = classOfBusinessQuery;
        _questionCarrierOfferDefaultCalcQuery = questionCarrierOfferDefaultCalcQuery;
        _insuredRiskCommand = insuredRiskCommand;
        _recommendationCommand = recommendationCommand;
        _carriersQuery = carriersQuery;
        _surveyDefinitionVersionUsesContextCommand = surveyDefinitionVersionUsesContextCommand;
        _quoteContingencyCommand = quoteContingencyCommand;
        _ratingCarrierVolumeDiscountCommand = ratingCarrierVolumeDiscountCommand;
        _surveyDefinitionQuery = surveyDefinitionQuery;
        _emailBlastLogCommand = emailBlastLogCommand;
    }

    public async Task<ProcessVersionSurveyResponse> ProcessVersionSurvey()
    {
        int recordCount = 0;
        using (TransactionScope scope = _quickAdapterAsync.CreateReadCommittedScope())
        {
            EntityCollection<SurveyDefinitionVersionEntity> versions =
                await _surveyDefinitionVersionQuery.GetVersionsToRev();
            foreach (SurveyDefinitionVersionEntity version in versions)
            {
                await RevSurvey(version.SurveyDefinitionVersionGuid);
            }

            scope.Complete();
            recordCount += versions.Count;
        }

        return new ProcessVersionSurveyResponse
        {
            Message = $"{recordCount} versions updated",
            TotalProcessed = recordCount
        };
    }

    public async Task<SurveyDefinitionVersion> GetLatestSurveyDefinitionVersionBySurveyDefinitionGuid(Guid surveyDefinitionGuid)
    {
        SurveyDefinitionVersionEntity surveyDefinitionVersionEntity = await _surveyDefinitionVersionQuery.GetLatestVersion(surveyDefinitionGuid);

        return ToView(surveyDefinitionVersionEntity);
    }

    private static SurveyDefinitionVersion ToView(SurveyDefinitionVersionEntity surveyDefinitionVersionEntity)
    {
        SurveyDefinitionVersion surveyDefinitionVersion = new();

        if (surveyDefinitionVersionEntity != null)
        {
            QuickReflection.CopyProps(surveyDefinitionVersionEntity, surveyDefinitionVersion);
        }

        return surveyDefinitionVersion;
    }

    private async Task RevSurvey(Guid surveyDefinitionVersionGuid)
    {
        //UnitOfWork2 work = new UnitOfWork2();
        SurveyDefinitionVersionEntity oldVersion =
            _surveyDefinitionVersionQuery.GetVersionWithMassivePrefetch(surveyDefinitionVersionGuid, false);
        SurveyDefinitionVersionEntity oldVersionWithLinkerQuestions = await CreateRowHierarchy(oldVersion);
        oldVersionWithLinkerQuestions.ReleasedByEmployeeGuid = _bart;
        oldVersionWithLinkerQuestions.ReleasedDateTimeZoned = DateTimeOffset.Now;
        await _quickAdapterAsync.SaveEntityAsync(oldVersionWithLinkerQuestions, true);
        Guid newSurveyDefinitionVersionGuid = Guid.NewGuid();
        SurveyDefinitionVersionEntity newVersion =
            await _surveyDefinitionVersionCommand.CreateNewVersion(oldVersionWithLinkerQuestions,
                newSurveyDefinitionVersionGuid);

        await RevInnerSurvey(oldVersionWithLinkerQuestions, newSurveyDefinitionVersionGuid,
            new List<OldCategoryAndNewCategoryStruct>(), null, null);
        SurveyDefinitionEntity definition = _surveyDefinitionQuery.GetById(newVersion.SurveyDefinitionGuid);
        definition.CurrentPublishedVersionId = oldVersionWithLinkerQuestions.VersionId;
        definition.IsRevving = false;
        await _quickAdapterAsync.SaveEntityAsync(definition);
        //QuickAdapter.CommitWork(work);
    }

    private async Task<SurveyDefinitionVersionEntity> CreateRowHierarchy(SurveyDefinitionVersionEntity versionEntity)
    {
        Contract.Requires(versionEntity != null, ErrorTypeEnum.ArgumentException, "versionEntity is null");
        if (versionEntity != null && versionEntity.Question.Any(q => q.DataTypeId == (int)DataTypeEnum.Row))
            return versionEntity;
        List<QuestionEntity> sortedVersion = versionEntity?.Question.OrderBy(q => q.SequenceNum).ToList();
        if (sortedVersion != null && sortedVersion.Count == 0)
        {
            return versionEntity;
        }

        EntityCollection<QuestionEntity> linkerQuestions = new();
        int sequenceNumber = sortedVersion.Max(s => s.SequenceNum) + 1;
        foreach (QuestionEntity question in sortedVersion)
        {
            if (question.DataTypeId == (int)DataTypeEnum.DataGrid ||
                question.DataTypeId == (int)DataTypeEnum.DynamicDataGrid ||
                question.DataTypeId == (int)DataTypeEnum.StaticGrid)
            {
                linkerQuestions = await CreateRowQuestionForTables(question, sequenceNumber, linkerQuestions);
                ++sequenceNumber;
            }
        }

        EntityCollection<QuestionEntity> questions = new();
        questions.AddRange(linkerQuestions);
        await _quickAdapterAsync.SaveEntityCollectionAsync(questions, false, false);
        ActionProcedures.ReorderQuestions(versionEntity.SurveyDefinitionVersionGuid);

        SurveyDefinitionVersionEntity updatedVersion =
            _surveyDefinitionVersionQuery.GetVersionWithMassivePrefetch(versionEntity.SurveyDefinitionVersionGuid,
                false);
        return updatedVersion;
    }

    private async Task<EntityCollection<QuestionEntity>> CreateRowQuestionForTables(QuestionEntity question,
        int sortOrderPlaceholder, EntityCollection<QuestionEntity> questions)
    {
        Contract.Requires(question != null, ErrorTypeEnum.ArgumentException, "question is null");
        Contract.Requires(question != null, ErrorTypeEnum.ArgumentException, "questions is null");

        string primaryDataName = question.DataName;
        string dataName = $"{primaryDataName}Row";
        QuestionEntity rowQuestion = await _questionQuery.GetRowQuestionIfExists(question, dataName);
        EntityCollection<QuestionRequiresCategoryEntity> categoriesOnParent = new();
        if (rowQuestion == null)
        {
            var indentLevel = question.IndentLevelId;
            rowQuestion = new QuestionEntity
            {
                QuestionGuid = Guid.NewGuid(),
                IsComputedField = true,
                DefaultOrCalc = "true",
                SurveyDefinitionVersionGuid = question.SurveyDefinitionVersionGuid,
                SequenceNum = ++sortOrderPlaceholder,
                FloatingSequence = question.FloatingSequence + 0.01,
                IndentLevelId = indentLevel + 1,
                DataTypeId = (int)DataTypeEnum.Row,
                ParentQuestionGuid = question.QuestionGuid,
                QuestionText = $"Row for {primaryDataName}",
                DataName = dataName
            };

            await _quickAdapterAsync.SaveEntityAsync(rowQuestion, true);
            QuestionRuleContainerEntity container =
                await _questionRuleContainerQuery.GetVisibilityContainer(rowQuestion.QuestionGuid);
            await _questionCommand.CreateStarterPack(container);
        }
        else
        {
            double epsilon = 0.00001; // Define a small threshold value
            if (Math.Abs(rowQuestion.FloatingSequence - question.FloatingSequence) > epsilon)
            {
                rowQuestion.SequenceNum = ++sortOrderPlaceholder;
                rowQuestion.FloatingSequence = question.FloatingSequence + 0.01;
            }

            if (rowQuestion.ParentQuestionGuid != question.QuestionGuid)
            {
                rowQuestion.ParentQuestionGuid = question.QuestionGuid;
            }

            await _quickAdapterAsync.SaveEntityAsync(rowQuestion, true);
        }

        categoriesOnParent =
            await _questionRequiresCategoryQuery.GetAllCategoriesAssociatedWithQuestion(question.QuestionGuid);
        EntityCollection<QuestionRequiresCategoryEntity> categoriesOnRow =
            await _questionRequiresCategoryQuery.GetAllCategoriesAssociatedWithQuestion(rowQuestion.QuestionGuid);
        List<Guid> unsavedCategories = categoriesOnParent.Select(c => c.CategoryGuid)
            .Except(categoriesOnRow.Select(c => c.CategoryGuid)).ToList();

        EntityCollection<QuestionRequiresCategoryEntity> categoriesToAddToRow = new();
        
        foreach (Guid categoryGuid in unsavedCategories)
        {
            QuestionRequiresCategoryEntity linkerCat = new()
            {
                QuestionGuid = rowQuestion.QuestionGuid,
                CategoryGuid = categoryGuid
            };
            categoriesToAddToRow.Add(linkerCat);
        }

        await _quickAdapterAsync.SaveEntityCollectionAsync(categoriesToAddToRow, false, false);

        EntityCollection<QuestionEntity> childQuestions =
            await _questionQuery.GetChildQuestionsSimpleWithoutResponse(question.QuestionGuid);
        foreach (QuestionEntity innerQuestion in childQuestions)
        {
            if (innerQuestion.DataTypeId != (int)DataTypeEnum.Row)
            {
                innerQuestion.ParentQuestionGuid = rowQuestion.QuestionGuid;
                questions.Add(innerQuestion);
            }
        }

        ++sortOrderPlaceholder;
        questions.Add(rowQuestion);
        return questions;
    }

    private async Task RevInnerSurvey(SurveyDefinitionVersionEntity oldVersionWithLinkerQuestions,
        Guid newSurveyDefinitionVersionGuid, List<OldCategoryAndNewCategoryStruct> oldAndNewCategoryGuids,
        EntityCollection<FactorGroupCollectionEntity> filteredFactorGroupCollections,
        EntityCollection<CategoryCarrierMaximumOfferEntity> filteredCategoryCarriers)
    {
        Contract.Requires(oldVersionWithLinkerQuestions != null, ErrorTypeEnum.ArgumentException,
            "oldVersionWithLinkerQuestions is null");

        await _surveyDefinitionVersionCommand.AddBaseRates(oldVersionWithLinkerQuestions,
            newSurveyDefinitionVersionGuid, filteredCategoryCarriers);
        (EntityCollection<FactorGroupEntity> factorGroups, EntityCollection<FactorEntity> factors,
            List<OldAndNewFactorGroupStruct> oldAndNewFactorGroupStructs) = await AddFactors(
            oldVersionWithLinkerQuestions,
            newSurveyDefinitionVersionGuid, filteredFactorGroupCollections);

        await _surveyDefinitionVersionCommand.AddPolicyFactors(oldVersionWithLinkerQuestions,
            newSurveyDefinitionVersionGuid);

        //do parentQuestions first
        Guid parentQuestionGuid = Guid.Empty;
        EntityCollection<QuestionEntity> questions = new();
        questions = await BuildQuestions(parentQuestionGuid, oldVersionWithLinkerQuestions,
            newSurveyDefinitionVersionGuid,
            factorGroups, questions, oldAndNewCategoryGuids, oldAndNewFactorGroupStructs);

        await AddQuestionRuleContainer(oldVersionWithLinkerQuestions?.Question, questions, oldAndNewCategoryGuids);
        await AddQuestionCarrierOfferDefaultCalculations(oldVersionWithLinkerQuestions?.Question, questions,
            oldAndNewCategoryGuids);
        EntityCollection<InsuredRiskEntity> issues =
            await _insuredRiskCommand.AddIssueAndSubTables(oldVersionWithLinkerQuestions?.InsuredRisk,
                newSurveyDefinitionVersionGuid);
        await AddRatingAndSubTables(oldVersionWithLinkerQuestions, questions, newSurveyDefinitionVersionGuid,
            oldAndNewFactorGroupStructs, filteredCategoryCarriers);
        EntityCollection<RecommendationEntity> newRecommendations =
            await _recommendationCommand.AddRecommendationAndSubTables(oldVersionWithLinkerQuestions,
                newSurveyDefinitionVersionGuid);
        await _insuredRiskCommand.BuildInsuredRiskSubTables(oldVersionWithLinkerQuestions, questions, issues,
            newRecommendations);
        await BuildNewBinderFormInclusion(oldVersionWithLinkerQuestions, newSurveyDefinitionVersionGuid, factors,
            questions,
            filteredCategoryCarriers);
        await RevLimitsDeductiblesAndTerms(oldVersionWithLinkerQuestions, newSurveyDefinitionVersionGuid, questions,
            filteredCategoryCarriers);
        await _surveyDefinitionVersionUsesContextCommand.AddSurveyDefinitionVersionUsesContext(
            oldVersionWithLinkerQuestions, newSurveyDefinitionVersionGuid);
        await _quoteContingencyCommand.AddQuoteContingencies(oldVersionWithLinkerQuestions, questions,
            newSurveyDefinitionVersionGuid);
        await _ratingCarrierVolumeDiscountCommand.AddVolumeDiscounts(oldVersionWithLinkerQuestions,
            newSurveyDefinitionVersionGuid);
    }

    private async Task<(EntityCollection<FactorGroupEntity>, EntityCollection<FactorEntity>,
            List<OldAndNewFactorGroupStruct>)>
        AddFactors(
            SurveyDefinitionVersionEntity oldVersion,
            Guid newSurveyDefinitionVersionGuid,
            EntityCollection<FactorGroupCollectionEntity> factorGroupCollections)
    {
        EntityCollection<FactorGroupEntity> factorGroups = new();
        EntityCollection<FactorEntity> factors = new();
        EntityCollection<CarrierFactorEntity> carrierFactors = new();
        EntityCollection<CarrierFactorByStateEntity> carrierFactorsByState = new();
        EntityCollection<FactorContextEntity> factorContexts = new(); //Remember the contexts on copy
        List<OldAndNewFactorGroupStruct> oldAndNewFactorGroupStructs = new();
        EntityCollection<ClassOfBusinessHazardEntity> oldClassOfBusinessHazards =
            await _classOfBusinessQuery.GetAllBySurveyDefinitionVersionGuid(oldVersion.SurveyDefinitionVersionGuid);
        EntityCollection<ClassOfBusinessHazardEntity> newClassOfBusinessHazards = new();

        foreach (ClassOfBusinessHazardEntity oldHazard in oldClassOfBusinessHazards)
        {
            ClassOfBusinessHazardEntity newHazard = new()
            {
                SurveyDefinitionVersionGuid = newSurveyDefinitionVersionGuid,
                CarrierMaximumOfferGuid = oldHazard.CarrierMaximumOfferGuid,
                ClassOfBusinessGuid = oldHazard.ClassOfBusinessGuid,
                HazardRateGuid = oldHazard.HazardRateGuid,
                SurveyDefinitionContextGuid = oldHazard.SurveyDefinitionContextGuid
            };
            newClassOfBusinessHazards.Add(newHazard);
        }

        foreach (FactorGroupEntity factorGroup in oldVersion.FactorGroup.Where(f => !f.IsDeleted))
        {
            bool skipFactorGroup = false;
            Guid newFactorGroupGuid = Guid.NewGuid();
            FactorGroupEntity clonedFactorGroup = new()
            {
                FactorGroupGuid = newFactorGroupGuid,
                SurveyDefinitionVersionGuid = newSurveyDefinitionVersionGuid,
                FactorGroupName = factorGroup.FactorGroupName,
                FactorGroupCollectionGuid = factorGroup.FactorGroupCollectionGuid,
                SortOrder = factorGroup.SortOrder,
                FactorGroupTypeId = factorGroup.FactorGroupTypeId,
                FactorGroupTimingId = factorGroup.FactorGroupTimingId
            };

            if (factorGroupCollections != null)
            {
                FactorGroupCollectionEntity newFactorGroupCollection =
                    factorGroupCollections.SingleOrDefault(r =>
                        r.CollectionName == factorGroup.FactorGroupCollection.CollectionName);
                if (newFactorGroupCollection != null)
                {
                    clonedFactorGroup.FactorGroupCollectionGuid = newFactorGroupCollection.FactorGroupCollectionGuid;
                }
                else
                {
                    skipFactorGroup = true;
                }
            }

            if (!skipFactorGroup)
            {
                oldAndNewFactorGroupStructs.Add(new OldAndNewFactorGroupStruct(factorGroup, clonedFactorGroup));
                foreach (FactorEntity factor in factorGroup.Factor)
                {
                    Guid newFactorGuid = Guid.NewGuid();
                    FactorEntity clonedFactor = new()
                    {
                        FactorGuid = newFactorGuid,
                        FactorGroupGuid = newFactorGroupGuid,
                        FactorDisplayName = factor.FactorDisplayName,
                        FactorTypeCode = factor.FactorTypeCode,
                        Value = factor.Value,
                        MinimumModifier = factor.MinimumModifier,
                        MaximumModifier = factor.MaximumModifier,
                        SortOrder = factor.SortOrder,
                        FactorCssClass = factor.FactorCssClass,
                        CarrierInstructions = factor.CarrierInstructions,
                        Abbreviation = factor.Abbreviation,
                        EnumName = factor.EnumName
                    };
                    if (factor.ClassOfBusinessGuid != Guid.Empty)
                    {
                        clonedFactor.ClassOfBusinessGuid = factor.ClassOfBusinessGuid;
                    }

                    factors.Add(clonedFactor);

                    foreach (CarrierFactorEntity carrierFactor in factor.CarrierFactor)
                    {
                        CarrierFactorEntity newCarrierFactor = new()
                        {
                            CarrierMaximumOfferGuid = carrierFactor.CarrierMaximumOfferGuid,
                            FactorGuid = newFactorGuid,
                            FactorTypeCode = carrierFactor.FactorTypeCode,
                            Value = carrierFactor.Value,
                            SurveyDefinitionContextGuid = carrierFactor.SurveyDefinitionContextGuid
                        };
                        carrierFactors.Add(newCarrierFactor);
                    }

                    foreach (CarrierFactorByStateEntity carrierFactorByState in factor.CarrierFactorByState)
                    {
                        CarrierFactorByStateEntity newCarrierFactorByState = new()
                        {
                            CarrierMaximumOfferGuid = carrierFactorByState.CarrierMaximumOfferGuid,
                            StateCode = carrierFactorByState.StateCode,
                            FactorGuid = newFactorGuid,
                            FactorTypeCode = carrierFactorByState.FactorTypeCode,
                            Value = carrierFactorByState.Value,
                            MinimumModifier = carrierFactorByState.MinimumModifier,
                            MaximumModifier = carrierFactorByState.MaximumModifier,
                            SurveyDefinitionContextGuid = carrierFactorByState.SurveyDefinitionContextGuid
                        };
                        carrierFactorsByState.Add(newCarrierFactorByState);
                    }

                    foreach (FactorContextEntity factorContext in factor.FactorContext)
                    {
                        FactorContextEntity newFactorContext = new()
                        {
                            FactorGuid = newFactorGuid,
                            SurveyDefinitionContextGuid = factorContext.SurveyDefinitionContextGuid,
                            Value = factorContext.Value,
                            MinimumModifier = factorContext.MinimumModifier,
                            MaximumModifier = factorContext.MaximumModifier
                        };
                        factorContexts.Add(newFactorContext);
                    }
                }

                factorGroups.Add(clonedFactorGroup);
            }
        }

        await _quickAdapterAsync.SaveEntityCollectionAsync(newClassOfBusinessHazards, false, true);
        await _quickAdapterAsync.SaveEntityCollectionAsync(factorGroups, false, true);
        // TODO: thinking the `recurse` flag being set above should make these call unnecessary...?
        await _quickAdapterAsync.SaveEntityCollectionAsync(factors, false, true);
        await _quickAdapterAsync.SaveEntityCollectionAsync(carrierFactors, false, true);
        await _quickAdapterAsync.SaveEntityCollectionAsync(carrierFactorsByState, false, true);
        await _quickAdapterAsync.SaveEntityCollectionAsync(factorContexts, false, true);

        return (factorGroups, factors, oldAndNewFactorGroupStructs);
    }

    private async Task<EntityCollection<QuestionEntity>> BuildQuestions(Guid parentQuestionGuid,
        SurveyDefinitionVersionEntity version, Guid newSurveyDefinitionVersionGuid,
        EntityCollection<FactorGroupEntity> factorGroups, EntityCollection<QuestionEntity> questions,
        List<OldCategoryAndNewCategoryStruct> oldAndNewCategoryGuids,
        List<OldAndNewFactorGroupStruct> oldAndNewFactorGroupStructs)
    {
        Contract.Requires(questions != null && version != null && oldAndNewCategoryGuids != null,
            ErrorTypeEnum.ArgumentException, "questions, version, or oldAndNewCategoryGuids is null");
        EntityCollection<QuestionReferralForCarrierOfferEntity> carrierReferrals = new();
        EntityCollection<QuestionAutoClearForCarrierOfferEntity> autoClearRules = new();
        EntityCollection<QuestionRequiresCategoryEntity> questionRequiresCategories = new();
        EntityCollection<QuestionRequiresCategoryGroupEntity> questionRequiresCategoryGroups = new();
        EntityCollection<QuestionReferralBypassAgencyEntity> newAgencyBypass = new();
        EntityCollection<QuestionIncludeDocEntity> includeDocs = new();
        EntityCollection<QuestionIncludeDocAgencyEntity> includeDocAgencies = new();
        EntityCollection<QuestionRequiresAccountTypeEntity> questionRequires = new();
        EntityCollection<DataMapperEntity> dataMappers = new();
        EntityCollection<QuestionLikertLabelEntity> questionLikertLabels = new();
        EntityCollection<QuestionRenewalCopyRuleEntity> questionRenewalCopyRuleEntities = new();
        IEnumerable<QuestionEntity> questionsToClone =
            version.Question.Where(q => q.ParentQuestionGuid == parentQuestionGuid);
        if (oldAndNewCategoryGuids.Any())
        {
            HashSet<QuestionEntity> newQuestionListToClone = new();
            foreach (OldCategoryAndNewCategoryStruct oldCategoryGuid in oldAndNewCategoryGuids)
            {
                newQuestionListToClone.AddRange(questionsToClone.Where(q =>
                    q.QuestionRequiresCategory.Any(qr => qr.CategoryGuid == oldCategoryGuid.OldCategoryGuid)));
            }

            questionsToClone = newQuestionListToClone;
        }

        foreach (QuestionEntity question in questionsToClone)
        {
            QuestionEntity clonedQuestion = CloneQuestion(newSurveyDefinitionVersionGuid, question, version.FactorGroup,
                oldAndNewFactorGroupStructs);
            if (parentQuestionGuid != Guid.Empty)
            {
                QuestionEntity matchingOldQuestion =
                    version.Question.SingleOrDefault(d => d.DataName == question.DataName);
                QuestionEntity matchingOldParentQuestion =
                    version.Question.SingleOrDefault(q => q.QuestionGuid == matchingOldQuestion.ParentQuestionGuid);
                QuestionEntity newQuestionParent =
                    questions.SingleOrDefault(q => q.DataName == matchingOldParentQuestion.DataName);

                if (newQuestionParent != null)
                    clonedQuestion.ParentQuestionGuid = newQuestionParent.QuestionGuid;
            }

            questions.Add(clonedQuestion);

            if (question.QuestionRenewalCopyRule != null)
            {
                questionRenewalCopyRuleEntities.Add(new QuestionRenewalCopyRuleEntity(clonedQuestion.QuestionGuid)
                    { NewValueAsString = question.QuestionRenewalCopyRule.NewValueAsString });
            }

            foreach (QuestionIncludeDocEntity includeDoc in question.QuestionIncludeDoc)
            {
                QuestionIncludeDocEntity newIncludeDoc = new()
                {
                    QuestionGuid = clonedQuestion.QuestionGuid,
                    DocGuid = includeDoc.DocGuid
                };

                includeDocs.Add(newIncludeDoc);
            }

            foreach (QuestionIncludeDocAgencyEntity questionIncludeDocAgency in question.QuestionIncludeDocAgency)
            {
                QuestionIncludeDocAgencyEntity newQuestionIncludeDocAgency = new()
                {
                    QuestionGuid = clonedQuestion.QuestionGuid,
                    DocGuid = questionIncludeDocAgency.DocGuid,
                    CompanyGuid = questionIncludeDocAgency.CompanyGuid,
                    ReplaceWithDocGuid = questionIncludeDocAgency.ReplaceWithDocGuid
                };
                includeDocAgencies.Add(newQuestionIncludeDocAgency);
            }

            foreach (QuestionRequiresAccountTypeEntity questionType in question.QuestionRequiresAccountType)
            {
                QuestionRequiresAccountTypeEntity newQuestionType = new()
                {
                    QuestionGuid = clonedQuestion.QuestionGuid,
                    AccountTypeId = questionType.AccountTypeId
                };
                questionRequires.Add(newQuestionType);
            }

            if (question.DataMapper != null)
            {
                DataMapperEntity newDataMapper = new()
                {
                    QuestionGuid = clonedQuestion.QuestionGuid,
                    NodeTypeId = question.DataMapper.NodeTypeId,
                    FieldName = question.DataMapper.FieldName
                };
                dataMappers.Add(newDataMapper);
            }

            if (question.QuestionLikertLabel != null)
            {
                QuestionLikertLabelEntity questionLikertLabelEntity = new()
                {
                    QuestionGuid = clonedQuestion.QuestionGuid,
                    StronglyDisagreeLabel = question.QuestionLikertLabel.StronglyDisagreeLabel,
                    StronglyAgreeLabel = question.QuestionLikertLabel.StronglyAgreeLabel
                };

                questionLikertLabels.Add(questionLikertLabelEntity);
            }

            EntityCollection<QuestionReferralForCarrierOfferEntity> carrierRefferals =
                question.QuestionReferralForCarrierOffer;
            foreach (QuestionReferralForCarrierOfferEntity carrierReferral in carrierRefferals)
            {
                carrierReferrals.Add(new QuestionReferralForCarrierOfferEntity(clonedQuestion.QuestionGuid,
                    carrierReferral.CarrierMaximumOfferGuid));
            }

            EntityCollection<QuestionAutoClearForCarrierOfferEntity> autoClearCarriers =
                question.QuestionAutoClearForCarrierOffer;
            foreach (QuestionAutoClearForCarrierOfferEntity autoClear in autoClearCarriers)
            {
                autoClearRules.Add(new QuestionAutoClearForCarrierOfferEntity(clonedQuestion.QuestionGuid,
                    autoClear.CarrierMaximumOfferGuid));
            }

            EntityCollection<QuestionReferralBypassAgencyEntity> agencyBypassReferrals =
                question.QuestionReferralBypassAgency;
            foreach (QuestionReferralBypassAgencyEntity bypass in agencyBypassReferrals)
            {
                newAgencyBypass.Add(
                    new QuestionReferralBypassAgencyEntity(clonedQuestion.QuestionGuid, bypass.CompanyGuid));
            }

            AddQuestionRequiresCategory(questionRequiresCategories, question, clonedQuestion, oldAndNewCategoryGuids);
            AddQuestionRequiresCategoryGroup(questionRequiresCategoryGroups, question, clonedQuestion);

            await _quickAdapterAsync.SaveEntityCollectionAsync(questions, true, true);
            await _quickAdapterAsync.SaveEntityCollectionAsync(questionRequiresCategories, false, true);
            await _quickAdapterAsync.SaveEntityCollectionAsync(questionRequiresCategoryGroups, false, true);

            await _quickAdapterAsync.SaveEntityCollectionAsync(carrierReferrals, false, true);
            await _quickAdapterAsync.SaveEntityCollectionAsync(autoClearRules, false, true);
            await _quickAdapterAsync.SaveEntityCollectionAsync(newAgencyBypass, false, true);
            await _quickAdapterAsync.SaveEntityCollectionAsync(includeDocs, false, true);
            await _quickAdapterAsync.SaveEntityCollectionAsync(includeDocAgencies, false, true);
            await _quickAdapterAsync.SaveEntityCollectionAsync(questionRequires, false, true);
            await _quickAdapterAsync.SaveEntityCollectionAsync(dataMappers, false, true);
            await _quickAdapterAsync.SaveEntityCollectionAsync(questionLikertLabels, false, true);
            await _quickAdapterAsync.SaveEntityCollectionAsync(questionRenewalCopyRuleEntities, false, true);

            await BuildQuestions(question.QuestionGuid, version, newSurveyDefinitionVersionGuid, factorGroups,
                questions,
                oldAndNewCategoryGuids, oldAndNewFactorGroupStructs);
        }

        return questions;
    }

    private QuestionEntity CloneQuestion(Guid newSurveyDefinitionVersionGuid, QuestionEntity question,
        EntityCollection<FactorGroupEntity> oldFactorGroups,
        List<OldAndNewFactorGroupStruct> oldAndNewFactorGroupStructs)
    {
        QuestionEntity clonedQuestion = new()
        {
            SurveyDefinitionVersionGuid = newSurveyDefinitionVersionGuid,
            QuestionGuid = Guid.NewGuid(),
            SequenceNum = question.SequenceNum,
            IndentLevelId = question.IndentLevelId,
            DataName = question.DataName,
            DataTypeId = question.DataTypeId,
            IsRequired = question.IsRequired,
            QuestionText = question.QuestionText,
            IsDeleted = question.IsDeleted,
            FloatingSequence = question.FloatingSequence,
            ComboBoxQuery = question.ComboBoxQuery,
            DataFormatStringForDoc = question.DataFormatStringForDoc,
            DataFormatStringForWeb = question.DataFormatStringForWeb,
            DefaultOrCalc = question.DefaultOrCalc,
            HasCarrierSpecificValues = question.HasCarrierSpecificValues,
            DefaultOrCalcParam1 = question.DefaultOrCalcParam1,
            DefaultOrCalcParam2 = question.DefaultOrCalcParam2,
            IsComputedField = question.IsComputedField,
            IsCriticalDisplay = question.IsCriticalDisplay,
            NumberOfColumns = question.NumberOfColumns,
            ConditionalDisplayText = question.ConditionalDisplayText,
            IsReferralRule = question.IsReferralRule,
            ComputedFastLaneOnly = question.ComputedFastLaneOnly,
            CustomAddButtonText = question.CustomAddButtonText,
            HyperLink = question.HyperLink,
            IsAjaxUpdated = question.IsAjaxUpdated,
            QuestionTextHtml = question.QuestionTextHtml,
            UseDataListFor = question.UseDataListFor,
            DisplayOnSummaries = question.DisplayOnSummaries,
            AutoGenerateNoteIfTrue = question.AutoGenerateNoteIfTrue,
            HidePolicyTermIfTrue = question.HidePolicyTermIfTrue,
            MvcController = question.MvcController,
            MvcAction = question.MvcAction,
            JavaScript = question.JavaScript,
            Tooltip = question.Tooltip,
            ValidateWhenHidden = question.ValidateWhenHidden,
            BooleanXmlForAllOptions = question.BooleanXmlForAllOptions,
            SubHeaderText = question.SubHeaderText,
            QuestionTextForTable = question.QuestionTextForTable,
            VisualMergeWithPriorQuestion = question.VisualMergeWithPriorQuestion,
            InvalidateRenewalPricingIfChanged = question.InvalidateRenewalPricingIfChanged,
            CopyForwardOnClone = question.CopyForwardOnClone
        };
        if (question.SystemTableLookupGuid != Guid.Empty)
            clonedQuestion.SystemTableLookupGuid = question.SystemTableLookupGuid;
        FactorGroupEntity matchingFactorGroup =
            oldFactorGroups.SingleOrDefault(q => q.FactorGroupGuid == question.RatingFactorGroupGuid);
        if (matchingFactorGroup != null)
        {
            OldAndNewFactorGroupStruct oldAndNewFactorGroup = oldAndNewFactorGroupStructs.Single(o =>
                o.OldFactorGroup.FactorGroupGuid == matchingFactorGroup.FactorGroupGuid);
            clonedQuestion.RatingFactorGroupGuid = oldAndNewFactorGroup.NewFactorGroup.FactorGroupGuid;
        }

        if (question.ImportQuestionAsDataName.SingleOrDefault() != null)
        {
            ImportQuestionAsDataNameEntity clonedImportQuestionDataName = new()
            {
                QuestionGuid = clonedQuestion.QuestionGuid,
                SurveyDefinitionVersionGuid = newSurveyDefinitionVersionGuid,
                ImportAsDataName = question.ImportQuestionAsDataName.Single().ImportAsDataName
            };
            clonedQuestion.ImportQuestionAsDataName.Add(clonedImportQuestionDataName);
        }

        return clonedQuestion;
    }

    private static void AddQuestionRequiresCategory(
        EntityCollection<QuestionRequiresCategoryEntity> questionRequiresCategories, QuestionEntity question,
        QuestionEntity clonedQuestion, List<OldCategoryAndNewCategoryStruct> oldCategoryAndNewCategoryStructs)
    {
        if (oldCategoryAndNewCategoryStructs.Any())
        {
            foreach (OldCategoryAndNewCategoryStruct oldAndNewCategory in oldCategoryAndNewCategoryStructs)
            {
                foreach (QuestionRequiresCategoryEntity questionCategory in question.QuestionRequiresCategory.Where(q =>
                             q.CategoryGuid == oldAndNewCategory.OldCategoryGuid))
                {
                    QuestionRequiresCategoryEntity newQuestionCategory = new()
                    {
                        QuestionGuid = clonedQuestion.QuestionGuid,
                        CategoryGuid = oldAndNewCategory.NewCategoryGuid
                    };
                    questionRequiresCategories.Add(newQuestionCategory);
                }
            }
        }
        else
        {
            foreach (QuestionRequiresCategoryEntity questionCategory in question.QuestionRequiresCategory)
            {
                QuestionRequiresCategoryEntity newQuestionCategory = new()
                {
                    QuestionGuid = clonedQuestion.QuestionGuid,
                    CategoryGuid = questionCategory.CategoryGuid
                };
                questionRequiresCategories.Add(newQuestionCategory);
            }
        }
    }

    private static void AddQuestionRequiresCategoryGroup(
        EntityCollection<QuestionRequiresCategoryGroupEntity> questionRequiresCategoryGroups, QuestionEntity question,
        QuestionEntity clonedQuestion)
    {
        foreach (QuestionRequiresCategoryGroupEntity questionCategory in question.QuestionRequiresCategoryGroup)
        {
            QuestionRequiresCategoryGroupEntity newQuestionCategory = new()
            {
                QuestionGuid = clonedQuestion.QuestionGuid,
                CategoryGroupGuid = questionCategory.CategoryGroupGuid
            };
            questionRequiresCategoryGroups.Add(newQuestionCategory);
        }
    }

    private async Task AddQuestionRuleContainer(EntityCollection<QuestionEntity> oldQuestions,
        EntityCollection<QuestionEntity> newQuestions, List<OldCategoryAndNewCategoryStruct> oldAndNewCategoryGuids)
    {
        Contract.Requires(oldAndNewCategoryGuids != null, ErrorTypeEnum.ArgumentException,
            "oldAndNewCategoryGuids is null");
        EntityCollection<QuestionRuleContainerEntity> newQuestionRuleContainers = new();
        EntityCollection<QuestionRuleEntity> newQuestionRules = new();
        List<QuestionEntity> questionsToClone = oldQuestions.ToList();
        if (oldAndNewCategoryGuids != null && oldAndNewCategoryGuids.Count != 0)
        {
            HashSet<QuestionEntity> newQuestionsToClone = new();
            foreach (OldCategoryAndNewCategoryStruct oldAndNew in oldAndNewCategoryGuids)
            {
                newQuestionsToClone.AddRange(questionsToClone.Where(q =>
                    q.QuestionRequiresCategory.Any(qr => qr.CategoryGuid == oldAndNew.OldCategoryGuid)));
            }

            questionsToClone = newQuestionsToClone.ToList();
        }

        foreach (QuestionEntity oldQuestion in questionsToClone)
        {
            foreach (QuestionRuleContainerEntity questionRuleContainer in oldQuestion.QuestionRuleContainer)
            {
                QuestionEntity clonedQuestion = newQuestions.Single(q => q.DataName == oldQuestion.DataName);
                Guid newQuestionRuleContainerGuid = Guid.NewGuid();
                QuestionRuleContainerEntity newQuestionRuleContainer = new()
                {
                    QuestionGuid = clonedQuestion.QuestionGuid,
                    QuestionRuleContainerGuid = newQuestionRuleContainerGuid,
                    BooleanOpCode = questionRuleContainer.BooleanOpCode,
                    RuleTypeId = questionRuleContainer.RuleTypeId,
                    InvertLogic = questionRuleContainer.InvertLogic
                };
                foreach (QuestionRuleEntity rule in questionRuleContainer.QuestionRule)
                {
                    QuestionRuleEntity newRule = new()
                    {
                        QuestionVisibilityRuleGuid = Guid.NewGuid(),
                        QuestionRuleContainerGuid = newQuestionRuleContainerGuid,
                        ComparisonTypeId = rule.ComparisonTypeId,
                        DataTransformId = rule.DataTransformId,
                        ApplyToChildren = rule.ApplyToChildren,
                        ComparisonValue = rule.ComparisonValue,
                        ComparisonValue2 = rule.ComparisonValue2,
                        ErrorMessage = rule.ErrorMessage,
                        AdvancedRuleText = rule.AdvancedRuleText
                    };
                    if (rule.ComparisonQuestionGuid != Guid.Empty)
                    {
                        QuestionEntity oldComparisonQuestion1 =
                            oldQuestions.Single(q => q.QuestionGuid == rule.ComparisonQuestionGuid);
                        QuestionEntity newComparisonQuestion1 =
                            newQuestions.Single(q => q.DataName == oldComparisonQuestion1.DataName);
                        newRule.ComparisonQuestionGuid = newComparisonQuestion1.QuestionGuid;
                    }

                    if (rule.ComparisonQuestionGuid2 != Guid.Empty)
                    {
                        QuestionEntity oldComparisonQuestion2 =
                            oldQuestions.Single(q => q.QuestionGuid == rule.ComparisonQuestionGuid2);
                        QuestionEntity newComparisonQuestion2 =
                            newQuestions.Single(q => q.DataName == oldComparisonQuestion2.DataName);
                        newRule.ComparisonQuestionGuid2 = newComparisonQuestion2.QuestionGuid;
                    }

                    newQuestionRuleContainer.QuestionRule.Add(newRule);
                    newQuestionRules.Add(newRule);
                }

                newQuestionRuleContainers.Add(newQuestionRuleContainer);
            }
        }

        await _quickAdapterAsync.SaveEntityCollectionAsync(newQuestionRuleContainers, false, true);
        await _quickAdapterAsync.SaveEntityCollectionAsync(newQuestionRules, false, true);
    }

    private async Task AddQuestionCarrierOfferDefaultCalculations(EntityCollection<QuestionEntity> oldQuestions,
        EntityCollection<QuestionEntity> newQuestions, List<OldCategoryAndNewCategoryStruct> oldAndNewCategoryGuids)
    {
        Contract.Requires(oldAndNewCategoryGuids != null, ErrorTypeEnum.ArgumentException,
            "oldAndNewCategoryGuids is null");
        List<QuestionEntity> questionsToClone = oldQuestions.ToList();
        if (oldAndNewCategoryGuids != null && oldAndNewCategoryGuids.Count != 0)
        {
            HashSet<QuestionEntity> newQuestionsToClone = new();
            foreach (OldCategoryAndNewCategoryStruct oldAndNew in oldAndNewCategoryGuids)
            {
                newQuestionsToClone.AddRange(questionsToClone.Where(q =>
                    q.QuestionRequiresCategory.Any(qr => qr.CategoryGuid == oldAndNew.OldCategoryGuid)));
            }

            questionsToClone = newQuestionsToClone.ToList();
        }

        EntityCollection<QuestionCarrierOfferDefaultCalcEntity> questionCarrierCalcs = new();
        EntityCollection<QuestionCarrierOfferDefaultCalcEntity> questionCarrierOfferDefaultCalcs =
            await _questionCarrierOfferDefaultCalcQuery.GetAllByQuestionCollection(oldQuestions);
        foreach (QuestionEntity oldQuestion in questionsToClone)
        {
            QuestionEntity clonedQuestion = newQuestions.Single(q => q.DataName == oldQuestion.DataName);
            IEnumerable<QuestionCarrierOfferDefaultCalcEntity> oldCalcs =
                questionCarrierOfferDefaultCalcs.Where(q => q.QuestionGuid == oldQuestion.QuestionGuid);
            foreach (QuestionCarrierOfferDefaultCalcEntity oldCalc in oldCalcs)
            {
                questionCarrierCalcs.Add(new QuestionCarrierOfferDefaultCalcEntity()
                {
                    QuestionGuid = clonedQuestion.QuestionGuid,
                    CarrierMaximumOfferGuid = oldCalc.CarrierMaximumOfferGuid,
                    DefaultCalcScript = oldCalc.DefaultCalcScript
                });
            }
        }

        await _quickAdapterAsync.SaveEntityCollectionAsync(questionCarrierCalcs, false, true);
    }

    private async Task AddRatingAndSubTables(SurveyDefinitionVersionEntity oldVersion,
        EntityCollection<QuestionEntity> newQuestions, Guid newSurveyDefinitionVersionGuid,
        List<OldAndNewFactorGroupStruct> factorGroups,
        EntityCollection<CategoryCarrierMaximumOfferEntity> filteredCategoryCarriers)
    {
        EntityCollection<RatingEntity> oldRatings = oldVersion.Rating;
        List<RatingEntity> ratingsToClone = new();
        if (filteredCategoryCarriers != null)
        {
            foreach (CategoryCarrierMaximumOfferEntity carrier in Enumerable.DistinctBy(filteredCategoryCarriers, f =>
                         f.CarrierMaximumOfferGuid))
            {
                ratingsToClone.AddRange(oldRatings.Where(o =>
                    o.CarrierMaximumOfferGuid == carrier.CarrierMaximumOfferGuid));
            }
        }
        else
        {
            ratingsToClone = oldRatings.ToList();
        }

        EntityCollection<QuestionEntity> oldQuestions = oldVersion.Question;
        EntityCollection<RatingEntity> ratingsToSave = new();
        foreach (RatingEntity oldRating in ratingsToClone)
        {
            List<RatingBlockViewModel> ratingModelBlocks = new();
            Guid newRatingGuid = Guid.NewGuid();
            RatingEntity newRating = new()
            {
                RatingGuid = newRatingGuid,
                SurveyDefinitionVersionGuid = newSurveyDefinitionVersionGuid,
                CarrierMaximumOfferGuid = oldRating.CarrierMaximumOfferGuid
            };
            foreach (RatingBlockEntity oldRatingBlock in oldRating.RatingBlock)
            {
                RatingBlockEntity newRatingBlock = new()
                {
                    RatingGuid = newRatingGuid,
                    RatingBlockGuid = Guid.NewGuid(),
                    Comment = oldRatingBlock.Comment,
                    IsOptionSpecific = oldRatingBlock.IsOptionSpecific
                };
                newRating.RatingBlock.Add(newRatingBlock);
                ratingModelBlocks.Add(new RatingBlockViewModel(oldRatingBlock, newRatingBlock));
            }

            await _quickAdapterAsync.SaveEntityAsync(newRating, true, true);
            foreach (RatingBlockEntity oldBlock in oldRating.RatingBlock)
            {
                RatingBlockEntity matchingNewBlock = ratingModelBlocks
                    .Single(r => r.OldBlock.RatingBlockGuid == oldBlock.RatingBlockGuid).NewBlock;
                if (oldBlock.ParentRatingBlockGuid != Guid.Empty)
                {
                    RatingBlockEntity oldParentBlock =
                        oldRating.RatingBlock.Single(r => r.RatingBlockGuid == oldBlock.ParentRatingBlockGuid);
                    RatingBlockViewModel matchingViewModel = ratingModelBlocks.Single(r =>
                        r.OldBlock.RatingBlockGuid == oldParentBlock.RatingBlockGuid);
                    matchingNewBlock.ParentRatingBlockGuid = matchingViewModel.NewBlock.RatingBlockGuid;
                }

                if (oldBlock.PriorSiblingGuid != Guid.Empty)
                {
                    RatingBlockEntity oldSiblingBlock =
                        oldRating.RatingBlock.Single(r => r.RatingBlockGuid == oldBlock.PriorSiblingGuid);
                    RatingBlockViewModel matchingViewModel = ratingModelBlocks.Single(r =>
                        r.OldBlock.RatingBlockGuid == oldSiblingBlock.RatingBlockGuid);
                    matchingNewBlock.PriorSiblingGuid = matchingViewModel.NewBlock.RatingBlockGuid;
                }

                if (oldBlock.NextSiblingGuid != Guid.Empty)
                {
                    RatingBlockEntity oldSiblingBlock =
                        oldRating.RatingBlock.Single(r => r.RatingBlockGuid == oldBlock.NextSiblingGuid);
                    RatingBlockViewModel matchingViewModel = ratingModelBlocks.Single(r =>
                        r.OldBlock.RatingBlockGuid == oldSiblingBlock.RatingBlockGuid);
                    matchingNewBlock.NextSiblingGuid = matchingViewModel.NewBlock.RatingBlockGuid;
                }

                if (oldBlock.RatingBlockApplyFactor != null)
                {
                    RatingBlockApplyFactorEntity oldApplyFactor = oldBlock.RatingBlockApplyFactor;
                    Guid targetRatingBlockGuid = ratingModelBlocks.Single(r =>
                            r.OldBlock.RatingBlockGuid == oldApplyFactor.TargetRatingBlockGuid)
                        .NewBlock.RatingBlockGuid;
                    RatingBlockApplyFactorEntity applyFactor = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        TargetRatingBlockGuid = targetRatingBlockGuid,
                        FactorCollectionGroupGuid = oldApplyFactor.FactorCollectionGroupGuid
                    };
                    matchingNewBlock.RatingBlockApplyFactor = applyFactor;
                }
                else if (oldBlock.RatingBlockApplyFactorFromRating != null)
                {
                    RatingBlockApplyFactorFromRatingEntity oldApplyFactorFromRating =
                        oldBlock.RatingBlockApplyFactorFromRating;
                    Guid targetRatingBlockGuid = ratingModelBlocks
                        .Single(r => r.OldBlock.RatingBlockGuid == oldApplyFactorFromRating.TargetRatingBlockGuid)
                        .NewBlock.RatingBlockGuid;

                    Guid sourceRatingBlockGuid = ratingModelBlocks
                        .Single(r => r.OldBlock.RatingBlockGuid == oldApplyFactorFromRating.SourceRatingBlockGuid)
                        .NewBlock.RatingBlockGuid;

                    OldAndNewFactorGroupStruct oldAndNewFactorGroup = factorGroups.Single(fg =>
                        fg.OldFactorGroup.FactorGroupGuid == oldApplyFactorFromRating.FactorGroupGuid);

                    RatingBlockApplyFactorFromRatingEntity newApplyFactorFromRating = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        TargetRatingBlockGuid = targetRatingBlockGuid,
                        FactorGroupGuid = oldAndNewFactorGroup.NewFactorGroup.FactorGroupGuid,
                        SourceRatingBlockGuid = sourceRatingBlockGuid,
                        FactorLookupRuleId = oldApplyFactorFromRating.FactorLookupRuleId
                    };

                    matchingNewBlock.RatingBlockApplyFactorFromRating = newApplyFactorFromRating;
                }
                else if (oldBlock.RatingBlockCalculate != null)
                {
                    Guid targetRatingBlockGuid = ratingModelBlocks
                        .Single(r => r.OldBlock.RatingBlockGuid == oldBlock.RatingBlockCalculate.TargetRatingBlockGuid)
                        .NewBlock.RatingBlockGuid;
                    RatingBlockCalculateEntity oldRatingBlockCalculate = oldBlock.RatingBlockCalculate;
                    RatingBlockCalculateEntity ratingBlockCalculate = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        TargetRatingBlockGuid = targetRatingBlockGuid,
                        RatingBlockOperationId = oldRatingBlockCalculate.RatingBlockOperationId
                    };
                    if (oldRatingBlockCalculate.Source1RatingBlockGuid != Guid.Empty)
                    {
                        Guid source1RatingBlockGuid = ratingModelBlocks
                            .Single(r => r.OldBlock.RatingBlockGuid == oldRatingBlockCalculate.Source1RatingBlockGuid)
                            .NewBlock.RatingBlockGuid;
                        ratingBlockCalculate.Source1RatingBlockGuid = source1RatingBlockGuid;
                    }

                    if (oldRatingBlockCalculate.Source2RatingBlockGuid != Guid.Empty)
                    {
                        Guid source2RatingBlockGuid = ratingModelBlocks
                            .Single(r => r.OldBlock.RatingBlockGuid == oldRatingBlockCalculate.Source2RatingBlockGuid)
                            .NewBlock.RatingBlockGuid;
                        ratingBlockCalculate.Source2RatingBlockGuid = source2RatingBlockGuid;
                    }

                    if (oldRatingBlockCalculate.Source1QuestionGuid != Guid.Empty)
                    {
                        QuestionEntity oldQuestion = oldQuestions.Single(q =>
                            q.QuestionGuid == oldRatingBlockCalculate.Source1QuestionGuid);
                        QuestionEntity newQuestion =
                            newQuestions.Single(q => q.DataName == oldQuestion.DataName);
                        ratingBlockCalculate.Source1QuestionGuid = newQuestion.QuestionGuid;
                    }

                    if (oldRatingBlockCalculate.Source2QuestionGuid != Guid.Empty)
                    {
                        QuestionEntity oldQuestion = oldQuestions.Single(q =>
                            q.QuestionGuid == oldRatingBlockCalculate.Source2QuestionGuid);
                        QuestionEntity newQuestion =
                            newQuestions.Single(q => q.DataName == oldQuestion.DataName);
                        ratingBlockCalculate.Source2QuestionGuid = newQuestion.QuestionGuid;
                    }

                    if (oldRatingBlockCalculate.Source1SystemTableLookupFieldGuid != Guid.Empty)
                        ratingBlockCalculate.Source1SystemTableLookupFieldGuid =
                            oldRatingBlockCalculate.Source1SystemTableLookupFieldGuid;
                    if (oldRatingBlockCalculate.Source2SystemTableLookupFieldGuid != Guid.Empty)
                        ratingBlockCalculate.Source2SystemTableLookupFieldGuid =
                            oldRatingBlockCalculate.Source2SystemTableLookupFieldGuid;

                    matchingNewBlock.RatingBlockCalculate = ratingBlockCalculate;
                }
                else if (oldBlock.RatingBlockCalculateString != null)
                {
                    Guid targetRatingBlockGuid = ratingModelBlocks.Single(r => r.OldBlock.RatingBlockGuid ==
                                                                               oldBlock.RatingBlockCalculateString
                                                                                   .TargetRatingBlockGuid).NewBlock
                        .RatingBlockGuid;
                    RatingBlockCalculateStringEntity oldRatingBlockCalculate = oldBlock.RatingBlockCalculateString;
                    RatingBlockCalculateStringEntity ratingBlockCalculate = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        TargetRatingBlockGuid = targetRatingBlockGuid,
                        RatingBlockOperationStringId = oldRatingBlockCalculate.RatingBlockOperationStringId
                    };
                    if (oldRatingBlockCalculate.Source1RatingBlockGuid != Guid.Empty)
                    {
                        Guid source1RatingBlockGuid = ratingModelBlocks
                            .Single(r => r.OldBlock.RatingBlockGuid == oldRatingBlockCalculate.Source1RatingBlockGuid)
                            .NewBlock.RatingBlockGuid;
                        ratingBlockCalculate.Source1RatingBlockGuid = source1RatingBlockGuid;
                    }

                    if (oldRatingBlockCalculate.Source2RatingBlockGuid != Guid.Empty)
                    {
                        Guid source2RatingBlockGuid = ratingModelBlocks
                            .Single(r => r.OldBlock.RatingBlockGuid == oldRatingBlockCalculate.Source2RatingBlockGuid)
                            .NewBlock.RatingBlockGuid;
                        ratingBlockCalculate.Source2RatingBlockGuid = source2RatingBlockGuid;
                    }

                    if (oldRatingBlockCalculate.Source1QuestionGuid != Guid.Empty)
                    {
                        QuestionEntity oldQuestion = oldQuestions.Single(q =>
                            q.QuestionGuid == oldRatingBlockCalculate.Source1QuestionGuid);
                        QuestionEntity newQuestion =
                            newQuestions.Single(q => q.DataName == oldQuestion.DataName);
                        ratingBlockCalculate.Source1QuestionGuid = newQuestion.QuestionGuid;
                    }

                    if (oldRatingBlockCalculate.Source2QuestionGuid != Guid.Empty)
                    {
                        QuestionEntity oldQuestion = oldQuestions.Single(q =>
                            q.QuestionGuid == oldRatingBlockCalculate.Source2QuestionGuid);
                        QuestionEntity newQuestion =
                            newQuestions.Single(q => q.DataName == oldQuestion.DataName);
                        ratingBlockCalculate.Source2QuestionGuid = newQuestion.QuestionGuid;
                    }

                    if (oldRatingBlockCalculate.Source1SystemTableLookupFieldGuid != Guid.Empty)
                        ratingBlockCalculate.Source1SystemTableLookupFieldGuid =
                            oldRatingBlockCalculate.Source1SystemTableLookupFieldGuid;
                    if (oldRatingBlockCalculate.Source2SystemTableLookupFieldGuid != Guid.Empty)
                        ratingBlockCalculate.Source2SystemTableLookupFieldGuid =
                            oldRatingBlockCalculate.Source2SystemTableLookupFieldGuid;

                    matchingNewBlock.RatingBlockCalculateString = ratingBlockCalculate;
                }
                else if (oldBlock.RatingBlockCalculateDate != null)
                {
                    RatingBlockCalculateDateEntity oldCalculateDate = oldBlock.RatingBlockCalculateDate;
                    Guid targetRatingBlockGuid = ratingModelBlocks.Single(r =>
                            r.OldBlock.RatingBlockGuid == oldCalculateDate.TargetRatingBlockGuid)
                        .NewBlock.RatingBlockGuid;
                    RatingBlockCalculateDateEntity ratingBlockCalculateDate = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        TargetRatingBlockGuid = targetRatingBlockGuid,
                        RatingBlockDateOperationId = oldCalculateDate.RatingBlockDateOperationId
                    };
                    if (oldCalculateDate.Source1RatingBlockGuid != Guid.Empty)
                    {
                        Guid source1RatingBlockGuid = ratingModelBlocks.Single(r =>
                                r.OldBlock.RatingBlockGuid == oldCalculateDate.Source1RatingBlockGuid)
                            .NewBlock.RatingBlockGuid;
                        ratingBlockCalculateDate.Source1RatingBlockGuid = source1RatingBlockGuid;
                    }

                    if (oldCalculateDate.Source2RatingBlockGuid != Guid.Empty)
                    {
                        Guid source2RatingBlockGuid = ratingModelBlocks.Single(r =>
                                r.OldBlock.RatingBlockGuid == oldCalculateDate.Source2RatingBlockGuid)
                            .NewBlock.RatingBlockGuid;
                        ratingBlockCalculateDate.Source2RatingBlockGuid = source2RatingBlockGuid;
                    }

                    if (oldCalculateDate.Source1QuestionGuid != Guid.Empty)
                    {
                        QuestionEntity oldQuestion =
                            oldQuestions.Single(q => q.QuestionGuid == oldCalculateDate.Source1QuestionGuid);
                        QuestionEntity newQuestion =
                            newQuestions.Single(q => q.DataName == oldQuestion.DataName);
                        ratingBlockCalculateDate.Source1QuestionGuid = newQuestion.QuestionGuid;
                    }

                    if (oldCalculateDate.Source2QuestionGuid != Guid.Empty)
                    {
                        QuestionEntity oldQuestion =
                            oldQuestions.Single(q => q.QuestionGuid == oldCalculateDate.Source2QuestionGuid);
                        QuestionEntity newQuestion =
                            newQuestions.Single(q => q.DataName == oldQuestion.DataName);
                        ratingBlockCalculateDate.Source2QuestionGuid = newQuestion.QuestionGuid;
                    }

                    matchingNewBlock.RatingBlockCalculateDate = ratingBlockCalculateDate;
                }
                else if (oldBlock.RatingBlockCalculateDateDiff != null)
                {
                    RatingBlockCalculateDateDiffEntity oldCalculateDateDiff = oldBlock.RatingBlockCalculateDateDiff;
                    Guid targetRatingBlockGuid = ratingModelBlocks.Single(r =>
                            r.OldBlock.RatingBlockGuid == oldCalculateDateDiff.TargetRatingBlockGuid)
                        .NewBlock.RatingBlockGuid;

                    RatingBlockCalculateDateDiffEntity ratingBlockCalculateDateDiff = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        TargetRatingBlockGuid = targetRatingBlockGuid,
                        RatingBlockDateOperationId = oldCalculateDateDiff.RatingBlockDateOperationId
                    };
                    if (oldCalculateDateDiff.Source1RatingBlockGuid != Guid.Empty)
                    {
                        Guid source1RatingBlockGuid = ratingModelBlocks
                            .Single(r => r.OldBlock.RatingBlockGuid == oldCalculateDateDiff.Source1RatingBlockGuid)
                            .NewBlock.RatingBlockGuid;
                        ratingBlockCalculateDateDiff.Source1RatingBlockGuid = source1RatingBlockGuid;
                    }

                    if (oldCalculateDateDiff.Source2RatingBlockGuid != Guid.Empty)
                    {
                        Guid source2RatingBlockGuid = ratingModelBlocks
                            .Single(r => r.OldBlock.RatingBlockGuid == oldCalculateDateDiff.Source2RatingBlockGuid)
                            .NewBlock.RatingBlockGuid;
                        ratingBlockCalculateDateDiff.Source2RatingBlockGuid = source2RatingBlockGuid;
                    }

                    if (oldCalculateDateDiff.Source1QuestionGuid != Guid.Empty)
                    {
                        QuestionEntity oldQuestion = oldQuestions.Single(q =>
                            q.QuestionGuid == oldCalculateDateDiff.Source1QuestionGuid);
                        QuestionEntity newQuestion =
                            newQuestions.Single(q => q.DataName == oldQuestion.DataName);
                        ratingBlockCalculateDateDiff.Source1QuestionGuid = newQuestion.QuestionGuid;
                    }

                    if (oldCalculateDateDiff.Source2QuestionGuid != Guid.Empty)
                    {
                        QuestionEntity oldQuestion = oldQuestions.Single(q =>
                            q.QuestionGuid == oldCalculateDateDiff.Source2QuestionGuid);
                        QuestionEntity newQuestion =
                            newQuestions.Single(q => q.DataName == oldQuestion.DataName);
                        ratingBlockCalculateDateDiff.Source2QuestionGuid = newQuestion.QuestionGuid;
                    }

                    matchingNewBlock.RatingBlockCalculateDateDiff = ratingBlockCalculateDateDiff;
                }
                else if (oldBlock.RatingBlockCalculateBoolean != null)
                {
                    RatingBlockCalculateBooleanEntity oldCalculateBoolean = oldBlock.RatingBlockCalculateBoolean;
                    Guid targetRatingBlockGuid = ratingModelBlocks.Single(r =>
                            r.OldBlock.RatingBlockGuid == oldCalculateBoolean.TargetRatingBlockGuid)
                        .NewBlock.RatingBlockGuid;

                    RatingBlockCalculateBooleanEntity ratingBlockCalculateBoolean = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        TargetRatingBlockGuid = targetRatingBlockGuid,
                        RatingBlockOperationBooleanId = oldCalculateBoolean.RatingBlockOperationBooleanId
                    };
                    if (oldCalculateBoolean.Source1RatingBlockGuid != Guid.Empty)
                    {
                        Guid source1RatingBlockGuid = ratingModelBlocks
                            .Single(r => r.OldBlock.RatingBlockGuid == oldCalculateBoolean.Source1RatingBlockGuid)
                            .NewBlock.RatingBlockGuid;
                        ratingBlockCalculateBoolean.Source1RatingBlockGuid = source1RatingBlockGuid;
                    }

                    if (oldCalculateBoolean.Source2RatingBlockGuid != Guid.Empty)
                    {
                        Guid source2RatingBlockGuid = ratingModelBlocks
                            .Single(r => r.OldBlock.RatingBlockGuid == oldCalculateBoolean.Source2RatingBlockGuid)
                            .NewBlock.RatingBlockGuid;
                        ratingBlockCalculateBoolean.Source2RatingBlockGuid = source2RatingBlockGuid;
                    }

                    if (oldCalculateBoolean.Source1RatingQuestionGuid != Guid.Empty)
                    {
                        QuestionEntity oldQuestion = oldQuestions.Single(q =>
                            q.QuestionGuid == oldCalculateBoolean.Source1RatingQuestionGuid);
                        QuestionEntity newQuestion =
                            newQuestions.Single(q => q.DataName == oldQuestion.DataName);
                        ratingBlockCalculateBoolean.Source1RatingQuestionGuid = newQuestion.QuestionGuid;
                    }

                    if (oldCalculateBoolean.Source2RatingQuestionGuid != Guid.Empty)
                    {
                        QuestionEntity oldQuestion = oldQuestions.Single(q =>
                            q.QuestionGuid == oldCalculateBoolean.Source2RatingQuestionGuid);
                        QuestionEntity newQuestion =
                            newQuestions.Single(q => q.DataName == oldQuestion.DataName);
                        ratingBlockCalculateBoolean.Source2RatingQuestionGuid = newQuestion.QuestionGuid;
                    }

                    matchingNewBlock.RatingBlockCalculateBoolean = ratingBlockCalculateBoolean;
                }
                else if (oldBlock.RatingBlockDeclareDate != null)
                {
                    RatingBlockDeclareDateEntity oldDeclareDate = oldBlock.RatingBlockDeclareDate;
                    RatingBlockDeclareDateEntity ratingBlockDeclareDate = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        VariableName = oldDeclareDate.VariableName,
                        InitialValue = oldDeclareDate.InitialValue
                    };
                    matchingNewBlock.RatingBlockDeclareDate = ratingBlockDeclareDate;
                }
                else if (oldBlock.RatingBlockDeclareDecimal != null)
                {
                    RatingBlockDeclareDecimalEntity oldDeclareDate = oldBlock.RatingBlockDeclareDecimal;
                    RatingBlockDeclareDecimalEntity ratingBlockDeclareDecimal = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        VariableName = oldDeclareDate.VariableName,
                        InitialValue = oldDeclareDate.InitialValue
                    };
                    matchingNewBlock.RatingBlockDeclareDecimal = ratingBlockDeclareDecimal;
                }
                else if (oldBlock.RatingBlockDeclareString != null)
                {
                    RatingBlockDeclareStringEntity oldDeclareString = oldBlock.RatingBlockDeclareString;
                    RatingBlockDeclareStringEntity ratingBlockDeclareString = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        VariableName = oldDeclareString.VariableName,
                        InitialValue = oldDeclareString.InitialValue
                    };
                    matchingNewBlock.RatingBlockDeclareString = ratingBlockDeclareString;
                }
                else if (oldBlock.RatingBlockCustomModel != null)
                {
                    RatingBlockCustomModelEntity oldCustomModel = oldBlock.RatingBlockCustomModel;
                    RatingBlockCustomModelEntity ratingBlockCustomModel = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        CustomModelId = oldCustomModel.CustomModelId
                    };
                    matchingNewBlock.RatingBlockCustomModel = ratingBlockCustomModel;
                }
                else if (oldBlock.RatingBlockDeclareBoolean != null)
                {
                    RatingBlockDeclareBooleanEntity oldDeclareBoolean = oldBlock.RatingBlockDeclareBoolean;
                    RatingBlockDeclareBooleanEntity ratingBlockDeclareBoolean = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        VariableName = oldDeclareBoolean.VariableName,
                        InitialValue = oldDeclareBoolean.InitialValue
                    };
                    matchingNewBlock.RatingBlockDeclareBoolean = ratingBlockDeclareBoolean;
                }
                else if (oldBlock.RatingBlockIfTrue != null)
                {
                    RatingBlockIfTrueEntity oldIfTrue = oldBlock.RatingBlockIfTrue;
                    Guid newSourceBooleanRatingBlockGuid = ratingModelBlocks.Single(r =>
                            r.OldBlock.RatingBlockGuid == oldIfTrue.SourceBooleanRatingBlockGuid)
                        .NewBlock.RatingBlockGuid;
                    RatingBlockIfTrueEntity ratingBlockIfTrue = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        SourceBooleanRatingBlockGuid = newSourceBooleanRatingBlockGuid
                    };
                    matchingNewBlock.RatingBlockIfTrue = ratingBlockIfTrue;
                }
                else if (oldBlock.RatingBlockIfFalse != null)
                {
                    RatingBlockIfFalseEntity oldIfFalse = oldBlock.RatingBlockIfFalse;
                    Guid newSourceBooleanRatingBlockGuid = ratingModelBlocks.Single(r =>
                            r.OldBlock.RatingBlockGuid == oldIfFalse.SourceBooleanRatingBlockGuid)
                        .NewBlock.RatingBlockGuid;
                    RatingBlockIfFalseEntity ratingBlockIfFalse = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        SourceBooleanRatingBlockGuid = newSourceBooleanRatingBlockGuid
                    };
                    matchingNewBlock.RatingBlockIfFalse = ratingBlockIfFalse;
                }
                else if (oldBlock.RatingBlockApplyCumulativePremium != null)
                {
                    RatingBlockApplyCumulativePremiumEntity oldCummulativePremium =
                        oldBlock.RatingBlockApplyCumulativePremium;
                    Guid sourceRatingBlockGuid = ratingModelBlocks.Single(r =>
                            r.OldBlock.RatingBlockGuid == oldCummulativePremium.SourceRatingBlockGuid)
                        .NewBlock.RatingBlockGuid;
                    Guid targetRatingBlockGuid = ratingModelBlocks.Single(r =>
                            r.OldBlock.RatingBlockGuid == oldCummulativePremium.TargetRatingBlockGuid)
                        .NewBlock.RatingBlockGuid;
                    RatingBlockApplyCumulativePremiumEntity ratingBlockApplyCumulative = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        TargetRatingBlockGuid = targetRatingBlockGuid,
                        SourceRatingBlockGuid = sourceRatingBlockGuid,
                        CumulativePremiumFactorLookupGuid = oldCummulativePremium.CumulativePremiumFactorLookupGuid
                    };
                    matchingNewBlock.RatingBlockApplyCumulativePremium = ratingBlockApplyCumulative;
                }
                else if (oldBlock.RatingBlockForEach != null)
                {
                    RatingBlockForEachEntity oldForEach = oldBlock.RatingBlockForEach;
                    QuestionEntity oldTableQuestion =
                        oldQuestions.Single(o => o.QuestionGuid == oldForEach.TableQuestionGuid);
                    QuestionEntity newTableQuestion =
                        newQuestions.Single(n => n.DataName == oldTableQuestion.DataName);
                    RatingBlockForEachEntity ratingBlockForEach = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        TableQuestionGuid = newTableQuestion.QuestionGuid
                    };
                    if (oldForEach.AltRateIfBlockGuidTrue != Guid.Empty)
                    {
                        Guid newAltRateIfBlockGuidTrue = ratingModelBlocks.Single(r =>
                                r.OldBlock.RatingBlockGuid == oldForEach.AltRateIfBlockGuidTrue)
                            .NewBlock.RatingBlockGuid;
                        ratingBlockForEach.AltRateIfBlockGuidTrue = newAltRateIfBlockGuidTrue;
                    }

                    matchingNewBlock.RatingBlockForEach = ratingBlockForEach;
                }
                else if (oldBlock.RatingBlockProgram != null)
                {
                    RatingBlockProgramEntity ratingBlockProgram = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid
                    };
                    matchingNewBlock.RatingBlockProgram = ratingBlockProgram;
                }
                else if (oldBlock.RatingBlockContext != null)
                {
                    RatingBlockContextEntity ratingBlockContext = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        SurveyDefinitionContextGuid = oldBlock.RatingBlockContext.SurveyDefinitionContextGuid,
                        ContextCarrierPriority = oldBlock.RatingBlockContext.ContextCarrierPriority
                    };
                    matchingNewBlock.RatingBlockContext = ratingBlockContext;
                }
                else if (oldBlock.RatingBlockCalculateContext != null)
                {
                    RatingBlockCalculateContextEntity ratingBlockCalculateContext = new()
                    {
                        RatingBlockGuid = matchingNewBlock.RatingBlockGuid,
                        ContextMappingRuleId = oldBlock.RatingBlockCalculateContext.ContextMappingRuleId
                    };
                    matchingNewBlock.RatingBlockCalculateContext = ratingBlockCalculateContext;
                }
                else //assume a new block has been made but not implemented in versioning, record critical error
                {
                    throw new ApplicationException(
                        $"Rating Block was found during versioning that did not have a sub type. Rating Block Guid for Old version: {oldBlock.RatingBlockGuid}");
                }
            }

            ratingsToSave.Add(newRating);
        }

        await _quickAdapterAsync.SaveEntityCollectionAsync(ratingsToSave, false, true);
    }

    private async Task BuildNewBinderFormInclusion(SurveyDefinitionVersionEntity version,
        Guid newSurveyDefinitionVersionGuid, EntityCollection<FactorEntity> factors,
        EntityCollection<QuestionEntity> questions,
        EntityCollection<CategoryCarrierMaximumOfferEntity> filteredCategoryCarriers)
    {
        EntityCollection<BinderFormInclusionEntity> newBinderFormInclusions = new();
        HashSet<BinderFormInclusionEntity> binderFormInclusionsToClone = new();
        if (filteredCategoryCarriers != null)
        {
            foreach (CategoryCarrierMaximumOfferEntity categoryCarrier in filteredCategoryCarriers)
            {
                var matchingCarrier = await _carriersQuery
                    .GetByCarrierMaximumOfferGuid(categoryCarrier.CarrierMaximumOfferGuid);
                Guid carrierGuid = matchingCarrier.CarrierGuid;
                IEnumerable<BinderFormInclusionEntity> filteredBinderFormInclusions =
                    version.BinderFormInclusion.Where(v => v.Form.CarrierGuid == carrierGuid);
                binderFormInclusionsToClone.AddRange(filteredBinderFormInclusions);
            }
        }
        else
        {
            binderFormInclusionsToClone = MoreEnumerable.ToHashSet(version.BinderFormInclusion);
        }

        foreach (BinderFormInclusionEntity binderFormInclusion in binderFormInclusionsToClone)
        {
            BinderFormInclusionEntity newInclusion = new()
            {
                BinderFormInclusionGuid = Guid.NewGuid(),
                FormGuid = binderFormInclusion.FormGuid,
                SurveyDataOpCode = binderFormInclusion.SurveyDataOpCode,
                SurveyDefinitionVersionGuid = newSurveyDefinitionVersionGuid,
                SourceBinderFormInclusion = binderFormInclusion.BinderFormInclusionGuid,
                VariationNumber = binderFormInclusion.VariationNumber
            };
            if (binderFormInclusion.AdditionalDocumentInfoTemplate != null)
                newInclusion.AdditionalDocumentInfoTemplate = binderFormInclusion.AdditionalDocumentInfoTemplate;
            foreach (BinderFormByCategoryEntity binderFormByCategory in binderFormInclusion.BinderFormByCategory)
            {
                BinderFormByCategoryEntity category = new()
                {
                    BinderFormInclusionGuid = newInclusion.BinderFormInclusionGuid,
                    ClassOfBusinessGuid = binderFormByCategory.ClassOfBusinessGuid,
                    IncludeForm = binderFormByCategory.IncludeForm
                };
                newInclusion.BinderFormByCategory.Add(category);
            }

            foreach (BinderFormForStateEntity binderState in binderFormInclusion.BinderFormForState)
            {
                BinderFormForStateEntity newState = new()
                {
                    BinderFormInclusionGuid = newInclusion.BinderFormInclusionGuid,
                    StateCode = binderState.StateCode,
                    FormStateRuleTypeId = binderState.FormStateRuleTypeId
                };
                newInclusion.BinderFormForState.Add(newState);
            }

            foreach (BinderFormByResponseEntity binderFormByResponse in binderFormInclusion.BinderFormByResponse)
            {
                QuestionEntity oldMatchingQuestion =
                    version.Question.Single(q => q.QuestionGuid == binderFormByResponse.QuestionGuid);
                QuestionEntity newMatchingQuestion =
                    questions.SingleOrDefault(q => q.DataName == oldMatchingQuestion.DataName);
                if (newMatchingQuestion != null)
                {
                    BinderFormByResponseEntity response = new()
                    {
                        BinderFormInclusionGuid = newInclusion.BinderFormInclusionGuid,
                        RequiredValue = binderFormByResponse.RequiredValue,
                        QuestionGuid = newMatchingQuestion.QuestionGuid
                    };
                    newInclusion.BinderFormByResponse.Add(response);
                }
            }

            foreach (BinderFormByFactorEntity binderFormByFactor in binderFormInclusion.BinderFormByFactor)
            {
                FactorEntity oldMatchingFactor = version.FactorGroup
                    .SelectMany(f => f.Factor).SingleOrDefault(f => f.FactorGuid == binderFormByFactor.FactorGuid);
                if (oldMatchingFactor != null)
                {
                    FactorEntity newMatchingFactor = factors.Single(f =>
                        f.FactorDisplayName == oldMatchingFactor.FactorDisplayName &&
                        f.Value == oldMatchingFactor.Value);
                    BinderFormByFactorEntity formByFactor = new()
                    {
                        BinderFormInclusionGuid = newInclusion.BinderFormInclusionGuid,
                        FactorGuid = newMatchingFactor.FactorGuid,
                        IncludeForm = binderFormByFactor.IncludeForm
                    };
                    newInclusion.BinderFormByFactor.Add(formByFactor);
                }
            }

            newBinderFormInclusions.Add(newInclusion);
        }

        await _quickAdapterAsync.SaveEntityCollectionAsync(newBinderFormInclusions, false, true);
    }

    private async Task RevLimitsDeductiblesAndTerms(SurveyDefinitionVersionEntity version,
        Guid newSurveyDefinitionVersionGuid, EntityCollection<QuestionEntity> questions,
        EntityCollection<CategoryCarrierMaximumOfferEntity> filteredCategoryCarriers)
    {
        EntityCollection<CarrierOfferLimitEntity> newLimits = new();
        EntityCollection<CarrierOfferDeductibleEntity> newDeductibles = new();
        EntityCollection<CarrierOffersTermEntity> newTerms = new();
        EntityCollection<LimitDeductibleBasedMinimumPremiumEntity> newLimitDeductCombos = new();

        HashSet<CarrierOfferLimitEntity> carrierOfferLimitsToClone = new();
        if (filteredCategoryCarriers != null)
        {
            foreach (CategoryCarrierMaximumOfferEntity categoryCarrier in filteredCategoryCarriers)
            {
                IEnumerable<CarrierOfferLimitEntity> filteredBinderFormInclusions =
                    version.CarrierOfferLimit.Where(v =>
                        v.CarrierMaximumOfferGuid == categoryCarrier.CarrierMaximumOfferGuid);
                carrierOfferLimitsToClone.AddRange(filteredBinderFormInclusions);
            }
        }
        else
        {
            carrierOfferLimitsToClone = MoreEnumerable.ToHashSet(version.CarrierOfferLimit);
        }

        foreach (CarrierOfferLimitEntity oldLimit in carrierOfferLimitsToClone)
        {
            Guid hideIfTrueQuestionGuid = questions
                                              .Where(q => q.DataName == oldLimit.Question?.DataName)?.SingleOrDefault()
                                              ?.QuestionGuid
                                          ?? Guid.Empty;

            CarrierOfferLimitEntity newLimit = new()
            {
                CarrierOfferLimitGuid = Guid.NewGuid(),
                CarrierMaximumOfferGuid = oldLimit.CarrierMaximumOfferGuid,
                OccLimitRequestedGuid = oldLimit.OccLimitRequestedGuid,
                AggLimitRequestedGuid = oldLimit.AggLimitRequestedGuid,
                Factor = oldLimit.Factor,
                MinimumPremium = oldLimit.MinimumPremium,
                IsFastLaneOnly = oldLimit.IsFastLaneOnly,
                IsAvailableOnline = oldLimit.IsAvailableOnline,
                IsAvailableFastLane = oldLimit.IsAvailableFastLane,
                IsAvailableManual = oldLimit.IsAvailableManual,
                MinimumRevenue = oldLimit.MinimumRevenue,
                SurveyDefinitionVersionGuid = newSurveyDefinitionVersionGuid,
                CopiedFromCarrierOfferLimitGuid = oldLimit.CarrierOfferLimitGuid,
                IsSecondaryForMarketSummary = oldLimit.IsSecondaryForMarketSummary,
                RequireBrokerApproval = oldLimit.RequireBrokerApproval
            };

            if (hideIfTrueQuestionGuid != Guid.Empty)
                newLimit.HideIfTrueQuestionGuid = hideIfTrueQuestionGuid;
            if (oldLimit.SurveyDefinitionContextGuid != Guid.Empty)
                newLimit.SurveyDefinitionContextGuid = oldLimit.SurveyDefinitionContextGuid;

            newLimits.Add(newLimit);
        }

        HashSet<CarrierOfferDeductibleEntity> carrierOfferDeductiblesToClone = new();
        if (filteredCategoryCarriers != null)
        {
            foreach (CategoryCarrierMaximumOfferEntity categoryCarrier in filteredCategoryCarriers)
            {
                IEnumerable<CarrierOfferDeductibleEntity> filteredBinderFormInclusions =
                    version.CarrierOfferDeductible.Where(v =>
                        v.CarrierMaximumOfferGuid == categoryCarrier.CarrierMaximumOfferGuid);
                carrierOfferDeductiblesToClone.AddRange(filteredBinderFormInclusions);
            }
        }
        else
        {
            carrierOfferDeductiblesToClone = MoreEnumerable.ToHashSet(version.CarrierOfferDeductible);
        }

        foreach (CarrierOfferDeductibleEntity oldDeductible in carrierOfferDeductiblesToClone)
        {
            Guid hideIfTrueQuestionGuid = questions
                                              .Where(q => q.DataName == oldDeductible.Question?.DataName)
                                              ?.SingleOrDefault()?.QuestionGuid
                                          ?? Guid.Empty;

            CarrierOfferDeductibleEntity newDeductible = new()
            {
                CarrierOfferDeductibleGuid = Guid.NewGuid(),
                CarrierMaximumOfferGuid = oldDeductible.CarrierMaximumOfferGuid,
                DeductibleRequestedGuid = oldDeductible.DeductibleRequestedGuid,
                Factor = oldDeductible.Factor,
                IncludeInSummary = oldDeductible.IncludeInSummary,
                MinimumPremium = oldDeductible.MinimumPremium,
                IsFastLaneOnly = oldDeductible.IsFastLaneOnly,
                IsAvailableOnline = oldDeductible.IsAvailableOnline,
                IsAvailableFastLane = oldDeductible.IsAvailableFastLane,
                IsAvailableManual = oldDeductible.IsAvailableManual,
                MinimumRevenue = oldDeductible.MinimumRevenue,
                SurveyDefinitionVersionGuid = newSurveyDefinitionVersionGuid,
                CopiedFromCarrierOfferDeductibleGuid = oldDeductible.CarrierOfferDeductibleGuid,
                IsSecondaryForMarketSummary = oldDeductible.IsSecondaryForMarketSummary
            };

            if (hideIfTrueQuestionGuid != Guid.Empty)
                newDeductible.HideIfTrueQuestionGuid = hideIfTrueQuestionGuid;

            if (oldDeductible.MinimumLimitCarrierOfferLimitGuid != Guid.Empty)
                newDeductible.MinimumLimitCarrierOfferLimitGuid = newLimits
                    .Single(l => l.SurveyDefinitionVersionGuid == newSurveyDefinitionVersionGuid &&
                                 l.CopiedFromCarrierOfferLimitGuid == oldDeductible.MinimumLimitCarrierOfferLimitGuid)
                    .CarrierOfferLimitGuid;

            if (oldDeductible.MaximumLimitCarrierOfferLimitGuid != Guid.Empty)
                newDeductible.MaximumLimitCarrierOfferLimitGuid = newLimits
                    .Single(l => l.SurveyDefinitionVersionGuid == newSurveyDefinitionVersionGuid &&
                                 l.CopiedFromCarrierOfferLimitGuid == oldDeductible.MaximumLimitCarrierOfferLimitGuid)
                    .CarrierOfferLimitGuid;

            newDeductibles.Add(newDeductible);
        }

        HashSet<CarrierOffersTermEntity> carrierOfferTermsToClone = new();
        if (filteredCategoryCarriers != null)
        {
            foreach (CategoryCarrierMaximumOfferEntity categoryCarrier in filteredCategoryCarriers)
            {
                IEnumerable<CarrierOffersTermEntity> filteredBinderFormInclusions =
                    version.CarrierOffersTerm.Where(v =>
                        v.CarrierMaximumOfferGuid == categoryCarrier.CarrierMaximumOfferGuid);
                carrierOfferTermsToClone.AddRange(filteredBinderFormInclusions);
            }
        }
        else
        {
            carrierOfferTermsToClone = MoreEnumerable.ToHashSet(version.CarrierOffersTerm);
        }

        foreach (CarrierOffersTermEntity oldTerm in carrierOfferTermsToClone)
        {
            Guid hideIfTrueQuestionGuid = questions
                                              .Where(q => q.DataName == oldTerm.Question?.DataName)?.SingleOrDefault()
                                              ?.QuestionGuid
                                          ?? Guid.Empty;
            CarrierOffersTermEntity newTerm = new()
            {
                CarrierOffersTermGuid = Guid.NewGuid(),
                CarrierMaximumOfferGuid = oldTerm.CarrierMaximumOfferGuid,
                PolicyTermId = oldTerm.PolicyTermId,
                Factor = oldTerm.Factor,
                MinimumPremium = oldTerm.MinimumPremium,
                IncludeInSummary = oldTerm.IncludeInSummary,
                IsFastLaneOnly = oldTerm.IsFastLaneOnly,
                SurveyDefinitionVersionGuid = newSurveyDefinitionVersionGuid,
                IsSecondaryForMarketSummary = oldTerm.IsSecondaryForMarketSummary
            };
            if (hideIfTrueQuestionGuid != Guid.Empty)
                newTerm.HideIfTrueQuestionGuid = hideIfTrueQuestionGuid;
            if (oldTerm.SurveyDefinitionContextGuid != Guid.Empty)
                newTerm.SurveyDefinitionContextGuid = oldTerm.SurveyDefinitionContextGuid;

            newTerms.Add(newTerm);
        }

        List<LimitDeductibleBasedMinimumPremiumEntity> allCombos =
            carrierOfferLimitsToClone.SelectMany(l => l.LimitDeductibleBasedMinimumPremium).ToList();
        allCombos.AddRange(
            carrierOfferDeductiblesToClone.SelectMany(d => d.LimitDeductibleBasedMinimumPremium).ToList());

        foreach (LimitDeductibleBasedMinimumPremiumEntity combo in allCombos.Distinct())
        {
            LimitDeductibleBasedMinimumPremiumEntity newCombo = new()
            {
                MinimumPremium = combo.MinimumPremium,
                Factor = combo.Factor,
                CarrierOfferLimitGuid = newLimits.Single(l =>
                        l.SurveyDefinitionVersionGuid == newSurveyDefinitionVersionGuid &&
                        l.CopiedFromCarrierOfferLimitGuid == combo.CarrierOfferLimitGuid)
                    .CarrierOfferLimitGuid,
                CarrierOfferDeductibleGuid = newDeductibles.Single(l =>
                        l.SurveyDefinitionVersionGuid == newSurveyDefinitionVersionGuid &&
                        l.CopiedFromCarrierOfferDeductibleGuid == combo.CarrierOfferDeductibleGuid)
                    .CarrierOfferDeductibleGuid,
            };
            newLimitDeductCombos.Add(newCombo);
        }

        await _quickAdapterAsync.SaveEntityCollectionAsync(newLimits, false, true);

        await _quickAdapterAsync.SaveEntityCollectionAsync(newDeductibles, false, true);
        await _quickAdapterAsync.SaveEntityCollectionAsync(newTerms, false, true);
        await _quickAdapterAsync.SaveEntityCollectionAsync(newLimitDeductCombos, false, true);
    }
}

public struct OldAndNewFactorGroupStruct : IEquatable<OldAndNewFactorGroupStruct>
{
    public FactorGroupEntity OldFactorGroup { get; set; }
    public FactorGroupEntity NewFactorGroup { get; set; }

    public OldAndNewFactorGroupStruct(FactorGroupEntity oldFactorGroup, FactorGroupEntity newFactorGroup)
    {
        OldFactorGroup = oldFactorGroup;
        NewFactorGroup = newFactorGroup;
    }

    public override bool Equals(object obj)
    {
        return base.Equals(obj);
    }

    public override int GetHashCode()
    {
        return base.GetHashCode();
    }

    public static bool operator ==(OldAndNewFactorGroupStruct left, OldAndNewFactorGroupStruct right)
    {
        return left.Equals(right);
    }

    public static bool operator !=(OldAndNewFactorGroupStruct left, OldAndNewFactorGroupStruct right)
    {
        return !left.Equals(right);
    }

    public bool Equals(OldAndNewFactorGroupStruct other)
    {
        return this.NewFactorGroup.FactorGroupGuid == other.NewFactorGroup.FactorGroupGuid &&
               this.OldFactorGroup.FactorGroupGuid == other.OldFactorGroup.FactorGroupGuid;
    }
}

public class RatingBlockViewModel
{
    public RatingBlockEntity OldBlock { get; set; }
    public RatingBlockEntity NewBlock { get; set; }

    public RatingBlockViewModel(RatingBlockEntity oldBlock, RatingBlockEntity newBlock)
    {
        OldBlock = oldBlock;
        NewBlock = newBlock;
    }
}