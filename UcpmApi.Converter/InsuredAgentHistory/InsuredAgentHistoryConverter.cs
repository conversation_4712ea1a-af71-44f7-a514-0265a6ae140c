using MoreLinq;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using SD.LLBLGen.Pro.LinqSupportClasses;
using StackExchange.Redis;
using UcpmApi.Query.InsuredAgentHistory;
using UcpmApi.Shared;
using UcpmApi.Shared.Policy;
using UcpmTools;

namespace UcpmApi.Converter
{
    public class InsuredAgentHistoryConverter : BaseConverter
    {
        private readonly InsuredAgentHistoryQuery _insuredAgentHistoryQuery;

        public InsuredAgentHistoryConverter(InsuredAgentHistoryQuery insuredAgentHistoryQuery)
        {
            _insuredAgentHistoryQuery = insuredAgentHistoryQuery;
        }

        public async Task<InsuredAgentHistoryModel> GetInsuredAgentHistoryByGuid(Guid insuredAgentHistoryGuid)
        {
            InsuredAgentHistoryEntity insuredAgentHistory = await _insuredAgentHistoryQuery.GetByInsuredAgentHistoryGuid(insuredAgentHistoryGuid);

            return ToView(insuredAgentHistory);
        }

        public async Task<InsuredAgentHistory> GetInsuredAgentHistoryByAgentAndInsuredGuid(Guid agentGuid, Guid insuredGuid)
        {
            InsuredAgentHistoryEntity insuredAgentHistory =
                await _insuredAgentHistoryQuery.GetInsuredAgentHistoryByAgentAndInsuredGuid(agentGuid, insuredGuid);

            return ToViewInsuredAgentHistoryEntity(insuredAgentHistory);
        }

        public async Task<InsuredAgentHistoryModel> GetByInsuredAgentWithLatestAgentByInsuredGuid(Guid insuredGuid)
        {
            InsuredAgentHistoryEntity insuredAgentHistory = await _insuredAgentHistoryQuery.GetByInsuredAgentWithLatestAgentByInsuredGuid(insuredGuid);

            return ToView(insuredAgentHistory);
        }

        public async Task<InsuredAgentHistoryModel> GetInsuredAgentHistoryWithAgentAndCompany(Guid insuredAgentHistoryGuid)
        {
            InsuredAgentHistoryEntity insuredAgentHistoryEntity = await _insuredAgentHistoryQuery.GetInsuredAgentHistoryWithAgentAndCompany()
                .Where(iah => iah.InsuredAgentHistoryGuid == insuredAgentHistoryGuid)
                .FirstOrDefaultAsync();

            return ToView(insuredAgentHistoryEntity);
        }

        public async Task<List<InsuredAgentHistoryModel>> GetInsuredHistoryByEmployee(Guid employeeGuid, int maxRecords)
        {
            EntityCollection<InsuredAgentHistoryEntity> history = await _insuredAgentHistoryQuery.GetHistoryByEmployee(employeeGuid, maxRecords);
            return ToView(history);
        }

        public static List<InsuredAgentHistoryModel> ToView(EntityCollection<InsuredAgentHistoryEntity> history)
        {
            List<InsuredAgentHistoryModel> list = [];
            foreach (InsuredAgentHistoryEntity item in history)
            {
                list.Add(ToView(item));
            }
            return list;
        }

        public async Task<List<InsuredAgentHistoryModel>> GetInsuredFromSearchTerm(string searchTerm)
        {
            EntityCollection<InsuredAgentHistoryEntity> history = await _insuredAgentHistoryQuery.GetInsuredFromSearchTerm(searchTerm);
            return ToView(history);
        }

        public async Task<List<InsuredAgentHistoryModel>> GetInsuredAgentHistoryByAgent(Guid agentGuid)
        {
            EntityCollection<InsuredAgentHistoryEntity> insuredAgentHistoryCollection = await _insuredAgentHistoryQuery.GetInsuredAgentHistoryByAgent(agentGuid);
            List<InsuredAgentHistoryModel> list = [];
            insuredAgentHistoryCollection.ForEach(f => list.Add(ToView(f)));
            return list;
        }
        public async Task<List<InsuredAgentHistoryModel>> GetInsuredAgentHistoryByInsuredAgent(Guid insuredAgentHistoryGuid)
        {
            EntityCollection<InsuredAgentHistoryEntity> insuredAgentHistoryCollection = await _insuredAgentHistoryQuery.GetInsuredAgentHistoryByInsuredAgent(insuredAgentHistoryGuid);
            List<InsuredAgentHistoryModel> list = [];
            insuredAgentHistoryCollection.ForEach(f => list.Add(ToView(f)));
            return list;
        }
        

        public static InsuredAgentHistoryModel ToSimpleView(InsuredAgentHistoryEntity entity)
        {
            InsuredAgentHistoryModel insuredAgentHistory = new();
            if (entity != null)
            {
                insuredAgentHistory = new()
                {
                    Name = entity.Insured?.Name,
                    Address = entity.Insured?.Address,
                    Address2 = entity.Insured?.Address2,
                    City = entity.Insured?.City,
                    State = entity.Insured?.State,
                    Zip = entity.Insured?.Zip,
                    County = entity.Insured?.RiskCounty,
                    Presenter = entity.Agent?.AgentName,
                    InsuredAgentHistoryGuid = entity.InsuredAgentHistoryGuid,
                    Packages = entity.Package?.Where(w => !w.IsDeleted).Select(s => s).OrderByDescending(o => o.PackageDate).ThenBy(o => o.Program.Abbreviation).ToDictionary(k => k.PackageGuid, v => $"Pkg: {v.PackageDate.Year} - {v.Program.Abbreviation}"),
                    Notes = entity.Insured?.Notes,
                    AgentGuid = entity.AgentGuid,
                    AgentEmail = entity.Agent?.AgentEmail,
                    InsuredGuid = entity.InsuredGuid
                };
            }

            return insuredAgentHistory;
        }

        public static InsuredAgentHistoryModel ToView(InsuredAgentHistoryEntity entity)
        {
            InsuredAgentHistoryModel insuredAgentHistory = new();
            if (entity != null)
            {
                insuredAgentHistory = new()
                {
                    Name = entity.Insured.Name,
                    Address = entity.Insured.Address,
                    Address2 = entity.Insured.Address2,
                    City = entity.Insured.City,
                    State = entity.Insured.State,
                    Zip = entity.Insured.Zip,
                    County = entity.Insured.RiskCounty,
                    Presenter = entity.Agent?.AgentName ?? "Not prefetched",
                    InsuredAgentHistoryGuid = entity.InsuredAgentHistoryGuid,
                    Packages = entity.Package.Where(w => !w.IsDeleted)
                    .Select(s => s)
                    .OrderByDescending(o => o.PackageDate)
                    .ThenBy(o => o.Program.Abbreviation)
                    .ToDictionary(k => k.PackageGuid, v => $"Pkg: {v.PackageDate.Year} - {v.Program.Abbreviation}")
                };

                List<PolicyProspectEntity> policies = entity.Package.SelectMany(p => p.PolicyProspect).ToList();

                PolicyProspectEntity chosenPolicy = policies?.Where(w => !w.IsDeleted &&
                    w.FinancialSummaryPolicy != null &&
                    !w.FinancialSummaryPolicy.CarrierSubmission.PolicyStatus.IsBound &&
                    w.FinancialSummaryPolicy.CarrierSubmission.PolicyStatus.IsOpen)
                    .OrderBy(d => d.EffectiveDateZoned)
                    .ThenBy(p => p.PolicyType.PriorityOrder)?.FirstOrDefault() ?? null;

                chosenPolicy ??= policies?.OrderByDescending(p => p.RecordCreatedZoned)?.FirstOrDefault() ?? null;
                if (chosenPolicy != null)
                {
                    insuredAgentHistory.EffectiveDate = chosenPolicy.EffectiveDateZoned;
                    insuredAgentHistory.QuoteDueDate = chosenPolicy.QuoteDueDateZoned;
                    insuredAgentHistory.ProposalDate = chosenPolicy.ProposedRetroDate;
                    insuredAgentHistory.PolicyGuid = chosenPolicy.PolicyProspectGuid;
                    insuredAgentHistory.PackageGuid = chosenPolicy.PackageGuid;
                }

                if (entity.Agent != null)
                {
                    Agent agent = new();
                    QuickReflection.CopyProps(entity.Agent, agent);
                    insuredAgentHistory.Agent = agent;

                    if (entity.Agent.Location != null && entity.Agent.Location.Company != null)
                    {
                        Company company = new();
                        QuickReflection.CopyProps(entity.Agent.Location, company);
                        insuredAgentHistory.Agency = company;
                    }
                    insuredAgentHistory.AgentEmail = entity.Agent.AgentEmail;
                }
                if (entity.Insured != null)
                {
                    Insured insured = new();
                    QuickReflection.CopyProps(entity.Insured, insured);
                    insuredAgentHistory.Insured = insured;
                }

                insuredAgentHistory.PolicyProspect = entity.Package.SelectMany(p => p.PolicyProspect).Select(ToListPolicyProspect).ToList();
                insuredAgentHistory.Notes = entity.Insured.Notes;
                insuredAgentHistory.AgentGuid = entity.AgentGuid;

                insuredAgentHistory.InsuredGuid = entity.InsuredGuid;
            }

            return insuredAgentHistory;
        }

        private static PolicyProspect ToListPolicyProspect(PolicyProspectEntity entity)
        {
            PolicyProspect policyProspect = new();
            QuickReflection.CopyProps(entity, policyProspect);

            if (entity.PolicyType != null)
            {
                policyProspect.PolicyType = new PolicyType()
                {
                    PolicyTypeName = entity.PolicyType.PolicyType
                };
                QuickReflection.CopyProps(entity.PolicyType, policyProspect.PolicyType);

            }

            if (entity.Package != null)
            {
                policyProspect.Package = new Package();
                QuickReflection.CopyProps(entity.Package, policyProspect.Package);

                if (entity.Package.Program != null)
                {
                    policyProspect.Package.Program = new ProgramModel();
                    QuickReflection.CopyProps(entity.Package.Program, policyProspect.Package.Program);
                }
            }
            policyProspect.CarrierSubmissions = entity.CarrierSubmission.Select(ToListCarrierSubmission).ToList();

            return policyProspect;
        }

        static CarrierSubmission ToListCarrierSubmission(CarrierSubmissionEntity entity)
        {
            CarrierSubmission carrierSubmission = new();
            QuickReflection.CopyProps(entity, carrierSubmission);

            if (entity.NxusType != null)
            {
                carrierSubmission.NxusType = new NxusType()
                {
                    NxusTypeId = entity.NxusType.NxusTypeId,
                    Name = entity.NxusType.Name
                };
                QuickReflection.CopyProps(entity.NxusType, carrierSubmission.NxusType);
            }
            if (entity.CarrierSubmissionOption != null)
            {
                carrierSubmission.CarrierSubmissionOption = entity.CarrierSubmissionOption.Select(option => new CarrierSubmissionOption
                {
                    LimitOccurrence = option.LimitOccurrence,
                    LimitAggregate = option.LimitAggregate

                }).ToList();
                QuickReflection.CopyProps(entity.CarrierSubmissionOption, carrierSubmission.CarrierSubmissionOption);
            }
            return carrierSubmission;
        }
        public async Task<IEnumerable<InsuredAgentHistory>> GetOrphanedInsuredAgentHistories(int rowsToTake)
        {
            EntityCollection<InsuredAgentHistoryEntity> insuredAgentHistories = await _insuredAgentHistoryQuery.GetOrphanedInsuredAgentHistories(rowsToTake);

            return insuredAgentHistories.Select(ToViewInsuredAgentHistoryEntity);
        }

        private InsuredAgentHistory ToViewInsuredAgentHistoryEntity(InsuredAgentHistoryEntity insuredAgentHistoryEntity)
        {
            InsuredAgentHistory insuredAgentHistory = new();

            QuickReflection.CopyProps(insuredAgentHistoryEntity, insuredAgentHistory);

            if (insuredAgentHistoryEntity.Insured != null)
            {
                insuredAgentHistory.Insured = new();
                QuickReflection.CopyProps(insuredAgentHistoryEntity.Insured, insuredAgentHistory.Insured);
            }

            return insuredAgentHistory;
        }
    }
}
