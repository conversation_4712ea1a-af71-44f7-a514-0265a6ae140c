using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using UcpmApi.Query.License;
using UcpmApi.Shared;
using UcpmTools;

namespace UcpmApi.Converter.License;

public class IssuedLicenseConverter : BaseConverter
{
    private readonly IssuedLicenseQuery _issuedLicenseQuery;
    
    public IssuedLicenseConverter(IssuedLicenseQuery issuedLicenseQuery)
    {
        _issuedLicenseQuery = issuedLicenseQuery;
    }
    
    public async Task<List<IssuedLicense>> GetAllIssuedLicensesByStateGuid(Guid stateLicenseGuid)
    {
        EntityCollection<IssuedLicenseEntity> issuedLicenseEntities =
            await _issuedLicenseQuery.GetIssuedLicensesByStateGuid(stateLicenseGuid);

        return issuedLicenseEntities.Select(ToView).OrderBy(l => l.LicenseNumber).ToList();
    }

    private IssuedLicense ToView(IssuedLicenseEntity issuedLicenseEntity)
    {
        IssuedLicense issuedLicense = new();
        QuickReflection.CopyProps(issuedLicenseEntity, issuedLicense);
        if (issuedLicenseEntity.LicenseType is not null)
        {
            LicenseType licenseType = new();
            QuickReflection.CopyProps(issuedLicenseEntity.LicenseType, licenseType);
            issuedLicense.LicenseType = licenseType;
        }

        if (issuedLicenseEntity.IssuedLicensePeriod is null ||
            issuedLicenseEntity.IssuedLicensePeriod.Count == 0) return issuedLicense;
        issuedLicense.IssuedLicensePeriod = [];
        
        foreach (IssuedLicensePeriodEntity issuedLicensePeriodEntity in issuedLicenseEntity.IssuedLicensePeriod)
        {
            IssuedLicensePeriod issuedLicensePeriod = new();
            QuickReflection.CopyProps(issuedLicensePeriodEntity, issuedLicensePeriod);
            issuedLicense.IssuedLicensePeriod.Add(issuedLicensePeriod);
            if (issuedLicensePeriodEntity.Employee is not null)
            {
                Employee employee = new();
                QuickReflection.CopyProps(issuedLicensePeriodEntity.Employee, employee);
                issuedLicensePeriod.Employee = employee;
            }
        }
        return issuedLicense;
    }
}