using AzureWebJob.Tasks;
using Moq;
using UcpmApi.Shared.Api.Carrier;
using UcpmApi.Shared.ApiClientBase.Interfaces;
using UcpmApi.Shared.Carrier;

namespace AzureWebJobTest;

[TestClass]
public class PolicyNumberRecoveryTest
{
    [TestMethod]
    public async Task Process_ReturnsExpectedMessage_NoPolicyRecordsToRecover()
    {
        // Arrange
        var carrierPolicyPoolRequestApi = new Mock<CarrierPolicyPoolRequestApi>();
        var errorComponent = new Mock<IErrorComponent>();
        var jwtToken = "eyJ";
        var policyNumberRecovery =
            new PolicyNumberRecovery(carrierPolicyPoolRequestApi.Object, errorComponent.Object, jwtToken);
        var response = new PolicyNumberRecoveryResponse
        {
            Message = "No records to recover",
            RecordsSaved = 0
        };
        carrierPolicyPoolRequestApi.Setup(x => x.GetPolicyNumberRecovery(errorComponent.Object, jwtToken))
            .ReturnsAsync(response);

        // Act
        var result = await policyNumberRecovery.Process();

        // Assert
        Assert.AreEqual("No records to recover", result);
    }

    [TestMethod]
    public async Task Process_ReturnsExpectedMessage_RecordsRecovered()
    {
        // Arrange
        var carrierPolicyPoolRequestApi = new Mock<CarrierPolicyPoolRequestApi>();
        var errorComponent = new Mock<IErrorComponent>();
        var jwtToken = "eyJ";
        var policyNumberRecovery =
            new PolicyNumberRecovery(carrierPolicyPoolRequestApi.Object, errorComponent.Object, jwtToken);
        var response = new PolicyNumberRecoveryResponse
        {
            Message = "2 policy numbers recovered",
            RecordsSaved = 2
        };
        carrierPolicyPoolRequestApi.Setup(x => x.GetPolicyNumberRecovery(errorComponent.Object, jwtToken))
            .ReturnsAsync(response);

        // Act
        var result = await policyNumberRecovery.Process();

        // Assert
        Assert.AreEqual("2 policy numbers recovered", result);
    }
}