using AzureWebJob.Tasks;
using Moq;
using System.Text;
using UcpmApi.Shared.Api.Bot;
using UcpmApi.Shared.ApiClientBase.Interfaces;
using UcpmApi.Shared.Background;

namespace AzureWebJobTest
{
    [TestClass]
    public class CelebrationBotTest
    {
        [TestMethod]
        public void Process_ReturnsExpectedMessage_MessageSending()
        {
            // Arrange
            Mock<CelebrationBotApi> celebrationBotApi = new();
            Job job = new() { JobGuid = Guid.NewGuid() };
            Mock<IErrorComponent> errorComponent = new();
            string jwtToken = "eyJ";
            string employeeName = "Test Employee";
            string insuredName = "Insured Name";
            decimal ucpmIncome = 1340.50M;
            JobLogApi jobLogApi = new();
            CelebrationBot celebrationBotProcess = new(celebrationBotApi.Object, job, jobLogApi, errorComponent.Object, jwtToken);

            StringBuilder message = new();
            message.Append($"{employeeName} has bound the following accounts!");
            message.AppendLine($"   REN - {insuredName} {ucpmIncome.ToString("C0")}");
            message.AppendLine($"   NB - {insuredName} {ucpmIncome.ToString("C0")}");
            message.AppendLine($"   ACQ - {insuredName} {ucpmIncome.ToString("C0")}");

            celebrationBotApi.Setup(x => x.SendMessage(message.ToString(), errorComponent.Object, jwtToken)).ReturnsAsync(true);

            // Act
            Task<string> result = celebrationBotProcess.Run();

            // Assert
            Assert.AreEqual(message, message);
        }
    }
}
