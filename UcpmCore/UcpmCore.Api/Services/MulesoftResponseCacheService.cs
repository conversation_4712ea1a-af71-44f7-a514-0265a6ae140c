using Google.Protobuf;

namespace UcpmCore.Api.Services
{
    public class MulesoftResponseCacheService : BaseService
    {
        private Dictionary<Guid, IMessage> MessageGuidMessagePairs { get; set; }
        public MulesoftResponseCacheService()
        {
            MessageGuidMessagePairs = [];
        }
        public IMessage GetMessage(Guid messageGuid)
        {
            if(MessageGuidMessagePairs.ContainsKey(messageGuid))
            {
                return MessageGuidMessagePairs[messageGuid];
            }
            else
            {
                return null;
            }
        }
        public void AddMessage(Guid messageGuid, IMessage message)
        {
            MessageGuidMessagePairs.Add(messageGuid, message);
        }
        public void RemoveMessage(Guid messageGuid)
        {
            MessageGuidMessagePairs.Remove(messageGuid);
        }
    }
}
