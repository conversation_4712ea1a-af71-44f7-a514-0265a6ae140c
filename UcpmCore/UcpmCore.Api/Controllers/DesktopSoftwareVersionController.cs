using ApiTools.ApiResponses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Command.Lookups;
using UcpmApi.Converter;
using UcpmApi.Query.Lookups;
using UcpmApi.Shared;
using UcpmTools;

namespace UcpmCore.Api.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v2/[controller]")]
    public class DesktopSoftwareVersionController : ControllerBase
    {
        private readonly DesktopSoftwareVersionCommand _desktopSoftwareVersionCommand;
        private readonly DesktopSoftwareVersionConverter _desktopSoftwareVersionConverter;
        private readonly DesktopSoftwareVersionQuery _desktopSoftwareVersionQuery;

        public DesktopSoftwareVersionController(
            DesktopSoftwareVersionCommand desktopSoftwareVersionCommand,
            DesktopSoftwareVersionConverter desktopSoftwareVersionConverter,
            DesktopSoftwareVersionQuery desktopSoftwareVersionQuery)
        {
            _desktopSoftwareVersionCommand = desktopSoftwareVersionCommand;
            _desktopSoftwareVersionConverter = desktopSoftwareVersionConverter;
            _desktopSoftwareVersionQuery = desktopSoftwareVersionQuery;
        }

        [HttpGet("GetActiveUsers")]
        public async Task<IActionResult> GetActiveUsers(int softwareId)
        {
            IEnumerable<DesktopSoftwareVersion> desktopSoftwareVersion =
                await _desktopSoftwareVersionConverter.GetActiveUsers(softwareId);

            return Ok(desktopSoftwareVersion);
        }

        [HttpGet("GetUserVersion")]
        public async Task<IActionResult> GetUserVersion(string machineName, int desktopSoftwareId)
        {
            DesktopSoftwareVersion desktopSoftwareVersion =
                await _desktopSoftwareVersionConverter.GetUserVersion(machineName, desktopSoftwareId);

            return Ok(desktopSoftwareVersion);
        }

        [HttpPost("AddLog")]
        public async Task<IActionResult> AddLog(DesktopSoftwareVersion logToAdd)
        {
            // get the desktop software version
            ORMStandard.EntityClasses.DesktopSoftwareVersionEntity desktopSoftwareVersionEntity = _desktopSoftwareVersionQuery.GetUserVersion(logToAdd.MachineName,
                logToAdd.DesktopSoftwareId);
            // copy the properties from the log to the entity
            QuickReflection.CopyProps(logToAdd, desktopSoftwareVersionEntity);
            bool addLogStatus = await _desktopSoftwareVersionCommand.AddLog(desktopSoftwareVersionEntity);
            if (!addLogStatus)
            {
                return BadRequest(ApiResponse<DesktopSoftwareVersion>.Get400Response("Failed to add log"));
            }
            return Ok(logToAdd);
        }

        [HttpGet("GetActiveInLastHour")]
        public async Task<IActionResult> GetActiveInLastHour(Guid employeeGuid)
        {
            IEnumerable<DesktopSoftwareVersion> activeUsersRecievers =
                await _desktopSoftwareVersionConverter.RetrieveActiveInLastHour(1, employeeGuid);
            return Ok(activeUsersRecievers);
        }
    }
}