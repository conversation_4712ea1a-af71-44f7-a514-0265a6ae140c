
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Converter.Flex;
using UcpmApi.Shared;

namespace UcpmApi.UcpmWebApi.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v2/[controller]")]
    public class CarrierSubmissionFlexResponseController : ControllerBase
    {
        private readonly CarrierSubmissionFlexResponseConverter _carrierSubmissionFlexResponseConverter;

        public CarrierSubmissionFlexResponseController(CarrierSubmissionFlexResponseConverter carrierSubmissionFlexResponseConverter)
        {
            _carrierSubmissionFlexResponseConverter = carrierSubmissionFlexResponseConverter;
        }

        [HttpGet("GetByFlexResponse")]
        public async Task<IActionResult> GetByFlexResponse(Guid flexResponseGuid)
        {
            IEnumerable<CarrierSubmissionFlexResponse> carrierSubmissions = await _carrierSubmissionFlexResponseConverter.GetByFlexResponse(flexResponseGuid);
            return Ok(carrierSubmissions);
        }
    }
}
