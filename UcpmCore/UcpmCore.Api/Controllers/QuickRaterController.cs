using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Converter.Flex;
using UcpmApi.Converter.FlexDefinition;
using UcpmApi.Shared;
using UcpmApi.Shared.Flex;

namespace UcpmCore.Api.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v2/[controller]")]
    public class QuickRaterController : ControllerBase
    {
        private readonly FlexPricingConverter _flexRatingConverter;
        private readonly FlexPublishedVersionConverter _flexPublishedVersionConverter;

        public QuickRaterController(FlexPublishedVersionConverter flexPublishedVersionConverter, FlexPricingConverter flexRatingConverter)
        {
            _flexRatingConverter = flexRatingConverter;
            _flexPublishedVersionConverter = flexPublishedVersionConverter;
        }

        [HttpPost("GetPricing")]
        public async Task<IActionResult> GetPricing(QuickRater quickRater)
        {
            int flexDefinitionId = 3;

            FlexPublishedVersion flexPublishedVersion = _flexPublishedVersionConverter.GetFlexPublishedVersionById(flexDefinitionId);
            FlexRating pricingModel = await _flexRatingConverter.GetQuickRaterPricing(quickRater, flexPublishedVersion, false);

            return Ok(pricingModel);
        }
    }
}
