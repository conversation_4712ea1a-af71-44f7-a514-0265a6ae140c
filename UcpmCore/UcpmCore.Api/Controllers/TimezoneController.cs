using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Converter;
using UcpmApi.Converter.Timezone;
using UcpmApi.Shared;

namespace UcpmCore.Api.Controllers
{
    [ApiController]
    [Authorize(Policy = "policytracker")]
    [Route("api/v2/[controller]")]
    public class TimezoneController : ControllerBase
    {
        private readonly TimezoneConverter _timezoneConverter;

        public TimezoneController(TimezoneConverter timezoneConverter)
        {
            _timezoneConverter = timezoneConverter;
        }

        [HttpGet("GetTimezones")]
        public async Task<IActionResult> GetTimezones()
        {
            List<Timezone> timezones = await _timezoneConverter.GetTimezone();

            return Ok(timezones);
        }
    }
}
