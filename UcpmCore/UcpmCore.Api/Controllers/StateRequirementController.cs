using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Converter;
using UcpmApi.Shared;

namespace UcpmCore.Api.Controllers
{
    [Route("api/v2/[controller]")]
    [ApiController]
    [Authorize]
    public class StateRequirementController : ControllerBase
    {
        private readonly StateRequirementConverter _stateRequirementConverter;

        public StateRequirementController(StateRequirementConverter stateRequirementConverter)
        {
            _stateRequirementConverter = stateRequirementConverter;
        }

        [HttpGet("GetByStateCode")]
        public async Task<IActionResult> GetByStateCode(string state)
        {
            IEnumerable<StateRequirement> stateRequirements = await _stateRequirementConverter.Get(state);

            return Ok(stateRequirements);
        }
    }
}
