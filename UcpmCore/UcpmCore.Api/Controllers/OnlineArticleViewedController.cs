using ApiTools.ApiResponses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Command.News;
using UcpmApi.Converter.News;
using UcpmApi.Query.News;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.News;

namespace UcpmCore.Api.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v2/[controller]")]
    public class OnlineArticleViewedController : Controller
    {
        private readonly OnlineArticleViewedCommand _onlineArticleViewedCommand;
        private readonly OnlineArticleViewedConverter _onlineArticleViewedConverter;

        public OnlineArticleViewedController(OnlineArticleViewedCommand onlineArticleViewedCommand,
            OnlineArticleViewedConverter onlineArticleViewedConverter)
        {
            _onlineArticleViewedCommand = onlineArticleViewedCommand;
            _onlineArticleViewedConverter = onlineArticleViewedConverter;
        }

        [HttpPost("CreateOnlineArticleViewEntry")]
        public async Task<IActionResult> CreateOnlineArticleViewEntry(Guid accountGuid, Guid onlineArticleGuid)
        {
            bool result = await _onlineArticleViewedCommand.CreateOnlineArticleViewEntry(accountGuid, onlineArticleGuid);
            return result ? Ok(result) : StatusCode(StatusCodes.Status500InternalServerError);
        }

        [HttpPost("ClearUnReadNotificationsByPublishLocation")]
        public async Task<IActionResult> ClearUnReadNotificationsByPublishLocation(Guid accountGuid, int publishLocation)
        {
            bool result = await _onlineArticleViewedConverter.ClearUnReadNotificationsByPublishLocation(accountGuid, (PublishLocationEnum)publishLocation);
            return result ? Ok(result) : StatusCode(StatusCodes.Status500InternalServerError);
        }
    }
}