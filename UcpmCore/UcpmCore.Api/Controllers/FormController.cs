using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Command.Coverage;
using UcpmApi.Converter.Coverage;
using UcpmApi.Shared.Coverage;

namespace UcpmCore.Api.Controllers
{
    [ApiController]
    [Authorize(Policy = "fullaccess")]
    [Route("api/v2/[controller]")]
    public class FormController : ControllerBase
    {
        private readonly FormCommand _formCommand;
        private readonly FormConverter _formConverter;

        public FormController(
            FormCommand formCommand,
            FormConverter formConverter)
        {
            _formCommand = formCommand;
            _formConverter = formConverter;
        }

        [HttpGet("GetAllForms")]
        public async Task<IActionResult> GetAllForms()
        {
            IEnumerable<Form> forms = await _formConverter.GetAllForms();

            return Ok(forms);
        }

        [HttpGet("GetAllFormsForOcrInCplProgram")]
        public async Task<IActionResult> GetAllFormsForOcrInCplProgram()
        {
            IEnumerable<Form> forms = await _formConverter.GetAllFormsForOcrInCplProgram();

            return Ok(forms);
        }
    }
}
