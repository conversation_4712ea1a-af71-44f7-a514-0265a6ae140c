using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using System.Text;
using UcpmApi.BusinessModel.Flex;
using UcpmApi.Command.Carrier;
using UcpmApi.Command.FlexDefinition;
using UcpmApi.Converter;
using UcpmApi.Converter.Flex;
using UcpmApi.Query.Flex;
using UcpmApi.Query.FlexDefinition;
using UcpmApi.Shared;
using UcpmApi.Shared.Api;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Flex;

namespace UcpmCore.Api.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/v2/[controller]")]
    public class FlexRatingController : ControllerBase
    {
        private readonly FlexRatingConverter _flexRatingConverter;
        private readonly FlexPricingConverter _flexPricingConverter;
        private readonly FlexDefinitionCommand _flexDefinitionCommand;
        private readonly FlexPublishedVersionQuery _flexPublishedVersionQuery;
        private readonly FlexDefinitionQuery _flexDefinitionQuery;
        private readonly FlexResponseQuery _flexResponseQuery;
        private readonly CarrierSubmissionConverter _carrierSubmissionConverter;
        private readonly CarrierSubmissionCommand _carrierSubmissionCommand;

        public FlexRatingController(
            FlexRatingConverter flexRatingConverter, FlexDefinitionCommand flexDefinitionCommand,
            FlexPublishedVersionQuery flexPublishedVersionQuery, FlexDefinitionQuery flexDefinitionQuery,
            FlexResponseQuery flexResponseQuery, FlexPricingConverter flexPricingConverter,
            CarrierSubmissionConverter carrierSubmissionConverter, CarrierSubmissionCommand carrierSubmissionCommand)
        {
            _flexRatingConverter = flexRatingConverter;
            _flexDefinitionCommand = flexDefinitionCommand;
            _flexPublishedVersionQuery = flexPublishedVersionQuery;
            _flexDefinitionQuery = flexDefinitionQuery;
            _flexResponseQuery = flexResponseQuery;
            _flexPricingConverter = flexPricingConverter;
            _carrierSubmissionConverter = carrierSubmissionConverter;
            _carrierSubmissionCommand = carrierSubmissionCommand;
        }

        [HttpGet("GetPricing")]
        public async Task<IActionResult> GetPricing(Guid flexResponseGuid, bool payableByInsured, bool includeOptions, int sourceOfContactId)
        {
            bool success = await _flexDefinitionCommand.UpdateCalculatedFields(Guid.Empty, true, flexResponseGuid);
            FlexPricingResult flexPricingResult = await _flexPricingConverter.GetPricing(flexResponseGuid, (SourceOfContactEnum)sourceOfContactId, false, (int)NxusTypeEnum.None, payableByInsured, includeOptions);

            return Ok(flexPricingResult.FlexRating);
        }

        [HttpGet("GetRating")]
        public async Task<IActionResult> GetRating(int variationNumber, int policyTypeId, int flexDefinitionId, Guid carrierGuid)
        {
            FlexVersionCarrierOffer offer = await _flexPublishedVersionQuery.GetRating(variationNumber, policyTypeId, flexDefinitionId, carrierGuid);
            return Ok(offer);
        }

        [HttpPost("GenerateSelectedOptions")]
        public async Task<IActionResult> GenerateSelectedOptions(FlexRating flexRating)
        {
            FlexRating pricingModel = await _flexPricingConverter.GenerateSelectedOptions(flexRating.PolicyProspectGuid, flexRating.FlexResponseGuid, flexRating.Options, flexRating.OptionsToDelete, flexRating.BasePremiumBreakdown);

            return Ok(pricingModel);
        }

        [HttpPost("GenerateSingleOption")]
        [AllowAnonymous]
        public async Task<IActionResult> GenerateSingleOption(CustomizeSubmitModel flexRating, int sourceOfContactId = -1)
        {
            bool success = await _flexDefinitionCommand.UpdateCalculatedFields(Guid.Empty, true, flexRating.FlexResponseGuid);
            FlexRating pricingModel = new();


            foreach (IGrouping<int, CustomizeOption> item in flexRating.SelectedOptions.GroupBy(d => d.UcpmRenewableStatus))
            {
                List<FlexOption> optionsToGenerate = [];
                FlexPricingResult flexPricingResult = await _flexPricingConverter.GetPricing(flexRating.FlexResponseGuid, (SourceOfContactEnum)sourceOfContactId, false, nxusTypeId: (int)NxusTypeEnum.NXUSMax, true);

                foreach (CustomizeOption subItem in item)
                {
                    foreach (FlexOption option in flexPricingResult.FlexRating.Options)
                    {
                        if (option.LimitLabel == subItem.LimitLabel &&
                            option.DeductibleLabel == subItem.DeductibleLabel &&
                            option.CarrierMaximumOfferGuid == subItem.CarrierMaximumOfferGuid)
                        {
                            option.UcpmRenewableStatus = subItem.UcpmRenewableStatus;
                            optionsToGenerate.Add(option);
                        }
                    }
                }

                List<FlexOption> optionsToDelete = flexPricingResult.FlexRating.Options.Except(optionsToGenerate).ToList();
                pricingModel = await _flexPricingConverter.GenerateSelectedOptions(Guid.Empty, flexRating.FlexResponseGuid,
                    optionsToGenerate, optionsToDelete, flexPricingResult.FlexRating.BasePremiumBreakdown);
            }


            return Ok(pricingModel);
        }

        [HttpPost("GenerateCustomOptions")]
        [AllowAnonymous]
        public async Task<IActionResult> GenerateCustomOptions(NxusCustomSubmitModel model)
        {
            FlexPricingResult flexPricingResult = await _flexPricingConverter.GetPricing(model.FlexResponseGuid, SourceOfContactEnum.Pathfinder, false, model.NxusTypeId, includeOptions: true);
            List<FlexOption> optionsToDelete = [];
            flexPricingResult.FlexRating = await _flexPricingConverter.GenerateSelectedOptions(Guid.Empty, model.FlexResponseGuid,
                flexPricingResult.FlexRating.Options, optionsToDelete, flexPricingResult.FlexRating.BasePremiumBreakdown);

            List<CarrierSubmission> submissions = await _carrierSubmissionConverter.GetCarrierSubmissionsByPackage(model.PackageGuid);
            CarrierSubmission? carrierSubmission = submissions.Where(x => x.NxusTypeId == (int)NxusTypeEnum.NXUSCustom).FirstOrDefault();
            if (carrierSubmission != null && carrierSubmission.PolicyStatusId != (int)PolicyStatusEnum.QuotedSent)
            {
                carrierSubmission.PolicyStatusId = (int)PolicyStatusEnum.QuotedSent;
                List<string> propertiesToExclude = [nameof(CarrierSubmissionFields.PolicySubStatusGuid), nameof(CarrierSubmissionFields.CarrierMarketerGuid)];
                await _carrierSubmissionCommand.UpdateSubmission(carrierSubmission, propertiesToExclude);
            }

            return Ok(flexPricingResult.FlexRating);
        }

        [HttpPost("GenerateNxusQuote")]
        [AllowAnonymous]
        public async Task<IActionResult> GenerateNxusQuote(GenerateNxusQuoteModel model)
        {
            FlexRating rating = new();
            List<FlexRating> ratings = [];
            List<FlexOption> optionsToDelete = [];
            foreach (NxusTypeEnum nxusTypeId in model.NxusTypes)
            {
                FlexPricingResult flexPricingResult = await _flexPricingConverter.GetPricing(model.FlexResponseGuid, SourceOfContactEnum.Pathfinder, false, (int)nxusTypeId, includeOptions: true);
                ratings.Add(flexPricingResult.FlexRating);
            }

            if (ratings.Any())
            {
                rating = await _flexPricingConverter.GenerateSelectedOptions(Guid.Empty, model.FlexResponseGuid, ratings.SelectMany(r => r.Options).ToList(), optionsToDelete, ratings.FirstOrDefault()?.BasePremiumBreakdown);
            }

            return Ok(rating);
        }

        [HttpPost("UpdateQuotePdf")]
        public async Task<IActionResult> UpdateQuotePdf(Guid flexResponseGuid, Guid carrierMaximumOfferGuid)
        {
            FlexRating pricingModel =
                await _flexPricingConverter.UpdateQuotePdf(flexResponseGuid, carrierMaximumOfferGuid);

            return Ok(pricingModel);
        }

        [HttpPost("UpdateAppPdf")]
        public async Task<IActionResult> UpdateAppPdf(Guid flexResponseGuid, Guid carrierMaximumOfferGuid)
        {
            FlexRating pricingModel =
                await _flexPricingConverter.UpdateAppPdf(flexResponseGuid, carrierMaximumOfferGuid);

            return Ok(pricingModel);
        }

        [HttpPost("UpdateFlexPdfs")]
        public async Task<IActionResult> UpdateFlexPdfs(Guid flexResponseGuid, Guid carrierMaximumOfferGuid)
        {
            FlexRating pricingModel =
                await _flexPricingConverter.UpdateFlexPdfs(flexResponseGuid, carrierMaximumOfferGuid);

            return Ok(pricingModel);
        }

        [HttpPost("CreateQuoteWithSaveOption")]
        public async Task<IActionResult> CreateQuoteWithSaveOption(Guid flexResponseGuid, Guid carrierSubmissionGuid, int saveOption)
        {
            DocInfo quote = await _flexPricingConverter.CreateQuoteWithSaveOption(flexResponseGuid, carrierSubmissionGuid, saveOption);

            return Ok(quote);
        }

        [HttpPost("CreateAppWithSaveOption")]
        public async Task<IActionResult> CreateAppWithSaveOption(Guid flexResponseGuid, Guid carrierSubmissionGuid, int saveOption)
        {
            DocInfo app = await _flexPricingConverter.CreateAppWithSaveOption(flexResponseGuid, carrierSubmissionGuid, saveOption);

            return Ok(app);
        }

        [HttpPost("CreateManualSubmission")]
        public async Task<IActionResult> CreateManualSubmission(FlexRating flexRating)
        {
            FlexRating pricingModel = await _flexPricingConverter.CreateManualSubmission(flexRating.FlexResponseGuid);

            return Ok(pricingModel);
        }

        [HttpGet("CreateDocsAndEmailsForSubmission")]

        public async Task<IActionResult> CreateDocsAndEmailsForSubmission(Guid carrierSubmissionGuid, Guid flexResponseGuid)
        {
            CarrierSubmissionEntity submission = FlexEntityFetcherModel.GetSubmissionForDocGeneration(carrierSubmissionGuid);
            StringBuilder result = new();
            string message = await _flexPricingConverter.CreateDocsAndEmailsForSubmission(submission, flexResponseGuid, result);
            ApiStringResponse response = new() { Result = message };
            return Ok(response);
        }
    }
}