using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using UcpmApi.BusinessModel.Filesystem;
using UcpmApi.Converter;
using UcpmApi.Shared;
using UcpmApi.Shared.Mail;
using UcpmApi.Shared.Request;

namespace UcpmCore.Api.Controllers
{
    [ApiController]
    [Authorize(Policy = "fullaccess")]
    [Route("api/v2/[controller]")]
    public class DirectSendController : ControllerBase
    {
        private readonly DirectSendReplacementModel _directSendReplacementModel;
        private readonly DirectSendTemplateVersionConverter _directSendTemplateVersionConverter;

        public DirectSendController(
            DirectSendReplacementModel directSendReplacementModel,
            DirectSendTemplateVersionConverter directSendTemplateVersionConverter)
        {
            _directSendReplacementModel = directSendReplacementModel;
            _directSendTemplateVersionConverter = directSendTemplateVersionConverter;
        }

        [HttpPost("SendEmail")]
        [AllowAnonymous]
        public async Task<IActionResult> SendEmail([FromBody][Required] DirectSend send)
        {
            await _directSendReplacementModel.SetUpModel(send);
            long directSendLogId = await _directSendReplacementModel.SendMailAsync(send);
            DirectSendLog log = new()
            {
                DirectSendLogId = directSendLogId
            };
            return Ok(log);
        }

        [HttpPost("SendCoverageVerifierDocProgressEmail")]
        [AllowAnonymous]
        public async Task<IActionResult> SendCoverageVerifierDocProgressEmail(SendCoverageVerifierDocProgressRequest request)
        {
            DirectSendTemplateVersion version = await _directSendTemplateVersionConverter.GetSimpleNoVariantsNoRules((int)DirectSendTemplateEnum.CvDocumentProgress, 1);
            DirectSendReplacementModel model = new(version, request.EvaluatorUser, request.Dictionary);
            DirectSendTargetCollection targets = new()
            {
                new DirectSendTarget(request.EvaluatorUser.EvaluatorUserEmail, RecipientTypeEnum.Normal, request.EvaluatorUser.EvaluatorUserGuid, NodeTypeEnum.FreeRangeEmail)
            };
            SendGridWebApiDefaultMode mode = new();

            long response = await model.SendMailAsync(targets, null, request.EvaluatorUser.EvaluatorUserGuid, (int)NodeTypeEnum.FreeRangeEmail, mode).ConfigureAwait(false);

            return Ok(response > 0);
        }

        [HttpPost("SendAutoCloseAccountsEmail")]
        public async Task<IActionResult> SendAutoCloseAccountsEmail(SendAutoCloseAccountsEmailRequest request)
        {
            DirectSendTemplateVersion version = await _directSendTemplateVersionConverter.GetSimpleNoVariantsNoRules((int)DirectSendTemplateEnum.AutoCloseAccounts, 1);
            DirectSendReplacementModel model = new(version, request.ExtraData);
            SendGridWebApiDefaultMode mode = new();
            DirectSendTargetCollection targets = request.targets;
            long response = await model.SendMailAsync(targets, null, request.PolicyProspectGuid, (int)NodeTypeEnum.Policy, mode);
            DirectSendLog log = new()
            {
                DirectSendLogId = response,
            };

            return Ok(log);
        }

        [HttpPost("SendCoverageVerifierPolicyUploadedEmailToUcpm")]
        [AllowAnonymous]
        public async Task<IActionResult> SendCoverageVerifierPolicyUploadedEmailToUcpm(SendCoverageVerifierDocProgressRequest request)
        {
            DirectSendTemplateVersion version = await _directSendTemplateVersionConverter.GetSimpleNoVariantsNoRules((int)DirectSendTemplateEnum.CvDocumentProgress, 1);
            DirectSendReplacementModel model = new(version, request.Dictionary);
            DirectSendTargetCollection targets = new()
            {
                new DirectSendTarget(request.EvaluatorUser.EvaluatorUserEmail, RecipientTypeEnum.Normal, request.EvaluatorUser.EvaluatorUserGuid, NodeTypeEnum.FreeRangeEmail)
            };
            SendGridWebApiDefaultMode mode = new();
            long response = await model.SendMailAsync(targets, null, request.EvaluatorUser.EvaluatorUserGuid, (int)NodeTypeEnum.FreeRangeEmail, mode).ConfigureAwait(false);

            return Ok(response);
        }
    }
}
