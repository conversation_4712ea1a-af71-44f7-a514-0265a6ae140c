using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Converter;
using UcpmApi.Shared;

namespace UcpmCore.Api.Controllers
{
    [ApiController]
    [Authorize(Policy = "policytracker")]
    [Route("api/v2/[controller]")]
    public class EmployeeRenewalHandlingController : ControllerBase
    {
        private readonly RenewalHandlingConverter _employeeRenewalHandlingConverter;

        public EmployeeRenewalHandlingController(RenewalHandlingConverter employeeRenewaHandlingConverter)
        {
            _employeeRenewalHandlingConverter = employeeRenewaHandlingConverter;
        }

        [HttpGet("GetEmployeeRenewalHandlings")]
        public async Task<IActionResult> GetEmployeeRenewalHandlings()
        {
            List<EmployeeRenewalHandling> EmployeeRenewalHandlings = await _employeeRenewalHandlingConverter.GetRenewalHandling();

            return Ok(EmployeeRenewalHandlings);
        }
    }
}
