using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Command.SurplusLines;
using UcpmApi.Converter.SurplusLines;
using UcpmApi.Shared.SurplusLines;

namespace UcpmCore.Api.Controllers
{
    [Route("api/v2/[controller]")]
    [ApiController]
    [Authorize]
    public class SurplusLinesReportStatusController : ControllerBase
    {
        private readonly SurplusLinesReportStatusCommand _surplusLinesReportStatusCommand;
        private readonly SurplusLinesReportStatusConverter _surplusLinesReportStatusConverter;

        public SurplusLinesReportStatusController(
            SurplusLinesReportStatusCommand surplusLinesReportStatusCommand,
            SurplusLinesReportStatusConverter surplusLinesReportStatusConverter)
        {
            _surplusLinesReportStatusCommand = surplusLinesReportStatusCommand;
            _surplusLinesReportStatusConverter = surplusLinesReportStatusConverter;
        }

        [HttpGet("GetSurplusLinesReportStatues")]
        public async Task<IActionResult> GetSurplusLinesReportStatues()
        {
            IEnumerable<SurplusLinesReportStatus> surplusLinesReportStatuses = await _surplusLinesReportStatusConverter.GetSurplusLinesReportStatuses();

            return Ok(surplusLinesReportStatuses);
        }
    }
}
