using BlazorStrap;
using Duende.AccessTokenManagement.OpenIdConnect;
using Duende.IdentityModel;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.Net.Http.Headers;
using {e.ProjectName}.Components;
using {e.ProjectName}.Services;
using {e.ProjectName}.Shared;
using ServerComponentLibrary.Helpers;
using ServerComponentLibrary.Models;
using ServerComponentLibrary.Services;
using ServerComponentLibrary.Services.Interfaces;
using System.Net;
using System.Reflection;

{e.NamespaceLine}
{
    public class Program
    {
        public static void Main(string[] args)
        {
            WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

            string api = Environment.GetEnvironmentVariable("new_ucpm_api") ?? "";
            if (api == "")
            {
                throw new Exception("new_ucpm_api environment variable not set");
            }

            string identityServer = Environment.GetEnvironmentVariable("identity_server") ?? "";
            if (identityServer == "")
            {
                throw new Exception("identity_server environment variable not set");
            }

            string proxyUri = "";

            builder.Services.AddHttpClient("IDPClient", client =>
            {
                client.BaseAddress = new Uri(identityServer);
            });

            // Add services to the container.
            builder.Logging.ClearProviders();
            builder.Logging.AddConsole();

            builder.Services.AddCascadingAuthenticationState();
            builder.Services.AddRazorComponents()
                .AddInteractiveServerComponents();

            builder.Services.AddManagers(typeof(ManagerBase).Assembly, null, null, null);
            builder.Services.AddManagers(Assembly.GetExecutingAssembly(), null, null, null);
            builder.Services.AddScoped<FlexSurveyState>();
            builder.Services.AddBlazorStrap();
            builder.Services.AddHttpContextAccessor();
            builder.Services.AddScoped<ToastService>();
            builder.Services.AddScoped<ILocalStorageService, LocalStorageService>();

            builder.Services.AddAuthentication(o =>
            {
                o.DefaultScheme = CookieAuthenticationDefaults.AuthenticationScheme;
                o.DefaultChallengeScheme = OpenIdConnectDefaults.AuthenticationScheme;
            })
            .AddCookie(CookieAuthenticationDefaults.AuthenticationScheme, options =>
            {
                options.Cookie.Name = "__Host-blazor";
                options.Cookie.SameSite = Microsoft.AspNetCore.Http.SameSiteMode.Lax;
            })
            .AddOpenIdConnect(OpenIdConnectDefaults.AuthenticationScheme, options =>
            {
                options.SignInScheme = CookieAuthenticationDefaults.AuthenticationScheme;
                options.Authority = identityServer;//url of the IDP
                options.ClientId = "{e.ProjectNameLowerCase}";

                options.ClientSecret = builder.Configuration["AppSettings:ClientSecret"];//THIS project's secret
                options.Scope.Clear();
                options.Scope.Add("openid");
                options.Scope.Add("profile");
                options.Scope.Add("{e.ProjectNameLowerCase}");
                options.Scope.Add("ucpmapi_fullaccess");
                options.Scope.Add("offline_access");
                options.Scope.Add("roles");
                options.ResponseType = "code";
                options.SaveTokens = true;
                options.GetClaimsFromUserInfoEndpoint = true;

                options.ClaimActions.MapJsonKey("role", "role");
                options.ClaimActions.MapUniqueJsonKey("permission", "permission");
                options.ClaimActions.MapUniqueJsonKey("accountguid", "accountguid");
                options.ClaimActions.MapUniqueJsonKey("invite_link_guid", "invite_link_guid");

                options.Events.OnSignedOutCallbackRedirect = (context) =>
                {
                    context.Response.Redirect(context.Options.SignedOutRedirectUri);
                    context.HandleResponse();

                    return Task.CompletedTask;
                };

                options.TokenValidationParameters = new()
                {
                    NameClaimType = JwtClaimTypes.Email,
                };
                options.Events.OnAuthenticationFailed = (context) =>
                {
                    return Task.CompletedTask;
                };
                options.EventsType = typeof(OidcEvents);
            });

            builder.Services.AddAuthorizationCore();
            builder.Services.AddSingleton<IUserTokenStore, ServerSideTokenStore>();
            builder.Services.AddTransient<OidcEvents>();

            if (!string.IsNullOrWhiteSpace(proxyUri))
            {
                WebProxy proxy = new()
                {
                    Address = new Uri(proxyUri),
                    BypassProxyOnLocal = false,
                };
                HttpClientHandler httpHandler = new()
                {
                    Proxy = proxy,
                    UseProxy = true,
                    ClientCertificateOptions = ClientCertificateOption.Manual,
                    ServerCertificateCustomValidationCallback = (HttpRequestMessage, cert, certChain, policyErrors) =>
                    {
                        return true;
                    }
                };
                builder.Services.AddHttpClient("APIClient", client =>
                {
                    client.BaseAddress = new Uri(api);
                    client.DefaultRequestHeaders.Clear();
                    client.DefaultRequestHeaders.Add(HeaderNames.Accept, "application/json");
                }).ConfigurePrimaryHttpMessageHandler(() => httpHandler);
            }
            else
            {
                builder.Services.AddHttpClient("APIClient", client =>
                {
                    client.BaseAddress = new Uri(api);
                    client.DefaultRequestHeaders.Clear();
                    client.DefaultRequestHeaders.Add(HeaderNames.Accept, "application/json");
                });
            }

            WebApplication app = builder.Build();

            // Configure the HTTP request pipeline.
            if (!app.Environment.IsDevelopment())
            {
                app.UseExceptionHandler("/Error", createScopeForErrors: true);
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            app.UseHttpsRedirection();
            app.UseForwardedHeaders();

            app.UseStaticFiles();
            app.UseErrorHandlingMiddleware();
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseRouting();
            app.UseAntiforgery();

            app.MapRazorComponents<App>()
                .AddInteractiveServerRenderMode();

            app.Run();
        }
    }
}