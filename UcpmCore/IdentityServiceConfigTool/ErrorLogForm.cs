using IdentityServerConfigTool.Engine;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IdentityServerConfigTool
{
    public partial class ErrorLogForm : Form
    {
        public ErrorLogForm()
        {
            InitializeComponent();
        }

        private void ErrorLogForm_Load(object sender, EventArgs e)
        {
            PopulateErrorLogData();
        }

        private void closeButton_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void clearErrorLogLinkLabel_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            Utilities.ClearErrorLog(this);
            errorLogOutputRichTextBox.Lines = new string[] { };
        }

        private void PopulateErrorLogData()
        {
            errorLogOutputRichTextBox.Lines = Utilities.ReadErrorLog(this);
        }
    }
}
