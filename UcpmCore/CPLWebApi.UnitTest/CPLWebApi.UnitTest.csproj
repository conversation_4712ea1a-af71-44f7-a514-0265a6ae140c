<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="MSTest" Version="3.8.2" />
    <PackageReference Include="MSTest.TestAdapter" Version="3.8.3" />
    <PackageReference Include="MSTest.TestFramework" Version="3.8.3" />
    <PackageReference Include="Radzen.Blazor" Version="6.1.1" />
    <PackageReference Include="SixLabors.ImageSharp" Version="2.1.10" />
    <PackageReference Include="System.Security.Cryptography.Xml" Version="9.0.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CPLApi\CPLWebApi.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Microsoft.VisualStudio.TestTools.UnitTesting" />
  </ItemGroup>

</Project>
