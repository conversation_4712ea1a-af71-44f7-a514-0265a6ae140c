namespace ServerComponentLibrary.Services.Interfaces
{
    public interface ILocalStorageService
    {
        Task<string> GetItem(string key);

        Task SetItem<T>(string key, T value);

        Task RemoveItem(string key);

        Task<T?> GetJsonItem<T>(string key);

        Task SetJsonItem<T>(string key, T value);

        Task RemoveJsonItem(string key);

        Task ClearLastGame(List<string> keys);
    }
}
