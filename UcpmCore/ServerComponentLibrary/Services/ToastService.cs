using System.Timers;

namespace ServerComponentLibrary.Services
{
    public class ToastService : IDisposable
    {
        private System.Timers.Timer? _countDownTimer;

        public event Action<string, ToastLevelEnum, int>? OnShow;
        public event Action? OnHide;

        public void ShowToast(string message, ToastLevelEnum level, int duration = 5000)
        {
            OnShow?.Invoke(message, level, duration);
            StartCountdown(duration);
        }

        public void Dispose()
            => _countDownTimer?.Dispose();

        private void StartCountdown(int duration)
        {
            SetCountdown(duration);
            if (_countDownTimer!.Enabled)
            {
                _countDownTimer.Stop();
                _countDownTimer.Start();
            }
            else
            {
                _countDownTimer!.Start();
            }
        }

        private void SetCountdown(int duration)
        {
            if (_countDownTimer != null)
            {
                return;
            }

            _countDownTimer = new System.Timers.Timer
            {
                Interval = duration,
                AutoReset = false
            };
            _countDownTimer.Elapsed += HideToast;
        }

        private void HideToast(object? source, ElapsedEventArgs args)
            => OnHide?.Invoke();

        public void ShowError(string? errorMessage)
        {
            if (string.IsNullOrEmpty(errorMessage))
            {
                return;
            }
            ShowToast(errorMessage, ToastLevelEnum.Error);
        }
        public void ShowWarning(string? warningMessage)
        {
            if (string.IsNullOrEmpty(warningMessage))
            {
                return;
            }
            ShowToast(warningMessage, ToastLevelEnum.Warning);
        }
        public void ShowInfo(string? infoMessage)
        {
            if (string.IsNullOrEmpty(infoMessage))
            {
                return;
            }
            ShowToast(infoMessage, ToastLevelEnum.Information);
        }

        public void ShowSuccess(string? successMessage)
        {
            if (string.IsNullOrEmpty(successMessage))
            {
                return;
            }

            ShowToast(successMessage, ToastLevelEnum.Success);
        }
    }
}
