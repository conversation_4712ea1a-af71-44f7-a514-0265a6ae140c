using Microsoft.AspNetCore.Components;

namespace ServerComponentLibrary.Components
{
    public class HeaderComponentBase : DataTypeComponent
    {
        [Parameter]
        public Guid FlexVersionGuid { get; set; }

        [Parameter]
        public int FlexDefinitionId { get; set; }


        public string QuestionHtml { get; set; }
        public DateTime DateValue { get; set; }
        public bool IsTooltipVideo { get; set; }
        public VideoModal VideoModal;

        protected override async Task OnInitializedAsync()
        {
            GetDefaults();
            SurveyState.OnChange += SurveyState_OnChange;
            await base.OnInitializedAsync();
        }

        public async Task ShowVideoModal()
        {
            await VideoModal.ShowModal();
        }

        private void SurveyState_OnChange()
        {
            GetDefaults();
            InvokeAsync(() => StateHasChanged());
        }

        private void GetDefaults()
        {
            QuestionText = Question.QuestionText;
            QuestionHtml = Question.QuestionHtml;
            IsTooltipVideo = Question.IsTooltipVideo;

            base.DetermineVisibility();
            base.DetermineWidth();
        }
    }
}
