using BlazorStrap.V5;
using Microsoft.JSInterop;

namespace ServerComponentLibrary.Components
{
    public partial class VideoModalBase : DataTypeComponent
    {
        public string VideoTitle { get; set; }
        public string VideoURL { get; set; }
        public string PlayerId { get; set; }
        public BSModal VideoModal { get; set; }

        protected override async Task OnInitializedAsync()
        {
            GetDefaults();

            await base.OnInitializedAsync();
        }

        public async Task ShowModal()
        {
            await VideoModal.ShowAsync();
        }

        public async Task HandleOnHide()
        {
            await StopVideo();
        }

        private void GetDefaults()
        {
            PlayerId = Question.DataName;
            VideoTitle = Question.VideoTitle;
            VideoURL = Question.VideoURL;
        }

        private async Task StopVideo()
        {
            await JSRuntime.InvokeVoidAsync("pauseVideo", PlayerId);
        }
    }
}
