using Newtonsoft.Json;

namespace FlexLibrary
{
    /// <summary>
    /// Represents an answer to a FlexQuestion in the survey system
    /// </summary>
    public class FlexAnswer
    {
        /// <summary>
        /// The answer value as a string
        /// </summary>
        [JsonProperty("Answer")]
        public string Answer { get; set; } = string.Empty;

        /// <summary>
        /// The data name of the question this answer corresponds to
        /// </summary>
        [JsonProperty("QuestionDataName")]
        public string QuestionDataName { get; set; } = string.Empty;

        /// <summary>
        /// Row number for data grid questions or multi-row scenarios
        /// </summary>
        [JsonProperty("RowNumber")]
        public int RowNumber { get; set; } = 0;

        /// <summary>
        /// GUID of the FlexResponse this answer belongs to
        /// </summary>
        [JsonProperty("FlexResponseGuid")]
        public Guid FlexResponseGuid { get; set; } = Guid.Empty;

        /// <summary>
        /// Child answers for hierarchical questions
        /// </summary>
        [JsonProperty("ChildAnswers")]
        public List<FlexAnswer> ChildAnswers { get; set; } = new List<FlexAnswer>();

        /// <summary>
        /// GUID of the security account that saved this answer
        /// </summary>
        [JsonProperty("SavedBySecurityAccountGuid")]
        public Guid SavedBySecurityAccountGuid { get; set; } = Guid.Empty;

        /// <summary>
        /// Serialized object data for complex answer types (ContactInfo, ZipCodeInfo, etc.)
        /// </summary>
        [JsonProperty("AnswerObject")]
        public string? AnswerObject { get; set; }

        /// <summary>
        /// Default constructor
        /// </summary>
        public FlexAnswer()
        {
        }

        /// <summary>
        /// Constructor with question data name
        /// </summary>
        /// <param name="questionDataName">The data name of the question</param>
        public FlexAnswer(string questionDataName)
        {
            QuestionDataName = questionDataName;
        }

        /// <summary>
        /// Constructor with question data name and answer
        /// </summary>
        /// <param name="questionDataName">The data name of the question</param>
        /// <param name="answer">The answer value</param>
        public FlexAnswer(string questionDataName, string answer)
        {
            QuestionDataName = questionDataName;
            Answer = answer;
        }

        /// <summary>
        /// Gets a string representation of the answer
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"{QuestionDataName}: {Answer}";
        }

        /// <summary>
        /// Checks if this answer has a value
        /// </summary>
        /// <returns>True if the answer has a non-empty value</returns>
        public bool HasValue()
        {
            return !string.IsNullOrWhiteSpace(Answer);
        }

        /// <summary>
        /// Checks if this answer is for the specified question
        /// </summary>
        /// <param name="questionDataName">The question data name to check</param>
        /// <returns>True if this answer is for the specified question</returns>
        public bool IsForQuestion(string questionDataName)
        {
            return string.Equals(QuestionDataName, questionDataName, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Creates a copy of this FlexAnswer
        /// </summary>
        /// <returns>A new FlexAnswer instance with the same values</returns>
        public FlexAnswer Clone()
        {
            return new FlexAnswer
            {
                Answer = Answer,
                QuestionDataName = QuestionDataName,
                RowNumber = RowNumber,
                FlexResponseGuid = FlexResponseGuid,
                SavedBySecurityAccountGuid = SavedBySecurityAccountGuid,
                AnswerObject = AnswerObject,
                ChildAnswers = ChildAnswers.Select(child => child.Clone()).ToList()
            };
        }

        /// <summary>
        /// Sets the answer object for complex data types
        /// </summary>
        /// <param name="obj">The object to serialize and store</param>
        public void SetAnswerObject(object obj)
        {
            if (obj != null)
            {
                AnswerObject = JsonConvert.SerializeObject(obj);
            }
            else
            {
                AnswerObject = null;
            }
        }

        /// <summary>
        /// Gets the answer object as the specified type
        /// </summary>
        /// <typeparam name="T">The type to deserialize to</typeparam>
        /// <returns>The deserialized object or default(T) if deserialization fails</returns>
        public T? GetAnswerObject<T>() where T : class
        {
            if (string.IsNullOrWhiteSpace(AnswerObject))
                return default(T);

            try
            {
                return JsonConvert.DeserializeObject<T>(AnswerObject);
            }
            catch (JsonException)
            {
                return default(T);
            }
        }
    }
}
