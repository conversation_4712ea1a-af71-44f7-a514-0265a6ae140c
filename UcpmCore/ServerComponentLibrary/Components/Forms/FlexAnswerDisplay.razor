@using FlexLibrary
@using FlexLibrary.Models
@using ServerComponentLibrary.Components.Inputs.Models
@using UcpmApi.Shared
@using UcpmApi.Shared.Survey
@using System.Text.Json
@inherits FlexAnswerDisplayBase

<div class="@CssClass">
    @if (ShowTitle && !string.IsNullOrWhiteSpace(Title))
    {
        <div class="flex-answer-display-title">
            <h4>@Title</h4>
        </div>
    }

    @if (IsLoading)
    {
        <div class="d-flex justify-content-center align-items-center p-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading answers...</span>
            </div>
            <span class="ms-2">Loading answers...</span>
        </div>
    }
    else if (VisibleQuestionAnswerPairs.Any())
    {
        <div class="@GetContainerClass()">
            @foreach (var pair in VisibleQuestionAnswerPairs)
            {
                <div class="@GetQuestionContainerClass(pair.Question)">
                    @if (ShowQuestionText)
                    {
                        <div class="flex-answer-display-question">
                            <label class="form-label fw-semibold">
                                @pair.Question.QuestionText
                                @if (pair.Question.FlexQuestionRequired)
                                {
                                    <span class="text-danger">*</span>
                                }
                            </label>
                            @if (!string.IsNullOrWhiteSpace(pair.Question.ToolTip))
                            {
                                <small class="text-muted d-block">@pair.Question.ToolTip</small>
                            }
                        </div>
                    }
                    
                    <div class="flex-answer-display-answer">
                        @RenderAnswerValue(pair.Question, pair.Answer)
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-muted text-center p-4">
            @EmptyMessage
        </div>
    }
</div>

@code {
    private RenderFragment RenderAnswerValue(FlexQuestion question, FlexAnswer? answer) => builder =>
    {
        var displayValue = GetDisplayValue(question, answer);
        var cssClass = GetAnswerCssClass(question, answer);

        <div class="@cssClass">
            @if (string.IsNullOrWhiteSpace(displayValue))
            {
                <span class="text-muted fst-italic">@NoAnswerText</span>
            }
            else
            {
                switch (question.DataType)
                {
                    case FlexDataTypeEnum.Boolean:
                        <span class="badge @(displayValue.Equals("Yes", StringComparison.OrdinalIgnoreCase) ? "bg-success" : "bg-secondary")">
                            @displayValue
                        </span>
                        break;

                    case FlexDataTypeEnum.Money:
                        <span class="fw-bold text-success">@displayValue</span>
                        break;

                    case FlexDataTypeEnum.Date:
                        <span class="text-primary">
                            <i class="bi bi-calendar3 me-1"></i>@displayValue
                        </span>
                        break;

                    case FlexDataTypeEnum.Email:
                        <a href="mailto:@displayValue" class="text-decoration-none">
                            <i class="bi bi-envelope me-1"></i>@displayValue
                        </a>
                        break;

                    case FlexDataTypeEnum.Phone:
                        <a href="tel:@displayValue" class="text-decoration-none">
                            <i class="bi bi-telephone me-1"></i>@displayValue
                        </a>
                        break;

                    case FlexDataTypeEnum.Url:
                        <a href="@displayValue" target="_blank" class="text-decoration-none">
                            <i class="bi bi-link-45deg me-1"></i>@displayValue
                        </a>
                        break;

                    case FlexDataTypeEnum.ContactInfo:
                        @RenderContactInfo(displayValue, answer)
                        break;

                    case FlexDataTypeEnum.ZipCode:
                        @RenderZipCodeInfo(displayValue, answer)
                        break;

                    case FlexDataTypeEnum.Percentage:
                        <span class="text-info">@displayValue</span>
                        break;

                    default:
                        <span>@displayValue</span>
                        break;
                }
            }
        </div>
    };

    private RenderFragment RenderContactInfo(string displayValue, FlexAnswer? answer) => builder =>
    {
        var contactInfo = GetContactInfoFromAnswer(answer);
        if (contactInfo != null)
        {
            <div class="contact-info-display">
                <div class="fw-bold">@contactInfo.Name</div>
                @if (!string.IsNullOrWhiteSpace(contactInfo.Title))
                {
                    <div class="text-muted small">@contactInfo.Title</div>
                }
                @if (!string.IsNullOrWhiteSpace(contactInfo.Company))
                {
                    <div class="text-muted small">@contactInfo.Company</div>
                }
                @if (!string.IsNullOrWhiteSpace(contactInfo.Email))
                {
                    <div class="small">
                        <a href="mailto:@contactInfo.Email" class="text-decoration-none">
                            <i class="bi bi-envelope me-1"></i>@contactInfo.Email
                        </a>
                    </div>
                }
                @if (!string.IsNullOrWhiteSpace(contactInfo.Phone))
                {
                    <div class="small">
                        <a href="tel:@contactInfo.Phone" class="text-decoration-none">
                            <i class="bi bi-telephone me-1"></i>@contactInfo.Phone
                        </a>
                    </div>
                }
            </div>
        }
        else
        {
            <span>@displayValue</span>
        }
    };

    private RenderFragment RenderZipCodeInfo(string displayValue, FlexAnswer? answer) => builder =>
    {
        var zipCodeInfo = GetZipCodeInfoFromAnswer(answer);
        if (zipCodeInfo != null)
        {
            <div class="zipcode-info-display">
                <span class="fw-bold">@zipCodeInfo.ZipCode</span>
                @if (!string.IsNullOrWhiteSpace(zipCodeInfo.City) && !string.IsNullOrWhiteSpace(zipCodeInfo.State))
                {
                    <span class="text-muted ms-2">(@zipCodeInfo.City, @zipCodeInfo.State)</span>
                }
            </div>
        }
        else
        {
            <span>@displayValue</span>
        }
    };
}
