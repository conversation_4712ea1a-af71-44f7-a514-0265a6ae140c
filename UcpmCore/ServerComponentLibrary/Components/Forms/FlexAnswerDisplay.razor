@using FlexLibrary
@using FlexLibrary.Models
@using ServerComponentLibrary.Components.Inputs.Models
@using UcpmApi.Shared
@using UcpmApi.Shared.Survey
@using System.Text.Json
@inherits FlexAnswerDisplayBase

<div class="@CssClass">
    @if (ShowTitle && !string.IsNullOrWhiteSpace(Title))
    {
        <div class="flex-answer-display-title">
            <h4>@Title</h4>
        </div>
    }

    @if (IsLoading)
    {
        <div class="d-flex justify-content-center align-items-center p-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading answers...</span>
            </div>
            <span class="ms-2">Loading answers...</span>
        </div>
    }
    else if (VisibleQuestionAnswerPairs.Any())
    {
        <div class="@GetContainerClass()">
            @foreach (var pair in VisibleQuestionAnswerPairs)
            {
                <div class="@GetQuestionContainerClass(pair.Question)">
                    @if (ShowQuestionText)
                    {
                        <div class="flex-answer-display-question">
                            <label class="form-label fw-semibold">
                                @pair.Question.QuestionText
                                @if (pair.Question.FlexQuestionRequired)
                                {
                                    <span class="text-danger">*</span>
                                }
                            </label>
                            @if (!string.IsNullOrWhiteSpace(pair.Question.ToolTip))
                            {
                                <small class="text-muted d-block">@pair.Question.ToolTip</small>
                            }
                        </div>
                    }
                    
                    <div class="flex-answer-display-answer">
                        @GetAnswerDisplayMarkup(pair.Question, pair.Answer)
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-muted text-center p-4">
            @EmptyMessage
        </div>
    }
</div>


