using System.Text.Json;
using FlexLibrary;
using Microsoft.AspNetCore.Components;

namespace ServerComponentLibrary.Components.Forms.Extensions;

/// <summary>
/// Static extension class providing component attribute generation for FlexQuestion objects.
/// Centralizes logic for creating component attributes from FlexQuestion properties.
/// </summary>
public static class ComponentAttributeExtensions
{
    /// <summary>
    /// Gets the string value for a field from form data.
    /// </summary>
    /// <param name="dataName">The field name</param>
    /// <param name="formData">The form data dictionary</param>
    /// <returns>String representation of the field value</returns>
    public static string GetFieldStringValue(this Dictionary<string, object> formData,string dataName)
    {
        var value = GetFieldValue(dataName, formData);
        return ValueConversionExtensions.ConvertToString(value);
    }

    /// <summary>
    /// Gets the boolean value for a field from form data.
    /// </summary>
    /// <param name="dataName">The field name</param>
    /// <param name="formData">The form data dictionary</param>
    /// <returns>Boolean value or null if not found/invalid</returns>
    public static bool? GetBooleanFieldValue(this Dictionary<string, object> formData, string dataName)
    {
        return !formData.TryGetValue(dataName, out var value)
            ? null
            : ValueConversionExtensions.ConvertToBoolean(value);
    }

    /// <summary>
    /// Gets the raw field value from form data.
    /// </summary>
    /// <param name="dataName">The field name</param>
    /// <param name="formData">The form data dictionary</param>
    /// <returns>The raw field value or empty string if not found</returns>
    public static object GetFieldValue(string dataName, Dictionary<string, object> formData)
    {
        return formData.TryGetValue(dataName, out var value) ? value : string.Empty;
    }

    /// <summary>
    /// Determines if a question should be treated as required based on visibility and configuration.
    /// </summary>
    /// <param name="question">The FlexQuestion to check</param>
    /// <param name="isVisible">Whether the question is currently visible</param>
    /// <returns>True if the question should be required</returns>
    public static bool IsEffectivelyRequired(this FlexQuestion question, bool isVisible)
    {
        return question.FlexQuestionRequired && isVisible && !question.IsCalculatedField;
    }
    
    public static string? TryGetSelectedValue<T>(this Dictionary<string, object?> formData, string dataName) where T : class
    {
        var selectedKey = $"{dataName}Selected";
        return formData.TryGetValue(selectedKey, out var selectedValue) && selectedValue is T typedValue
            ? JsonSerializer.Serialize(typedValue)
            : null;
    }
}