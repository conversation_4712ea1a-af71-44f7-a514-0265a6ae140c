using FlexLibrary;
using ServerComponentLibrary.Components.Inputs;
using ServerComponentLibrary.Components.Inputs.Models;

namespace ServerComponentLibrary.Components.Forms.Extensions;

/// <summary>
/// Static extension class providing common value conversion methods for form components.
/// Centralizes conversion logic between different data types and string representations.
/// </summary>
public static class ValueConversionExtensions
{
    /// <summary>
    /// Converts any object value to its string representation based on its type.
    /// Handles special cases like ZipCodeInfo, DateTime, and null values.
    /// </summary>
    /// <param name="value">The value to convert</param>
    /// <returns>String representation of the value</returns>
    public static string ConvertToString(object? value)
    {
        return value switch
        {
            null => string.Empty,
            string str => str,
            ZipCodeInfo zipCodeInfo => zipCodeInfo.ZipCode,
            ContactInfo contactInfo => contactInfo.Name,
            DateTime dateTime => dateTime.ToString("d"),
            DateTimeOffset dateTimeOffset => dateTimeOffset.ToString("d"),
            DateOnly dateOnly => dateOnly.ToString("d"),
            bool boolean => boolean.ToString().ToLower(),
            decimal number => number.ToString("F2"),
            double number => number.ToString("F2"),
            int number => number.ToString(),
            float number => number.ToString("F2"),
            long number => number.ToString(),
            _ => value.ToString() ?? string.Empty
        };
    }

    /// <summary>
    /// Converts form values to string format based on data type for FlexAnswer storage.
    /// </summary>
    /// <param name="value">The value to convert</param>
    /// <param name="dataType">The target data type</param>
    /// <returns>String representation formatted for the specific data type</returns>
    public static string ConvertAnswerByDataType(this FlexDataTypeEnum dataType, object? value)
    {
        if (value == null) return string.Empty;

        return dataType switch
        {
            FlexDataTypeEnum.Boolean => value.ToString()?.ToLower() ?? "false",
            FlexDataTypeEnum.Number => value.ToString() ?? "0",
            FlexDataTypeEnum.Money => value.ToString() ?? "0",
            FlexDataTypeEnum.Percentage => value.ToString() ?? "0",
            FlexDataTypeEnum.Date => value is DateTime date 
                ? date.ToString("d") 
                : value.ToString() ?? "",
            FlexDataTypeEnum.Time => value is TimeSpan time 
                ? time.ToString(@"hh\:mm") 
                : value.ToString() ?? "",
            _ => ConvertToString(value)
        };
    }

    /// <summary>
    /// Converts string values to boolean values, handling various string representations.
    /// </summary>
    /// <param name="value">The string value to convert</param>
    /// <returns>Boolean value or null if conversion fails</returns>
    public static bool? ConvertStringToBoolean(string? value)
    {
        if (string.IsNullOrWhiteSpace(value)) return null;
        
        return value.ToLower() switch
        {
            "true" => true,
            "false" => false,
            "1" => true,
            "0" => false,
            "yes" => true,
            "no" => false,
            "on" => true,
            "off" => false,
            _ when bool.TryParse(value, out var parsed) => parsed,
            _ => null
        };
    }

    /// <summary>
    /// Converts various value types to boolean, handling multiple input formats.
    /// </summary>
    /// <param name="value">The value to convert</param>
    /// <returns>Boolean value or null if conversion fails</returns>
    public static bool? ConvertToBoolean(object? value)
    {
        return value switch
        {
            bool boolVal => boolVal,
            string strVal => ConvertStringToBoolean(strVal),
            int intVal => intVal != 0,
            decimal decVal => decVal != 0,
            double doubleVal => doubleVal != 0,
            _ => null
        };
    }

    /// <summary>
    /// Safely converts values to decimal, handling various numeric formats.
    /// </summary>
    /// <param name="value">The value to convert</param>
    /// <returns>Decimal value or null if conversion fails</returns>
    public static decimal? ConvertToDecimal(object? value)
    {
        if (value == null) return null;

        return value switch
        {
            decimal decVal => decVal,
            double doubleVal => (decimal)doubleVal,
            float floatVal => (decimal)floatVal,
            int intVal => intVal,
            long longVal => longVal,
            string strVal when decimal.TryParse(strVal, out var parsed) => parsed,
            _ => null
        };
    }

    /// <summary>
    /// Safely converts values to DateTime, handling various date formats.
    /// </summary>
    /// <param name="value">The value to convert</param>
    /// <returns>DateTime value or null if conversion fails</returns>
    public static DateTime? ConvertToDateTime(object? value)
    {
        if (value == null) return null;

        return value switch
        {
            DateTime dateTime => dateTime,
            DateTimeOffset dateTimeOffset => dateTimeOffset.DateTime,
            DateOnly dateOnly => dateOnly.ToDateTime(TimeOnly.MinValue),
            string strVal when DateTime.TryParse(strVal, out var parsed) => parsed,
            _ => null
        };
    }
}
