using System.Text.Json;
using FlexLibrary;
using FlexLibrary.Models;
using ServerComponentLibrary.Components.Inputs;
using ServerComponentLibrary.Components.Inputs.Models;

namespace ServerComponentLibrary.Components.Forms.Extensions;

/// <summary>
/// Static extension class providing common field validation methods for form components.
/// Centralizes validation logic to eliminate code duplication across different form implementations.
/// </summary>
public static class FieldValidationExtensions
{
    /// <summary>
    /// Determines if a field has a valid answer based on its data type and value.
    /// This method consolidates the validation logic used across FlexDynamicForm, FormStatusComponent, and EnvironmentalQuestionnaire.
    /// </summary>
    /// <param name="value">The field value to validate</param>
    /// <param name="dataType">The FlexDataTypeEnum representing the expected data type</param>
    /// <param name="dataName">The field name (used for complex object lookups)</param>
    /// <param name="answerLookup">Optional dictionary of FlexAnswer objects for complex type validation</param>
    /// <returns>True if the field has a valid answer, false otherwise</returns>
    public static bool IsFieldAnswerPresent(object? value, FlexDataTypeEnum dataType, string? dataName = null, Dictionary<string, FlexAnswer>? answerLookup = null)
    {
        if (value == null)
            return false;

        return dataType switch
        {
            FlexDataTypeEnum.Boolean => IsBooleanAnswerPresent(value, dataName, answerLookup),
            FlexDataTypeEnum.Number => IsNumericAnswerPresent(value),
            FlexDataTypeEnum.Money => IsNumericAnswerPresent(value),
            FlexDataTypeEnum.Date => IsDateAnswerPresent(value),
            FlexDataTypeEnum.Multiselect => IsMultiselectAnswerPresent(value),
            FlexDataTypeEnum.Combobox => IsComboboxAnswerPresent(value, dataName, answerLookup),
            FlexDataTypeEnum.ContactInfo => IsContactInfoAnswerPresent(value, dataName, answerLookup),
            FlexDataTypeEnum.ZipCode => IsZipCodeAnswerPresent(value, dataName, answerLookup),
            FlexDataTypeEnum.String or FlexDataTypeEnum.Memo => IsStringAnswerPresent(value),
            FlexDataTypeEnum.Phone => IsStringAnswerPresent(value),
            FlexDataTypeEnum.Email => IsStringAnswerPresent(value),
            FlexDataTypeEnum.Url => IsStringAnswerPresent(value),
            FlexDataTypeEnum.Password => IsStringAnswerPresent(value),
            FlexDataTypeEnum.Percentage => IsNumericAnswerPresent(value),
            FlexDataTypeEnum.Time => IsStringAnswerPresent(value),
            FlexDataTypeEnum.RadioGroup => IsStringAnswerPresent(value),
            _ => IsStringAnswerPresent(value)
        };
    }

    /// <summary>
    /// Validates Boolean field values, supporting both boolean types and string representations.
    /// </summary>
    private static bool IsBooleanAnswerPresent(object? value, string? dataName, Dictionary<string, FlexAnswer>? answerLookup)
    {
        // Check if it's already a boolean value
        if (value is bool)
            return true;

        // Check if it's a valid string representation of a boolean
        if (value is string strValue)
        {
            return strValue.Equals("true", StringComparison.OrdinalIgnoreCase) ||
                   strValue.Equals("false", StringComparison.OrdinalIgnoreCase);
        }

        // Check if we have existing FlexAnswer data with boolean value
        if (!string.IsNullOrEmpty(dataName) && answerLookup?.TryGetValue(dataName, out var flexAnswer) == true && 
            !string.IsNullOrWhiteSpace(flexAnswer.Answer))
        {
            var answerValue = flexAnswer.Answer.ToLower();
            return answerValue == "true" || answerValue == "false";
        }

        return false;
    }

    /// <summary>
    /// Validates numeric field values (Number, Money, Percentage).
    /// </summary>
    private static bool IsNumericAnswerPresent(object? value)
    {
        if (value == null) return false;

        return value switch
        {
            decimal => true,
            double => true,
            int => true,
            float => true,
            long => true,
            _ => decimal.TryParse(value.ToString(), out _)
        };
    }

    /// <summary>
    /// Validates Date field values, supporting both DateTime objects and parseable strings.
    /// </summary>
    private static bool IsDateAnswerPresent(object? value)
    {
        if (value == null) return false;

        return value switch
        {
            DateTime => true,
            DateTimeOffset => true,
            DateOnly => true,
            _ => value is string str && DateTime.TryParse(str, out _)
        };
    }

    /// <summary>
    /// Validates Multiselect field values.
    /// </summary>
    private static bool IsMultiselectAnswerPresent(object? value)
    {
        return value switch
        {
            List<string> list => list.Count > 0,
            ICollection<string> collection => collection.Count > 0,
            IEnumerable<string> enumerable => enumerable.Any(),
            _ => false
        };
    }

    /// <summary>
    /// Validates string-based field values (String, Memo, Phone, Email, etc.).
    /// </summary>
    private static bool IsStringAnswerPresent(object? value)
    {
        return !string.IsNullOrWhiteSpace(value?.ToString());
    }

    /// <summary>
    /// Validates ComboBox field values, supporting both FlexFactor objects and string selections.
    /// </summary>
    private static bool IsComboboxAnswerPresent(object? value, string? dataName, Dictionary<string, FlexAnswer>? answerLookup)
    {
        // Check if it's already a FlexFactor object
        if (value is FlexFactor)
            return true;

        // Check if it's a valid string selection
        if (IsStringAnswerPresent(value))
            return true;

        // Check if we have existing FlexAnswer data with serialized FlexFactor
        if (!string.IsNullOrEmpty(dataName) && answerLookup?.TryGetValue(dataName, out var flexAnswer) == true && 
            !string.IsNullOrWhiteSpace(flexAnswer.AnswerObject))
        {
            try
            {
                var flexFactor = JsonSerializer.Deserialize<FlexFactor>(flexAnswer.AnswerObject);
                return flexFactor != null && !string.IsNullOrWhiteSpace(flexFactor.FactorDisplayName);
            }
            catch (JsonException)
            {
                return false;
            }
        }

        return false;
    }

    /// <summary>
    /// Validates ContactInfo field values, supporting both ContactInfo objects and serialized data.
    /// </summary>
    private static bool IsContactInfoAnswerPresent(object? value, string? dataName, Dictionary<string, FlexAnswer>? answerLookup)
    {
        // Check if it's already a ContactInfo object
        if (value is ContactInfo)
            return true;
        

        // Check if we have existing FlexAnswer data with serialized ContactInfo
        if (!string.IsNullOrEmpty(dataName) && answerLookup?.TryGetValue(dataName, out var flexAnswer) == true && 
            !string.IsNullOrWhiteSpace(flexAnswer.AnswerObject))
        {
            try
            {
                var contactInfo = JsonSerializer.Deserialize<ContactInfo>(flexAnswer.AnswerObject);
                return contactInfo != null && !string.IsNullOrWhiteSpace(contactInfo.Name);
            }
            catch (JsonException)
            {
                return false;
            }
        }

        // Check if it's a valid string selection (email)
        return IsStringAnswerPresent(value);
    }

    /// <summary>
    /// Validates ZipCode field values, supporting ZipCodeInfo objects, strings, and serialized data.
    /// </summary>
    private static bool IsZipCodeAnswerPresent(object? value, string? dataName, Dictionary<string, FlexAnswer>? answerLookup)
    {
        // Check if it's already a ZipCodeInfo object
        if (value is ZipCodeInfo)
            return true;

        // Check if it's a valid zip code string
        if (IsStringAnswerPresent(value))
            return true;

        // Check if we have existing FlexAnswer data with serialized ZipCodeInfo
        if (!string.IsNullOrEmpty(dataName) && answerLookup?.TryGetValue(dataName, out var flexAnswer) == true && 
            !string.IsNullOrWhiteSpace(flexAnswer.AnswerObject))
        {
            try
            {
                var zipCodeInfo = JsonSerializer.Deserialize<ZipCodeInfo>(flexAnswer.AnswerObject);
                return zipCodeInfo != null && !string.IsNullOrWhiteSpace(zipCodeInfo.ZipCode);
            }
            catch (JsonException)
            {
                return false;
            }
        }

        return false;
    }

    /// <summary>
    /// Convenience method for FlexDynamicForm compatibility.
    /// Determines if a field answer is valid based on data type.
    /// </summary>
    /// <param name="value">The field value to validate</param>
    /// <param name="dataType">The FlexDataTypeEnum representing the expected data type</param>
    /// <returns>True if the field has a valid answer, false otherwise</returns>
    public static bool IsAnswerValid(object? value, FlexDataTypeEnum dataType)
    {
        return IsFieldAnswerPresent(value, dataType);
    }

    /// <summary>
    /// Convenience method for EnvironmentalQuestionnaire compatibility.
    /// Determines if an answer is present based on value, data type, and field name.
    /// </summary>
    /// <param name="value">The field value to validate</param>
    /// <param name="dataType">The FlexDataTypeEnum representing the expected data type</param>
    /// <param name="dataName">The field name</param>
    /// <param name="answerLookup">Optional dictionary of FlexAnswer objects for complex type validation</param>
    /// <returns>True if an answer is present, false otherwise</returns>
    public static bool IsAnswerPresent(object? value, FlexDataTypeEnum dataType, string dataName, Dictionary<string, FlexAnswer>? answerLookup = null)
    {
        return IsFieldAnswerPresent(value, dataType, dataName, answerLookup);
    }
}
