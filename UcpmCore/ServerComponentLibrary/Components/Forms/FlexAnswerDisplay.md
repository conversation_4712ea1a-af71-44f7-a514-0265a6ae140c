# FlexAnswerDisplay Component

A read-only display component for showing FlexQuestions and their corresponding answers from FlexAnswers. This component is designed for reviewing and displaying completed survey responses without any editing functionality.

## Features

- **Read-only display** of FlexQuestions and FlexAnswers
- **Multiple layout modes** (Vertical, Horizontal, Grid, Compact)
- **Data type-specific formatting** for different answer types
- **Responsive design** with Bootstrap integration
- **Customizable styling** and display options
- **Support for complex data types** (ContactInfo, ZipCode, etc.)
- **Loading states** and empty state handling
- **Accessibility features** with proper ARIA labels

## Basic Usage

```razor
@using ServerComponentLibrary.Components.Forms

<!-- Basic usage with questions and answer container -->
<FlexAnswerDisplay Questions="@questions" 
                   AnswerContainer="@answerContainer" />

<!-- With custom title and layout -->
<FlexAnswerDisplay Questions="@questions" 
                   AnswerContainer="@answerContainer"
                   Title="Survey Responses"
                   LayoutMode="AnswerDisplayLayoutMode.Grid"
                   ShowOnlyAnsweredQuestions="true" />

<!-- Using direct answers list -->
<FlexAnswerDisplay Questions="@questions" 
                   Answers="@answersList"
                   LayoutMode="AnswerDisplayLayoutMode.Horizontal" />
```

## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `Questions` | `List<FlexQuestion>` | `[]` | List of FlexQuestions to display |
| `AnswerContainer` | `FlexAnswerContainer?` | `null` | Container with FlexAnswers |
| `Answers` | `List<FlexAnswer>` | `[]` | Direct list of FlexAnswers |
| `Title` | `string` | `"Survey Responses"` | Display title for the component |
| `ShowTitle` | `bool` | `true` | Whether to show the title |
| `ShowQuestionText` | `bool` | `true` | Whether to show question text |
| `CssClass` | `string` | `"flex-answer-display"` | CSS class for the container |
| `LayoutMode` | `AnswerDisplayLayoutMode` | `Vertical` | Layout mode for display |
| `EmptyMessage` | `string` | `"No answers to display."` | Message when no data |
| `NoAnswerText` | `string` | `"Not answered"` | Text for unanswered questions |
| `ShowOnlyAnsweredQuestions` | `bool` | `false` | Hide unanswered questions |
| `ShowRequiredIndicator` | `bool` | `true` | Show asterisk for required fields |
| `VariableHolder` | `Dictionary<string, object>` | `new()` | Variables for visibility rules |
| `IsLoading` | `bool` | `false` | Show loading state |

## Layout Modes

### Vertical (Default)
Questions and answers displayed in a single column, stacked vertically.

### Horizontal
Questions and answers displayed in a two-column grid layout.

### Grid
Questions and answers displayed in a responsive grid (1-3 columns based on screen size).

### Compact
Questions and answers displayed in a flexible wrap layout with borders.

## Data Type Support

The component automatically formats different data types:

### Boolean
- Displays as "Yes"/"No" badges with appropriate colors
- Green badge for "Yes", gray badge for "No"

### Money
- Formatted as currency with appropriate symbols
- Displayed in green color with bold font

### Date
- Formatted as MM/dd/yyyy
- Displayed with calendar icon

### Email
- Displayed as clickable mailto link
- Shows envelope icon

### Phone
- Displayed as clickable tel link
- Shows telephone icon

### URL
- Displayed as clickable external link
- Shows link icon

### ContactInfo
- Shows contact details in a formatted card
- Includes name, title, company, email, and phone
- Email and phone are clickable links

### ZipCode
- Shows zip code with city and state if available
- Formatted as "12345 (City, State)"

### Percentage
- Formatted with % symbol
- Displayed in info color

### Combobox
- Shows the selected value
- Can be extended to show display text vs. value

## Styling

The component includes comprehensive CSS styling:

- **Responsive design** that works on mobile and desktop
- **Hover effects** for better interactivity
- **Print-friendly** styles for documentation
- **Accessibility** considerations with proper contrast
- **Bootstrap integration** for consistent theming

### Custom CSS Classes

You can override the default styling by targeting these CSS classes:

- `.flex-answer-display` - Main container
- `.flex-answer-display-title` - Title section
- `.flex-answer-display-item` - Individual question/answer pair
- `.flex-answer-display-question` - Question text area
- `.flex-answer-display-answer` - Answer value area
- `.flex-answer-value` - Answer text styling
- `.contact-info-display` - Contact information card
- `.zipcode-info-display` - Zip code information

## Examples

### Complete Example

```razor
@page "/survey-review"
@using FlexLibrary
@using FlexLibrary.Models
@using ServerComponentLibrary.Components.Forms

<div class="container mt-4">
    <FlexAnswerDisplay Questions="@surveyQuestions" 
                       AnswerContainer="@surveyAnswers"
                       Title="Application Review"
                       LayoutMode="AnswerDisplayLayoutMode.Grid"
                       ShowOnlyAnsweredQuestions="true"
                       CssClass="flex-answer-display shadow"
                       IsLoading="@isLoading" />
</div>

@code {
    private List<FlexQuestion> surveyQuestions = new();
    private FlexAnswerContainer? surveyAnswers;
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        // Load your questions and answers
        surveyQuestions = await LoadQuestions();
        surveyAnswers = await LoadAnswers();
        isLoading = false;
    }
}
```

### Compact Display for Summary

```razor
<FlexAnswerDisplay Questions="@keyQuestions" 
                   AnswerContainer="@answers"
                   Title="Key Information"
                   LayoutMode="AnswerDisplayLayoutMode.Compact"
                   ShowOnlyAnsweredQuestions="true"
                   ShowQuestionText="false" />
```

## Integration Notes

- **Compatible** with existing FlexDynamicForm component
- **Uses same data models** (FlexQuestion, FlexAnswer, FlexAnswerContainer)
- **No dependencies** on form validation or input components
- **Lightweight** and focused on display only
- **Extensible** through inheritance of FlexAnswerDisplayBase

## Performance Considerations

- **Efficient rendering** with minimal re-renders
- **Lazy evaluation** of display values
- **Caching** of answer lookups
- **Responsive** to parameter changes
- **Memory efficient** with proper disposal patterns
