@page "/flex-answer-display-test"
@using FlexLibrary
@using FlexLibrary.Models
@using ServerComponentLibrary.Components.Forms
@using ServerComponentLibrary.Components.Inputs.Models

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2>FlexAnswerDisplay Test Page</h2>
            <p class="text-muted">Testing the FlexAnswerDisplay component with sample data.</p>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <h4>Vertical Layout (Default)</h4>
            <FlexAnswerDisplay Questions="@sampleQuestions" 
                               Answers="@sampleAnswers"
                               Title="Sample Survey - Vertical Layout"
                               LayoutMode="AnswerDisplayLayoutMode.Vertical" />
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <h4>Grid Layout</h4>
            <FlexAnswerDisplay Questions="@sampleQuestions" 
                               Answers="@sampleAnswers"
                               Title="Sample Survey - Grid Layout"
                               LayoutMode="AnswerDisplayLayoutMode.Grid" />
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <h4>Only Answered Questions</h4>
            <FlexAnswerDisplay Questions="@sampleQuestions" 
                               Answers="@sampleAnswers"
                               Title="Sample Survey - Only Answered"
                               LayoutMode="AnswerDisplayLayoutMode.Horizontal"
                               ShowOnlyAnsweredQuestions="true" />
        </div>
    </div>
</div>

@code {
    private List<FlexQuestion> sampleQuestions = new();
    private List<FlexAnswer> sampleAnswers = new();

    protected override void OnInitialized()
    {
        CreateSampleData();
    }

    private void CreateSampleData()
    {
        // Create sample questions
        sampleQuestions = new List<FlexQuestion>
        {
            new FlexQuestion
            {
                DataName = "CompanyName",
                QuestionText = "Company Name",
                DataType = FlexDataTypeEnum.String,
                FlexQuestionRequired = true,
                ToolTip = "Enter the legal name of your company"
            },
            new FlexQuestion
            {
                DataName = "IsActive",
                QuestionText = "Is the company currently active?",
                DataType = FlexDataTypeEnum.Boolean,
                FlexQuestionRequired = true
            },
            new FlexQuestion
            {
                DataName = "AnnualRevenue",
                QuestionText = "Annual Revenue",
                DataType = FlexDataTypeEnum.Money,
                FlexQuestionRequired = false,
                ToolTip = "Enter your company's annual revenue"
            },
            new FlexQuestion
            {
                DataName = "FoundedDate",
                QuestionText = "Date Founded",
                DataType = FlexDataTypeEnum.Date,
                FlexQuestionRequired = false
            },
            new FlexQuestion
            {
                DataName = "ContactEmail",
                QuestionText = "Primary Contact Email",
                DataType = FlexDataTypeEnum.Email,
                FlexQuestionRequired = true
            },
            new FlexQuestion
            {
                DataName = "ContactPhone",
                QuestionText = "Primary Contact Phone",
                DataType = FlexDataTypeEnum.Phone,
                FlexQuestionRequired = false
            },
            new FlexQuestion
            {
                DataName = "Website",
                QuestionText = "Company Website",
                DataType = FlexDataTypeEnum.Url,
                FlexQuestionRequired = false
            },
            new FlexQuestion
            {
                DataName = "GrowthRate",
                QuestionText = "Expected Growth Rate",
                DataType = FlexDataTypeEnum.Percentage,
                FlexQuestionRequired = false
            }
        };

        // Create sample answers
        sampleAnswers = new List<FlexAnswer>
        {
            CreateFlexAnswer("CompanyName", "Acme Corporation"),
            CreateFlexAnswer("IsActive", "true"),
            CreateFlexAnswer("AnnualRevenue", "1500000"),
            CreateFlexAnswer("FoundedDate", "2010-03-15"),
            CreateFlexAnswer("ContactEmail", "<EMAIL>"),
            CreateFlexAnswer("ContactPhone", "(*************"),
            CreateFlexAnswer("Website", "https://www.acmecorp.com"),
            CreateFlexAnswer("GrowthRate", "15.5")
        };
    }

    private FlexAnswer CreateFlexAnswer(string questionDataName, string answer)
    {
        return new FlexAnswer
        {
            QuestionDataName = questionDataName,
            Answer = answer,
            RowNumber = 0,
            FlexResponseGuid = Guid.NewGuid(),
            SavedBySecurityAccountGuid = Guid.NewGuid(),
            ChildAnswers = new List<FlexAnswer>()
        };
    }
}

<style>
    .container {
        max-width: 1200px;
    }
    
    .row + .row {
        margin-top: 2rem;
    }
    
    h4 {
        color: #495057;
        border-bottom: 2px solid #dee2e6;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }
</style>
