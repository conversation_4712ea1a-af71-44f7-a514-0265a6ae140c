@page "/flex-answer-display-example"
@using FlexLibrary
@using FlexLibrary.Models
@using ServerComponentLibrary.Components.Forms
@using ServerComponentLibrary.Components.Inputs.Models

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2>FlexAnswerDisplay Component Examples</h2>
            <p class="text-muted">Demonstrating different layouts and data types for the read-only FlexAnswerDisplay component.</p>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <h4>Vertical Layout (Default)</h4>
            <FlexAnswerDisplay Questions="@sampleQuestions" 
                               Answers="@sampleAnswers"
                               Title="Sample Survey - Vertical Layout"
                               LayoutMode="AnswerDisplayLayoutMode.Vertical" />
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <h4>Grid Layout</h4>
            <FlexAnswerDisplay Questions="@sampleQuestions" 
                               Answers="@sampleAnswers"
                               Title="Sample Survey - Grid Layout"
                               LayoutMode="AnswerDisplayLayoutMode.Grid" />
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <h4>Compact Layout</h4>
            <FlexAnswerDisplay Questions="@sampleQuestions" 
                               Answers="@sampleAnswers"
                               Title="Sample Survey - Compact Layout"
                               LayoutMode="AnswerDisplayLayoutMode.Compact"
                               ShowOnlyAnsweredQuestions="true" />
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <h4>Horizontal Layout (Only Answered)</h4>
            <FlexAnswerDisplay Questions="@sampleQuestions" 
                               Answers="@sampleAnswers"
                               Title="Sample Survey - Horizontal Layout"
                               LayoutMode="AnswerDisplayLayoutMode.Horizontal"
                               ShowOnlyAnsweredQuestions="true" />
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <h4>Loading State</h4>
            <FlexAnswerDisplay Questions="@sampleQuestions" 
                               Answers="@sampleAnswers"
                               Title="Loading Example"
                               IsLoading="true" />
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <h4>Empty State</h4>
            <FlexAnswerDisplay Questions="@emptyQuestions" 
                               Answers="@emptyAnswers"
                               Title="No Data Example"
                               EmptyMessage="No survey data available for display." />
        </div>
    </div>
</div>

@code {
    private List<FlexQuestion> sampleQuestions = new();
    private List<FlexAnswer> sampleAnswers = new();
    private List<FlexQuestion> emptyQuestions = new();
    private List<FlexAnswer> emptyAnswers = new();

    protected override void OnInitialized()
    {
        CreateSampleData();
    }

    private void CreateSampleData()
    {
        // Create sample questions
        sampleQuestions = new List<FlexQuestion>
        {
            new FlexQuestion
            {
                DataName = "CompanyName",
                QuestionText = "Company Name",
                DataType = FlexDataTypeEnum.String,
                FlexQuestionRequired = true,
                ToolTip = "Enter the legal name of your company"
            },
            new FlexQuestion
            {
                DataName = "IsActive",
                QuestionText = "Is the company currently active?",
                DataType = FlexDataTypeEnum.Boolean,
                FlexQuestionRequired = true
            },
            new FlexQuestion
            {
                DataName = "AnnualRevenue",
                QuestionText = "Annual Revenue",
                DataType = FlexDataTypeEnum.Money,
                FlexQuestionRequired = false,
                ToolTip = "Enter your company's annual revenue"
            },
            new FlexQuestion
            {
                DataName = "FoundedDate",
                QuestionText = "Date Founded",
                DataType = FlexDataTypeEnum.Date,
                FlexQuestionRequired = false
            },
            new FlexQuestion
            {
                DataName = "ContactEmail",
                QuestionText = "Primary Contact Email",
                DataType = FlexDataTypeEnum.Email,
                FlexQuestionRequired = true
            },
            new FlexQuestion
            {
                DataName = "ContactPhone",
                QuestionText = "Primary Contact Phone",
                DataType = FlexDataTypeEnum.Phone,
                FlexQuestionRequired = false
            },
            new FlexQuestion
            {
                DataName = "Website",
                QuestionText = "Company Website",
                DataType = FlexDataTypeEnum.Url,
                FlexQuestionRequired = false
            },
            new FlexQuestion
            {
                DataName = "GrowthRate",
                QuestionText = "Expected Growth Rate",
                DataType = FlexDataTypeEnum.Percentage,
                FlexQuestionRequired = false
            },
            new FlexQuestion
            {
                DataName = "BusinessType",
                QuestionText = "Type of Business",
                DataType = FlexDataTypeEnum.Combobox,
                FlexQuestionRequired = true
            },
            new FlexQuestion
            {
                DataName = "Description",
                QuestionText = "Business Description",
                DataType = FlexDataTypeEnum.Memo,
                FlexQuestionRequired = false,
                ToolTip = "Provide a brief description of your business"
            }
        };

        // Create sample answers
        sampleAnswers = new List<FlexAnswer>
        {
            CreateFlexAnswer("CompanyName", "Acme Corporation"),
            CreateFlexAnswer("IsActive", "true"),
            CreateFlexAnswer("AnnualRevenue", "1500000"),
            CreateFlexAnswer("FoundedDate", "2010-03-15"),
            CreateFlexAnswer("ContactEmail", "<EMAIL>"),
            CreateFlexAnswer("ContactPhone", "(*************"),
            CreateFlexAnswer("Website", "https://www.acmecorp.com"),
            CreateFlexAnswer("GrowthRate", "15.5"),
            CreateFlexAnswer("BusinessType", "Manufacturing"),
            CreateFlexAnswer("Description", "We manufacture high-quality widgets and gadgets for various industries.")
        };
    }

    private FlexAnswer CreateFlexAnswer(string questionDataName, string answer)
    {
        // Create a basic FlexAnswer - this would normally come from your data source
        return new FlexAnswer(questionDataName, answer)
        {
            RowNumber = 0,
            FlexResponseGuid = Guid.NewGuid(),
            SavedBySecurityAccountGuid = Guid.NewGuid()
        };
    }
}

<style>
    .container {
        max-width: 1200px;
    }
    
    .row + .row {
        margin-top: 2rem;
    }
    
    h4 {
        color: #495057;
        border-bottom: 2px solid #dee2e6;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }
</style>
