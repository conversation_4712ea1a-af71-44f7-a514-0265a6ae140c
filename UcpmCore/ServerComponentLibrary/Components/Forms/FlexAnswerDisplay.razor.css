.flex-answer-display {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
}

.flex-answer-display-title {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #dee2e6;
}

.flex-answer-display-title h4 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.flex-answer-display-item {
    background-color: white;
    padding: 0.75rem;
    border-radius: 0.25rem;
    transition: box-shadow 0.15s ease-in-out;
}

.flex-answer-display-item:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.flex-answer-display-question {
    margin-bottom: 0.5rem;
}

.flex-answer-display-question .form-label {
    margin-bottom: 0.25rem;
    color: #495057;
    font-size: 0.875rem;
}

.flex-answer-display-question .text-muted {
    font-size: 0.75rem;
    line-height: 1.2;
}

.flex-answer-display-answer {
    min-height: 1.5rem;
}

.flex-answer-value {
    font-size: 0.9rem;
    line-height: 1.4;
}

.flex-answer-value.no-answer {
    color: #6c757d;
    font-style: italic;
}

.flex-answer-value.required-missing {
    color: #dc3545;
    font-weight: 500;
}

.contact-info-display {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 0.5rem;
    font-size: 0.875rem;
}

.contact-info-display .fw-bold {
    color: #495057;
    margin-bottom: 0.25rem;
}

.contact-info-display .small {
    margin-bottom: 0.125rem;
}

.contact-info-display .small:last-child {
    margin-bottom: 0;
}

.zipcode-info-display {
    font-size: 0.9rem;
}

.zipcode-info-display .fw-bold {
    color: #495057;
}

/* Layout-specific styles */
.flex-answer-display .row .flex-answer-display-item {
    margin-bottom: 1rem;
}

.flex-answer-display .d-flex.flex-wrap .flex-answer-display-item {
    min-width: 200px;
    flex: 1 1 auto;
}

/* Compact layout styles */
.flex-answer-display-item.border.rounded.p-2 {
    background-color: white;
    border-color: #dee2e6 !important;
}

.flex-answer-display-item.border.rounded.p-2:hover {
    border-color: #adb5bd !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
}

/* Data type specific styles */
.badge.bg-success {
    background-color: #198754 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

.text-success {
    color: #198754 !important;
    font-weight: 600;
}

.text-primary {
    color: #0d6efd !important;
}

.text-info {
    color: #0dcaf0 !important;
}

/* Link styles */
a.text-decoration-none {
    color: inherit;
    transition: color 0.15s ease-in-out;
}

a.text-decoration-none:hover {
    color: #0d6efd !important;
    text-decoration: underline !important;
}

/* Loading state */
.spinner-border {
    width: 1.5rem;
    height: 1.5rem;
}

/* Empty state */
.text-muted.text-center.p-4 {
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    color: #6c757d;
    font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .flex-answer-display {
        padding: 0.75rem;
    }
    
    .flex-answer-display-item {
        padding: 0.5rem;
    }
    
    .flex-answer-display-question .form-label {
        font-size: 0.8rem;
    }
    
    .flex-answer-value {
        font-size: 0.85rem;
    }
    
    .contact-info-display {
        font-size: 0.8rem;
    }
}

/* Print styles */
@media print {
    .flex-answer-display {
        background-color: white !important;
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .flex-answer-display-item {
        background-color: white !important;
        box-shadow: none !important;
        break-inside: avoid;
    }
    
    .flex-answer-display-item:hover {
        box-shadow: none !important;
    }
    
    a {
        color: #000 !important;
        text-decoration: underline !important;
    }
}
