@using Microsoft.AspNetCore.Components.Rendering
@using ServerComponentLibrary.Components.Forms.Configuration
@using ServerComponentLibrary.Components.Forms.Extensions
@using ServerComponentLibrary.Components.Inputs
@using ServerComponentLibrary.Components.Inputs.Models
@inherits FlexDynamicFormBase

@* Loading indicator for initial form load *@
@if (ShowLoadingIndicators && IsFormLoading)
{
    <div class="d-flex justify-content-center align-items-center p-4">
        <div class="@LoadingSpinnerClass text-primary me-2" role="status" aria-hidden="true"></div>
        <span class="text-muted">@LoadingText</span>
    </div>
} 

@* Progress bar for component loading *@
@if (ShowLoadingIndicators && IsAnyComponentLoading && !IsFormLoading)
{
    <div class="mb-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <small class="text-muted">Loading form components...</small>
            <small class="text-muted">@LoadingProgress%</small>
        </div>
        <div class="progress" style="height: 4px;">
            <div class="progress-bar progress-bar-striped progress-bar-animated"
                 role="progressbar"
                 style="width: @LoadingProgress%"
                 aria-valuenow="@LoadingProgress"
                 aria-valuemin="0"
                 aria-valuemax="100"></div>
        </div>
    </div>
}

<div class="@FlexDynamicFormConfiguration.GetFormContainerClass(LayoutMode); @CssClass">
    @foreach (var question in VisibleQuestions.Where(q => string.IsNullOrWhiteSpace(q.ParentQuestionDataName)))
    {
        <div class="@GetBootstrapColumnClass(question.DisplayWidth)">
            @RenderQuestion(question)
        </div>

        @* Render child questions if they exist and are visible *@
        @if (question.ChildQuestions?.Any() == true)
        {
            @RenderChildQuestions(question.ChildQuestions, 1)
        }
    }
</div>

@if (ShowValidationSummary && ValidationStates.Any(v => !v.Value))
{
    <div class="alert alert-danger mt-3">
        <h6>Please correct the following errors:</h6>
        <ul class="mb-0">
            @foreach (var invalidField in ValidationStates.Where(v => !v.Value))
            {
                var question = QuestionLookup.GetValueOrDefault(invalidField.Key);
                <li>@(question?.QuestionText ?? invalidField.Key) has validation errors</li>
            }
        </ul>
    </div>
}

@code {
    // Common attribute configuration record
    private record ComponentAttributes(
        string Label,
        string QuestionHtml,
        string Value,
        EventCallback<string> ValueChanged,
        string FieldName,
        Dictionary<string, object?> FormData,
        bool IsRequired,
        string Placeholder,
        string ToolTip,
        string HelpText,
        string InputCssClass,
        string LabelCssClass,
        bool IsDisabled = false,
        EventCallback<bool>? IsValidChanged = null
    );

    private RenderFragment RenderChildQuestions(List<FlexQuestion> childQuestions, int indentLevel) => builder =>
    {
        var indentClass = $"ms-{Math.Min(indentLevel * 3, 9)}";
        var levelClass = $"child-question level-{indentLevel}";

        foreach (var childQuestion in childQuestions.Where(IsQuestionVisible))
        {
            builder.OpenElement(0, "div");
            builder.AddAttribute(1, "class", $"{GetBootstrapColumnClass(childQuestion.DisplayWidth)} {indentClass}");

            builder.OpenElement(2, "div");
            builder.AddAttribute(3, "class", levelClass);

            if (indentLevel > 0)
            {
                builder.OpenElement(4, "div");
                builder.AddAttribute(5, "class", "hierarchy-indicator mb-1");
                builder.CloseElement();
            }

            builder.AddContent(7, RenderQuestion(childQuestion));

            if (childQuestion.ChildQuestions?.Any() == true)
            {
                builder.AddContent(8, RenderChildQuestions(childQuestion.ChildQuestions, indentLevel + 1));
            }

            builder.CloseElement();
            builder.CloseElement();
        }
    };

    private ComponentAttributes CreateCommonAttributes(FlexQuestion question)
    {
        return new ComponentAttributes(
            Label: question.QuestionText,
            QuestionHtml: question.QuestionHtml,
            Value: FormData.GetFieldStringValue(question.DataName),
            ValueChanged: EventCallback.Factory.Create<string>(this, value => OnFieldValueChanged(question.DataName, value)),
            FieldName: question.DataName,
            FormData: FormData,
            IsRequired: question.FlexQuestionRequired && IsQuestionVisible(question),
            Placeholder: question.PlaceHolderText,
            ToolTip: question.ToolTip,
            HelpText: question.ToolTip,
            InputCssClass: FlexDynamicFormConfiguration.GetInputBootstrapClass(question.InputWidth),
            LabelCssClass: FlexDynamicFormConfiguration.DefaultFontWeightLabel,
            IsDisabled: question.IsCalculatedField,
            IsValidChanged: EventCallback.Factory.Create<bool>(this, isValid => OnFieldValidationChanged(question.DataName, isValid))
        );
    }

    private void AddCommonAttributes<T>(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs) where T : ComponentBase
    {
        builder.OpenComponent<T>(sequence++);
        builder.AddAttribute(sequence++, "Label", attrs.Label);
        builder.AddAttribute(sequence++, "QuestionHtml", attrs.QuestionHtml);
        builder.AddAttribute(sequence++, "Value", attrs.Value);
        builder.AddAttribute(sequence++, "ValueChanged", attrs.ValueChanged);
        builder.AddAttribute(sequence++, "FieldName", attrs.FieldName);
        builder.AddAttribute(sequence++, "FormData", attrs.FormData);
        builder.AddAttribute(sequence++, "IsRequired", attrs.IsRequired);
        builder.AddAttribute(sequence++, "Placeholder", attrs.Placeholder);
        builder.AddAttribute(sequence++, "ToolTip", attrs.ToolTip);
        builder.AddAttribute(sequence++, "HelpText", attrs.HelpText);
        builder.AddAttribute(sequence++, "InputCssClass", attrs.InputCssClass);
        builder.AddAttribute(sequence++, "LabelCssClass", attrs.LabelCssClass);
        builder.AddAttribute(sequence++, "IsDisabled", attrs.IsDisabled);
        if (attrs.IsValidChanged.HasValue)
        {
            builder.AddAttribute(sequence++, "IsValidChanged", attrs.IsValidChanged.Value);
        }
    }

    private RenderFragment RenderQuestion(FlexQuestion question) => builder =>
    {
        var sequence = 0;
        var commonAttrs = CreateCommonAttributes(question);

        switch (question.DataType)
        {
            case FlexDataTypeEnum.String:
            case FlexDataTypeEnum.PathfinderCobRevenues:
            case FlexDataTypeEnum.ProjectSpecific:
            case FlexDataTypeEnum.Situations:
            case FlexDataTypeEnum.SalesConfiguration:
                RenderTextInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.Combobox:
                RenderSelectInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.Multiselect:
                RenderMultiSelectInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.Phone:
                RenderPhoneInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.Email:
                RenderEmailInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.Number:
                RenderNumberInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.Money:
                RenderMoneyInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.Percentage:
                RenderPercentageInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.Date:
                RenderDateInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.Time:
                RenderTimeInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.Password:
                RenderPasswordInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.Url:
                RenderUrlInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.FileUpload:
                RenderFileUploadInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.Memo:
                RenderTextAreaInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.Boolean:
                RenderBooleanInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.RadioGroup:
                RenderRadioGroupInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.Body:
                RenderBodyContent(builder, ref sequence, question);
                break;

            case FlexDataTypeEnum.ZipCode:
                RenderZipCodeInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.ContactInfo:
                RenderContactInfoInput(builder, ref sequence, commonAttrs, question);
                break;

            case FlexDataTypeEnum.Video:
            case FlexDataTypeEnum.DataGrid:
            case FlexDataTypeEnum.MatchMyCoverage:
            case FlexDataTypeEnum.BallparkPrice:
                RenderInformationalContent(builder, ref sequence, question);
                break;

            default:
                RenderTextInput(builder, ref sequence, commonAttrs, question);
                break;
        }
    };

    // Specialized render methods for each component type
    private void RenderTextInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        AddCommonAttributes<FlexTextInput>(builder, ref sequence, attrs);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderSelectInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        AddCommonAttributes<FlexSelectInput>(builder, ref sequence, attrs);
        builder.AddAttribute(sequence++, "Options", GetComboBoxOptions(question));
        builder.AddAttribute(sequence++, "FlexFactorGroup", GetFactorGroup(question.ComboBoxQuery));
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderMultiSelectInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        builder.OpenComponent<FlexMultiSelectInput>(sequence++);
        builder.AddAttribute(sequence++, "Label", attrs.Label);
        builder.AddAttribute(sequence++, "QuestionHtml", attrs.QuestionHtml);
        builder.AddAttribute(sequence++, "SelectedValues", GetFieldValue(question.DataName) as List<string> ?? new List<string>());
        builder.AddAttribute(sequence++, "SelectedValuesChanged", EventCallback.Factory.Create<List<string>>(this, values => OnFieldValueChanged(question.DataName, values)));
        builder.AddAttribute(sequence++, "FieldName", attrs.FieldName);
        builder.AddAttribute(sequence++, "FormData", attrs.FormData);
        builder.AddAttribute(sequence++, "Options", GetComboBoxOptions(question));
        builder.AddAttribute(sequence++, "IsRequired", attrs.IsRequired);
        builder.AddAttribute(sequence++, "Placeholder", attrs.Placeholder);
        builder.AddAttribute(sequence++, "ToolTip", attrs.ToolTip);
        builder.AddAttribute(sequence++, "HelpText", attrs.HelpText);
        builder.AddAttribute(sequence++, "InputCssClass", attrs.InputCssClass);
        builder.AddAttribute(sequence++, "LabelCssClass", attrs.LabelCssClass);
        builder.AddAttribute(sequence++, "MinSelections", 0);
        builder.AddAttribute(sequence++, "MaxSelections", int.MaxValue);
        builder.AddAttribute(sequence++, "ShowSelectedCount", true);
        if (attrs.IsValidChanged.HasValue)
            builder.AddAttribute(sequence++, "IsValidChanged", attrs.IsValidChanged.Value);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderPhoneInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        AddCommonAttributes<FlexPhoneInput>(builder, ref sequence, attrs);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderEmailInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        AddCommonAttributes<FlexEmailInput>(builder, ref sequence, attrs);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderNumberInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        AddCommonAttributes<FlexNumberInput>(builder, ref sequence, attrs);
        builder.AddAttribute(sequence++, "DecimalPlaces", 0);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderMoneyInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        AddCommonAttributes<FlexMoneyInput>(builder, ref sequence, attrs);
        builder.AddAttribute(sequence++, "DecimalPlaces", 2);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderPercentageInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        AddCommonAttributes<FlexPercentageInput>(builder, ref sequence, attrs);
        builder.AddAttribute(sequence++, "DecimalPlaces", 2);
        builder.AddAttribute(sequence++, "MinValue", 0m);
        builder.AddAttribute(sequence++, "MaxValue", 100m);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderDateInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        AddCommonAttributes<FlexDateInput>(builder, ref sequence, attrs);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderTimeInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        AddCommonAttributes<FlexTimeInput>(builder, ref sequence, attrs);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderPasswordInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        AddCommonAttributes<FlexPasswordInput>(builder, ref sequence, attrs);
        builder.AddAttribute(sequence++, "ShowToggleButton", true);
        builder.AddAttribute(sequence++, "ShowStrengthIndicator", true);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderUrlInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        AddCommonAttributes<FlexUrlInput>(builder, ref sequence, attrs);
        builder.AddAttribute(sequence++, "AutoAddProtocol", true);
        builder.AddAttribute(sequence++, "DefaultProtocol", "https://");
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderFileUploadInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        builder.OpenComponent<FlexFileUploadInput>(sequence++);
        builder.AddAttribute(sequence++, "Label", attrs.Label);
        builder.AddAttribute(sequence++, "QuestionHtml", attrs.QuestionHtml);
        builder.AddAttribute(sequence++, "SelectedFiles", GetFieldValue(question.DataName) as IBrowserFile[] ?? []);
        builder.AddAttribute(sequence++, "FilesChanged", EventCallback.Factory.Create<IBrowserFile[]>(this, files => OnFieldValueChanged(question.DataName, files)));
        builder.AddAttribute(sequence++, "FieldName", attrs.FieldName);
        builder.AddAttribute(sequence++, "FormData", attrs.FormData);
        builder.AddAttribute(sequence++, "IsRequired", attrs.IsRequired);
        builder.AddAttribute(sequence++, "ToolTip", attrs.ToolTip);
        builder.AddAttribute(sequence++, "HelpText", attrs.HelpText);
        builder.AddAttribute(sequence++, "InputCssClass", attrs.InputCssClass);
        builder.AddAttribute(sequence++, "LabelCssClass", attrs.LabelCssClass);
        builder.AddAttribute(sequence++, "AcceptedFileTypes", ".pdf,.doc,.docx,.jpg,.png");
        builder.AddAttribute(sequence++, "MaxFileSize", 5242880L);
        builder.AddAttribute(sequence++, "MaxFiles", 1);
        builder.AddAttribute(sequence++, "ShowFileList", true);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderTextAreaInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        AddCommonAttributes<FlexTextAreaInput>(builder, ref sequence, attrs);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderBooleanInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        builder.OpenComponent<FlexYesNoRadioInput>(sequence++);
        builder.AddAttribute(sequence++, "Label", attrs.Label);
        builder.AddAttribute(sequence++, "QuestionHtml", attrs.QuestionHtml);
        builder.AddAttribute(sequence++, "BooleanValue",FormData.GetBooleanFieldValue(question.DataName));
        builder.AddAttribute(sequence++, "BooleanValueChanged", EventCallback.Factory.Create<bool?>(this, value => OnFieldValueChanged(question.DataName, value)));
        builder.AddAttribute(sequence++, "FieldName", attrs.FieldName);
        builder.AddAttribute(sequence++, "FormData", attrs.FormData);
        builder.AddAttribute(sequence++, "IsRequired", attrs.IsRequired);
        builder.AddAttribute(sequence++, "ToolTip", attrs.ToolTip);
        builder.AddAttribute(sequence++, "HelpText", attrs.HelpText);
        builder.AddAttribute(sequence++, "InputCssClass", attrs.InputCssClass);
        builder.AddAttribute(sequence++, "LabelCssClass", attrs.LabelCssClass);
        if (attrs.IsValidChanged.HasValue)
            builder.AddAttribute(sequence++, "IsValidChanged", attrs.IsValidChanged.Value);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderRadioGroupInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        AddCommonAttributes<FlexRadioGroupInput>(builder, ref sequence, attrs);
        builder.AddAttribute(sequence++, "Options", GetRadioGroupOptions(question));
        builder.AddAttribute(sequence++, "Layout", RadioGroupLayout.Vertical);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderBodyContent(RenderTreeBuilder builder, ref int sequence, FlexQuestion question)
    {
        builder.OpenElement(sequence++, "div");
        builder.AddAttribute(sequence++, "class", $"{GetBootstrapColumnClass(question.DisplayWidth)} mb-3");

        if (!string.IsNullOrWhiteSpace(question.QuestionHtml))
        {
            builder.AddMarkupContent(sequence, question.QuestionHtml);
        }
        else if (!string.IsNullOrWhiteSpace(question.QuestionText))
        {
            builder.OpenElement(sequence++, "div");
            builder.AddAttribute(sequence++, "class", "alert alert-info");
            builder.AddContent(sequence, question.QuestionText);
            builder.CloseElement();
        }

        builder.CloseElement();
    }

    private void RenderZipCodeInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        AddCommonAttributes<FlexZipCodeInput>(builder, ref sequence, attrs);
        builder.AddAttribute(sequence++, "ZipCodeInfoChanged", EventCallback.Factory.Create<ZipCodeInfo>(this, value => OnFieldValueChanged(question.DataName, value)));
        builder.AddAttribute(sequence++, "LoadingStateChanged", EventCallback.Factory.Create<Tuple<string, bool>>(this, OnOtherComponentLoadingStateChanged));
        builder.AddAttribute(sequence++, "ShowLoadingIndicator", ShowLoadingIndicators);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderContactInfoInput(RenderTreeBuilder builder, ref int sequence, ComponentAttributes attrs, FlexQuestion question)
    {
        AddCommonAttributes<FlexContactInfoInput>(builder, ref sequence, attrs);
        builder.AddAttribute(sequence++, "Contacts", GetContactInfoOptions(question));
        builder.AddAttribute(sequence++, "EmailFieldName", question.EmailFieldName ?? "");
        builder.AddAttribute(sequence++, "PhoneFieldName", question.PhoneFieldName ?? "");
        builder.AddAttribute(sequence++, "VariableHolder", VariableHolder);
        builder.AddAttribute(sequence++, "LoadingStateChanged", EventCallback.Factory.Create<bool>(this, isLoading => OnComponentLoadingStateChanged(question.DataName, isLoading)));
        builder.AddAttribute(sequence++, "ShowLoadingIndicator", ShowLoadingIndicators);
        AddValidationAndVisibilityAttributes(builder, ref sequence, question);
        builder.CloseComponent();
    }

    private void RenderInformationalContent(RenderTreeBuilder builder, ref int sequence, FlexQuestion question)
    {
        var (icon, title, description) = question.DataType switch
        {
            FlexDataTypeEnum.Video => ("📹", "Video", "Video functionality"),
            FlexDataTypeEnum.DataGrid => ("📊", "Data Grid", "Complex data grid functionality not yet implemented in FlexDynamicForm."),
            FlexDataTypeEnum.MatchMyCoverage => ("🔍", "Match My Coverage", "Advanced coverage matching functionality."),
            FlexDataTypeEnum.BallparkPrice => ("💰", "Ballpark Pricing", "Pricing estimation functionality."),
            _ => ("ℹ️", "Information", "Special content")
        };

        builder.OpenElement(sequence++, "div");
        builder.AddAttribute(sequence++, "class", $"{GetBootstrapColumnClass(question.DisplayWidth)} mb-3");
        builder.OpenElement(sequence++, "div");
        builder.AddAttribute(sequence++, "class", "alert alert-info");
        builder.AddContent(sequence++, $"{icon} {title}: {question.QuestionText}");
        builder.OpenElement(sequence++, "br");
        builder.CloseElement();
        builder.AddContent(sequence++, description);
        builder.CloseElement();
        builder.CloseElement();
    }

    private void AddValidationAndVisibilityAttributes(RenderTreeBuilder builder, ref int sequence, FlexQuestion question)
    {
        if (question.ValidationRuleContainer?.ValidationRules?.Any() == true)
        {
            builder.AddAttribute(sequence++, "ValidationRuleContainer", question.ValidationRuleContainer);
        }

        if (question.VisibilityRuleContainer?.VisibilityRules?.Any() == true)
        {
            builder.AddAttribute(sequence++, "VisibilityRuleContainer", question.VisibilityRuleContainer);
        }
    }
}