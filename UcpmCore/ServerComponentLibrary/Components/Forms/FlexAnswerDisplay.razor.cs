using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using FlexLibrary;
using FlexLibrary.Models;
using ServerComponentLibrary.Components.Inputs.Models;
using ServerComponentLibrary.Components.Inputs;
using UcpmApi.Shared;
using UcpmApi.Shared.Survey;
using System.Text.Json;
using System.Globalization;
using System.Reflection;

namespace ServerComponentLibrary.Components.Forms;

public enum AnswerDisplayLayoutMode
{
    Vertical,
    Horizontal,
    Grid,
    Compact
}

public class QuestionAnswerPair
{
    public FlexQuestion Question { get; set; } = new();
    public FlexAnswer? Answer { get; set; }
}

public class FlexAnswerDisplayBase : ComponentBase
{
    [Parameter]
    public List<FlexQuestion> Questions { get; set; } = [];

    [Parameter]
    public FlexAnswerContainer? AnswerContainer { get; set; }

    [Parameter]
    public List<FlexAnswer> Answers { get; set; } = [];

    [Parameter]
    public string Title { get; set; } = "Survey Responses";

    [Parameter]
    public bool ShowTitle { get; set; } = true;

    [Parameter]
    public bool ShowQuestionText { get; set; } = true;

    [Parameter]
    public string CssClass { get; set; } = "flex-answer-display";

    [Parameter]
    public AnswerDisplayLayoutMode LayoutMode { get; set; } = AnswerDisplayLayoutMode.Vertical;

    [Parameter]
    public string EmptyMessage { get; set; } = "No answers to display.";

    [Parameter]
    public string NoAnswerText { get; set; } = "Not answered";

    [Parameter]
    public bool ShowOnlyAnsweredQuestions { get; set; } = false;

    [Parameter]
    public bool ShowRequiredIndicator { get; set; } = true;

    [Parameter]
    public Dictionary<string, object> VariableHolder { get; set; } = new();

    [Parameter]
    public bool IsLoading { get; set; } = false;

    protected List<QuestionAnswerPair> VisibleQuestionAnswerPairs = [];
    private readonly Dictionary<string, FlexAnswer> _answerLookup = new();

    protected override void OnParametersSet()
    {
        BuildAnswerLookup();
        BuildQuestionAnswerPairs();
        base.OnParametersSet();
    }

    private void BuildAnswerLookup()
    {
        _answerLookup.Clear();

        // Add answers from AnswerContainer if provided
        if (AnswerContainer?.FlexAnswers != null)
        {
            foreach (var answer in AnswerContainer.FlexAnswers)
            {
                if (!string.IsNullOrWhiteSpace(answer.QuestionDataName))
                {
                    _answerLookup[answer.QuestionDataName] = answer;
                }
            }
        }

        // Add answers from direct Answers list if provided
        foreach (var answer in Answers)
        {
            if (!string.IsNullOrWhiteSpace(answer.QuestionDataName))
            {
                _answerLookup[answer.QuestionDataName] = answer;
            }
        }
    }

    private void BuildQuestionAnswerPairs()
    {
        VisibleQuestionAnswerPairs.Clear();

        foreach (var question in Questions)
        {
            if (!IsQuestionVisible(question))
                continue;

            var answer = _answerLookup.GetValueOrDefault(question.DataName);

            // Skip if showing only answered questions and this question has no answer
            if (ShowOnlyAnsweredQuestions && (answer == null || string.IsNullOrWhiteSpace(answer.Answer)))
                continue;

            VisibleQuestionAnswerPairs.Add(new QuestionAnswerPair
            {
                Question = question,
                Answer = answer
            });
        }
    }

    protected virtual bool IsQuestionVisible(FlexQuestion question)
    {
        // Basic visibility check - can be overridden for more complex logic
        return !string.IsNullOrWhiteSpace(question.QuestionText);
    }

    protected string GetDisplayValue(FlexQuestion question, FlexAnswer? answer)
    {
        if (answer == null || string.IsNullOrWhiteSpace(answer.Answer))
            return string.Empty;

        return question.DataType switch
        {
            FlexDataTypeEnum.Boolean => FormatBooleanValue(answer.Answer),
            FlexDataTypeEnum.Date => FormatDateValue(answer.Answer),
            FlexDataTypeEnum.Money => FormatMoneyValue(answer.Answer),
            FlexDataTypeEnum.Percentage => FormatPercentageValue(answer.Answer),
            FlexDataTypeEnum.ContactInfo => FormatContactInfoValue(answer),
            FlexDataTypeEnum.ZipCode => FormatZipCodeValue(answer),
            FlexDataTypeEnum.Combobox => FormatComboboxValue(answer.Answer, question),
            _ => answer.Answer
        };
    }

    private string FormatBooleanValue(string value)
    {
        if (bool.TryParse(value, out var boolValue))
        {
            return boolValue ? "Yes" : "No";
        }
        return value.Equals("true", StringComparison.OrdinalIgnoreCase) ? "Yes" : "No";
    }

    private string FormatDateValue(string value)
    {
        if (DateTime.TryParse(value, out var dateValue))
        {
            return dateValue.ToString("MM/dd/yyyy");
        }
        return value;
    }

    private string FormatMoneyValue(string value)
    {
        if (decimal.TryParse(value, out var decimalValue))
        {
            return decimalValue.ToString("C", CultureInfo.CurrentCulture);
        }
        return value;
    }

    private string FormatPercentageValue(string value)
    {
        if (decimal.TryParse(value, out var decimalValue))
        {
            return $"{decimalValue:F2}%";
        }
        return value.EndsWith('%') ? value : $"{value}%";
    }

    private string FormatContactInfoValue(FlexAnswer answer)
    {
        var contactInfo = GetContactInfoFromAnswer(answer);
        return contactInfo?.Name ?? answer.Answer;
    }

    private string FormatZipCodeValue(FlexAnswer answer)
    {
        var zipCodeInfo = GetZipCodeInfoFromAnswer(answer);
        if (zipCodeInfo != null)
        {
            return !string.IsNullOrWhiteSpace(zipCodeInfo.City) && !string.IsNullOrWhiteSpace(zipCodeInfo.State)
                ? $"{zipCodeInfo.ZipCode} ({zipCodeInfo.City}, {zipCodeInfo.State})"
                : zipCodeInfo.ZipCode;
        }
        return answer.Answer;
    }

    private string FormatComboboxValue(string value, FlexQuestion question)
    {
        // For combobox, we might want to show the display text instead of the value
        // This would require additional logic to map values to display text
        return value;
    }

    protected ContactInfo? GetContactInfoFromAnswer(FlexAnswer? answer)
    {
        if (answer == null)
            return null;

        // Try to get from AnswerObject first (if it exists)
        var answerObject = GetAnswerObject(answer);
        if (!string.IsNullOrWhiteSpace(answerObject))
        {
            try
            {
                return JsonSerializer.Deserialize<ContactInfo>(answerObject);
            }
            catch (JsonException)
            {
                // Fall through to try parsing from Answer field
            }
        }

        // Try to parse from Answer field as JSON
        if (!string.IsNullOrWhiteSpace(answer.Answer))
        {
            try
            {
                return JsonSerializer.Deserialize<ContactInfo>(answer.Answer);
            }
            catch (JsonException)
            {
                // Return a simple ContactInfo with just the name
                return new ContactInfo { Name = answer.Answer };
            }
        }

        return null;
    }

    protected ZipCodeInfo? GetZipCodeInfoFromAnswer(FlexAnswer? answer)
    {
        if (answer == null)
            return null;

        // Try to get from AnswerObject first (if it exists)
        var answerObject = GetAnswerObject(answer);
        if (!string.IsNullOrWhiteSpace(answerObject))
        {
            try
            {
                return JsonSerializer.Deserialize<ZipCodeInfo>(answerObject);
            }
            catch (JsonException)
            {
                // Fall through to try parsing from Answer field
            }
        }

        // Try to parse from Answer field as JSON
        if (!string.IsNullOrWhiteSpace(answer.Answer))
        {
            try
            {
                return JsonSerializer.Deserialize<ZipCodeInfo>(answer.Answer);
            }
            catch (JsonException)
            {
                // Return a simple ZipCodeInfo with just the zip code
                return new ZipCodeInfo { ZipCode = answer.Answer };
            }
        }

        return null;
    }

    private string? GetAnswerObject(FlexAnswer answer)
    {
        // Use reflection to safely get AnswerObject property if it exists
        var answerType = answer.GetType();
        var answerObjectProperty = answerType.GetProperty("AnswerObject");

        if (answerObjectProperty != null)
        {
            var value = answerObjectProperty.GetValue(answer);
            return value?.ToString();
        }

        return null;
    }

    protected string GetContainerClass()
    {
        return LayoutMode switch
        {
            AnswerDisplayLayoutMode.Horizontal => "row",
            AnswerDisplayLayoutMode.Grid => "row row-cols-1 row-cols-md-2 row-cols-lg-3 g-3",
            AnswerDisplayLayoutMode.Compact => "d-flex flex-wrap gap-3",
            _ => "d-flex flex-column gap-3"
        };
    }

    protected string GetQuestionContainerClass(FlexQuestion question)
    {
        var baseClass = "flex-answer-display-item";
        
        return LayoutMode switch
        {
            AnswerDisplayLayoutMode.Horizontal => $"{baseClass} col-md-6",
            AnswerDisplayLayoutMode.Grid => $"{baseClass} col",
            AnswerDisplayLayoutMode.Compact => $"{baseClass} border rounded p-2",
            _ => $"{baseClass} border-bottom pb-2"
        };
    }

    protected string GetAnswerCssClass(FlexQuestion question, FlexAnswer? answer)
    {
        var baseClass = "flex-answer-value";
        
        if (answer == null || string.IsNullOrWhiteSpace(answer.Answer))
        {
            baseClass += " no-answer";
        }

        if (question.FlexQuestionRequired && (answer == null || string.IsNullOrWhiteSpace(answer.Answer)))
        {
            baseClass += " required-missing";
        }

        return baseClass;
    }

    protected MarkupString GetAnswerDisplayMarkup(FlexQuestion question, FlexAnswer? answer)
    {
        var displayValue = GetDisplayValue(question, answer);
        var cssClass = GetAnswerCssClass(question, answer);

        if (string.IsNullOrWhiteSpace(displayValue))
        {
            return new MarkupString($"<div class=\"{cssClass}\"><span class=\"text-muted fst-italic\">{NoAnswerText}</span></div>");
        }

        var content = question.DataType switch
        {
            FlexDataTypeEnum.Boolean => GetBooleanMarkup(displayValue),
            FlexDataTypeEnum.Money => $"<span class=\"fw-bold text-success\">{displayValue}</span>",
            FlexDataTypeEnum.Date => $"<span class=\"text-primary\"><i class=\"bi bi-calendar3 me-1\"></i>{displayValue}</span>",
            FlexDataTypeEnum.Email => $"<a href=\"mailto:{displayValue}\" class=\"text-decoration-none\"><i class=\"bi bi-envelope me-1\"></i>{displayValue}</a>",
            FlexDataTypeEnum.Phone => $"<a href=\"tel:{displayValue}\" class=\"text-decoration-none\"><i class=\"bi bi-telephone me-1\"></i>{displayValue}</a>",
            FlexDataTypeEnum.Url => $"<a href=\"{displayValue}\" target=\"_blank\" class=\"text-decoration-none\"><i class=\"bi bi-link-45deg me-1\"></i>{displayValue}</a>",
            FlexDataTypeEnum.ContactInfo => GetContactInfoMarkup(answer),
            FlexDataTypeEnum.ZipCode => GetZipCodeMarkup(answer),
            FlexDataTypeEnum.Percentage => $"<span class=\"text-info\">{displayValue}</span>",
            _ => $"<span>{displayValue}</span>"
        };

        return new MarkupString($"<div class=\"{cssClass}\">{content}</div>");
    }

    private string GetBooleanMarkup(string displayValue)
    {
        var badgeClass = displayValue.Equals("Yes", StringComparison.OrdinalIgnoreCase) ? "bg-success" : "bg-secondary";
        return $"<span class=\"badge {badgeClass}\">{displayValue}</span>";
    }

    private string GetContactInfoMarkup(FlexAnswer? answer)
    {
        var contactInfo = GetContactInfoFromAnswer(answer);
        if (contactInfo == null)
        {
            return $"<span>{answer?.Answer ?? ""}</span>";
        }

        var markup = "<div class=\"contact-info-display\">";
        markup += $"<div class=\"fw-bold\">{contactInfo.Name}</div>";

        if (!string.IsNullOrWhiteSpace(contactInfo.Title))
        {
            markup += $"<div class=\"text-muted small\">{contactInfo.Title}</div>";
        }

        if (!string.IsNullOrWhiteSpace(contactInfo.Company))
        {
            markup += $"<div class=\"text-muted small\">{contactInfo.Company}</div>";
        }

        if (!string.IsNullOrWhiteSpace(contactInfo.Email))
        {
            markup += $"<div class=\"small\"><a href=\"mailto:{contactInfo.Email}\" class=\"text-decoration-none\"><i class=\"bi bi-envelope me-1\"></i>{contactInfo.Email}</a></div>";
        }

        if (!string.IsNullOrWhiteSpace(contactInfo.Phone))
        {
            markup += $"<div class=\"small\"><a href=\"tel:{contactInfo.Phone}\" class=\"text-decoration-none\"><i class=\"bi bi-telephone me-1\"></i>{contactInfo.Phone}</a></div>";
        }

        markup += "</div>";
        return markup;
    }

    private string GetZipCodeMarkup(FlexAnswer? answer)
    {
        var zipCodeInfo = GetZipCodeInfoFromAnswer(answer);
        if (zipCodeInfo == null)
        {
            return $"<span>{answer?.Answer ?? ""}</span>";
        }

        var markup = "<div class=\"zipcode-info-display\">";
        markup += $"<span class=\"fw-bold\">{zipCodeInfo.ZipCode}</span>";

        if (!string.IsNullOrWhiteSpace(zipCodeInfo.City) && !string.IsNullOrWhiteSpace(zipCodeInfo.State))
        {
            markup += $"<span class=\"text-muted ms-2\">({zipCodeInfo.City}, {zipCodeInfo.State})</span>";
        }

        markup += "</div>";
        return markup;
    }
}