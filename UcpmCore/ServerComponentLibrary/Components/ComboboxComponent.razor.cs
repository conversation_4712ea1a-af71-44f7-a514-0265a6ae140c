using FlexLibrary;
using FlexLibrary.Models;
using Microsoft.AspNetCore.Components;
using ServerComponentLibrary.Managers;

namespace ServerComponentLibrary.Components
{
    public class ComboboxComponentBase : DataTypeComponent
    {
        public string CurrentAnswer { get; set; }
        public List<FlexFactor> Factors { get; set; }

        public List<FlexCobFactor> Cobs { get; set; }
        public List<UcpmApi.Shared.State> States { get; set; }
        public string QuestionHtml { get; set; }
        public bool IsTooltipVideo { get; set; }
        public VideoModal VideoModal;
        [Inject]
        protected StateManager StateManager { get; set; }

        protected override async Task OnInitializedAsync()
        {
            GetDefaults();
            SurveyState.AllComponents.Add(this);
            SurveyState.OnChange += SurveyState_OnChange;
            await base.OnInitializedAsync();
        }

        public async Task ShowVideoModal()
        {
            await VideoModal.ShowModal();
        }

        private void SurveyState_OnChange()
        {
            GetDefaults();
        }

        private void GetDefaults()
        {
            QuestionText = Question.QuestionText;
            QuestionHtml = Question.QuestionHtml;
            IsTooltipVideo = Question.IsTooltipVideo;

            switch (Question.ComboBoxTypeId)
            {
                case 0:
                    IEnumerable<FlexFactorGroup> match = SurveyState.FactorGroups.Where(f => f.FactorGroupName == Question.ComboBoxQuery);
                    Factors = match.FirstOrDefault()?.Factors ?? [];
                    break;
                case 1:
                    Cobs = SurveyState.CobList;
                    break;
                case 2:
                    States = SurveyState.States;
                    break;
                default:
                    break;
            }

            if (Question.ParentQuestionDataName != "" && Question.ParentQuestionDataName != null)
            {
                FlexAnswer answer = SurveyState.FindByQuestion(Question.ParentQuestionDataName, SurveyState.FullAnswerList[FlexResponseGuid], ParentRowNumber);
                if (answer != null)
                {
                    string childAnswer = answer.ChildAnswers.SingleOrDefault(s => s.RowNumber == RowNumber && s.QuestionDataName == Question.DataName)?.Answer;
                    CurrentAnswer = childAnswer;
                }
            }
            else
            {
                List<FlexAnswer> matches = [];
                List<FlexAnswer> answers = SurveyState.FindByQuestion(Question.DataName, SurveyState.FullAnswerList[FlexResponseGuid], matches);
                CurrentAnswer = answers.DistinctBy(r => r.RowNumber).Where(r => r.RowNumber == RowNumber).SingleOrDefault()?.Answer ?? "";
            }

            base.DetermineWidth();
            base.DetermineVisibility();
        }

        public void MyListValueChangedHandler(string newValue)
        {
            CurrentAnswer = newValue;
            base.SaveNewAnswer(CurrentAnswer);
        }
    }
}
