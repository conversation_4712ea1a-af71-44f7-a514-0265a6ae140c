using BlazorStrap.V5;
using Microsoft.AspNetCore.Components;
using ServerComponentLibrary.Shared;

namespace ServerComponentLibrary.Components
{
    public class CustomButtonBase : ComponentBase
    {
        [Parameter]
        public required EventCallback ButtonEvent { get; set; }
        [Parameter]
        public string? ButtonLabel { get; set; } = "";
        [Parameter]
        public RenderFragment? ChildContent { get; set; } = null;
        [Parameter]
        public required string ButtonLoadingLabel { get; set; }
        [Parameter]
        public required string Class { get; set; }
        protected bool DataIsLoading { get; set; }

        protected async Task TriggerButtonEvent()
        {
            DataIsLoading = true;

            await ButtonEvent.InvokeAsync();

            DataIsLoading = false;
        }
    }
}
