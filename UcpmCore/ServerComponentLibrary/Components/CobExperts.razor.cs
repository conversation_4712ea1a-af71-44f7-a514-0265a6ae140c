using Microsoft.AspNetCore.Components;
using UcpmApi.Shared;
using ServerComponentLibrary.Managers;
using ServerComponentLibrary.Shared;

namespace ServerComponentLibrary.Components
{
    public class CobExpertsBase : UcpmComponentBase
    {
        [Parameter]
        public Guid CobGuid { get; set; }
        [Parameter]
        public string CobName { get; set; }
        public List<Employee> Experts { get; set; } = [];
        [Inject]
        private EmployeeManager employeeManager { get; set; }

        protected override async Task OnInitializedAsync()
        {
            UcpmApi.Shared.CobExperts experts = await employeeManager.GetCobExperts(CobGuid);
            CobName = experts.CobName;
            Experts = experts.Experts;
            await base.OnInitializedAsync();
        }
    }
}