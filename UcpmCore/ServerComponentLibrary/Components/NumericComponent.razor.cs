using FlexLibrary;

namespace ServerComponentLibrary.Components
{
    public class NumericComponentBase : DataTypeComponent
    {
        public decimal AnswerDecimalValue { get; set; }
        public string QuestionHtml { get; set; }
        public bool IsTooltipVideo { get; set; }
        public VideoModal VideoModal;

        protected override async Task OnInitializedAsync()
        {
            GetDefaults();
            SurveyState.AllComponents.Add(this);
            SurveyState.OnChange += SurveyState_OnChange;

            await base.OnInitializedAsync();
        }

        public async Task ShowVideoModal()
        {
            await VideoModal.ShowModal();
        }

        private void SurveyState_OnChange()
        {
            GetDefaults();
        }

        private void GetDefaults()
        {
            QuestionText = Question.QuestionText;
            QuestionHtml = Question.QuestionHtml;
            IsTooltipVideo = Question.IsTooltipVideo;
            List<FlexAnswer> matches = [];
            List<FlexAnswer> answers = SurveyState.FindByQuestion(Question.DataName, SurveyState.FullAnswerList[FlexResponseGuid], matches);
            FlexLibrary.FlexAnswer existingAnswer = SurveyState.FullAnswerList[FlexResponseGuid].SingleOrDefault(a => a.QuestionDataName == Question.DataName && a.RowNumber == RowNumber);
            if (answers.Any())
            {
                if (Question.ParentQuestionDataName != "" && Question.ParentQuestionDataName != null)
                {
                    matches = [];
                    FlexAnswer answer = SurveyState.FindByQuestion(Question.ParentQuestionDataName, SurveyState.FullAnswerList[FlexResponseGuid], ParentRowNumber);
                    if (answer != null)
                    {
                        string? childAnswer = answer.ChildAnswers.SingleOrDefault(s => s.RowNumber == RowNumber && s.QuestionDataName == Question.DataName)?.Answer;
                        AnswerDecimalValue = decimal.Parse(string.IsNullOrEmpty(childAnswer) ? "0" : childAnswer);
                    }

                }
                else
                {
                    string? value = answers.DistinctBy(r => r.RowNumber).Where(r => r.RowNumber == RowNumber).SingleOrDefault()?.Answer;
                    AnswerDecimalValue = decimal.Parse(string.IsNullOrEmpty(value) ? "0" : value);
                }


            }
            else
            {
                if (existingAnswer != null)
                {
                    AnswerDecimalValue = decimal.Parse(existingAnswer.Answer);
                }
            }

            base.DetermineWidth();
            base.DetermineVisibility();
        }

        public void OnNumericChanged(decimal newValue)
        {
            AnswerDecimalValue = newValue;
            base.SaveNewAnswer(AnswerDecimalValue.ToString());

        }
    }
}
