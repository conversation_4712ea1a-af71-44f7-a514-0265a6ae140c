@using ServerComponentLibrary.Components.Inputs
@using Microsoft.AspNetCore.Components.Forms
@using ServerComponentLibrary.Components.Inputs.Models

<div class="container mt-4">
    <h2>🎨 Enhanced Flex Form Components Example</h2>
    <p class="text-muted">This demonstrates all the reusable flex input components with <strong>QuestionHtml
            support</strong>, rich HTML labels, icons, and modern validation features.</p>

    <form>
        <div class="row">
            <div class="col-md-6">
                <h4>🎨 Rich HTML Labels (QuestionHtml)</h4>
                <p class="text-muted small">Components now support rich HTML content in labels with icons, badges, and
                    formatting.</p>

                <FlexTextInput
                    QuestionHtml="<i class='bi bi-person me-2'></i><strong>Full Name</strong> <small class='text-muted'>(First and Last)</small>"
                    @bind-Value="FullName"
                    IsRequired="true"
                    MinLength="2"
                    MaxLength="50"
                    Placeholder="Enter your full name"
                    HelpText="Please enter your first and last name"/>

                <FlexEmailInput
                    QuestionHtml="<i class='bi bi-envelope me-2'></i>Email Address <span class='badge bg-primary'>Required</span>"
                    @bind-Value="Email"
                    IsRequired="true"
                    Placeholder="<EMAIL>"
                    HelpText="We'll use this to contact you"/>

                <FlexPhoneInput
                    QuestionHtml="<i class='bi bi-telephone me-2'></i>Phone Number <small class='text-muted'>(Auto-formatted)</small>"
                    @bind-Value="Phone"
                    IsRequired="true"
                    AutoFormat="true"
                    HelpText="10-digit US phone number"/>

                <FlexNumberInput
                    QuestionHtml="<i class='bi bi-currency-dollar me-2'></i>Annua.l Income <small class='text-muted'>(USD)</small>"
                    @bind-Value="Income"
                    @bind-NumericValue="IncomeValue"
                    MinValue="0"
                    MaxValue="999999"
                    DecimalPlaces="0"
                    AllowNegative="false"
                    Placeholder="Enter annual income"
                    HelpText="Your gross annual income in USD"/>

                <FlexDateInput
                    Label="Date of Birth"
                    @bind-Value="DateOfBirth"
                    @bind-DateValue="DateOfBirthValue"
                    IsRequired="true"
                    AllowFutureDates="false"
                    MaxDate="@DateTime.Today.AddYears(-18)"
                    ToolTip="Must be at least 18 years old"/>

                <FlexPasswordInput
                    Label="Password"
                    @bind-Value="Password"
                    IsRequired="true"
                    MinLength="8"
                    RequireUppercase="true"
                    RequireLowercase="true"
                    RequireDigit="true"
                    RequireSpecialChar="true"
                    ShowToggleButton="true"
                    ShowStrengthIndicator="true"
                    ToolTip="Must be at least 8 characters with mixed case, numbers, and symbols"/>

                <FlexUrlInput
                    Label="Website"
                    @bind-Value="Website"
                    AutoAddProtocol="true"
                    DefaultProtocol="https://"
                    ToolTip="Your personal or company website"/>

                <FlexZipCodeInput
                    Label="ZIP Code"
                    @bind-Value="ZipCode"
                    @bind-ZipCodeInfo="ZipCodeInfo"
                    IsRequired="true"
                    AutoPopulateCityState="true"
                    ShowCityStateLabels="true"
                    ToolTip="Enter your ZIP code to auto-populate city and state"/>
            </div>

            <div class="col-md-6">
                <h4>Selection Inputs</h4>

                <FlexSelectInput
                    Label="Preferred Contact Method"
                    @bind-Value="ContactMethod"
                    IsRequired="true"
                    Options="ContactMethodOptions"
                    DefaultOptionText="Choose contact method..."
                    ToolTip="How would you like us to reach you?"/>

                <FlexMultiSelectInput
                    Label="Interests"
                    @bind-SelectedValues="Interests"
                    Options="InterestOptions"
                    MinSelections="1"
                    MaxSelections="5"
                    ShowSelectedCount="true"
                    ToolTip="Select up to 5 areas of interest"/>

                <FlexTimeInput
                    Label="Preferred Contact Time"
                    @bind-Value="PreferredTime"
                    @bind-TimeValue="PreferredTimeValue"
                    Use24HourFormat="false"
                    StepMinutes="30"
                    ToolTip="Best time to reach you"/>

                <FlexRadioGroupInput
                    Label="Employment Status"
                    @bind-Value="EmploymentStatus"
                    IsRequired="true"
                    Options="EmploymentOptions"
                    Layout="RadioGroupLayout.Vertical"
                    ToolTip="Select your current employment status"/>

                <FlexCheckboxInput
                    CheckboxText="I agree to the terms and conditions"
                    @bind-IsChecked="AgreeToTerms"
                    RequireChecked="true"
                    ToolTip="You must agree to proceed"/>

                <FlexCheckboxInput
                    CheckboxText="Subscribe to newsletter"
                    @bind-IsChecked="SubscribeNewsletter"
                    ToolTip="Optional: Receive updates and news"/>

                <FlexYesNoRadioInput
                    Label="Are you a US citizen?"
                    @bind-BooleanValue="IsUSCitizen"
                    IsRequired="true"
                    YesText="Yes"
                    NoText="No"
                    Layout="RadioGroupLayout.Horizontal"
                    ToolTip="Required for eligibility verification"/>

                <FlexYesNoRadioInput
                    Label="Do you have a driver's license?"
                    @bind-BooleanValue="HasDriversLicense"
                    YesText="Yes, I do"
                    NoText="No, I don't"
                    Layout="RadioGroupLayout.Vertical"
                    ToolTip="Optional: For identification purposes"/>

                <h4>Text Area</h4>

                <FlexTextAreaInput
                    Label="Additional Comments"
                    @bind-Value="Comments"
                    MinLength="10"
                    MaxLength="500"
                    Rows="4"
                    ShowCharacterCount="true"
                    Placeholder="Tell us more about yourself..."
                    ToolTip="Optional: Any additional information you'd like to share"/>

                <FlexFileUploadInput
                    Label="Resume/CV"
                    @bind-SelectedFiles="UploadedFiles"
                    AcceptedFileTypes=".pdf,.doc,.docx"
                    MaxFileSize="5242880"
                    MaxFiles="1"
                    ShowFileList="true"
                    ToolTip="Upload your resume (PDF or Word document, max 5MB)"/>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h4>Form Summary</h4>
                <div class="alert alert-info">
                    <strong>Form Validation Status:</strong>
                    @if (IsFormValid())
                    {
                        <span class="text-success">✓ All required fields are valid</span>
                    }
                    else
                    {
                        <span class="text-danger">✗ Please complete all required fields</span>
                    }
                </div>

                <BSButton Color="BSColor.Primary" IsDisabled="@(!IsFormValid())" OnClick="SubmitForm">
                    Submit Form
                </BSButton>

                <BSButton Color="BSColor.Secondary" OnClick="ClearForm" Class="ms-2">
                    Clear Form
                </BSButton>
            </div>
        </div>
    </form>
</div>

@code {

    // Form data properties
    private string FullName = "";
    private string Email = "";
    private string Phone = "";
    private string Income = "";
    private decimal? IncomeValue;
    private string DateOfBirth = "";
    private DateTime? DateOfBirthValue;
    private string Password = "";
    private string Website = "";
    private string ZipCode = "";
    private ZipCodeInfo ZipCodeInfo = new();
    private string ContactMethod = "";
    private List<string> Interests = new();
    private string PreferredTime = "";
    private TimeSpan? PreferredTimeValue;
    private string EmploymentStatus = "";
    private bool AgreeToTerms = false;
    private bool SubscribeNewsletter = false;
    private bool? IsUSCitizen = null;
    private bool? HasDriversLicense = null;
    private string Comments = "";
    private IBrowserFile[] UploadedFiles = Array.Empty<IBrowserFile>();

    // Options for select and radio inputs
    private List<SelectOption> ContactMethodOptions = new()
    {
        new("email", "Email"),
        new("phone", "Phone"),
        new("mail", "Mail"),
        new("text", "Text Message")
    };

    private List<RadioOption> EmploymentOptions = new()
    {
        new("employed", "Employed Full-time"),
        new("parttime", "Employed Part-time"),
        new("selfemployed", "Self-employed"),
        new("unemployed", "Unemployed"),
        new("retired", "Retired"),
        new("student", "Student")
    };

    private List<SelectOption> InterestOptions = new()
    {
        new("technology", "Technology"),
        new("sports", "Sports"),
        new("music", "Music"),
        new("travel", "Travel"),
        new("cooking", "Cooking"),
        new("reading", "Reading"),
        new("gaming", "Gaming"),
        new("fitness", "Fitness"),
        new("art", "Art"),
        new("photography", "Photography")
    };

    private bool IsFormValid()
    {
        return !string.IsNullOrWhiteSpace(FullName) &&
               !string.IsNullOrWhiteSpace(Email) &&
               !string.IsNullOrWhiteSpace(Phone) &&
               DateOfBirthValue.HasValue &&
               !string.IsNullOrWhiteSpace(Password) &&
               !string.IsNullOrWhiteSpace(ZipCode) &&
               !string.IsNullOrWhiteSpace(ContactMethod) &&
               Interests.Any() &&
               !string.IsNullOrWhiteSpace(EmploymentStatus) &&
               AgreeToTerms &&
               IsUSCitizen.HasValue;
    }

    private void SubmitForm()
    {
        if (IsFormValid())
        {
            // Handle form submission
            Console.WriteLine("Form submitted successfully!");
        }
    }

    private void ClearForm()
    {
        FullName = "";
        Email = "";
        Phone = "";
        Income = "";
        IncomeValue = null;
        DateOfBirth = "";
        DateOfBirthValue = null;
        Password = "";
        Website = "";
        ZipCode = "";
        ZipCodeInfo = new();
        ContactMethod = "";
        Interests.Clear();
        PreferredTime = "";
        PreferredTimeValue = null;
        EmploymentStatus = "";
        AgreeToTerms = false;
        SubscribeNewsletter = false;
        IsUSCitizen = null;
        HasDriversLicense = null;
        Comments = "";
        UploadedFiles = Array.Empty<IBrowserFile>();
    }

}
