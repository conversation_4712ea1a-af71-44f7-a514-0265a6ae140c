@inherits FlexCheckboxInputBase

@if (IsVisible)
{
    <div class="@ContainerCssClass">
        <div class="form-check">
            <input type="checkbox"
                   id="@GetInputId()"
                   checked="@IsChecked"
                   @onchange="OnCheckboxChanged"
                   disabled="@IsDisabled"
                   class="@($"form-check-input {InputCssClass} {(HasValidationErrors ? "is-invalid" : "")}")"
                   title="@ToolTip"/>

            <label id="@FieldName" for="@GetInputId()" class="form-check-label @LabelCssClass">
                @if (!string.IsNullOrWhiteSpace(CheckboxText))
                {
                    @CheckboxText
                }
                else if (!string.IsNullOrEmpty(QuestionHtml))
                {
                    @((MarkupString)QuestionHtml)
                }
                else if (!string.IsNullOrWhiteSpace(Label))
                {
                    @Label
                }
                @if (IsRequired)
                {
                    <span class="text-danger">*</span>
                }
            </label>
        </div>

        @if (HasValidationErrors)
        {
            <div class="invalid-feedback">
                @foreach (var error in ValidationErrors)
                {
                    <div>@error</div>
                }
            </div>
        }

        @if (!string.IsNullOrEmpty(HelpText))
        {
            <div class="form-text fs-10">
                <i class="bi bi-info-circle-fill"></i>
                @HelpText
            </div>
        }
    </div>
}
