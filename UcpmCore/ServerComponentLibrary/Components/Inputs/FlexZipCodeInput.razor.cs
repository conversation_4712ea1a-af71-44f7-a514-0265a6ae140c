using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using ServerComponentLibrary.Components.Inputs.Models;
using ServerComponentLibrary.Managers;

namespace ServerComponentLibrary.Components.Inputs;

public class FlexZipCodeInputBase : FlexInputBase
{
    [Parameter]
    public bool AutoPopulateCityState { get; set; } = true;

    [Parameter]
    public bool ShowCityStateLabels { get; set; } = true;

    [Parameter]
    public string ApiKey { get; set; } = string.Empty; // For external APIs if needed

    [Parameter]
    public EventCallback<ZipCodeInfo> ZipCodeInfoChanged { get; set; }

    [Parameter]
    public string ContainerCssClass { get; set; } = "mb-3";

    [Parameter]
    public string LabelCssClass { get; set; } = string.Empty;

    [Parameter]
    public string HelpText { get; set; } = string.Empty;

    [Inject]
    public ZipCodeManager ZipManager { get; set; } = null!;

    protected bool HasValidationErrors => _validationErrors.Any();
    protected List<string> ValidationErrors => _validationErrors;

    protected ZipCodeInfo _zipCodeInfo = new();
    protected bool IsLookingUp {get; set;}

    [Parameter]
    public EventCallback<Tuple<string,bool>> LoadingStateChanged { get; set; }

    [Parameter]
    public bool ShowLoadingIndicator { get; set; } = true;

    // Static cache for ZIP code data - shared across all instances
    private static Dictionary<string, ZipCodeInfo>? _zipCodeCache = null;
    private static DateTime _cacheLastUpdated = DateTime.MinValue;
    private static readonly TimeSpan CacheExpiry = TimeSpan.FromHours(24); // Cache for 24 hours
    private static readonly object _cacheLock = new object();

    // Override ValidateInput to handle ZIP code specific validation
    protected override void ValidateInput()
    {
        _validationErrors.Clear();

        // Check if required and empty
        if (IsRequired && string.IsNullOrWhiteSpace(Value))
        {
            _validationErrors.Add($"{Label} is required.");
            IsValid = false;
            return;
        }

        // Check ZIP code format if not empty
        if (!string.IsNullOrWhiteSpace(Value) && !IsValidZipCode(Value))
        {
            _validationErrors.Add("Please enter a valid ZIP code format (e.g., 12345 or 12345-6789).");
            IsValid = false;
            return;
        }

        IsValid = true;
    }

    /*public override async Task SetParametersAsync(ParameterView parameters)
    {
        // Get the new value before calling base method
        var newValue = parameters.GetValueOrDefault<string>("Value");
        var oldValue = Value;

        // Call base method to set all parameters
        await base.SetParametersAsync(parameters);

        // If Value changed and we have enhanced validation, clear errors and validate
        if (newValue != oldValue && !string.IsNullOrEmpty(newValue))
        {
            // Clear any existing validation errors
            _validationErrors.Clear();

            // Update FormData if available
            if (FormData != null && !string.IsNullOrWhiteSpace(FieldName))
            {
                FormData[FieldName] = newValue;
            }
        }
    }*/


    [Parameter]
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Usage", "BL0007:Component parameter should be auto property",
        Justification = "Custom setter needed for ZIP code lookup and two-way binding")]
    public ZipCodeInfo ZipCodeInfo
    {
        get => _zipCodeInfo;
        set
        {
            if (_zipCodeInfo != value)
            {
                _zipCodeInfo = value;
                _value = _zipCodeInfo.ZipCode;
                ValidateInput();
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        if (string.IsNullOrEmpty(Placeholder))
        {
            Placeholder = "12345 or 12345-6789";
        }
        await EnsureCacheLoaded();
        await base.OnInitializedAsync();
    }

    protected override void PerformCustomValidation()
    {
        base.PerformCustomValidation();

        if (string.IsNullOrWhiteSpace(_value))
        {
            _zipCodeInfo = new ZipCodeInfo();
            return;
        }

        // ZIP code format validation
        if (!IsValidZipCode(_value))
        {
            _validationErrors.Add($"{Label} must be a valid ZIP code (12345 or 12345-6789).");
        }
    }

    private async Task OnZipCodeChanged(string? zipCode)
    {
        // Update the value using the inherited property
        Value = zipCode ?? string.Empty;

        if (AutoPopulateCityState && IsValidZipCode(zipCode))
        {
            await LookupZipCodeInfoCached(zipCode);
        }
        else
        {
            _zipCodeInfo = new ZipCodeInfo { ZipCode = zipCode ?? string.Empty };
        }

        // Update FormData if available
        if (FormData != null && !string.IsNullOrWhiteSpace(FieldName))
        {
            FormData[FieldName] = Value;
            FormData[FieldName + "Selected"] = _zipCodeInfo;
        }

        // Call the callbacks for FlexDynamicForm integration and specialized binding
        //await ValueChanged.InvokeAsync(Value);
        await ZipCodeInfoChanged.InvokeAsync(_zipCodeInfo);

        // Validate the input
        ValidateInput();
    }

    protected async Task OnZipCodeChangedAfter(ChangeEventArgs e)
    {
        var inputValue = e.Value?.ToString() ?? string.Empty;
        Value = inputValue;
        await OnZipCodeChanged(Value);
    }
    
    protected async Task OnBlurValidation(FocusEventArgs e)
    {
        // Validate when user leaves the input field (on blur/focus out)
        // This provides immediate feedback for ZIP code format validation
        await ValidateWithEnhancedRulesAsync();
        //StateHasChanged(); // Ensure UI updates to show validation state
    }

    private async Task LookupZipCodeInfoCached(string? zipCode)
    {
        if (string.IsNullOrWhiteSpace(zipCode) || !IsValidZipCode(zipCode))
            return;

        // Extract 5-digit ZIP for lookup
        var zip5 = zipCode.Length > 5 ? zipCode.Substring(0, 5) : zipCode;
            
        // Show loading state
        IsLookingUp = true;
        //StateHasChanged();
        // Check cache first
        var cachedInfo = GetFromCache(zip5);
        if (cachedInfo != null)
        {
            _zipCodeInfo = new ZipCodeInfo
            {
                ZipCode = zipCode, // Keep the full ZIP code as entered
                City = cachedInfo.City,
                State = cachedInfo.State
            };
            IsLookingUp = false;
            return;
        }

        try
        {
            // Load all ZIP codes if cache is empty or expired
            await EnsureCacheLoaded();

            // Try cache again after loading
            cachedInfo = GetFromCache(zip5);
            if (cachedInfo != null)
            {
                _zipCodeInfo = new ZipCodeInfo
                {
                    ZipCode = zipCode,
                    City = cachedInfo.City,
                    State = cachedInfo.State
                };
            }
            else
            {
                _zipCodeInfo = new ZipCodeInfo
                {
                    ZipCode = zipCode,
                    City = "Unknown",
                    State = "Unknown"
                };
            }
        }
        catch (Exception)
        {
            _zipCodeInfo = new ZipCodeInfo
            {
                ZipCode = zipCode,
                City = "Lookup Failed",
                State = "Unknown"
            };
        }
        finally
        {
            IsLookingUp = false;
            // StateHasChanged();
        }
    }

    private ZipCodeInfo? GetFromCache(string zipCode)
    {
        lock (_cacheLock)
        {
            return _zipCodeCache?.GetValueOrDefault(zipCode);
        }
    }

    private async Task EnsureCacheLoaded()
    {
        lock (_cacheLock)
        {
            // Check if cache is valid
            if (_zipCodeCache != null && DateTime.Now - _cacheLastUpdated < CacheExpiry)
            {
                return;
            }
        }

        // Load cache outside of lock to avoid blocking
        await LoadingStateChanged.InvokeAsync(new Tuple<string, bool>(FieldName, true));
        var zipCodes = await ZipManager.GetAllZipCodes();
        var newCache = new Dictionary<string, ZipCodeInfo>();

        foreach (var zipCode in zipCodes)
        {
            newCache[zipCode.Zip] = new ZipCodeInfo
            {
                ZipCode = zipCode.Zip,
                City = zipCode.City,
                State = zipCode.State,
                County = string.Empty, // Not available in ZipCodeModel
                Latitude = 0, // Not available in ZipCodeModel
                Longitude = 0 // Not available in ZipCodeModel
            };
        }

        // Update cache atomically
        lock (_cacheLock)
        {
            _zipCodeCache = newCache;
            _cacheLastUpdated = DateTime.Now;
        }
        await LoadingStateChanged.InvokeAsync(new Tuple<string, bool>(FieldName, false));
    }

    protected string GetCityStateDisplay()
    {
        if (string.IsNullOrWhiteSpace(_zipCodeInfo.City) || string.IsNullOrWhiteSpace(_zipCodeInfo.State))
            return string.Empty;

        if (_zipCodeInfo.City == "Unknown" || _zipCodeInfo.State == "Unknown")
            return "ZIP code not found";

        if (_zipCodeInfo.City == "Lookup Failed")
            return "Unable to lookup ZIP code";

        return
            $"<abbr class='text-muted' title='City: {_zipCodeInfo.City} | State: {_zipCodeInfo.State}'> {_zipCodeInfo.City} </abbr>, <span class='text-brown' title='State: {_zipCodeInfo.State}'> {_zipCodeInfo.State} </span>";
    }

    protected string GetLookupStatusIcon()
    {
        if (IsLookingUp)
            return "bi-arrow-clockwise spin";

        if (!string.IsNullOrWhiteSpace(_zipCodeInfo.City) && _zipCodeInfo.City != "Unknown" &&
            _zipCodeInfo.City != "Lookup Failed")
            return "bi-check-circle text-success";

        return "bi-search";
    }
}