@inherits FlexMultiSelectInputBase

@if (IsVisible)
{
    <div class="@ContainerCssClass">
        @if (!string.IsNullOrEmpty(QuestionHtml) || !string.IsNullOrEmpty(Label))
        {
            <label id="@FieldName" class="form-label @LabelCssClass">
                @if (!string.IsNullOrEmpty(QuestionHtml))
                {
                    @((MarkupString)QuestionHtml)
                }
                else
                {
                    @Label
                }
                @if (IsRequired)
                {
                    <span class="text-danger">*</span>
                }
            </label>
        }

        <div class="dropdown">
            <button
                class="btn btn-outline-secondary dropdown-toggle w-100 text-start @InputCssClass @(HasValidationErrors ? "is-invalid" : "")"
                type="button"
                data-bs-toggle="dropdown"
                disabled="@IsDisabled"
                title="@ToolTip">
                <div class="d-flex justify-content-between align-items-center">
                <span class="@(SelectedValues.Any() ? "" : "text-muted")">
                    @GetSelectedText()
                </span>
                    @if (ShowSelectedCount)
                    {
                        <small class="text-muted ms-2">@GetSelectedCountText()</small>
                    }
                </div>
            </button>

            <ul class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto;">
                @if (SelectedValues.Any())
                {
                    <li>
                        <button class="dropdown-item text-danger" type="button" @onclick="ClearAllSelections">
                            <i class="fa fa-times me-2"></i>Clear All
                        </button>
                    </li>
                    <li>
                        <hr class="dropdown-divider">
                    </li>
                }

                @foreach (var option in Options)
                {
                    <li>
                        <div class="dropdown-item-text">
                            <div class="form-check">
                                <input type="checkbox"
                                       id="@($"multi_{GetInputId()}_{option.Value}")"
                                       class="form-check-input"
                                       checked="@IsOptionSelected(option.Value)"
                                       disabled="@IsOptionDisabled(option)"
                                       @onchange="@(async (e) => await OnOptionChanged(option.Value, (bool)(e.Value ?? false)))"/>

                                <label class="form-check-label" for="@($"multi_{GetInputId()}_{option.Value}")">
                                    @option.Text
                                </label>
                            </div>
                        </div>
                    </li>
                }

                @if (!Options.Any())
                {
                    <li>
                        <span class="dropdown-item-text text-muted">No options available</span>
                    </li>
                }
            </ul>
        </div>

        @if (HasValidationErrors)
        {
            <div class="invalid-feedback">
                @foreach (var error in ValidationErrors)
                {
                    <div>@error</div>
                }
            </div>
        }

        @if (!string.IsNullOrEmpty(HelpText))
        {
            <div class="form-text fs-10">
                <i class="bi bi-info-circle-fill"></i>
                @HelpText
            </div>
        }
    </div>
}
