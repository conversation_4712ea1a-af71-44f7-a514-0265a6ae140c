using Duende.AccessTokenManagement.OpenIdConnect;
using Microsoft.AspNetCore.Components.Authorization;
using ServerComponentLibrary.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UcpmApi.Shared;
using UcpmTools.StringUtilities;

namespace ServerComponentLibrary.Managers
{
    public class CountryManager : ManagerBase
    {
        public CountryManager(IHttpClientFactory httpClientFactory, IUserTokenStore userTokenStore, AuthenticationStateProvider authenticationStateProvider)
            : base(httpClientFactory, userTokenStore, authenticationStateProvider)
        {

        }

        public async Task<Country> GetCountryByCode(string code)
        {
            QueryStringBuilder query = new($"{Version}Country/GetCountryByCode");
            query.Add(nameof(code), code);

            return await SendHttpRequest<Country>(query.ToString(), HttpMethod.Get);
        }

        public async Task<List<Country>> GetAllCountries()
        {
            QueryStringBuilder query = new($"{Version}Country/GetAllCountries");

            return await SendHttpRequest<List<Country>>(query.ToString(), HttpMethod.Get);
        }
    }
}
