using Duende.AccessTokenManagement.OpenIdConnect;
using Microsoft.AspNetCore.Components.Authorization;
using ServerComponentLibrary.Models;
using UcpmApi.Shared.Lookups;
using UcpmTools.StringUtilities;

namespace ServerComponentLibrary.Managers
{
    public class EnvironmentManager : ManagerBase
    {
        public EnvironmentManager(IHttpClientFactory httpClientFactory, IUserTokenStore userTokenStore, AuthenticationStateProvider authenticationStateProvider) : base(httpClientFactory, userTokenStore, authenticationStateProvider)
        {
        }

        public async Task<EnvironmentEnumModel> GetEnvironment()
        {
            QueryStringBuilder query = new($"{Version}Environment/Get");

            EnvironmentEnumModel result = await SendHttpRequest<EnvironmentEnumModel>(query.ToString(), HttpMethod.Get);
            return result;
        }
    }
}
