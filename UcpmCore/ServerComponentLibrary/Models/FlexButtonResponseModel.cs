using FlexLibrary.Enums;

namespace ServerComponentLibrary.Models
{
    public class FlexButtonResponseModel
    {
        public string PostSubmitHtml { get; set; } = string.Empty;

        /// <summary>
        /// This property determines which flex page response button component to be created.
        /// </summary>
        public FlexButtonEnum FlexPageButtonType { get; set; }

        /// <summary>
        /// This property contains the parameters that will be passed to the flex page response button component.
        /// </summary>
        public Dictionary<string, object> ButtonParameters { get; set; } = [];
    }
}
