using EnvRiskMgmt.ApiServices;
using EnvRiskMgmt.ApiServices.Interfaces;
using UcpmApi.Shared;
using UcpmApi.Shared.Kato;

namespace EnvRiskMgmt.IntegrationTest.MockServices;

public class AgentProfileServiceMock : BaseService, IAgentProfileService
{
    public Task<AgentProfileViewModel> GetAgentProfile(Guid inviteLinkGuid)
    {
        // Return mock data
        return Task.FromResult(new AgentProfileViewModel
        {
            AgentGuid = Guid.NewGuid(),
            AgentName = "Mock Agent",
            CompanyName = "Mock Company",
            AgentTitle = "Mock Title",
            CompanyGuid = Guid.NewGuid(),
            AgencyLogoUrl = "https://via.placeholder.com/150",
            InsuredName = "Mock Insured",
            AgentImgUrl = "https://via.placeholder.com/150",
        });
    }

    public Task<InviteLinkViewModel> GetInviteLink(Guid ilg)
    {
        // Return mock data
        return Task.FromResult(new InviteLinkViewModel
        {
            InviteLinkGuid = ilg,
            PackageGuid = Guid.NewGuid(),
            PassCode = "90909"
        });
    }

    public Task<Agent> GetAgentByGuid(Guid agentGuid)
    {
        throw new NotImplementedException();
    }

    public AgentProfileServiceMock(IHttpClientFactory client) : base(client)
    {
        
    }
}