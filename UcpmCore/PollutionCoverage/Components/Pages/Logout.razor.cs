using Duende.IdentityModel.Client;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Components;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using PollutionCoverage.Shared;

namespace PollutionCoverage.Components.Pages
{
    public class LogoutBase : PollutionCoverageComponentBase
    {
        private const string _clientSecret = "AC070EBE-1928-41E0-B560-3B6B778CB405";
        private const string _clientId = "rpspollutioncoverage";

        [CascadingParameter]
        private HttpContext HttpContext { get; set; } = default!;

        protected override async Task OnInitializedAsync()
        {
            if (HttpMethods.IsGet(HttpContext.Request.Method) && await IsUserAuthenticated())
            {
                HttpClient client = HttpClientFactory.CreateClient("IDPClient");

                DiscoveryDocumentResponse discoveryDocumentResponse = await client
                    .GetDiscoveryDocumentAsync();
                if (discoveryDocumentResponse.IsError)
                {
                    throw new Exception(discoveryDocumentResponse.Error);
                }

                TokenRevocationResponse accessTokenRevocationResponse = await client
                    .RevokeTokenAsync(new()
                    {
                        Address = discoveryDocumentResponse.RevocationEndpoint,
                        ClientId = _clientId,
                        ClientSecret = _clientSecret,
                        Token = await HttpContext.GetTokenAsync(OpenIdConnectParameterNames.AccessToken)
                    });

                if (accessTokenRevocationResponse.IsError)
                {
                    throw new Exception(accessTokenRevocationResponse.Error);
                }

                TokenRevocationResponse refreshTokenRevocationResponse = await client
                    .RevokeTokenAsync(new()
                    {
                        Address = discoveryDocumentResponse.RevocationEndpoint,
                        ClientId = _clientId,
                        ClientSecret = _clientSecret,
                        Token = await HttpContext.GetTokenAsync(OpenIdConnectParameterNames.RefreshToken)
                    });

                if (refreshTokenRevocationResponse.IsError)
                {
                    throw new Exception(refreshTokenRevocationResponse.Error);
                }

                // Clears the  local cookie
                await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);

                string idToken = await HttpContext.GetTokenAsync(OpenIdConnectParameterNames.IdToken);

                // Redirect to the IDP's end session endpoint
                string endSessionUrl = discoveryDocumentResponse.EndSessionEndpoint;
                string redirectUrl = $"{NavManager.BaseUri}signout-callback-oidc";
                Console.WriteLine($"UCPM: RedirectUrl {redirectUrl}");
                NavManager.NavigateTo($"{endSessionUrl}?id_token_hint={idToken}&post_logout_redirect_uri={Uri.EscapeDataString(redirectUrl)}");
            }
        }

        protected override async Task OnParametersSetAsync()
        {
            if (!await IsUserAuthenticated())
            {
                NavManager.NavigateTo("/");
            }

            await base.OnParametersSetAsync();
        }
    }
}
