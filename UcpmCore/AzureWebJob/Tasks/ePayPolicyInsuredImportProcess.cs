using UcpmApi.Shared;
using UcpmApi.Shared.Api.Mail;
using UcpmApi.Shared.Api.Payment;
using UcpmApi.Shared.Crm;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Mail;

namespace AzureWebJob.Tasks
{
    public class ePayPolicyInsuredImportProcess
    {
        private DateTime _startingDate;
        private readonly ePayPolicyApi _ePayPolicyApi;
        private readonly DirectSendLogApi _directSendLogApi;
        private readonly DirectSendApi _directSendApi;
        private readonly AgentEnvNotificationTypeApi _agentEnvNotificationTypeApi;
        private readonly DirectSendTemplateApi _directSendTemplateApi;
        private readonly DirectSendTemplateVersionApi _directSendTemplateVersionApi;
        private readonly AgentApi _agentApi;
        private readonly InsuredApi _insuredApi;
        private readonly IErrorComponent _errorComponent;
        private readonly string _authBearerToken;


        public ePayPolicyInsuredImportProcess(DateTime startingDate,
            ePayPolicyApi ePayPolicyApi,
            DirectSendLogApi directSendLogApi,
            DirectSendApi directSendApi,
            AgentEnvNotificationTypeApi agentEnvNotificationTypeApi,
            DirectSendTemplateApi directSendTemplateApi,
            DirectSendTemplateVersionApi directSendTemplateVersionApi,
            IErrorComponent errorComponent,
            string authBearerToken)
        {
            _startingDate = startingDate;
            _ePayPolicyApi = ePayPolicyApi;
            _directSendLogApi = directSendLogApi;
            _directSendApi = directSendApi;
            _agentEnvNotificationTypeApi = agentEnvNotificationTypeApi;
            _directSendTemplateApi = directSendTemplateApi;
            _directSendTemplateVersionApi = directSendTemplateVersionApi;
            _errorComponent = errorComponent;
            _authBearerToken = authBearerToken;
        }

        public async Task<string> Run()
        {
            try
            {
                ProcessEPayPolicyImportResponse processResponse = await _ePayPolicyApi.ProcessInsuredEPayPolicyImport(new ProcessEPayPolicyImportRequest { StartingDate = _startingDate }, _errorComponent, _authBearerToken);
                if (processResponse != null && processResponse.FullyPaidInvoices.Count > 0)
                {
                    DirectSendLog result = null;

                    foreach ((Guid AgentGuid, Guid InsuredGuid, Guid InvoiceGuid, string DocLink) in processResponse.FullyPaidInvoices)
                    {
                        if (!string.IsNullOrEmpty(DocLink))
                        {
                            result = await SendPremiumPaymentProcessedNotification(AgentGuid, InsuredGuid, InvoiceGuid, DocLink);
                        }
                    }
                }

                return processResponse.Message;
            }
            catch (Exception e)
            {
                return $" {e.Message}";
            }
        }

        private async Task<bool> CheckIfNotificationSentBySearchCriteria(Guid agentGuid, int directSendTemplateId, Guid regardingGuid)
        {
            bool sent = await _directSendLogApi.CheckIfNotificationSentBySearchCriteria(agentGuid, directSendTemplateId, regardingGuid, _errorComponent, _authBearerToken);
            return sent;
        }

        private async Task<DirectSendLog> SendPremiumPaymentProcessedNotification(Guid agentGuid, Guid insuredGuid, Guid invoiceGuid, string invoiceDocLink)
        {
            DirectSendLog result = null;

            AgentEnvNotificationType agentEnvNotificationType = await _agentEnvNotificationTypeApi.GetAgentEnvNotificationTypeById((int)AgentEnvNotificationTypeEnum.PremiumPaymentProcessed,
                _errorComponent,
                _authBearerToken);
            bool sent = await CheckIfNotificationSentBySearchCriteria(agentGuid, agentEnvNotificationType.DirectSendTemplateId.Value, invoiceGuid);
            if (!sent && (agentEnvNotificationType.DirectSendTemplateId != null || agentEnvNotificationType.DirectSendTemplateId != 0))
            {
                DirectSendTemplate directSendTemplate = await _directSendTemplateApi.GetDirectSendTemplateByIdAsync(agentEnvNotificationType.DirectSendTemplateId.Value,
                    _errorComponent,
                    _authBearerToken);
                if (directSendTemplate != null && directSendTemplate.DirectSendTemplateId != 0)
                {
                    DirectSendTemplateVersion directSendTemplateVersion = await _directSendTemplateVersionApi.GetByTemplate(directSendTemplate.DirectSendTemplateId, Guid.Empty,
                        _errorComponent,
                        _authBearerToken);
                    Agent agent = await _agentApi.GetAgent(agentGuid, _errorComponent, _authBearerToken);
                    Insured insured = await _insuredApi.GetInsured(insuredGuid, _errorComponent, _authBearerToken);
                    Dictionary<string, string> extraData = BuildExtraData(agent, insured, directSendTemplateVersion);
                    extraData.Add("InvoiceLink", invoiceDocLink);

                    DirectSend directSend = new()
                    {
                        Attachments = [],
                        ExtraData = extraData,
                        NodeGuid = insuredGuid,
                        NodeType = NodeTypeEnum.Insured,
                        TemplateId = directSendTemplateVersion.DirectSendTemplateId,

                        Targets =
                        [
                            new DirectSendTarget()
                            {
                                TargetEmail = agent.AgentEmail,
                                TargetMode = RecipientTypeEnum.Normal,
                                NodeTypeId = (int)NodeTypeEnum.Agent,
                                UserGuid = agentGuid
                            }
                        ]
                    };

                    result = await _directSendApi.SendEmail(directSend, _errorComponent, _authBearerToken);
                }
            }

            return result;
        }

        private Dictionary<string, string> BuildExtraData(Agent agent, Insured insured, DirectSendTemplateVersion directSendTemplateVersion)
        {
            Dictionary<string, string> extraData = new()
            {
                { "AgentName", agent.AgentName },
                { "Content", directSendTemplateVersion.HtmlBodyTemplate },
                { "Subject", directSendTemplateVersion.SubjectLine },
                { "InsuredName", insured.Name }
            };

            return extraData;
        }
    }
}