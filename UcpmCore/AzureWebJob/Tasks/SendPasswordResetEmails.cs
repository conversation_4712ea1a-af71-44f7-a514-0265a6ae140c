using PasswordHash;
using UcpmApi.Shared;
using UcpmApi.Shared.Api.Mail;
using UcpmApi.Shared.Api.Security;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Mail;
using UcpmApi.Shared.Security;

namespace AzureWebJob.Tasks
{
    public class SendPasswordResetEmails
    {
        private readonly PasswordResetRequestLogApi _passwordResetRequestLogApi;
        private readonly DirectSendTemplateVersionApi _directSendTemplateVersionApi;
        private readonly DirectSendLogApi _directSendLogApi;
        private readonly DirectSendApi _directSendApi;
        private readonly SiteUrlApi _siteUrlApi;
        private readonly InviteLinkApi _inviteLinkApi;
        private readonly AgentApi _agentApi;
        private readonly EvaluatorUserApi _evaluatorUserApi;
        private readonly AccountApi _accountApi;
        private readonly IErrorComponent _errorComponent;
        private readonly string _authToken;

        public SendPasswordResetEmails(PasswordResetRequestLogApi passwordResetRequestLogApi,
            DirectSendTemplateVersionApi directSendTemplateVersionApi,
            DirectSendLogApi directSendLogApi,
            DirectSendApi directSendApi,
            SiteUrlApi siteUrlApi,
            InviteLinkApi inviteLinkApi,
            AgentApi agentApi,
            EvaluatorUserApi evaluatorUserApi,
            AccountApi accountApi,
            IErrorComponent errorComponent,
            string authToken)
        {
            _passwordResetRequestLogApi = passwordResetRequestLogApi;
            _directSendTemplateVersionApi = directSendTemplateVersionApi;
            _directSendLogApi = directSendLogApi;
            _directSendApi = directSendApi;
            _siteUrlApi = siteUrlApi;
            _inviteLinkApi = inviteLinkApi;
            _agentApi = agentApi;
            _evaluatorUserApi = evaluatorUserApi;
            _accountApi = accountApi;
            _errorComponent = errorComponent;
            _authToken = authToken;
        }

        public async Task<string> Run()
        {
            IEnumerable<PasswordResetRequestLog> requests = await _passwordResetRequestLogApi.GetRequestsWithoutEmailSendAtZoned(DateTimeOffset.Now, _errorComponent, _authToken);
            IEnumerable<DirectSendTemplateVersion> directSendTemplates = await _directSendTemplateVersionApi.GetAll(_errorComponent, _authToken);

            int count = 0;

            foreach (PasswordResetRequestLog request in requests)
            {
                if (request.RequestForEmailAddress != null)
                {
                    int templateId = 0;
                    int versionId = 0;
                    int accountTypeId = 0;
                    int inviteLinkTypeId = 0;
                    Guid userGuid = Guid.Empty;
                    string userName = string.Empty;

                    foreach (DirectSendTemplateVersion template in directSendTemplates)
                    {
                        switch (request.SiteProjectId)
                        {
                            case (int)SiteProjectEnum.PathfinderBlazor:
                                if (template.DirectSendTemplateId == (int)DirectSendTemplateEnum.ResetPasswordEmailPathfinder)
                                {
                                    Agent agent = await _agentApi.GetByEmail(request.RequestForEmailAddress, _errorComponent, _authToken);
                                    templateId = template.DirectSendTemplateId;
                                    versionId = template.VersionId;
                                    userGuid = agent.AgentGuid;
                                    userName = agent.AgentName;
                                    accountTypeId = (int)SecurityAccountTypeEnum.Agent;
                                    inviteLinkTypeId = (int)InviteLinkTypeEnum.PathfinderPasswordResetInvite;
                                }
                                break;
                            case (int)SiteProjectEnum.CoverageVerifierBlazor:
                                if (template.DirectSendTemplateId == (int)DirectSendTemplateEnum.CoverageVerifierResetPasswordEmail)
                                {
                                    EvaluatorUser evaluatorUser = await _evaluatorUserApi
                                        .GetEvaluatorUserByEmail(request.RequestForEmailAddress, _errorComponent, _authToken);
                                    templateId = template.DirectSendTemplateId;
                                    versionId = template.VersionId;
                                    userGuid = evaluatorUser.EvaluatorUserGuid;
                                    userName = evaluatorUser.FirstName;
                                    accountTypeId = (int)SecurityAccountTypeEnum.EvaluatorUser;
                                    inviteLinkTypeId = (int)InviteLinkTypeEnum.CoverageVerifierPasswordResetInvite;
                                }
                                break;
                            default:
                                break;
                        }
                    }
                        
                    DirectSendTemplateVersion selectedTemplate = await _directSendTemplateVersionApi.GetSimpleNoVariantsNoRules(templateId, versionId, _errorComponent, _authToken);                                       
                    SiteUrl siteUrl = await _siteUrlApi.GetSiteUrlBySiteProjectId(request.SiteProjectId, _errorComponent, _authToken);

                    Guid tokenGuid = Guid.NewGuid();
                    string resetHash = PasswordHasherCore.HashPbkdf2Sha256(tokenGuid.ToString());
                    AgentPasswordUpdate userPasswordUpdate = new()
                    {
                        AgentGuid = userGuid,
                        ResetPasswordHash = resetHash,
                    };
                    await _accountApi.AgentSaveResetPasswordHash(userPasswordUpdate, _errorComponent, _authToken);

                    string baseUrl = siteUrl.BaseUrl.ToLower();

                    Guid templateVersionGuid = selectedTemplate.DirectSendTemplateVersionGuid;

                    Dictionary<string, string> linkParams = new()
                    {
                        { "UserGuid", userGuid.ToString() },
                        { "AccountTypeId", accountTypeId.ToString() }
                    };

                    InviteLink link = new()
                    {
                        InviteLinkGuid = Guid.NewGuid(),
                        AccountGuid = Guid.Empty,
                        InviteExpiredZoned = DateTimeOffset.Now.AddHours(1),
                        InviteLinkTypeId = inviteLinkTypeId,
                        LinkParams = linkParams,
                        LinkTokenHash = resetHash
                    };

                    InviteLink passwordResetLink = await _inviteLinkApi.CreateNewInviteLink(link, _errorComponent, _authToken);

                    List<DirectSendTarget> targets = new()
                    {
                        new()
                        {
                            TargetEmail = request.RequestForEmailAddress,
                            TargetMode = RecipientTypeEnum.Normal,
                        }
                    };

                    DirectSend directSend = new()
                    {
                        TemplateId = selectedTemplate.DirectSendTemplateId,
                        NodeGuid = Guid.Empty,
                        Targets = targets,
                        ExtraData = new Dictionary<string, string>()
                        {
                            { "UserName", userName },
                            { "Content", selectedTemplate.HtmlBodyTemplate },
                            { "PasswordResetLink", $"{baseUrl}/PasswordReset/{tokenGuid}/{link.InviteLinkGuid}" },
                            { "LinkText", "Reset Link" },
                            { "Subject", selectedTemplate.SubjectLine },
                        }
                    };
                        
                    await _directSendApi.SendEmail(directSend, _errorComponent, _authToken).ConfigureAwait(false);
                    request.EmailSendAtZoned = DateTimeOffset.Now;

                    bool updateResult = await _passwordResetRequestLogApi.UpdateEmailSendAtZoned(request, _errorComponent, _authToken);
                    if (updateResult)
                    {
                        count++;
                    }
                }
            }
            return $"{count} password reset emails sent.";
        }
    }
}