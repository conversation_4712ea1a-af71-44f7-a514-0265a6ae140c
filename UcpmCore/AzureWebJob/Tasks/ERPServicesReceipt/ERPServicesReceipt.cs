using UcpmApi.Shared;
using UcpmApi.Shared.Enums;

namespace AzureWebJob.Tasks.ERPServicesReceipt
{
    public class ERPServicesReceipt
    {
        private readonly InvoiceApi _invoiceApi;
        private readonly ActivityApi _activityApi;
        private readonly IErrorComponent _errorComponent;
        private readonly string _authBearerToken;

        public ERPServicesReceipt(
            InvoiceApi invoiceApi,
            ActivityApi activityApi,
            IErrorComponent errorComponent,
            string authBearerToken) 
        {
            _invoiceApi = invoiceApi;
            _activityApi = activityApi;
            _errorComponent = errorComponent;
            _authBearerToken = authBearerToken;
        }

        public async Task<string> Update()
        {
            int totalUpdatedCount = 0;
            IEnumerable<Invoice> matchingInvoices = await _invoiceApi.GetRecentInvoicesForErpServicesReceipt(_errorComponent, _authBearerToken);
            foreach (Invoice invoice in matchingInvoices)
            {
                UpdateActivityNotesByTaskTemplateRequest request = new()
                {
                    TaskTemplateGuid = TasksTemplateGuidEnum.ErpServicesReceipt,
                    LinkingGuid = invoice.InvoiceGuid,
                    NodeType = NodeTypeEnum.Invoice,
                    GeneratedType = GeneratedTypeEnum.ERPServicesReceipt,
                    QueueGuid = TasksQueueGuidEnum.ErpServicesReceipt,
                    Notes = "ERP Services Receipt"
                };

                UpdateActivityNotesByTaskTemplateResponse response = await _activityApi.UpdateActivityNotesByTaskTemplate(request, _errorComponent, _authBearerToken);
                totalUpdatedCount += response.UpdatedCount;
            }

            return $"{totalUpdatedCount} ERP Services Receipts queued";
        }
    }
}
