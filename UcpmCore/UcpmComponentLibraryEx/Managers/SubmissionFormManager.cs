using UcpmApi.Shared;
using UcpmApi.Shared.Coverage;
using UcpmComponentLibraryEx.Models;
using UcpmComponentLibraryEx.Services.Interfaces;
using UcpmTools.StringUtilities;

namespace UcpmComponentLibraryEx.Managers
{
    public class SubmissionFormManager : ManagerBase
    {
        public SubmissionFormManager(HttpClient httpClient, IAuthenticationService userAuth)
        {
            HttpClient = httpClient;
            UserAuth = userAuth;
        }

        public async Task<SubmissionForm> AddFormToSubmissionAndGetForm(Guid submissionGuid, int sortOrder, Guid formGuid)
        {
            QueryStringBuilder query = new("/SubmissionForm/AddFormToSubmissionAndGetForm");

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.PostAsync<SubmissionForm, SubmissionForm>(query.ToString(), new SubmissionForm
            { CarrierSubmissionGuid = submissionGuid, SortOrder = sortOrder, FormGuid = formGuid }, UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<IEnumerable<SubmissionForm>> GetByCarrierSubmissionGuid(Guid carrierSubmissionGuid)
        {
            QueryStringBuilder query = new("/SubmissionForm/GetByCarrierSubmissionGuid");
            query.Add(nameof(carrierSubmissionGuid), carrierSubmissionGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<List<SubmissionForm>>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<IEnumerable<SubmissionForm>> GetSubmissionFormsByCarrierSubmission(Guid carrierSubmissionGuid)
        {
            QueryStringBuilder query = new("/SubmissionForm/GetSubmissionFormsByCarrierSubmission");
            query.Add(nameof(carrierSubmissionGuid), carrierSubmissionGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<List<SubmissionForm>>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<bool> SaveAddedSubmissionForms(List<SubmissionForm> added)
        {
            QueryStringBuilder query = new("/SubmissionForm/SaveAddedSubmissionForms");

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.PutAsync<bool, List<SubmissionForm>>(query.ToString(), added, UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<bool> SaveRemovedSubmissionForms(List<SubmissionForm> removed)
        {
            QueryStringBuilder query = new("/SubmissionForm/SaveRemovedSubmissionForms");

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.PutAsync<bool, List<SubmissionForm>>(query.ToString(), removed, UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<bool> UpdateSubmissionForm(SubmissionForm submissionForm)
        {
            QueryStringBuilder query = new("/SubmissionForm/UpdateSubmissionForm");
            
            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.PutAsync<bool, SubmissionForm>(query.ToString(), submissionForm, UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<bool> UpdateSubmissionForms(Guid carrierSubmissionGuid, List<SubmissionForm> forms)
        {
            QueryStringBuilder query = new("/SubmissionForm/UpdateSubmissionForms");
            query.Add(nameof(carrierSubmissionGuid), carrierSubmissionGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.PutAsync<bool, List<SubmissionForm>>(query.ToString(), forms, UserAuth?.CurrentUser?.JwtToken);
        }
    }
}
