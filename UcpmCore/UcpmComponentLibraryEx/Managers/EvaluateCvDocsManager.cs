using UcpmApi.Shared.Doc;
using UcpmComponentLibraryEx.Models;
using UcpmComponentLibraryEx.Services.Interfaces;
using UcpmTools.StringUtilities;

namespace UcpmComponentLibraryEx.Managers
{
    public class EvaluateCvDocsManager : ManagerBase
    {
        public EvaluateCvDocsManager(HttpClient httpClient, IAuthenticationService userAuth)
        {
            HttpClient = httpClient;
            UserAuth = userAuth;
        }

        public async Task<EvaluateCvDocsResult> RunDocEvaluations(EvaluateCvDocs evaluateCvDocs)
        {
            QueryStringBuilder query = new("/EvaluateCvDocs/RunDocEvaluations");

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.PostAsync<EvaluateCvDocsResult, EvaluateCvDocs>(query.ToString(), evaluateCvDocs, UserAuth?.CurrentUser?.JwtToken);
        }
    }
}
