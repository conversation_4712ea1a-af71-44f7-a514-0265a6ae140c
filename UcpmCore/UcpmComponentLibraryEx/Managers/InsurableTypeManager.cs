using System.Collections.Generic;
using System.Threading.Tasks;
using UcpmApi.Shared.Policy;
using UcpmComponentLibraryEx.Models;
using UcpmComponentLibraryEx.Services.Interfaces;
using UcpmTools.StringUtilities;

namespace UcpmComponentLibraryEx.Managers
{
    public class InsurableTypeManager : ManagerBase
    {
        public InsurableTypeManager(HttpClient httpClient, IAuthenticationService userAuth)
        {
            HttpClient = httpClient;
            UserAuth = userAuth;
        }

        public async Task<List<InsurableType>> GetInsurableTypes()
        {
            QueryStringBuilder query = new("/InsurableType/GetInsurableTypes");

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<List<InsurableType>>(
                query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }
    }
}
