using UcpmApi.Shared.Coverage;
using UcpmComponentLibraryEx.Models;
using UcpmComponentLibraryEx.Services.Interfaces;
using UcpmTools.StringUtilities;

namespace UcpmComponentLibraryEx.Managers
{
    public class NewFormRequestManager : ManagerBase
    {
        public NewFormRequestManager(HttpClient httpClient, IAuthenticationService userAuth)
        {
            HttpClient = httpClient;
            UserAuth = userAuth;
        }

        public async Task<NewFormRequest> GetNewFormRequestByGuid(Guid newFormRequestGuid)
        {
            QueryStringBuilder query = new("/NewFormRequest/GetNewFormRequestByGuid");
            query.Add(nameof(newFormRequestGuid), newFormRequestGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<NewFormRequest>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<List<NewFormRequest>> GetNewFormRequestsByProcessInstance(Guid processInstanceGuid)
        {
            QueryStringBuilder query = new("/NewFormRequest/GetNewFormRequestsByProcessInstance");
            query.Add(nameof(processInstanceGuid), processInstanceGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<List<NewFormRequest>>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<List<NewFormRequest>> GetCoverageVerifierNewFormRequests()
        {
            QueryStringBuilder query = new("/NewFormRequest/GetCoverageVerifierNewFormRequests");

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<List<NewFormRequest>>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<List<NewFormRequest>> GetCoverageVerifierNewFormRequestsByStatus(int newFormRequestAssignmentStatusId)
        {
            QueryStringBuilder query = new("/NewFormRequest/GetCoverageVerifierNewFormRequestsByStatus");
            query.Add(nameof(newFormRequestAssignmentStatusId), newFormRequestAssignmentStatusId);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<List<NewFormRequest>>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<bool> AddNewFormRequest(NewFormRequest newFormRequest)
        {
            QueryStringBuilder query = new("/NewFormRequest/AddNewFormRequest");

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.PostAsync<bool, NewFormRequest>(query.ToString(), newFormRequest, UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<bool> AddNewFormRequests(List<NewFormRequest> newFormRequests)
        {
            QueryStringBuilder query = new("/NewFormRequest/AddNewFormRequests");

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.PostAsync<bool, List<NewFormRequest>>(query.ToString(), newFormRequests, UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<bool> UpdateNewFormRequest(NewFormRequest newFormRequest)
        {
            QueryStringBuilder query = new("/NewFormRequest/UpdateNewFormRequest");

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.PutAsync<bool, NewFormRequest>(query.ToString(), newFormRequest, UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<bool> DeleteNewFormRequest(Guid newFormRequestGuid)
        {
            QueryStringBuilder query = new("/NewFormRequest/DeleteNewFormRequest");
            query.Add(nameof(newFormRequestGuid), newFormRequestGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.DeleteAsync<bool>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }
    }
}
