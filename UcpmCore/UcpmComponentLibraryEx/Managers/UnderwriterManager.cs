using UcpmApi.Shared.Carrier;
using UcpmComponentLibraryEx.Models;
using UcpmComponentLibraryEx.Services.Interfaces;
using UcpmTools.StringUtilities;

namespace UcpmComponentLibraryEx.Managers
{
    public class UnderwriterManager : ManagerBase
    {
        public UnderwriterManager(HttpClient httpClient, IAuthenticationService userAuth)
        {
            HttpClient = httpClient;
            UserAuth = userAuth;
        }

        public async Task<IEnumerable<Underwriter>> GetAllByCarrierGuid(Guid carrierGuid)
        {
            QueryStringBuilder query = new("/Underwriter/GetAllByCarrierGuid");
            query.Add(nameof(carrierGuid), carrierGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<IEnumerable<Underwriter>>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }
    }
}