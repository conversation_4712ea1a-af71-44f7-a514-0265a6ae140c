using UcpmApi.Shared.Cache;
using UcpmApi.Shared.Kato;
using UcpmComponentLibraryEx.Models;
using UcpmComponentLibraryEx.Services.Interfaces;
using UcpmTools.StringUtilities;

namespace UcpmComponentLibraryEx.Managers
{
    public class KatoManager : ManagerBase
    {
        public KatoManager(HttpClient httpClient, IAuthenticationService userAuth)
        {
            HttpClient = httpClient;
            UserAuth = userAuth;
        }

        public async Task<KatoBindingCoverageModel> BindCoverage(Guid policyProspectGuid)
        {
            QueryStringBuilder query = new("/Kato/BindCoverage");
            query.Add(nameof(policyProspectGuid), policyProspectGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<KatoBindingCoverageModel>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<KatoCoverageConsiderationsModel> CoverageConsiderations(Guid policyProspectGuid)
        {
            QueryStringBuilder query = new("/Kato/CoverageConsiderations");
            query.Add(nameof(policyProspectGuid), policyProspectGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<KatoCoverageConsiderationsModel>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<KatoDCATAnalysisModel> DCATAnalysis(Guid policyProspectGuid)
        {
            QueryStringBuilder query = new("/Kato/DCATAnalysis");
            query.Add(nameof(policyProspectGuid), policyProspectGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<KatoDCATAnalysisModel>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<KatoLearnMoreModel> LearnMore(Guid policyProspectGuid)
        {
            QueryStringBuilder query = new("/Kato/LearnMore");
            query.Add(nameof(policyProspectGuid), policyProspectGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<KatoLearnMoreModel>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<KatoTableOfContentModel> GetTableOfContent(Guid policyProspectGuid)
        {
            QueryStringBuilder query = new("/Kato/GetTableOfContent");
            query.Add(nameof(policyProspectGuid), policyProspectGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<KatoTableOfContentModel>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<KatoQuoteOptionsModel> QuoteOptions(Guid policyProspectGuid)
        {
            QueryStringBuilder query = new("/Kato/QuoteOptions");
            query.Add(nameof(policyProspectGuid), policyProspectGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<KatoQuoteOptionsModel>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<KatoRiskManagementModel> RiskManagement(Guid policyProspectGuid)
        {
            QueryStringBuilder query = new("/Kato/RiskManagement");
            query.Add(nameof(policyProspectGuid), policyProspectGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<KatoRiskManagementModel>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }
        public async Task<KatoAgentSelectionModel> GetForProposalPage(Guid packageGuid, string loggedInAccount = "")
        {
            QueryStringBuilder query = new("/Kato/GetForProposalPage");
            query.Add(nameof(packageGuid), packageGuid);
            query.Add(nameof(loggedInAccount), loggedInAccount);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<KatoAgentSelectionModel>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }
        public async Task<bool> UpdateSelectedOptions(Guid katoSelectedContentGuid, string json)
        {
            QueryStringBuilder query = new("/Kato/UpdateSelectedOptions");
            query.Add(nameof(katoSelectedContentGuid), katoSelectedContentGuid);
            query.Add(nameof(json), json);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.PostAsync<bool, Guid>(query.ToString(), katoSelectedContentGuid, UserAuth?.CurrentUser?.JwtToken);
        }
        public async Task<ProjectOwnerDebug> GetQualifyingProjectOwnersDebug(Guid ilg)
        {
            QueryStringBuilder query = new("/ProjectOwner/GetQualifyingProjectOwnersDebug");
            query.Add(nameof(ilg), ilg);
            UcpmComponentApiBase apiBase = new( HttpClient);
            return await apiBase.GetAsync<ProjectOwnerDebug>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<KatoAgentSelectionModel> GetForDratInviteLink(Guid flexResponseGuid, string loggedInAccount = "")
        {
            QueryStringBuilder query = new("/Kato/GetForDratInviteLink");
            query.Add(nameof(flexResponseGuid), flexResponseGuid);
            query.Add(nameof(loggedInAccount), loggedInAccount);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<KatoAgentSelectionModel>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }
    }
}
