using UcpmApi.Shared;
using UcpmComponentLibraryEx.Models;
using UcpmComponentLibraryEx.Services.Interfaces;
using UcpmTools.StringUtilities;

namespace UcpmComponentLibraryEx.Managers
{
    public class GameManager : ManagerBase
    {
        public GameManager(HttpClient httpClient, IAuthenticationService userAuth)
        {
            HttpClient = httpClient;
            UserAuth = userAuth;
        }

        public async Task<Game> GetGameByGuid(Guid gameGuid)
        {
            QueryStringBuilder query = new("/Game/GetGameByGuid");
            query.Add(nameof(gameGuid), gameGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<Game>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<IEnumerable<Game>> GetGameByInviteLink(Guid inviteLinkGuid)
        {
            QueryStringBuilder query = new("/Game/GetGameByInviteLink");
            query.Add(nameof(inviteLinkGuid), inviteLinkGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<IEnumerable<Game>>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<IEnumerable<Game>> GetAllGames()
        {
            QueryStringBuilder query = new("/Game/GetAllGames");

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<IEnumerable<Game>>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<IEnumerable<Game>> GetByGameState(int gameStateId)
        {
            QueryStringBuilder query = new("/Game/GetByGameState");
            query.Add(nameof(gameStateId), gameStateId);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<IEnumerable<Game>>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<IEnumerable<Game>> GetByGameType(int gameTypeId)
        {
            QueryStringBuilder query = new("/Game/GetByGameType");
            query.Add(nameof(gameTypeId), gameTypeId);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<IEnumerable<Game>>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<BingoHomePageModel> GetHomePageModel(Guid? inviteLinkGuid)
        {
            QueryStringBuilder query = new("/Game/GetHomePageModel");
            query.Add(nameof(inviteLinkGuid), inviteLinkGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<BingoHomePageModel>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<bool> AddGame(Game game)
        {
            QueryStringBuilder query = new("/Game/AddGame");

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.PostAsync<bool, Game>(query.ToString(), game, UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<bool> UpdateGame(Game game)
        {
            QueryStringBuilder query = new("/Game/UpdateGame");

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.PutAsync<bool, Game>(query.ToString(), game, UserAuth?.CurrentUser?.JwtToken);
        }
    }
}
