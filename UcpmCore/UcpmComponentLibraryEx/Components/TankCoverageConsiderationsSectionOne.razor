@inherits TankCoverageConsiderationsSectionOneBase

<div class="container-xl mb-3">
    <div class="row pb-3">
        <div class="col-lg-4 col-12">
            <div class="risk-management-header">
                <h4>Exposure</h4>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-6">
            <div class="kato-content-section">
                <h4 class="kato-sub-header">Understanding Tank Exposures</h4>
                <p>
                    Overview: Underground storage tanks (USTs) and Aboveground storage tanks (ASTs) are used to store bulk liquids or gases below grade.
                    They may be used to store hazardous materials, petroleum products, or hazardous wastes. Spills or releases can occur during loading
                    and unloading of materials or from the tanks, piping, dispensers and other components of UST/AST systems. Releases can occur
                    overtime or suddenly.
                </p>
                <a href="@Model.TankExposuresPdf" target="_blank">Learn more.</a>
            </div>
            @if (Model.EroDocs.Any())
            {
                <div class="kato-content-section">
                    <h4 class="kato-sub-header">Environmental Risk Overviews</h4>
                    <ul class="kato-list">
                        @foreach (var item in Model.EroDocs)
                        {
                            <li>
                                <a class="ms-4" @onclick="@(async () => await DownloadDocument(item))" target="_blank">@item.DocName</a>
                            </li>
                        }
                    </ul>
                </div>
            }
        </div>
        <div class="col-lg-6">
            @if (Model.ClaimsExamples.Any())
            {
                <h4 class="kato-sub-header">Claims Examples</h4>
                <div class="accordion kato-accordion" id="katoClaimsExamples">
                    @for (int i = 0; i < Model.ClaimsExamples.Count; i++)
                    {
                        var claim = Model.ClaimsExamples[i];
                        if (i == 0)
                        {
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="@claim.DataTarget"
                                            aria-expanded="true" aria-controls="@claim.Id">
                                        @claim.TemplateName
                                    </button>
                                </h2>
                                <div id="@claim.Id" class="accordion-collapse collapse show" data-bs-parent="#katoClaimsExamples">
                                    <div class="accordion-body">
                                        @claim.ClaimTemplateClean
                                    </div>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="@claim.DataTarget"
                                            aria-expanded="false" aria-controls="@claim.Id">
                                        @claim.TemplateName
                                    </button>
                                </h2>
                                <div id="@claim.Id" class="accordion-collapse collapse collapsed" data-bs-parent="#katoClaimsExamples">
                                    <div class="accordion-body">
                                        @claim.ClaimTemplateClean
                                    </div>
                                </div>
                            </div>
                        }
                    }
                </div>
            }
        </div>
    </div>
</div>