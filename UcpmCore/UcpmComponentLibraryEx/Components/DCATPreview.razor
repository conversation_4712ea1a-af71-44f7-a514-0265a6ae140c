@inherits DCATPreviewComponentBase
@using UtilityParsersStandard
<BSModal id="dcat-preview" DataId="dCATPreview" Size="Size.ExtraExtraLarge" IsFullScreen="true" Class="fi" @ref="DCATPreview"> 
    <BSModalContent style="background:#ccc; overflow:hidden;">
        @if(!DataIsLoading)
        {
            <button class="carousel-control-prev" type="button" id="leftBtn" @onclick="ClickLeft">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <div class="d-flex align-items-center justify-content-center">
                @((MarkupString)DCATHTMLFlipBook)
            </div>
            <button class="carousel-control-next" type="button" id="rightBtn" @onclick="ClickRight">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>
        }
        else
        {
            <div class="text-center">
                <p>Loading</p>
                <div class="spinner-grow text-success" role="status">
                    <span class="visually-hidden">Loading DCAT Booklet...</span>
                </div>
            </div>
        }      
    </BSModalContent>
    <BSModalFooter>
        @if (!isDownloading)
        {
            <BSButton Class="btn btn-primary float-end" OnClick="DownloadDcat"><i class="bi bi-download"></i> Download</BSButton>
        }
        else
        {
            <BSButton class="btn btn-primary" type="button" disabled>
                <span class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                <span role="status">Downloading...</span>
            </BSButton>
        }
        <BSButton class="btn btn-danger" type="button" OnClick="HideModal">Close</BSButton>
    </BSModalFooter>
</BSModal>