using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UcpmComponentLibraryEx.Components.FlexComponents
{
    public class FlexContext
    {
        public bool UsePayrollInstead { get; set; }

        public event Action OnChange = delegate { };

        public void TogglePayroll(bool value)
        {
            if (UsePayrollInstead != value)
            {
                UsePayrollInstead = value;
                OnChange?.Invoke();
            }
        }
    }
}
