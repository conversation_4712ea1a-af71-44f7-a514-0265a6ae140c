using FlexLibrary.Models;
using Microsoft.AspNetCore.Components;
using UcpmComponentLibraryEx.Managers;
using UcpmComponentLibraryEx.Models;
using UcpmComponentLibraryEx.Shared;

namespace UcpmComponentLibraryEx.Components.FlexComponents.PageButtons
{
    /// <summary>
    /// This render the Manual Submission button component.
    /// </summary>
    public class ManualSubmissionButtonBase : UcpmComponentBase
    {
        [Inject]
        public FlexRatingManager FlexRatingManager { get; set; }

        [Parameter]
        public Guid FlexResponseGuid { get; set; }

        [Parameter]
        public FlexButton FlexButton { get; set; }

        [Parameter]
        public EventCallback<FlexButtonResponseModel> OnClick { get; set; }

        public async Task HandleOnClick()
        {
            await FlexRatingManager.CreateManualSubmission(FlexResponseGuid);

            FlexButtonResponseModel responseModel = new()
            {
                PostSubmitHtml = FlexButton.PostSubmitHtml,
                FlexPageButtonType = FlexButton.ButtonActionId,
                ButtonParameters = new() {
                    { "RemoveButton", false }
                }
            };

            if (OnClick.HasDelegate)
            {
                await OnClick.InvokeAsync(responseModel);
            }
        }
    }
}
