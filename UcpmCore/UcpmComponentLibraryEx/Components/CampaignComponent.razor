@using Microsoft.AspNetCore.Authorization
@using UcpmApi.Shared.Pathfinder
@inherits CampaignComponentBase
@attribute [AllowAnonymous]
@if (!HideCobs)
{
    <p>
        Select a class of business
    </p>

    <ul>
        @if (Cobs.Count == 0)
        {
            <li>
                no cobs :(
            </li>
        }
        else
        {
            foreach (var item in Cobs)
            {
                <li>
                    <a @onclick="@(()=> SelectCob(item.CobGuid))" @onclick:preventDefault>
                        @item.CobName
                    </a>
                </li>
            }
        }
    </ul>
}

<div class="container-fluid p-4 @ShowModules">
    @if (Pages != null && Pages.Count() > 1)
    {
        <div class="row">
            <div class="col" style="max-width:fit-content">
                <div class="nav flex-column survey-prog-nav" id="surveyLayoutTab" role="tablist" aria-orientation="vertical">
                    @foreach (var item in Pages.OrderBy(p => p.SortOrder))
                    {
                        <a class="nav-link @(@item.PathfinderModuleSetPageGuid == Pages.OrderBy(p=> p.SortOrder).First().PathfinderModuleSetPageGuid ? "active" : "")" id="<EMAIL>-tab" data-bs-toggle="pill" data-bs-target="#<EMAIL>" role="tab" aria-controls="v-pills-1">@item.PageName</a>
                    }
                </div>
            </div>
            <div class="col">
                <div class="tab-content" id="myTabContent">
                    @foreach (var item in Pages.OrderBy(p => p.SortOrder))
                    {

                        <div class="tab-pane fade  @(@item.PathfinderModuleSetPageGuid == Pages.OrderBy(p=> p.SortOrder).First().PathfinderModuleSetPageGuid ? "show active" : "")" id="<EMAIL>" role="tabpanel" aria-labelledby="home-tab">
                            @foreach (var module in UniqueModules.Where(u => u.PathfinderModuleSetPageGuid == item.PathfinderModuleSetPageGuid))
                            {
                                <ModuleComponent Module="module" PathFinderAgent="PathFinderAgent" FlexResponseGuid="FlexResponseGuid" PayableByInsured="@PayableByInsured" SourceOfContactEnum="SourceOfContactEnum.Pathfinder"></ModuleComponent>
                            }
                            <div class="row">
                                @if (SituationVisibility && Situations != null && Situations.Any())
                                {
                                    @foreach (var situation in Situations)
                                    {
                                        <div class="col-12 d-flex justify-content-end">
                                            <a href="#" @onclick="@(()=> UpdateModules(situation))" @onclick:preventDefault>@situation.SituationDescription</a>
                                        </div>
                                    }
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    }
    else if (UniqueModules != null && UniqueModules.Any())
    {
        <div class="row">
            @foreach (PathfinderModule subModule in UniqueModules)
            {
                <ModuleComponent Module="subModule" PathFinderAgent="PathFinderAgent" FlexResponseGuid="FlexResponseGuid" PayableByInsured="@PayableByInsured" SourceOfContactEnum="SourceOfContactEnum.Pathfinder"></ModuleComponent>
            }
        </div>
        <div class="row">
            @if (SituationVisibility && Situations != null && Situations.Any())
            {
                @foreach (var situation in Situations)
                {
                    <div class="col-12 d-flex justify-content-end">
                        <a href="#" @onclick="@(()=> UpdateModules(situation))" @onclick:preventDefault>@situation.SituationDescription</a>
                    </div>
                }
            }
        </div>
    }
    else
    {
        @if (PathFinderAgent != null)
        {
            @if (PathFinderAgent.BestBrokerContacts.Any())
            {
                @foreach (Employee employee in PathFinderAgent.BestBrokerContacts)
                {
                    <BSTable Color="BSColor.Default" IsBordered="true" IsBorderLess="false" IsCaptionTop="false" IsStriped="true">
                        <BSTHead>
                            <BSTR>
                                <BSTD>@employee.EmployeeName</BSTD>
                            </BSTR>
                        </BSTHead>
                        <BSTBody>
                            <BSTR>
                                <BSTD>
                                    <label>Phone: </label>
                                    <label>@employee.PhoneNumber</label>
                                </BSTD>
                            </BSTR>
                        </BSTBody>
                    </BSTable>
                }
            }
        }
    }
</div>