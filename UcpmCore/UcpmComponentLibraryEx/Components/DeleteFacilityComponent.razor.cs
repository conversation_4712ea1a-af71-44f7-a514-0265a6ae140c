using Blazored.Modal;
using FlexLibrary;
using Microsoft.AspNetCore.Components;
using UcpmComponentLibraryEx.Components.FlexComponents;
using UcpmComponentLibraryEx.Shared;

namespace UcpmComponentLibraryEx.Components
{
    public class DeleteFacilityComponentBase : UcpmComponentBase
    {
        [Parameter]
        public DataTypeComponent DataTypeComponent { get; set; }

        [Parameter]
        public int RowNumber { get; set; }

        [Parameter]
        public int ParentRowNumber { get; set; }

        [Parameter]
        public FlexQuestion Question { get; set; }

        [CascadingParameter]
        public BlazoredModalInstance BlazoredModal { get; set; } = default!;

        public async Task Cancel()
        {
            await BlazoredModal.CloseAsync();
        }

        public async Task DeleteFacility()
        {
            await DataTypeComponent.DeleteRow(RowNumber, ParentRowNumber, Question, null);
            NavManager.NavigateTo(NavManager.GetUriWithQueryParameters(new Dictionary<string, object>
            {
                ["pageNumber"] = 3,
                ["rowNumber"] = null
            }), true);
            await InvokeAsync(() => StateHasChanged());
        }
    }
}
