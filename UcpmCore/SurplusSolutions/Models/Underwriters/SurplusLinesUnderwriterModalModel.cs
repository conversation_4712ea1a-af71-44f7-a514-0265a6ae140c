using System.ComponentModel.DataAnnotations;
using UcpmApi.Shared.SurplusLines;

namespace InsureSolutionsHub.Models.Underwriters
{
    public class SurplusLinesUnderwriterModalModel : SurplusLinesUnderwriter
    {
        [Required]
        public string UserName {  get; set; } = string.Empty;

        [Required]
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
    }
}
