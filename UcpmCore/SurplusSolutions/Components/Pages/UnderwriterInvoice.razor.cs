using InsureSolutionsHub.Components.Modals;
using InsureSolutionsHub.Extensions;
using InsureSolutionsHub.Models;
using InsureSolutionsHub.Shared;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.JSInterop;
using MoreLinq;
using ServerComponentLibrary.Managers;
using ServerComponentLibrary.Services;
using System.Reflection.Metadata;
using System.Security.Claims;
using UcpmApi.BusinessModel.Invoice;
using UcpmApi.Shared;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Helpers;
using UcpmApi.Shared.Logging;
using UcpmApi.Shared.SurplusLines;


namespace InsureSolutionsHub.Components.Pages
{
    public class UnderwriterInvoiceBase : InsureSolutionsHubComponentBase
    {
        [Inject]
        public SurplusLinesUnderwriterInvoiceManager UnderWriterInvoiceManager { get; set; }
        [Inject]
        public SurplusLinesInvoiceManager SurplusLinesInvoiceManager { get; set; }
        [Inject]
        public FeeTaxManager FeeTaxManager { get; set; }
        [Inject]
        public TransactionTypeManager TransactionTypeManager { get; set; }
        [Inject]
        public TransactionCompanyManager TransactionCompanyManager { get; set; }
        [Inject]
        public IFeeTaxCalculator FeeTaxCalculator { get; set; }
        [Inject]
        public SurplusLinesPolicyTypeManager SurplusLinesPolicyTypeManager { get; set; }
        [Inject]
        public FeeTaxLimitedStampingFeeManager FeeTaxLimitedStampingFeeManager { get; set; }
        [Inject]
        public StateManager StateManager { get; set; }
        [Inject]
        public DirectSendManager DirectSendManager { get; set; }
        [Inject]
        public SurplusLinesDueDiligenceManager DueDiligenceManager { get; set; }
        [Inject]
        public SurplusLinesInvoiceItemManager SurplusLinesInvoiceItemManager { get; set; }
        [Inject]
        public SurplusLinesInsuredManager InsuredManager { get; set; }
        [Inject]
        public SurplusLinesPolicyManager PolicyManager { get; set; }
        [Inject]
        public SurplusLinesEndorsementManager EndorsementsManager { get; set; }
        [Inject]
        public DocManager DocManager { get; set; }
        [Inject]
        public DocTypeManager DocTypeManager { get; set; }
        [Inject]
        public CountryManager CountryManager { get; set; }
        [Inject]
        public LimitsRequestedManager LimitsRequestedManager { get; set; }

        [Inject]
        public ErrorLogManager ErrorLogManager { get; set; }

        [Parameter]
        public Guid InvoiceGuid { get; set; }

        public IBrowserFile EndorsementFile { get; set; }
        public IBrowserFile PolicyFile { get; set; }
        public IBrowserFile DueDiligenceFile { get; set; }
        public SLAccountDetailModel SLAccountDetails { get; set; } = new();
        public SurplusLinesInsuredDetails InsuredDetails { get; set; } = new();
        public IEnumerable<SurplusLinesPolicyType> PolicyTypes { get; set; } = [];
        public IEnumerable<Country> Countries { get; set; } = [];
        public IEnumerable<State> States { get; set; } = [];
        public IEnumerable<LimitsRequested> Limits { get; set; } = [];
        public IEnumerable<DocModel> Docs { get; set; } = [];
        public IEnumerable<DocModel> InvoiceDocs { get; set; } = [];
        public List<DocType> DocTypes { get; set; } = [];
        public IEnumerable<SurplusLinesEndorsement> Endorsements { get; set; } = [];
        public IEnumerable<DirectSendLog> Logs { get; set; } = [];
        public IEnumerable<SurplusLinesDueDiligence> DueDiligences { get; set; } = [];
        public string Tab { get; set; } = "";
        public IEnumerable<SurplusLinesInvoiceItem> InvoiceItems { get; set; } = [];
        public bool IsReviewer { get; set; }
        public NoteModel NoteModel { get; set; } = new([], new Guid());
        public int PolicyTypeIdPolicy { get; set; } = 0;
        public int PolicyTypeIdDueDiligence { get; set; } = 0;
        public AddNewEndorsementModal AddNewEndorsementModal { get; set; } = new();
        public string IsPolicyUploaded { get; set; } = "text-white disabled";
        public string IsDueDiligenceUploaded { get; set; } = "text-white disabled";
        public bool IsDueDiligenceUploading { get; set; } = false;
        public bool IsPolicyUploading { get; set; } = false;
        public bool IsLoading { get; set; }
        public IEnumerable<TransactionType> TransactionTypes { get; set; } = [];

        private async Task LoadData()
        {
            IsLoading = true;
            IEnumerable<Claim>? claims = User.Claims;
            TransactionTypes = await TransactionTypeManager.GetTransactionTypes();
            InsuredDetails = await UnderWriterInvoiceManager.GetSurplusLinesInsuredDetails(InvoiceGuid);
            PolicyTypes = await SurplusLinesPolicyTypeManager.GetSurplusLinesPolicyTypes();
            Countries = await CountryManager.GetAllCountries();
            States = await StateManager.GetAllStates();
            Limits = await LimitsRequestedManager.GetAllLimitsRequested();
            DocTypes = (await DocTypeManager.GetAllDocTypes()).ToList();
            PolicyTypeIdPolicy = DocTypes.FirstOrDefault(l => l.DocTypeName == "Policy").DocTypeId;
            PolicyTypeIdDueDiligence = DocTypes.FirstOrDefault(l => l.DocTypeName == "Diligent Search").DocTypeId;
            IsReviewer = User.IsInRole("Surplus Solutions Reviewer") || User.IsInRole("Surplus Solutions Sr. Admin") || User.IsInRole("Surplus Solutions Admin");
            Docs = await DocManager.GetDocsBySourceGuid(InsuredDetails.SurplusLinesInvoiceGuid);
            InvoiceDocs = await DocManager.GetDocsBySourceGuid(InvoiceGuid);
            Logs = await DirectSendManager.GetByPolicy(InsuredDetails.SurplusLinesPolicyGuid);
            DueDiligences = await DueDiligenceManager.GetSurplusLinesDueDiligences();
            InvoiceItems = await SurplusLinesInvoiceItemManager.GetSurplusLinesInvoiceItemsByInvoiceGuid(InvoiceGuid);
            Endorsements = await EndorsementsManager.GetSurplusLinesEndorsementsByPolicyGuid(InsuredDetails.SurplusLinesPolicyGuid);
            SLAccountDetailModel model = new(InsuredDetails, PolicyTypes.ToList(), Countries.ToList(), States.ToList(), Limits.ToList(), DocTypes, Docs, Endorsements, Logs, DueDiligences, Tab, InvoiceItems.ToList(), NoteModel, IsReviewer);
            SLAccountDetails = model;
            SLAccountDetails.Premium = Math.Round(SLAccountDetails.Premium, 2);
            SLAccountDetails.AgencyFee = Math.Round(SLAccountDetails.AgencyFee, 2);
            IsLoading = false;
        }

        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();
            await LoadData();
        }

        public async Task<bool> UpdateInsured()
        {
            ToastService.ShowToast("Updating changes...", ToastLevelEnum.Information);
            SurplusLinesInsuredDetails insuredDetails = new()
            {
                SurplusLinesInvoiceGuid = SLAccountDetails.SurplusLinesInvoiceGuid,
                SurplusLinesInsuredGuid = SLAccountDetails.SurplusLinesInsuredGuid,
                InsuredName = SLAccountDetails.InsuredName,
                Address1 = SLAccountDetails.Address1,
                Address2 = SLAccountDetails.Address2,
                City = SLAccountDetails.City,
                StateCode = SLAccountDetails.StateCode,
                Zip = SLAccountDetails.Zip,
                CountryCode = SLAccountDetails.CountryCode,
                RiskCounty = SLAccountDetails.RiskCounty,
                MailingAddress1 = SLAccountDetails.MailingAddress1,
                MailingAddress2 = SLAccountDetails.MailingAddress2,
                MailingCity = SLAccountDetails.MailingCity,
                MailingStateCode = SLAccountDetails.MailingState,
                MailingZip = SLAccountDetails.MailingZip,
                PartnerAccountNumber = SLAccountDetails.PartnerAccountNumber,
                PolicyNumber = SLAccountDetails.PolicyNumber,
                EffectiveDate = SLAccountDetails.EffectiveDate,
                ExpirationDate = SLAccountDetails.ExpirationDate,
                NeedByDate = SLAccountDetails.NeedByDate,
                LimitOccurrence = SLAccountDetails.LimitOccurrence,
                LimitAggregate = SLAccountDetails.LimitAggregate,
                PolicyTypeId = SLAccountDetails.PolicyTypeId,
                DueDiligenceStatusId = SLAccountDetails.DueDiligenceStatusId,
                Premium = SLAccountDetails.Premium,
                AgencyFee = SLAccountDetails.AgencyFee,
                IsTaxExempt = SLAccountDetails.IsTaxExempt,
                TaxExemptReason = SLAccountDetails.TaxExemptReason,
            };

            bool result = await InsuredManager.UpdateInsured(insuredDetails);
            await UpdateInvoiceByGuid(insuredDetails.SurplusLinesInvoiceGuid, insuredDetails, "Inspection Fee");
            
            await LoadData();
            if (result)
            {
                ToastService.ShowToast("Successfully updated changes.", ToastLevelEnum.Success);
            }
            else
            {
                ToastService.ShowToast("A error occurred while updating changes. Changes were not updated.", ToastLevelEnum.Error);
            }
            StateHasChanged();
            return result;
        }

        public void BackToEmployeeIndex()
        {
            NavManager.NavigateTo("/EmployeeIndex");
        }

        public void BackToUnderWriterIndex()
        {
            NavManager.NavigateTo("/UnderWriterIndex");
        }

        public async Task<bool> MovetoOpen()
        {
            ToastService.ShowToast("Moving to open...", ToastLevelEnum.Information);
            bool result = await PolicyManager.SetRequestStatus(InvoiceGuid, (int)SurplusLinesRequestStatusEnum.Submitted);
            await LoadData();
            if (result)
            {
                ToastService.ShowToast("Successfully moved to open.", ToastLevelEnum.Success);
            }
            else
            {
                ToastService.ShowToast("A problem occurred while attempting to move to open.", ToastLevelEnum.Error);
            }
            return result;
        }

        public async Task<bool> MovetoClose()
        {
            ToastService.ShowToast("Moving to closed...", ToastLevelEnum.Information);
            bool result = await PolicyManager.SetRequestStatus(InvoiceGuid, (int)SurplusLinesRequestStatusEnum.Held);
            await LoadData();
            if (result)
            {
                ToastService.ShowToast("Successfully moved to closed.", ToastLevelEnum.Success);
            }
            else
            {
                ToastService.ShowToast("A problem occurred while attempting to move to closed.", ToastLevelEnum.Error);
            }
            return result;
        }

        public async Task GenerateQuote()
        {
            ToastService.ShowToast("Doc Generating....", ToastLevelEnum.Information);
            DocModel? quoteDoc = await DocManager.GenerateSurplusLinesQuoteDoc(InvoiceGuid);
            ToastService.ShowToast("Doc Successfully Generated.", ToastLevelEnum.Success);
            if (quoteDoc != null && quoteDoc.DocGuid != Guid.Empty)
            {
                // If quote is generated and policy is uploaded , change status to submitted
                if (SLAccountDetails.Docs.Any(d => d.DocTypeId == PolicyTypeIdPolicy))
                {
                    await PolicyManager.SetRequestStatus(InvoiceGuid, (int)SurplusLinesRequestStatusEnum.BindRequested);
                    await LoadData();
                }

                NavManager.NavigateTo(quoteDoc.CloudUrl);
            }
            else
            {
                ToastService.ShowToast("A problem occurred generating the quote. No document was returned from the quote generation process.", ToastLevelEnum.Error, 8000);
            }
        }

        public async Task DownloadAll()
        {
            HttpResponseMessage r = await DocManager.DownloadAll(SLAccountDetails.SurplusLinesPolicyGuid);
            byte[] fileBytes = await r.Content.ReadAsByteArrayAsync();
            MemoryStream fileContent = new(fileBytes);
            string? fileName = r.Content.Headers.ContentDisposition.FileName;
            await JsRuntime.InvokeVoidAsync("downloadFile", fileBytes, fileName);
        }

        public void HandleFileChangePolicy(InputFileChangeEventArgs args)
        {
            PolicyFile = args.File;
            IsPolicyUploaded = "text-white active";
        }

        public void HandleFileChangeDueDiligence(InputFileChangeEventArgs args)
        {
            DueDiligenceFile = args.File;
            IsDueDiligenceUploaded = "text-white active";
        }

        public async void PolicyFileUpload(int doctype)
        {
            bool result = false;
            IsPolicyUploading = true;
            StateHasChanged();
            try
            {
                if (PolicyFile != null)
                {
                    if (SLAccountDetails.Docs.Any())
                    {
                        // Foreach will archive files that are the same doctype as the uploaded
                        foreach (DocModel existingDoc in SLAccountDetails.Docs.Where(d => d.DocTypeId == doctype))
                        {
                            await DocManager.ArchiveDoc(existingDoc);
                        }
                    }

                    DocModel doc = await PolicyProcessUploadedFile(PolicyFile, doctype);
                    if (doc != null && doc.FileData != null && doc.FileData.Length > 0)
                    {
                        doc = await DocManager.UploadDoc(doc);
                        if (doc != null && doc.DocGuid != Guid.Empty)
                        {
                            result = true;
                        }
                        else
                        {
                            result = false;
                        }
                    }
                    else
                    {
                        result = false;
                    }

                    if (result)
                    {
                        // If policy is uploaded and qoute is already generated, change status to bindrequested
                        if (InvoiceDocs.Any(d => d.DocTypeName == "Federated Quote Template.docx"))
                        {
                            await PolicyManager.SetRequestStatus(InvoiceGuid, (int)SurplusLinesRequestStatusEnum.BindRequested);
                        }

                        await LoadData();
                        IsPolicyUploading = false;
                        StateHasChanged();
                        ToastService.ShowToast("Document uploaded successfully", ToastLevelEnum.Success);
                    }
                    else
                    {
                        IsPolicyUploading = false;
                        StateHasChanged();
                        ToastService.ShowToast("A problem occurred uploading the document. The document was not uploaded.", ToastLevelEnum.Error, 8000);
                    }
                }
                else
                {
                    IsPolicyUploading = false;
                    StateHasChanged();
                    ToastService.ShowToast("No document was selected to upload.", ToastLevelEnum.Warning, 7000);
                }
            }
            catch (Exception ex)
            {
                ErrorLog log = ErrorLogHelper.CreateFromException(ex);
                await ErrorLogManager.AddErrorLogAsync(log);
                IsPolicyUploading = false;
                StateHasChanged();
                ToastService.ShowToast("A problem occurred uploading the document. The document was not uploaded.", ToastLevelEnum.Error, 8000);
                result = false;
            }

        }

        private async Task<DocModel> PolicyProcessUploadedFile(IBrowserFile file, int docTypeId)
        {
            DocModel doc = new()
            {
                DocGuid = Guid.Empty,
                FileName = file.Name,
                FolderGuid = SLAccountDetails.SurplusLinesInvoiceGuid,
                EmployeeGuid = Guid.Empty,
                DocTypeId = docTypeId
            };

            using (Stream fileStream = file.OpenReadStream(1024 * 5000))
            {
                using MemoryStream stream = new();
                byte[] buffer = new byte[4096];
                int read = 0;

                while ((read = await fileStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    stream.Write(buffer, 0, read);
                }

                doc.FileData = stream.ToArray();
            }

            return doc;
        }

        public async void DueDiligenceFileUpload(int doctype)
        {
            bool result = false;
            IsDueDiligenceUploading = true;
            StateHasChanged();
            try
            {
                if (DueDiligenceFile != null)
                {
                    if (SLAccountDetails.Docs.Any())
                    {
                        // Foreach will archive files that are the same doctype as the uploaded
                        foreach (DocModel existingDoc in SLAccountDetails.Docs.Where(d => d.DocTypeId == doctype))
                        {
                            await DocManager.ArchiveDoc(existingDoc);
                        }
                    }

                    DocModel doc = await DueDiligenceProcessUploadedFile(DueDiligenceFile, doctype);
                    if (doc != null && doc.FileData != null && doc.FileData.Length > 0)
                    {
                        doc = await DocManager.UploadDoc(doc);
                        if (doc != null && doc.DocGuid != Guid.Empty)
                        {
                            result = true;
                        }
                        else
                        {
                            result = false;
                        }
                    }
                    else
                    {
                        result = false;
                    }

                    if (result)
                    {
                        // If policy is uploaded and qoute is already generated, change status to Bindrequested
                        if (InvoiceDocs.Any(d => d.DocTypeName == "Federated Quote Template.docx"))
                        {
                            await PolicyManager.SetRequestStatus(InvoiceGuid, (int)SurplusLinesRequestStatusEnum.BindRequested);
                        }

                        await LoadData();
                        IsDueDiligenceUploading = false;
                        StateHasChanged();
                        ToastService.ShowToast("Document uploaded successfully", ToastLevelEnum.Success);
                    }
                    else
                    {
                        IsDueDiligenceUploading = false;
                        StateHasChanged();
                        ToastService.ShowToast("A problem occurred uploading the document. The document was not uploaded.", ToastLevelEnum.Error, 8000);
                    }
                }
                else
                {
                    IsDueDiligenceUploading = false;
                    StateHasChanged();
                    ToastService.ShowToast("No document was selected to upload.", ToastLevelEnum.Warning, 7000);
                }
            }
            catch (Exception ex)
            {
                ErrorLog log = ErrorLogHelper.CreateFromException(ex);
                await ErrorLogManager.AddErrorLogAsync(log);
                IsDueDiligenceUploading = false;
                StateHasChanged();
                ToastService.ShowToast("A problem occurred uploading the document. The document was not uploaded.", ToastLevelEnum.Error, 8000);
                result = false;
            }

        }

        private async Task UpdateInvoiceByGuid(Guid invoiceGuid, SurplusLinesInsuredDetails insured, string feeDescription)
        {
            SurplusLinesInvoice invoice = await SurplusLinesInvoiceManager.GetSurplusLinesInvoiceByGuid(invoiceGuid);
            if (invoice != null)
            {
                if (invoice.SurplusLinesInvoiceItems == null)
                {
                    invoice.SurplusLinesInvoiceItems = [];
                }

                SurplusLinesPolicy surplusLinesPolicy = await PolicyManager.GetSurplusLinesPolicyByGuid(invoice.SurplusLinesPolicyGuid);
                await AddEstimatedPremium(invoice, surplusLinesPolicy.MaxExpectedPremium);

                // Add UCPM Fee
                TransactionType transactionType = await TransactionTypeManager.GetTransactionByPtCode("CFE");
                SurplusLinesInvoiceItem ucpmFeeInvoiceItem = await CreateUcpmFee(invoice, surplusLinesPolicy.ExpectedAgencyFee, transactionType, insured.StateCode);
                FeeTax feeTax = await FeeTaxManager.GetByTransactionTypeAndState(transactionType.TransactionTypeId, insured.StateCode);
                if (feeTax != null && feeTax.FeeTaxId != 0)
                {
                    if (feeTax.TransactionTypeId == (int)TransactionTypeEnum.PolicyFee && surplusLinesPolicy.ExpectedAgencyFee > feeTax.FeeTaxMax)
                    {
                        ucpmFeeInvoiceItem.GrossAmount = feeTax.FeeTaxMax;
                    }

                    ucpmFeeInvoiceItem.LineDescription = string.IsNullOrWhiteSpace(feeDescription)
                        ? await GetInvoiceItemDescription(feeTax, surplusLinesPolicy.ExpectedAgencyFee, transactionType.NewtonCompanyCode, transactionType.TransactionTypeId, insured.StateCode)
                        : feeDescription;

                    invoice.SurplusLinesInvoiceItems.Add(ucpmFeeInvoiceItem);
                }
                else
                {
                    invoice.SurplusLinesInvoiceItems.Add(ucpmFeeInvoiceItem);
                }

                // Add Fee Tax Items
                await AddFeeTaxItems(insured, invoice);

                await SurplusLinesInvoiceItemManager.DeleteAllByInvoiceId(invoice.SurplusLinesInvoiceGuid);
                await SurplusLinesInvoiceItemManager.AddSurplusLinesInvoiceItems(invoice.SurplusLinesInvoiceItems);
            }
        }

        public async Task AddFeeTaxItems(SurplusLinesInsuredDetails insuredDetails, SurplusLinesInvoice surplusLinesInvoice)
        {
            IEnumerable<FeeTax> feeTaxes = await GetCurrentUcpmFeeTaxesForFederated(insuredDetails);

            if (surplusLinesInvoice.SurplusLinesEndorsementGuid != Guid.Empty)
            {
                feeTaxes = feeTaxes.Where(ft => ft.TransactionTypeId != (int)TransactionTypeEnum.PolicyFee);

                if (await FeeTaxLimitedStampingFeeManager.GetIsStampingFeeIsWaived(insuredDetails.StateCode, insuredDetails.EffectiveDate.Date))
                {
                    feeTaxes = feeTaxes.Where(ft => ft.TransactionTypeId != (int)TransactionTypeEnum.SurplusLinesStampingFee);
                }
            }

            //Need to enforce order of fees first, then taxes. This ensures that taxes that are 
            //factoring both fees and premium compute properly. Taxes will return True (1) and 
            //all other items will return False (0).
            IEnumerable<SurplusLinesInvoiceItem> originalItems = [.. surplusLinesInvoice.SurplusLinesInvoiceItems];
            foreach (FeeTax feeTax in feeTaxes.OrderBy(f => f.TransactionType.TransactionGroupId == (int)TransactionGroupEnum.SlTax))
            {
                decimal premiumTotal = GetTaxableAmount(originalItems, feeTax.FeeTaxPaidOnId);
                decimal amountAllowedByLaw = FeeTaxCalculator.ComputeFeesAndTaxes(premiumTotal, feeTax);

                await UpdateOrCreateInvoiceItem(feeTax, surplusLinesInvoice, premiumTotal, amountAllowedByLaw);
            }

            if (insuredDetails.IsTaxExempt)
            {
                await SurplusLinesInvoiceItemManager.UpdateSurplusLinesInvoiceItemsForTaxExempt(surplusLinesInvoice.SurplusLinesInvoiceItems);
            }
        }

        private async Task<IEnumerable<FeeTax>> GetCurrentUcpmFeeTaxesForFederated(SurplusLinesInsuredDetails insuredDetails)
        {
            if(insuredDetails.IsTaxExempt)
            {
                return [];
            }

            return await FeeTaxManager.GetCurrentUcpmFeeTaxesForFederated(insuredDetails.StateCode,insuredDetails.EffectiveDate);
        }

        public decimal GetTaxableAmount(IEnumerable<SurplusLinesInvoiceItem> surplusLinesInvoiceItems, int paidOn)
        {
            return paidOn switch
            {
                (int)FeeTaxPaidOnEnum.Fixed => 0,
                (int)FeeTaxPaidOnEnum.PremiumOnly => surplusLinesInvoiceItems.GetTotalPremium(TransactionTypes),
                (int)FeeTaxPaidOnEnum.PremiumAndAllFees => surplusLinesInvoiceItems.GetTotalPremiumAndFees(TransactionTypes),
                (int)FeeTaxPaidOnEnum.PremiumAndCarrierFees => surplusLinesInvoiceItems.GetTotalPremiumAndCarrierFees(TransactionTypes),
                (int)FeeTaxPaidOnEnum.PremiumAndUcpmFees => surplusLinesInvoiceItems.GetTotalPremiumAndUcpmFees(TransactionTypes),
                _ => throw new ArgumentOutOfRangeException($"GetTaxableAmount passed out of range paidOn {paidOn}"),
            };
        }

        private async Task UpdateOrCreateInvoiceItem(FeeTax feeTax, SurplusLinesInvoice surplusLinesInvoice, decimal premiumTotal, decimal amountAllowedByLaw)
        {
            IEnumerable<SurplusLinesInvoiceItem> existingItems =
                            from i in surplusLinesInvoice.SurplusLinesInvoiceItems
                            where i.TransactionTypeId == feeTax.TransactionTypeId &&
                            i.TransactionCompanyGuid == feeTax.TransactionCompanyGuid
                            select i;

            if (existingItems.Count() == 1)
            {
                SurplusLinesInvoiceItem existingItem = existingItems.First();
                existingItem.GrossAmount = amountAllowedByLaw;
            }
            else if (!existingItems.Any())
            {
                if (TransactionTypes == null || TransactionTypes.Count() == 0)
                {
                    TransactionTypes = await TransactionTypeManager.GetActiveTransactionTypes();
                }

                TransactionType? transaction = TransactionTypes.SingleOrDefault(f => f.TransactionTypeId == feeTax.TransactionTypeId);

                SurplusLinesInvoiceItem newItem = await GetNewItem(surplusLinesInvoice.SurplusLinesInvoiceGuid, feeTax, premiumTotal, transaction);
                if (newItem.GrossAmount != 0)
                {
                    surplusLinesInvoice.SurplusLinesInvoiceItems.Add(newItem);
                }
            }
            else
            {
                throw new ArgumentOutOfRangeException(
                    $"AddIfMissingTransaction() existingItems.Count() = {existingItems.Count()}");
            }
        }

        private async Task<SurplusLinesInvoiceItem> GetNewItem(Guid invoiceGuid, FeeTax feeTax, decimal premiumTotal, TransactionType transaction)
        {
            SurplusLinesInvoiceItem newItem = new()
            {
                SurplusLinesInvoiceGuid = invoiceGuid,
                SurplusLinesInvoiceItemGuid = Guid.NewGuid(),
                TransactionTypeId = feeTax.TransactionTypeId,
                TransactionCompanyGuid = feeTax.TransactionCompanyGuid,
                LineDescription = await GetInvoiceItemDescription(feeTax, premiumTotal, transaction.NewtonCompanyCode, transaction.TransactionTypeId, feeTax.State),
                GrossAmount = FeeTaxCalculator.ComputeFeesAndTaxes(premiumTotal, feeTax),
                IsNew = true,
            };

            return newItem;
        }

        private async Task<string> GetInvoiceItemDescription(FeeTax feeTax, decimal taxable, string newtonCompanyLookupCode, int transactionTypeId, string slState)
        {
            if (feeTax.FeeTaxDescriptionOverride)
            {
                return feeTax.FeeTaxDescription;
            }

            TransactionType transaction = await TransactionTypeManager.GetTransactionTypeById(transactionTypeId);
            string descriptionLeft = GetItemDescription(newtonCompanyLookupCode, transaction, slState);
            string descriptionRight = FeeTaxCalculator.ComputeDescriptionFromPremium(taxable, feeTax);

            return $"{descriptionLeft} {descriptionRight}";

        }

        public async Task<SurplusLinesInvoiceItem> CreateUcpmFee(SurplusLinesInvoice invoice, decimal fee, TransactionType transactionType, string slState)
        {
            TransactionCompany transactionCompany = await TransactionCompanyManager.GetTransactionCompanyByCode("UC1");
            SurplusLinesInvoiceItem feeItem = invoice.SurplusLinesInvoiceItems
                .SingleOrDefault(i => i.TransactionTypeId == transactionType.TransactionTypeId && i.TransactionCompanyGuid == transactionCompany.TransactionCompanyGuid);

            if (feeItem == null)
            {
                feeItem = new SurplusLinesInvoiceItem
                {
                    SurplusLinesInvoiceItemGuid = Guid.NewGuid(),
                    SurplusLinesInvoiceGuid = invoice.SurplusLinesInvoiceGuid,
                    TransactionTypeId = transactionType.TransactionTypeId,
                    GrossAmount = fee,
                    LineDescription = $"{transactionType.TransactionDescription}",
                    TransactionCompanyGuid = transactionCompany.TransactionCompanyGuid,
                    RecordCreatedZoned = DateTime.Now,
                    IsNew = true,
                };
            }
            else
            {
                feeItem.GrossAmount = fee;
            }

            return feeItem;
        }

        private string GetItemDescription(string newtonLookupCode, TransactionType transactionType, string stateCode)
        {
            if (transactionType == null)
            {
                return "Null Transaction Type";
            }

            string description = transactionType.TransactionDescription;

            if (!string.IsNullOrWhiteSpace(newtonLookupCode) && transactionType.NewtonCompanyCode.Contains("*") &&
                !transactionType.NewtonCompanyCode.Equals("B**"))
            {
                description += $" ({stateCode})";
            }

            return description;
        }

        private async Task AddEstimatedPremium(SurplusLinesInvoice invoice, decimal premium)
        {
            TransactionType transactionType = await TransactionTypeManager.GetTransactionByPtCode("NEW");
            if (transactionType != null)
            {
                SurplusLinesInvoiceItem? premiumItem = await GetPremiumItem(invoice, premium, transactionType);
                if (premiumItem != null && premiumItem.IsNew)
                {
                    invoice.SurplusLinesInvoiceItems.Add(premiumItem);
                }
            }
        }

        private async Task<SurplusLinesInvoiceItem?> GetPremiumItem(SurplusLinesInvoice invoice, decimal premium, TransactionType transactionType)
        {
            TransactionCompany transactionCompany = await TransactionCompanyManager.GetDefault();
            SurplusLinesInvoiceItem? premiumItem = invoice?.SurplusLinesInvoiceItems?.SingleOrDefault(i =>
                i.TransactionTypeId == transactionType.TransactionTypeId &&
                i.TransactionCompanyGuid == transactionCompany.TransactionCompanyGuid);

            if (invoice != null)
            {
                if (premiumItem == null)
                {
                    premiumItem = new SurplusLinesInvoiceItem
                    {
                        SurplusLinesInvoiceItemGuid = Guid.NewGuid(),
                        SurplusLinesInvoiceGuid = invoice.SurplusLinesInvoiceGuid,
                        TransactionTypeId = transactionType.TransactionTypeId,
                        GrossAmount = premium,
                        LineDescription = $"{transactionType.TransactionDescription}",
                        IsNew = true,
                        TransactionCompanyGuid = transactionCompany.TransactionCompanyGuid,
                        RecordCreatedZoned = DateTime.Now,
                    };
                }
                else
                {
                    premiumItem.GrossAmount = premium;
                }
            }

            return premiumItem;
        }

        private async Task<DocModel> DueDiligenceProcessUploadedFile(IBrowserFile file, int docTypeId)
        {
            DocModel doc = new()
            {
                DocGuid = Guid.Empty,
                FileName = file.Name,
                FolderGuid = SLAccountDetails.SurplusLinesInvoiceGuid,
                EmployeeGuid = Guid.Empty,
                DocTypeId = docTypeId
            };

            using (Stream fileStream = file.OpenReadStream(1024 * 5000))
            {
                using MemoryStream stream = new();
                byte[] buffer = new byte[4096];
                int read = 0;

                while ((read = await fileStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    stream.Write(buffer, 0, read);
                }

                doc.FileData = stream.ToArray();
            }

            return doc;
        }

        public async Task LoadEndorsements()
        {
            IEnumerable<SurplusLinesEndorsement> endorsements = await EndorsementsManager.GetSurplusLinesEndorsementsByPolicyGuid(InsuredDetails.SurplusLinesPolicyGuid);
            SLAccountDetails.Endorsements = endorsements;
        }

        public async Task AddNewEndorsements()
        {
            await AddNewEndorsementModal.ShowModal();
        }

        public async Task HandleGenerateQuoteButton(Guid invoiceGuid)
        {
            DocModel quoteDoc = await DocManager.GenerateSurplusLinesQuoteDoc(invoiceGuid);

            NavManager.NavigateTo(quoteDoc.CloudUrl);
        }

        public void endorsementHandleFileChange(InputFileChangeEventArgs args)
        {
            EndorsementFile = args.File;
        }

        public async Task<bool> endorsementFileUpload(Guid endorsementGuid)
        {
            bool result = false;
            try
            {
                if (EndorsementFile != null)
                {
                    DocModel existingDoc = await DocManager.GetDocCheck(endorsementGuid);

                    if (existingDoc.DocGuid != Guid.Empty)
                    {
                        await DocManager.ArchiveDoc(existingDoc);
                    }

                    await DocManager.UploadDoc(await endorsementProcessUploadedFile(EndorsementFile, 5, endorsementGuid));
                    await EndorsementsManager.UpdateSurplusLinesEndorsementStatus(endorsementGuid, (int)SurplusLinesRequestStatusEnum.BindRequested);

                    result = true;
                    await LoadData();
                }

            }
            catch (Exception)
            {
                result = false;
            }

            if (result)
            {
                ToastService.ShowToast("Document uploaded successfully", ToastLevelEnum.Success);
            }
            else 
            { 
                ToastService.ShowToast("A problem occurred uploading the document. The document was not uploaded.", ToastLevelEnum.Error, 8000);
            }

            return result;
        }

        private async Task<DocModel> endorsementProcessUploadedFile(IBrowserFile file, int docTypeId, Guid endorsementGuid)
        {
            DocModel doc = new()
            {
                DocGuid = Guid.Empty,
                FileName = file.Name,
                FolderGuid = endorsementGuid,
                EmployeeGuid = Guid.Empty,
                DocTypeId = docTypeId
            };

            using (Stream fileStream = file.OpenReadStream(1024 * 5000))
            {
                using MemoryStream stream = new();
                byte[] buffer = new byte[4096];
                int read = 0;

                while ((read = await fileStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    stream.Write(buffer, 0, read);
                }

                doc.FileData = stream.ToArray();
            }

            return doc;
        }
    }
}
