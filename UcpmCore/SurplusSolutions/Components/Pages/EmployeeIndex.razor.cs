using InsureSolutionsHub.Shared;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ServerComponentLibrary.Managers;
using UcpmApi.Shared;

namespace InsureSolutionsHub.Components.Pages
{
    public class EmployeeIndexBase : InsureSolutionsHubComponentBase
    {
        [Inject]
        public StateManager StateManager { get; set; }
        [Inject]
        public SurplusLinesEndorsementManager SurplusLinesEndorsementManager { get; set; }
        [Inject]
        public SurplusLinesPolicyManager SurplusLinesPolicyManager { get; set; }
        [Inject]
        public SurplusSolutionsInsuredOverviewManager InsureSolutionsHubInsuredOverviewManager { get; set; }

        protected bool HasAssignedStates = false;
        protected bool isDataLoading { get; set; }
        public string ReadyToFileActive { get; set; }
        public string CompletedActive { get; set; }
        public string ReadyToFileShow { get; set; }
        public string CompletedShow { get; set; }
        protected DateTime EndDate { get; set; }
        protected SurplusLinesTableStatusEnum SelectedTab { get; set; } = SurplusLinesTableStatusEnum.ReadyToFile;
        protected List<string> StateOptions { get; set; } = [];
        protected List<string> reportOnZeroStateCodes { get; set; } = [];
        private bool _hasLoadedStates = false;
        public List<SurplusLinesFilingBaseModel> AllAccounts { get; set; } = [];
        public List<SurplusLinesFilingBaseModel> ReadyToFileAccounts { get; set; } = [];
        public List<SurplusLinesFilingBaseModel> BoundAccounts { get; set; } = [];
        public List<SurplusLinesFilingBaseModel> AllBoundedAccounts { get; set; } = [];

        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();
            await LoadData(SurplusLinesTableStatusEnum.ReadyToFile);
        }

        private async Task LoadData(SurplusLinesTableStatusEnum surplusLinesTableStatusEnum)
        {
            isDataLoading = true;
            StateHasChanged();
            try
            {
                if (!_hasLoadedStates)
                {
                    List<State> states = await StateManager.GetSlEmployeeStates(AccountGuid);
                    StateOptions = new List<string> { "All My States" };
                    if (states != null)
                    {
                        StateOptions.AddRange(states.Select(s => s.Code).Where(code => !string.IsNullOrWhiteSpace(code)));
                        
                        HasAssignedStates = states.Any(s => s.SurplusLinesState != null
                        && (s.SurplusLinesState.AssignedToEmployeeGuid == AccountGuid
                        || s.SurplusLinesState.SecondaryAssignedEmployeeGuid == AccountGuid));
                    }

                    List<State> reportOnZeroStates = await StateManager.GetSurplusLinesReportingOnZeroEndorsementsStates();
                    reportOnZeroStateCodes = reportOnZeroStates.Select(x => x.Code).ToList();

                    _hasLoadedStates = true;
                }

                if (surplusLinesTableStatusEnum == SurplusLinesTableStatusEnum.ReadyToFile)
                {
                    await GetAllFilingsWithEndorsements();
                }
                else
                {
                    await GetBoundedFilingsWithEndorsements();
                }
            }
            finally
            {
                isDataLoading = false;
                StateHasChanged();
            }
        }

        public async Task OnTabChanged(SurplusLinesTableStatusEnum tab)
        {
            if (tab == SurplusLinesTableStatusEnum.Completed)
            {
                if (AllBoundedAccounts == null || !AllBoundedAccounts.Any())
                {
                    await LoadData(SurplusLinesTableStatusEnum.Completed);
                }
                else
                {
                    ReadyToFileActive = "";
                    ReadyToFileShow = "";
                    CompletedActive = "active";
                    CompletedShow = "show";
                    SelectedTab = SurplusLinesTableStatusEnum.Completed;
                    await LoadData(SelectedTab);
                }
            }
            else if (tab == SurplusLinesTableStatusEnum.ReadyToFile)
            {
                CompletedActive = "";
                CompletedShow = "";
                ReadyToFileActive = "active";
                ReadyToFileShow = "show";
                SelectedTab = SurplusLinesTableStatusEnum.ReadyToFile;
                await LoadData(SelectedTab);
            }
        }

        private async Task GetAllFilingsWithEndorsements()
        {
            EndDate = DateTime.Now.AddDays(30);
            IEnumerable<SurplusLinesFilingBaseModel> upcomingSurplusLinesFiling =
                await InsureSolutionsHubInsuredOverviewManager.GetSurplusLinesFiling(AccountGuid, EndDate, SurplusLinesTableStatusEnum.ReadyToFile);

            List<SurplusLinesFilingBaseModel> allFilings = upcomingSurplusLinesFiling.ToList();
            IEnumerable<SurplusLinesFilingBaseModel> endorsements = await SurplusLinesEndorsementManager.GetEndorsementsForProcessing(AccountGuid, EndDate, SurplusLinesTableStatusEnum.ReadyToFile);

            foreach (SurplusLinesFilingBaseModel endorsement in endorsements)
            {
                if (endorsement.Premium != 0 || reportOnZeroStateCodes.Contains(endorsement.StateCode))
                {
                    allFilings.Add(endorsement);
                }
            }

            AllAccounts = [.. allFilings.OrderBy(x => x.FilingDate).ThenBy(x => x.StateCode).ThenBy(x => x.InsuredName)];
            ReadyToFileAccounts = allFilings.ToList();
            ReadyToFileActive = "active";
            ReadyToFileShow = "show";
            SelectedTab = SurplusLinesTableStatusEnum.ReadyToFile;
        }

        private async Task GetBoundedFilingsWithEndorsements()
        {
            IEnumerable<SurplusLinesFilingBaseModel> boundAccounts =
            await InsureSolutionsHubInsuredOverviewManager.GetSurplusLinesFiling(AccountGuid, EndDate, SurplusLinesTableStatusEnum.Completed);

            List<SurplusLinesFilingBaseModel> allBoundedFilings = boundAccounts.ToList();
            IEnumerable<SurplusLinesFilingBaseModel> boundedEndorsements = await SurplusLinesEndorsementManager.GetEndorsementsForProcessing(AccountGuid, EndDate, SurplusLinesTableStatusEnum.Completed);
            foreach (SurplusLinesFilingBaseModel endorsement in boundedEndorsements)
            {
                if (endorsement.Premium != 0 || reportOnZeroStateCodes.Contains(endorsement.StateCode))
                {
                    allBoundedFilings.Add(endorsement);
                }
            }

            AllBoundedAccounts = [.. allBoundedFilings.OrderBy(x => x.FilingDate).ThenBy(x => x.StateCode).ThenBy(x => x.InsuredName)];
            BoundAccounts = allBoundedFilings.ToList();

            CompletedActive = "active";
            CompletedShow = "show";
            ReadyToFileActive = "";
            ReadyToFileShow = "";
            SelectedTab = SurplusLinesTableStatusEnum.Completed;
        }

        public async Task MarkAsFiled((Guid guid, bool isPolicy) args)
        {
            (Guid guid, bool isPolicy) = args;

            bool result = false;

            if (isPolicy)
            {
                result = await SurplusLinesPolicyManager.MarkSurplusLinesPolicyAsFiled(guid);
            }
            else
            {
                result = await SurplusLinesEndorsementManager.MarkSurplusLinesEndorsementAsFiled(guid);
            }

            if (result)
            {
                ToastService.ShowSuccess("Marked as filed successfully.");
                await LoadData(SelectedTab);
            }
            else
            {
                ToastService.ShowError("Failed to mark as filed.");
            }
        }

        public async Task SetSlFilingDate((Guid guid, DateTime date, bool isPolicy) args)
        {
            (Guid guid, DateTime date, bool isPolicy) = args;

            bool result = false;

            if (isPolicy)
            {
                result = await SurplusLinesPolicyManager.UpdateSurplusLinesPolicyNeedByDate(guid, date);
            }
            else
            {
                result = await SurplusLinesEndorsementManager.UpdateSurplusLinesEndorsementActualFilingDate(guid, date);
            }

            if (result)
            {
                ToastService.ShowSuccess("Filing date set successfully.");
            }
            else
            {
                ToastService.ShowError("Failed to set filing date.");
            }
        }

    }
}
