@inherits NoteFormModalBase
<BSModal @ref="_refModal" OnHide="OnModalHide" DataId="@ModalDataId" IsStaticBackdrop="true" Size="Size.Large">
    <BSForm Model="NoteFormModel" OnValidSubmit="@Update" EditContext="NoteEditContext">
        <DataAnnotationsValidator/>
        <BSModalHeader HasCloseButton="@(!IsSaving)">
            @ModalTitle
        </BSModalHeader>
        <BSModalContent>
            <div class="alert alert-light py-1 row justify-content-between" role="alert">
                <div class="col-6 w-auto align-self-center">
                    <label>Entered by:</label>
                    <span
                        class="badge align-items-center p-1 pe-2 text-info-emphasis bg-info-subtle border border-info-subtle rounded-pill">
                        @if (!string.IsNullOrEmpty(CurrentEmployee.WebPhotoUrl))
                        {
                            <img class="rounded-circle me-1" width="22" height="22" src="@CurrentEmployee.WebPhotoUrl"
                                 loading="lazy" alt="">
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(CurrentEmployee.EmailFooterImgUrl))
                            {
                                <img class="rounded-circle me-1" width="24" height="24"
                                     src="@CurrentEmployee.EmailFooterImgUrl"
                                     loading="lazy" alt="">
                            }
                            else
                            {
                                <i class="bi bi-person-circle me-1 fs-6"></i>
                            }
                        }
                        @CurrentEmployee.EmployeeName
                    </span>
                </div>
                <div class="col-6 w-auto align-self-center">
                    <label>
                        Date Entered: <span>@DateTimeOffset.Now.ToString("MM/dd/yyyy")</span>
                    </label>
                </div>
            </div>
            @*<BSValidationSummary/>*@
            @*<div class="mb-3 row">
                <div class="col-md-6">
                    <BSLabel>
                        Entered By:
                    </BSLabel>
                    <BSInput IsDisabled="@(IsSaving || DisableScheduledFor)" InputType="InputType.Select"
                             @bind-Value="NoteFormModel.ActionByGuid">
                        <option value="@Guid.Empty">Select user</option>
                        @foreach (var employee in EmployeeLookup)
                        {
                            <option value="@employee.Key">
                                @employee.Value
                            </option>
                        }
                    </BSInput>
                    <BSFeedback For="@(() => NoteFormModel.ActionByGuid)"/>
                </div>
            </div>*@
            <div class="mb-3">
                <BSLabel>
                    Source:
                    @if (!string.IsNullOrEmpty(NoteFormModel.Source))
                    {
                        <CharacterCountBadge CurrentLength="@NoteFormModel.Source.Length" MaxLength="100"/>
                    }
                </BSLabel>
                <BSInput ValidateOnInput="true" ValidateOnBlur="true" ValidateOnChange="true" IsDisabled="IsSaving" InputType="InputType.Text" @bind-Value="NoteFormModel.Source"/>
                <BSFeedback For="@(() => NoteFormModel.Source)"/>
            </div>
            <div class="mb-3">
                <BSLabel>
                    Topic:
                    @if (!string.IsNullOrEmpty(NoteFormModel.Topic))
                    {
                        <CharacterCountBadge CurrentLength="@NoteFormModel.Topic.Length" MaxLength="100"/>
                    }
                </BSLabel>
                <BSInput ValidateOnInput="true" ValidateOnBlur="true" ValidateOnChange="true" IsDisabled="IsSaving" InputType="InputType.Text" @bind-Value="NoteFormModel.Topic"/>
                <BSFeedback For="@(() => NoteFormModel.Topic)"/>
            </div>
            <div class="mb-3">
                <BSLabel>
                    <i class="bi bi-stickies-fill me-1 text-warning"></i>Notes
                    @if (!string.IsNullOrEmpty(NoteFormModel.RegardingAndNotes))
                    {
                        <CharacterCountBadge CurrentLength="@NoteFormModel.RegardingAndNotes.Length" MaxLength="4000"/>
                    }
                </BSLabel>
                <BSInput ValidateOnInput="true" ValidateOnBlur="true" IsDisabled="IsSaving" Class="textarea" InputType="InputType.TextArea"
                         @bind-Value="NoteFormModel.RegardingAndNotes"/>
                <BSFeedback For="@(() => NoteFormModel.RegardingAndNotes)"/>
            </div>
        </BSModalContent>
        <BSModalFooter Context="_refModal">
            <BSButton IsDisabled="IsSaving" Color="BSColor.Secondary" Class="border border-secondary-subtle shadow-sm"
                      IsOutlined="true" @onclick="_refModal.HideAsync" Target="@ModalDataId">
                <i class="bi bi-x-circle me-1"></i> Close
            </BSButton>
            <BSButton IsDisabled="IsSaving" IsSubmit="true" Color="BSColor.Success"
                      Class="border border-success-subtle shadow-sm"
                      IsOutlined="true">
                @if (IsSaving)
                {
                    <span class="spinner-grow spinner-grow-sm" aria-hidden="true"></span>
                    <span role="status">Saving task...</span>
                }
                else
                {
                    if (IsNewNote)
                    {
                        <span><i class="bi bi-save-fill me-1"></i> Save</span>
                    }
                    else
                    {
                        <span><i class="bi bi-save-fill me-1"></i> Update</span>
                    }
                }
            </BSButton>
        </BSModalFooter>
    </BSForm>
</BSModal>