using InsureSolutionsHub.Shared;
using Microsoft.AspNetCore.Components;
using ServerComponentLibrary.Managers;
using UcpmApi.Shared;

namespace InsureSolutionsHub.Components.Shared.ActivityCenter.Tasks;

public class TaskListBase : InsureSolutionsHubComponentBase
{
    private readonly Guid _personalTaskQueueGuid = Guid.Parse("00000001-0001-0001-0001-000000000001");
    [CascadingParameter(Name = "EmployeesReference")]
    public List<Employee> Employees { get; set; } = [];
    [Parameter]
    public required List<ActivityModel> Tasks { get; set; } = [];
    [Parameter]
    public EventCallback<ActivityModel> OnSelectedTaskChanged { get; set; }
    [Inject]
    public ActivityManager ActivityManager { get; set; } = null!;
    private Employee CurrentEmployee { get; set; } = new();
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        CurrentEmployee = Employees.FirstOrDefault(e => e.EmployeeGuid == AccountGuid) ?? new Employee();
    }

    protected async Task OnClickStatus(ActivityModel task, int statusId)
    {
        task.TaskStatusId = statusId;
        var updatedActivity =  await ActivityManager.UpdateActivity(task);
        updatedActivity.ScheduledForEmployee = task.ScheduledForEmployee;
        updatedActivity.ScheduledByEmployee = task.ScheduledByEmployee;
        // update the task in the list
        var index = Tasks.FindIndex(t => t.ActivityGuid == updatedActivity.ActivityGuid);
        if (index >= 0)
        {
            Tasks[index] = updatedActivity;
        }
        else
        {
            Tasks.Add(updatedActivity);
        }
        OnTaskSelected(updatedActivity);
    }
    
    protected async Task OnClickAssign(ActivityModel task)
    {
        // Logic to handle task assignment
        task.ScheduledFor = CurrentEmployee.EmployeeGuid;
        task.ScheduledForEmployee = CurrentEmployee;
        var updatedActivity = await ActivityManager.AssignTask(task);
        updatedActivity.ScheduledForEmployee = task.ScheduledForEmployee;
        updatedActivity.ScheduledByEmployee = task.ScheduledByEmployee;
        // update the task in the list
        var index = Tasks.FindIndex(t => t.ActivityGuid == updatedActivity.ActivityGuid);
        if (index >= 0)
        {
            Tasks[index] = updatedActivity;
        }
        else
        {
            Tasks.Add(updatedActivity);
        }
        OnTaskSelected(updatedActivity);
    }
    
    protected void OnTaskSelected(ActivityModel task)
    {
        // Logic to handle task selection
        OnSelectedTaskChanged.InvokeAsync(task);
    }
    protected bool IsPersonalTaskQueueOrAssignedToCurrentEmployee(ActivityModel task)
    {
        return  IsPersonalTaskQueue(task) || 
               IsAssignedToCurrentEmployee(task);
    }

    private bool IsAssignedToCurrentEmployee(ActivityModel task)
    {
        return task.ScheduledFor == CurrentEmployee.EmployeeGuid;
    }

    protected bool IsPersonalTaskQueue(ActivityModel task)
    {
        return task.QueueGuid == _personalTaskQueueGuid;
    }
}