@using InsureSolutionsHub.Extensions
@using InsureSolutionsHub.Components.Loadings
<div class="my-3 p-3 bg-body rounded shadow-sm">
  <h6 class="border-bottom pb-2 mb-0">
    <i class="bi bi-paperclip me-1 text-primary-emphasis"></i>
    Attachments
    @if (AttachmentModels.Any())
    {
      <span class="badge bg-primary-subtle text-primary-emphasis rounded-circle border border-primary-subtle">
        @AttachmentModels.Count
      </span>
    }
  </h6>
  @if (IsLoadingAttachments)
  {
    <div class="py-1">
      <FormPlaceholder/>
    </div>
  }
  else
  {
    if (AttachmentModels.Any())
    {
      foreach (var attachment in AttachmentModels)
      {
        <div class="d-flex text-body-secondary pt-3">
          @if (attachment.IsUploaded)
          {
            <i class="bi bi-file-earmark-medical-fill  fs-2 me-2"></i>
          }
          else
          {
            <i class="bi bi-file-earmark-medical fs-2 me-2"></i>
          }
          <div class="pb-2 mb-0 small lh-sm border-bottom w-100">
            <div class="d-flex d-inline-grid justify-content-between">
              @if (attachment.ToggleEditFileName)
              {
                <div class="input-group input-group-sm pb-2 pe-2">
                  <input disabled="@(attachment.IsUploading || IsSaving)" type="text"
                         @bind="@attachment.FileNameWithoutExtension"
                         class="form-control border border-light-subtle" placeholder="enter file name"
                         aria-label="File name" required>
                  <span class="input-group-text" id="basic-addon2">@attachment.FileExtension</span>
                  <button disabled="@(attachment.IsUploading || IsSaving)" title="Apply changes"
                          class="btn btn-outline-success border border-success-subtle" type="button"
                          @onclick="() => OnUpdateFileName(attachment)">
                    @if (IsSaving)
                    {
                      <span class="spinner-grow spinner-grow-sm" aria-hidden="true"></span>
                      <span class="visually-hidden" role="status">Loading...</span>
                    }
                    else
                    {
                      <i class="bi bi-check-circle"></i>
                    }
                  </button>
                  <button disabled="@IsSaving" title="Cancel changes" @onclick="() => OnCancelEditFileName(attachment)"
                          class="btn btn-outline-danger border border-danger-subtle" type="button">
                    <i class="bi bi-x-circle "></i>
                  </button>
                </div>
              }
              else
              {
                <span class="text-gray-dark fs-semibold">
                  @attachment.FileName
                  <button disabled="@(attachment.IsUploading || IsSaving || attachment.IsDeleting)" class="btn btn-link px-1 py-0 btn-sm"
                          type="button" @onclick="() => { attachment.ToggleEditFileName = true; }">
                    <i class="bi bi-pencil-fill ms-1"></i>
                  </button>
                </span>
              }
              @if (attachment.IsUploaded)
              {
                <small class="text-muted">Last updated: @attachment.LastWrite.ToString("MM/dd/yyyy HH:mm:ss tt")</small>
              }
              <button disabled="@(attachment.IsUploading || attachment.IsDeleting || IsSaving)" type="button" title="Remove"
                      @onclick="() => OnRemoveAttachment(attachment)"
                      class="btn btn-outline-danger rounded-circle lh-1 p-2  shadow-sm border-danger-subtle border">
                @if (attachment.IsDeleting)
                {
                  <span class="spinner-grow text-danger spinner-grow-sm" aria-hidden="true"></span>
                }
                else
                {
                 <i class="bi bi-trash3"></i>
                }
              </button>
            </div>
            <span class="d-block">
              @if (attachment.IsUploading)
              {
                <div class="my-2">
                  <span class="spinner-grow text-success spinner-grow-sm" aria-hidden="true"></span>
                  <span role="status">uploading...</span>
                </div>
              }
              else
              {
                if (attachment.IsUploaded)
                {
                  <a class="small" href="@attachment.CloudUrl" target="_blank">
                    @attachment.CloudUrl.GetLastSegment() <i class="bi bi-box-arrow-up-right ms-1"></i>
                  </a>
                }
                else
                {
                  <small class=" text-danger">File not uploaded yet</small>
                }
              }
            </span>
            <div class="d-flex justify-content-between py-2">
              <label class="list-group-item d-flex gap-2 ">
                <input disabled="@(attachment.IsUploading || IsSaving || attachment.IsDeleting)" class="form-check-input flex-shrink-0 mt-0 fs-6"
                       type="checkbox"
                       @onchange="(changeEventArgs) => OnShareWithUnderwriter(changeEventArgs,attachment)"
                       checked="@attachment.IsSharedUnderwriter">
                <span class="@(attachment.IsSharedUnderwriter ? "fw-semibold" : "") align-self-center cursor-pointer">
                  Share with Underwriter
                </span>
              </label>
              <label class="list-group-item d-flex gap-2 ">
                <input disabled="@(attachment.IsUploading || IsSaving || attachment.IsDeleting)"
                       @onchange="(changeEventArgs) => OnShareWithClearance(changeEventArgs,attachment)"
                       class="form-check-input flex-shrink-0" type="checkbox" checked="@attachment.IsSubmissionDoc">
                <span class="@(attachment.IsSubmissionDoc ? "fw-semibold" : "") align-self-center cursor-pointer">
                  Share with Clearance
                </span>
              </label>
            </div>
          </div>
        </div>
      }
    }
  }
  <div class="row justify-content-center text-center my-2">
    <div class="col-12 ">
      <label title="Add new attachment"
             class="@(IsLoadingAttachments ? "d-none" : "") @(IsAnyAttachmentUploading()? "" : "btn btn-sm btn-outline-secondary shadow-sm mx-1 border-secondary-subtle border")"
             for="newAttachment">
        <i class="bi bi-plus-circle-dotted me-1"></i>Add file
        <code class="fs-11 fw-semibold text-uppercase">(Max file size is 5MB)</code>
        <InputFile id="newAttachment"
                   class="d-none" disabled="@(IsAnyAttachmentUploading() || IsSaving || IsFileValidating)"
                   OnChange="async (eventArgs) => await ValidateAndUploadFile(eventArgs)"
                   accept="@string.Join(",", DocModelExtensions.AllowedExtensions)"/>
      </label>
    </div>
  </div>
  <div class="d-block text-center mt-1">
    <span class="text-muted text-success-emphasis fs-9">
      @if (IsAnyAttachmentUploading())
      {
        <small>
          Uploading @GetCurrentlyUploadingAttachmentName() ...
        </small>
      }
      @if (IsFileValidating)
      {
        <small>
          Validating file...
        </small>
      }
    </span>
  </div>
</div>