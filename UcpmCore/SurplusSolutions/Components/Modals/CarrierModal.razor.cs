using BlazorStrap.V5;
using InsureSolutionsHub.Models.Carrier;
using InsureSolutionsHub.Shared;
using Microsoft.AspNetCore.Components;
using ServerComponentLibrary.Managers;
using ServerComponentLibrary.Services;
using UcpmApi.Shared;
using UcpmApi.Shared.SurplusLines;
using UcpmTools;

namespace InsureSolutionsHub.Components.Modals
{
    public class CarrierModalBase : InsureSolutionsHubComponentBase
    {
        [Parameter]
        public SurplusLinesCarrier Carrier { get; set; } = new();

        [Parameter]
        public EventCallback OnPerformButtonClick { get; set; }

        [Inject]
        public SurplusLinesCarrierManager SurplusLinesCarrierManager { get; set; }

        [Inject]
        public StateManager StateManager { get; set; }

        public SurplusLinesCarrierModalModel TempCarrier { get; set; } = new();

        public IEnumerable<State> States { get; set; } = [];

        public bool IsLoading { get; set; } = false;

        public string CommandName { get; set; } = string.Empty;

        public string ErrorMessage { get; set; } = string.Empty;

        public BSModal CarrierModal = new();

        protected override async Task OnInitializedAsync()
        {
            IEnumerable<State> states = await StateManager.GetAllStates();
            States = states.Where(state => !string.IsNullOrEmpty(state.Code.Trim()));

            await base.OnInitializedAsync();
        }

        protected override async Task OnParametersSetAsync()
        {
            QuickReflection.CopyProps(Carrier, TempCarrier);
            CommandName = GetCommandName();

            await base.OnParametersSetAsync();
        }

        public async Task ShowModal()
        {
            await CarrierModal.ShowAsync();
        }

        public async Task PerformCommand()
        {
            try
            {
                IsLoading = true;
                CommandName = "Loading...";
                SurplusLinesCarrier request = new();
                QuickReflection.CopyProps(TempCarrier, request);
                bool success = TempCarrier.SurplusLinesCarrierGuid == Guid.Empty ?
                    await SurplusLinesCarrierManager.AddCarrier(request) :
                    await SurplusLinesCarrierManager.EditCarrier(request);

                if (success)
                {
                    await CarrierModal.HideAsync();
                    await OnPerformButtonClick.InvokeAsync();
                }
                else
                {
                    string command = Carrier.SurplusLinesCarrierGuid == Guid.Empty ? "adding" : "editing";
                    ToastService.ShowToast($"Something went wrong while ${command} the carrier.", ToastLevelEnum.Error);
                    ErrorMessage = $"Something went wrong while ${command} the carrier.";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = ex.Message;
            }
            finally
            {
                IsLoading = false;
                CommandName = GetCommandName();
            }
        }

        private string GetCommandName()
        {
            return Carrier.SurplusLinesCarrierGuid == Guid.Empty ? "Add" : "Edit";
        }
    }
}
