@inherits AddSLInsuredModalBase

<BSModal DataId="add-sl-modal" Size="Size.Large" @ref="AddSLInsuredModal">
    <Toast />
    <BSForm Model="AccountDetailModel" OnValidSubmit="AddInsured">
        <DataAnnotationsValidator />
        <BSModalHeader>
            <h5 class="modal-title">Add New Insured</h5>
        </BSModalHeader>
        <BSModalContent>
            <BSValidationSummary />
            <div class="row">
                <div class="col">
                    <div class="row">
                        <div class="col-12 mb-3">
                            <BSLabel class="form-label">Insured Name</BSLabel>
                            <BSInput InputType="InputType.Text" Class="form-control" @bind-Value="AccountDetailModel.InsuredName" required />
                            <BSFeedback For="@(() => AccountDetailModel.InsuredName)" />
                        </div>
                        <div class="col-12 mb-3">
                            <BSLabel class="form-label">Address</BSLabel>
                            <BSInput InputType="InputType.Text" Class="form-control" @bind-Value="AccountDetailModel.Address1" required />
                            <BSFeedback For="@(() => AccountDetailModel.Address1)" />
                        </div>
                        <div class="col-12 mb-3">
                            <BSLabel class="form-label">Address Line 2</BSLabel>
                            <BSInput InputType="InputType.Text" Class="form-control" @bind-Value="AccountDetailModel.Address2" />
                        </div>
                        <div class="col-12 mb-3">
                            <BSLabel class="form-label">City</BSLabel>
                            <BSInput InputType="InputType.Text" Class="form-control" @bind-Value="AccountDetailModel.City" required />
                            <BSFeedback For="@(() => AccountDetailModel.City)" />
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-6">
                            <BSLabel class="form-label">State</BSLabel>
                            <BSInput InputType="InputType.Select" Class="form-select" @bind-Value="AccountDetailModel.StateCode" required>
                                @if (StateList == null || StateList.Count() <= 0)
                                {
                                    <option value="">None</option>
                                }
                                else
                                {
                                    @foreach (var state in StateList)
                                    {
                                        <option value="@state.Code">@state.StateName</option>
                                    }
                                }
                            </BSInput>
                        </div>
                        <div class="col-6">
                            <BSLabel class="form-label">Zip</BSLabel>
                            <BSInput InputType="InputType.Text" Class="form-control" @bind-Value="AccountDetailModel.Zip" required />
                            <BSFeedback For="@(() => AccountDetailModel.Zip)" />
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-6">
                            <BSLabel class="form-label">Country Code</BSLabel>
                            <BSInput InputType="InputType.Select" Class="form-select" @bind-Value="AccountDetailModel.CountryCode">
                                @if (CountryList == null || CountryList.Count() <= 0)
                                {
                                    <option value="">None</option>
                                }
                                else
                                {
                                    @foreach (var country in CountryList)
                                    {
                                        <option value="@country.CountryCodeIsoAlpha2">@country.CountryName</option>
                                    }
                                }
                            </BSInput>
                        </div>
                        <div class="col-md-3 mb-3">
                            <BSLabel class="form-label">RiskCounty</BSLabel>
                            <BSInput InputType="InputType.Text" Class="form-control" @bind-Value="AccountDetailModel.RiskCounty" />
                            <BSFeedback For="@(() => AccountDetailModel.RiskCounty)" />
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-6 mb-3">
                            <BSLabel class="form-label">EffectiveDate</BSLabel>
                            <BSInput InputType="InputType.Date" Class="form-control" @bind-Value="AccountDetailModel.EffectiveDate" />
                            <BSFeedback For="@(() => AccountDetailModel.EffectiveDate)" />
                        </div>
                        <div class="col-6 mb-3">
                            <BSLabel class="form-label">ExpirationDate</BSLabel>
                            <BSInput InputType="InputType.Date" Class="form-control" @bind-Value="AccountDetailModel.ExpirationDate" />
                            <BSFeedback For="@(() => AccountDetailModel.ExpirationDate)" />
                        </div>
                    </div>
                    <div class="col-12 mb-3">
                        <BSLabel class="form-label">NeedByDate</BSLabel>
                        <BSInput InputType="InputType.Date" Class="form-control" @bind-Value="AccountDetailModel.NeedByDate" />
                        <BSFeedback For="@(() => AccountDetailModel.NeedByDate)" />
                    </div>
                    <div class="col">
                        <BSLabel class="form-label">Policy Type</BSLabel>
                        <BSInput InputType="InputType.Select" Class="form-select" @bind-Value="AccountDetailModel.PolicyTypeId" required>
                            @if (PolicyTypeList == null || PolicyTypeList.Count() <= 0)
                            {
                                <option value="">None</option>
                            }
                            else
                            {
                                @foreach (var policyType in PolicyTypeList)
                                {
                                    <option value="@policyType.SurplusLinesPolicyTypeId">@policyType.PolicyType</option>
                                }
                            }
                        </BSInput>
                    </div>
                    <div class="row mb-3">
                        <div class="col-6">
                            <BSLabel class="form-label">Limit Occurrence</BSLabel>
                            <BSInput InputType="InputType.Select" Class="form-select" @bind-Value="AccountDetailModel.LimitOccurrence">
                                @if (LimitList == null || LimitList.Count() <= 0)
                                {
                                    <option value="">None</option>
                                }
                                else
                                {
                                    @foreach (var limit in LimitList)
                                    {
                                        <option value="@limit.LimitsRequestedValue">@limit.ValueFormatted</option>
                                    }
                                }
                            </BSInput>
                        </div>
                        <div class="col-6">
                            <BSLabel class="form-label">Limit Aggregate</BSLabel>
                            <BSInput InputType="InputType.Select" Class="form-select" @bind-Value="AccountDetailModel.LimitAggregate">
                                @if (LimitList == null || LimitList.Count() <= 0)
                                {
                                    <option value="">None</option>
                                }
                                else
                                {
                                    @foreach (var limit in LimitList)
                                    {
                                        <option value="@limit.LimitsRequestedValue">@limit.ValueFormatted</option>
                                    }
                                }
                            </BSInput>
                        </div>
                    </div>
                </div>
            </div>
        </BSModalContent>
        <BSModalFooter>
            <BSButton Class="btn btn-secondary" Target="add-sl-modal">Close</BSButton>
            <BSButton Class="btn cv-save-btn" IsSubmit="true">Save</BSButton>
        </BSModalFooter>
    </BSForm>
</BSModal>
