using BlazorStrap.V5;
using InsureSolutionsHub.Shared;
using Microsoft.AspNetCore.Components;

namespace InsureSolutionsHub.Components.Modals
{
    public class ConfirmationModalBase : InsureSolutionsHubComponentBase
    {
        [Parameter]
        public string? ConfirmationMessage { get; set; }
        [Parameter]
        public EventCallback<bool> OnConfirmation { get; set; }

        public BSModal ConfirmationModal { get; set; }

        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();
        }

        public async Task ShowModal()
        {
            await ConfirmationModal.ShowAsync();
        }

        public async Task CloseModal()
        {
            await ConfirmationModal.HideAsync();
        }

        public async Task ProcessAnswer(bool confirmation)
        {
            await OnConfirmation.InvokeAsync(confirmation);
            await CloseModal();
        }
    }
}
