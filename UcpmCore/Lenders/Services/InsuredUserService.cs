using System.Security.Claims;
using Lenders.Managers.Loans;
using Lenders.Managers.Authentication;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using ServerComponentLibrary.Services.Interfaces;
using UcpmApi.Shared.Survey;

namespace Lenders.Services;

public class InsuredUserService
{
    [CascadingParameter] private Task<AuthenticationState> AuthenticationStateTask { get; set; } = null!;
    private readonly IHttpContextAccessor _httpContextAccessor;
    [CascadingParameter(Name = "InsuredUserGlobal")]
    public InsuredUser InsuredUser { get; set; } = new();

    private readonly ILocalStorageService _localStorageService;
    private readonly LendersPolicyManager _lendersPolicyManager;
    private readonly AuthenticationManager _authenticationManager;

    public InsuredUserService(ILocalStorageService localStorageService,
        LendersPolicyManager lendersPolicyManager,  IHttpContextAccessor httpContextAccessor, AuthenticationManager authenticationManager)
    {
        _localStorageService = localStorageService;
        _lendersPolicyManager = lendersPolicyManager;
        _httpContextAccessor = httpContextAccessor;
        _authenticationManager = authenticationManager;
    }

    public async Task<InsuredUser> GetInsuredUser()
    {
        var context = _httpContextAccessor.HttpContext;
        
        if (context?.User.Identity?.IsAuthenticated == true)
        {
            Claim insuredUserGuid = context.User.Claims.First(a => a.Type == ClaimTypes.NameIdentifier);

            // Check if the insured user is already in local storage
            var insuredUserValue =
                await _localStorageService.GetJsonItem<InsuredUser>($"insured-{insuredUserGuid.Value}");

            if (insuredUserValue != null)
            {
                InsuredUser = insuredUserValue;
                return InsuredUser;
            }

            // If not, load the insured user from the API
            InsuredUser = await _lendersPolicyManager.GetInsuredUserByGuid(Guid.Parse(insuredUserGuid.Value));
            // Store the insured user in local storage
            await _localStorageService.SetJsonItem($"insured-{insuredUserGuid.Value}", InsuredUser);
            return InsuredUser;
        }
        return new InsuredUser();
    }
}