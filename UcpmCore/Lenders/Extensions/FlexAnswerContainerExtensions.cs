using System.Text.Json;
using FlexLibrary;
using FlexLibrary.Models;
using Lenders.Models;
using ServerComponentLibrary.Components.Inputs;
using ServerComponentLibrary.Components.Inputs.Models;
using UcpmApi.Shared;
using UcpmApi.Shared.Flex;
using UcpmApi.Shared.Survey;

namespace Lenders.Extensions;

public static class FlexAnswerContainerExtensions
{
    public static FlexAnswer GetAnswerByDataName(this List<FlexAnswer> flexAnswers, string dataName)
    {
        return flexAnswers.First(answer => answer.QuestionDataName == dataName);
    }

    public static Dictionary<string, string> GetAnswersLenderResponse(this List<FlexAnswer> flexAnswers)
    {
        return flexAnswers.Where(answer => DataNameConstant.LenderResponseData.Contains(answer.QuestionDataName))
            .ToDictionary(answer => answer.QuestionDataName, answer => answer.Answer);
    }
    
    public static object? DeserializeAnswerObject(this FlexAnswer flexAnswer, FlexQuestion question)
    {
        try
        {
            return question.DataType switch
            {
                FlexDataTypeEnum.ContactInfo => JsonSerializer.Deserialize<ContactInfo>(flexAnswer.AnswerObject),
                FlexDataTypeEnum.ZipCode => JsonSerializer.Deserialize<ZipCodeInfo>(flexAnswer.AnswerObject),
                FlexDataTypeEnum.Combobox => JsonSerializer.Deserialize<FlexFactor>(flexAnswer.AnswerObject),
                _ => null
            };
        }
        catch (JsonException ex)
        {
            Console.WriteLine($"Error deserializing answer object for {question.DataName}: {ex.Message}");
            return null;
        }
    }
    
    public static int CountTotalAnswers(this IEnumerable<FlexAnswer> answers)
    {
        var count = 0;
        var answersToCount = new Stack<FlexAnswer>(answers);

        while (answersToCount.Count > 0)
        {
            var answer = answersToCount.Pop();
            count++;

            if (answer.ChildAnswers?.Any() == true)
            {
                foreach (var childAnswer in answer.ChildAnswers)
                {
                    answersToCount.Push(childAnswer);
                }
            }
        }

        return count;
    }
}