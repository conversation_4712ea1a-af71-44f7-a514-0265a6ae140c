using Duende.AccessTokenManagement.OpenIdConnect;
using Microsoft.AspNetCore.Components.Authorization;
using ServerComponentLibrary.Models;
using UcpmApi.Shared.Lender.RequestModels;
using UcpmApi.Shared.Lender.ResponseModels;
using UcpmTools.StringUtilities;

namespace Lenders.Managers.Loans;

public class LendersPolicyLoansManager(
    IHttpClientFactory httpClientFactory,
    IUserTokenStore userTokenStore,
    AuthenticationStateProvider authenticationStateProvider)
    : ManagerBase(httpClientFactory, userTokenStore, authenticationStateProvider)
{
    public async Task<List<LoanQuoteResponse>> GetLenderPoliciesForManageLoans(LoanQuotesRequest request)
    {
        QueryStringBuilder query = new($"{Version}LendersPolicy/GetLenderPoliciesForManageLoans");
        List<LoanQuoteResponse> responses =
            await SendHttpRequest<List<LoanQuoteResponse>, LoanQuotesRequest>(query.ToString(), HttpMethod.Post,
                request);
        return responses.Count == 0 ? [] : responses;
    }
    
    public async Task<List<LoanQuoteResponse>> GetLenderResponsesForPolicy(LoanQuotesRequest request)
    {
        QueryStringBuilder query = new($"{Version}LendersPolicy/GetLenderResponsesForPolicy");
        List<LoanQuoteResponse> responses =
            await SendHttpRequest<List<LoanQuoteResponse>, LoanQuotesRequest>(query.ToString(), HttpMethod.Post,
                request);
        return responses.Count == 0 ? [] : responses;
    }
    
    public async Task<FundLoanResponse> FundLoan(LoanQuoteResponse request)
    {
        QueryStringBuilder query = new($"{Version}LendersPolicy/FundLoan");
        FundLoanResponse response =
            await SendHttpRequest<FundLoanResponse, LoanQuoteResponse>(query.ToString(), HttpMethod.Post, request);
        return response;
    }
}