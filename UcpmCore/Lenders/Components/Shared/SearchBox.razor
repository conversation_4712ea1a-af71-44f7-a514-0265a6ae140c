@implements IDisposable

@code {

    [Parameter]
    public string Value { get; set; }

    [Parameter]
    public string Id { get; set; } = "search-box";

    [Parameter]
    public bool Disabled { get; set; }

    [Parameter]
    public EventCallback<string> ValueChanged { get; set; }

    [Parameter]
    public EventCallback OnDebouncedInput { get; set; }

    [Parameter]
    public string Placeholder { get; set; } = "Search...";

    [Parameter]
    public int DebounceMilliseconds { get; set; } = 400;
    [Parameter]
    public string Class { get; set; } = string.Empty;
    private System.Timers.Timer? _debounceTimer;

    private async Task HandleInput(ChangeEventArgs e)
    {
        var newValue = e.Value?.ToString() ?? string.Empty;
        if (newValue != Value)
        {
            Value = newValue;
            await ValueChanged.InvokeAsync(Value);
        }

        // Reset debounce timer
        _debounceTimer?.Stop();
        _debounceTimer?.Dispose();

        _debounceTimer = new System.Timers.Timer(DebounceMilliseconds);
        _debounceTimer.Elapsed += async (s, args) =>
        {
            _debounceTimer?.Stop();
            _debounceTimer?.Dispose();
            _debounceTimer = null;

            // Switch back to the Blazor UI thread
            await InvokeAsync(async () =>
            {
                if (OnDebouncedInput.HasDelegate)
                    await OnDebouncedInput.InvokeAsync();
            });
        };
        _debounceTimer.AutoReset = false;
        _debounceTimer.Start();
    }

    public void Dispose()
    {
        _debounceTimer?.Stop();
        _debounceTimer?.Dispose();
        _debounceTimer = null;
    }

}

<div class="input-group my-2 search-group @Class">
    <span class="input-group-text shadow-sm border-end-0" id="search-addon">
        <i class="bi bi-search text-secondary"></i>
    </span>
    <input disabled="@(Disabled)" id="@Id" type="text"
           class="form-control shadow-sm border-start-0 bg-body-tertiary"
           placeholder="@Placeholder"
           @oninput="HandleInput"
           value="@Value"
           aria-describedby="search-addon">
</div>