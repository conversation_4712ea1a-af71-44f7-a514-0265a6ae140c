@code {
}
<div class="container p-0">
    <div class="nav-scroller bg-body border border-light-subtle rounded-bottom">
        <nav class="nav" aria-label="">
            <NavLink href="/loans/manage-loans" ActiveClass="active" class="nav-link text-muted sub-menu" Match="NavLinkMatch.All" >
                <i class="bi bi-journals"></i>
                Policy Loans
            </NavLink>
            <NavLink href="/loans/manage-loans/all" ActiveClass="active" class="nav-link text-muted sub-menu" Match="NavLinkMatch.All" >
                <i class="bi bi-journals"></i>
                All Loans
            </NavLink>
            <NavLink href="/loans/archived" ActiveClass="active" class="nav-link text-muted sub-menu" Match="NavLinkMatch.All" >
                <i class="bi bi-archive-fill"></i>
                Archived Loans
            </NavLink>
            <NavLink href="/logs/email-logs" ActiveClass="active" Match="NavLinkMatch.All"  class="nav-link text-muted sub-menu">
                <i class="bi bi-envelope-at-fill me-1"></i>
                Emails
            </NavLink>
            @*<NavLink ActiveClass="active" Match="NavLinkMatch.All" class="nav-link text-muted sub-menu" >
                <i class="bi bi-graph-up-arrow me-1"></i>
                Reports
            </NavLink>
            <NavLink ActiveClass="active" href="/loans/securitize"  class="nav-link text-muted sub-menu" Match="NavLinkMatch.All">
                <i class="bi bi-shield-lock-fill me-1"></i>
                Securitize Loans
            </NavLink>*@
            <a class="nav-link text-muted sub-menu" type="button">
                <i class="bi bi-file-earmark-bar-graph-fill me-1"></i>
                Open Loan Report
            </a>
        </nav>
    </div>
</div>
