@page "/surveys/environmental-questionnaire/{FlexResponseGuid:guid?}"
@using Lenders.Components.Shared
@using ServerComponentLibrary.Components.Forms.Configuration
@layout AdminLayout
@rendermode InteractiveServer
<PageTitle>Environmental Questionnaire</PageTitle>
<div class="container p-0">
    <div class="bg-body border border-light-subtle rounded-bottom">
        <div class="row justify-content-between align-items-center">
            <div class="col d-flex">
                <button type="button" title="Back" class="btn btn-sm lender-btn-primary my-1 shadow-sm mx-2 align-self-center"
                        onclick="history.back()">
                    <i class="bi bi-arrow-return-left"></i>
                </button>
                <h6 class="mb-0 text-start p-2 align-self-center">
                    <i class="bi bi-clipboard-check fs-5"></i>
                    Questionnaire
                </h6>
            </div>
            <div class="col-auto">
                <span
                    class="badge bg-danger-subtle border border-danger-subtle text-danger-emphasis rounded-pill fw-medium fs-10 me-2">
                    Questions marked with 
                    <span class="text-danger fs-6">* </span>
                    are required
                </span>
            </div>
        </div>
    </div>
</div>
<div class="container py-4">
    <div class="row">
        <div class="col-8">
            <!-- Survey Form -->
            <div class="card mb-4 p-3 border-light-subtle shadow-sm">
                <!-- Header Section -->
                <div class="alert alert-info mb-0">
                    <div class="row">
                        <div class="col-md-8">
                            <h6><i class="bi bi-info-circle me-2"></i>Instructions</h6>
                            <p class="mb-2">
                                General & Loan Information - To Be Completed By Lender
                            </p>
                        </div>
                    </div>
                </div>
                <div class="separator my-3 separator-dashed"></div>
                <div class="card-body">
                    <div class="row">
                        <!-- Form Column -->
                        <div class="col-lg-12">
                            @if (IsLoading)
                            {
                                <LoaderOverlay IsOverlayVisible="false"
                                               LoadingText="Loading questions, please wait..."></LoaderOverlay>
                            }
                            else
                            {
                                <FlexDynamicForm @ref="_flexDynamicForm"
                                                 Questions="@LenderSurveyQuestions"
                                                 @bind-FormData="FormData"
                                                 ValidationStateChanged="@OnValidationStateChanged"
                                                 ShowValidationSummary="false"
                                                 LayoutMode="FormLayoutMode.Auto"
                                                 CssClass="survey-form"
                                                 InsuredGuid="@InsuredUser.InsuredGuid"
                                                 ShowLoadingIndicators="true"
                                                 LoadingText="Loading questions, please wait..."
                                                 NewFlexRatingContainer="@NewFlexRatingContainer"
                                />
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Form Status Sidebar -->
        <div class="col-lg-4">
            <div class="sticky-top" style="top: 5rem;">
                @if (IsLoading)
                {
                    <LoaderOverlay IsOverlayVisible="false"
                                   LoadingText="Loading Form Status, please wait..."></LoaderOverlay>
                }
                else
                {
                    <FormStatusComponent
                        Questions="@LenderSurveyQuestions"
                        FormData="@FormData"
                        ValidationStates="@ValidationStates"
                        CssClass="mb-3"
                        FormTitle="@FormStatusTitle"/>
                }
                <!-- Quick Actions Card -->
                <div class="card border-light-subtle shadow-sm">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-lightning me-2"></i>
                            Quick Actions
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-primary btn-sm"
                                    disabled="@(IsSubmitting)"
                                    @onclick="SaveForm">
                                <i class="bi bi-floppy me-1"></i>
                                Save Progress
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm"
                                    @onclick="ClearForm">
                                <i class="bi bi-arrow-counterclockwise me-1"></i>
                                Reset Form
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm"
                                    @onclick="BackToLoansPage">
                                <i class="bi bi-arrow-return-left"></i>
                                Back to Previous Page
                            </button>
                            <hr class="my-2">
                            <button type="button" class="btn lender-btn-primary btn-sm" @onclick="SubmitForm"
                                    disabled="@(!IsFormReadyForSubmission || IsSubmitting)">
                                <i class="bi bi-check-circle me-1"></i>
                                Submit Form
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @if (IsSubmitting)
    {
        <LoaderOverlay LoadingText="@LoadingText"></LoaderOverlay>
    }
</div>