using Microsoft.AspNetCore.Components;
using FlexLibrary;
using FlexLibrary.Models;
using Lenders.Extensions;
using Lenders.Managers.Loans;
using Lenders.Models;
using Lenders.Services;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.JSInterop;
using ServerComponentLibrary.Components.Forms;
using ServerComponentLibrary.Components.Forms.Extensions;
using ServerComponentLibrary.Components.Inputs;
using ServerComponentLibrary.Components.Inputs.Models;
using ServerComponentLibrary.Managers;
using UcpmApi.Shared;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Flex;
using UcpmApi.Shared.Lender;
using UcpmApi.Shared.Lender.ResponseModels;
using UcpmApi.Shared.Survey;

namespace Lenders.Components.Pages.Surveys;

public partial class EnvironmentalQuestionnaire : ComponentBase
{
    private const int LenderFlexDefinitionId = 11;

    [CascadingParameter(Name = "InsuredUserGlobal")]
    public InsuredUser InsuredUser { get; set; } = new();

    [Parameter]
    public Guid? FlexResponseGuid { get; set; }

    private FlexDynamicForm? _flexDynamicForm;
    private Dictionary<string, object?> FormData { get; set; } = new();
    private List<FlexQuestion> LenderSurveyQuestions { get; set; } = [];
    private NewFlexRatingContainer NewFlexRatingContainer { get; set; } = new();

    private Dictionary<string, bool> ValidationStates { get; set; } = new();

    //private bool IsFormValid => ValidationStates.Values.All(v => v);
    private bool? _isFormValidCache;

    private bool IsFormValid
    {
        get
        {
            if (_isFormValidCache.HasValue) return _isFormValidCache.Value;
            _isFormValidCache = ValidationStates.Values.All(v => v);
            return _isFormValidCache.Value;
        }
    }

    private bool IsFormReadyForSubmission => IsFormValid && ValidationStates.Values.All(v => v);

    private LoanPolicyResponse ActivePolicy { get; set; } = new();

    // Cache answer lookup to avoid multiple processing
    private Dictionary<string, FlexAnswer>? _answerLookupCache;
    private Dictionary<string, FlexAnswer> AnswerLookup => _answerLookupCache ??= CreateAnswerLookup();

    private Dictionary<string, FlexQuestion>? _questionLookupCache;
    private Dictionary<string, FlexQuestion>? QuestionLookup => _questionLookupCache ??= CreateQuestionLookup();

    [Inject]
    public FlexPublishedVersionManager FlexPublishedVersionManager { get; set; } = null!;

    [Inject]
    public LendersFlexResponseManager FlexResponseManager { get; set; } = null!;

    [Inject]
    public HybridCache HybridCache { get; set; } = null!;

    [Inject]
    public LendersPolicyManager LendersPolicyManager { get; set; } = null!;

    [Inject]
    public NavigationManager NavigationManager { get; set; } = null!;

    [Inject]
    public IJSRuntime JsRuntime { get; set; } = null!;

    private FlexResponseSaveState FlexResponseSaveState { get; set; } = new();
    private FlexResponse FlexResponse { get; set; } = new();
    private FlexPublishedVersion FlexPublishedVersion { get; set; } = new();
    private bool IsLoading { get; set; }
    private string FormStatusTitle { get; set; } = "Loan Application Form Status";
    private bool IsSubmitting { get; set; }
    private bool IsCleared { get; set; }
    private LoanTransactionTypeEnum LoanTransactionType { get; set; } = LoanTransactionTypeEnum.Draft;
    private SurveySubmittedByEnum SurveySubmittedBy { get; set; } = SurveySubmittedByEnum.NotSubmitted;
    private string SuccessMessage { get; set; } = "Saved successfully.";
    private string LoadingText { get; set; } = "Submitting form, please wait...";

    protected override Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            string message = "⚠️You have unsaved changes. Are you sure you want to leave?";
            if (FlexResponseGuid.HasValue)
            {
                message =
                    "⚠️ Leaving or refreshing this page will create a new draft. Please submit the form before leaving.";
            }

            JsRuntime.InvokeVoidAsync("setupBeforeUnload",
                message);
        }

        return base.OnAfterRenderAsync(firstRender);
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadQuestionsAndPolicy();
    }

    private async Task ClearCache()
    {
        await HybridCache.RemoveAsync($"{InsuredUser.InsuredGuid}-new-flex-response");
    }

    protected override async Task OnParametersSetAsync()
    {
        IsLoading = true;

        try
        {
            if (InsuredUser.InsuredGuid != Guid.Empty)
            {
                ActivePolicy = await GetActivePolicy();
                await ClearCache();
                if (FlexResponseGuid.HasValue)
                {
                    FlexResponse.FlexResponseGuid = FlexResponseGuid.Value;
                    await GetSurveyResponse();
                    ClearAllCaches(); // Clear all caches when new data is loaded
                    // Only initialize form data if we have survey response data
                    InitializeFormDataFromLoadedVersion();
                    InitializeValidationStates();
                }
            }
        }
        finally
        {
            IsLoading = false;
        }
    }

    private void ClearAllCaches()
    {
        _answerLookupCache = null;
        _questionLookupCache = null;
        _isFormValidCache = null;
    }

    private async Task LoadQuestionsAndPolicy()
    {
        try
        {
            IsLoading = true;

            // Load questions and policy in parallel for better performance
            FlexPublishedVersion =
                await FlexPublishedVersionManager.GetFlexPublishedVersionById(LenderFlexDefinitionId);

            GetFlexRating();

            // Only initialize form data if we don't have a specific FlexResponseGuid
            // (OnParametersSetAsync will handle initialization when FlexResponseGuid is present)
            if (!FlexResponseGuid.HasValue)
            {
                InitializeFormDataFromLoadedVersion();
                InitializeValidationStates();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading questions and policy: {ex.Message}");
            FlexPublishedVersion = new FlexPublishedVersion();
            ActivePolicy = new LoanPolicyResponse();
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task GetSurveyResponse()
    {
        try
        {
            if (FlexResponseSaveState.FlexResponseSaveStateGuid != Guid.Empty)
            {
                return;
            }

            FlexResponseSaveState =
                await FlexResponseManager.GetFlexResponseSaveState(FlexResponse.FlexResponseGuid);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading survey response: {ex.Message}");
        }
    }

    private async Task<LoanPolicyResponse> GetActivePolicy()
    {
        var activePolicies = await HybridCache.GetOrCreateAsync(
            $"{InsuredUser.InsuredGuid}-ActivePolicies",
            async cancel => await LendersPolicyManager.GetLenderActivePoliciesForInsured(InsuredUser.InsuredGuid),
            cancellationToken: CancellationToken.None
        );

        return activePolicies.OrderByDescending(d => d.PolicyEffectiveDate).First();
    }

    private void InitializeFormDataFromLoadedVersion()
    {
        // Add comprehensive null checks to prevent deserialization errors
        if (FlexPublishedVersion?.FlexVersion == null)
        {
            Console.WriteLine("FlexPublishedVersion or FlexVersion is null");
            return;
        }

        // Check if FlexQuestionContainer can be safely accessed
        FlexQuestionContainer? questionContainer;
        try
        {
            questionContainer = FlexPublishedVersion.FlexVersion.FlexQuestionContainer;
            if (questionContainer?.FlexSurvey?.NewFlexPages == null)
            {
                Console.WriteLine("FlexQuestionContainer, FlexSurvey, or NewFlexPages is null");
                return;
            }
        }
        catch (ArgumentNullException ex)
        {
            Console.WriteLine($"Error accessing FlexQuestionContainer: {ex.Message}");
            return;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Unexpected error accessing FlexQuestionContainer: {ex.Message}");
            return;
        }

        var firstPage = questionContainer.FlexSurvey.NewFlexPages
            .OrderBy(q => q.PageNumber).FirstOrDefault();

        if (firstPage?.DataNamesOnPage == null)
        {
            Console.WriteLine("First page or DataNamesOnPage is null");
            return;
        }

        var dataNameCount = firstPage.DataNamesOnPage.Count;
        FormData = new Dictionary<string, object?>(dataNameCount);
        LenderSurveyQuestions = new List<FlexQuestion>(dataNameCount);

        if (QuestionLookup == null)
        {
            Console.WriteLine("Question lookup creation failed");
            return;
        }

        foreach (string dataName in firstPage.DataNamesOnPage)
        {
            if (QuestionLookup.TryGetValue(dataName, out var question))
            {
                InitializeFormDataEntry(dataName, question);
                AddQuestionWithChildren(question);
            }
        }
    }

    private Dictionary<string, FlexQuestion>? CreateQuestionLookup()
    {
        try
        {
            // Safe access with null checks to prevent JSON deserialization errors
            var questionContainer = FlexPublishedVersion?.FlexVersion?.FlexQuestionContainer;
            return questionContainer?.FlexSurvey?.FlexQuestions?.ToDictionary(q => q.DataName, q => q);
        }
        catch (ArgumentNullException ex)
        {
            Console.WriteLine($"Null value encountered during question lookup creation: {ex.Message}");
            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating question lookup: {ex.Message}");
            return null;
        }
    }

    private Dictionary<string, FlexAnswer> CreateAnswerLookup()
    {
        if (FlexResponseSaveState.Answers?.FlexAnswers == null)
            return new Dictionary<string, FlexAnswer>();

        // Pre-calculate capacity to avoid dictionary resizing
        var totalAnswers = FlexResponseSaveState.Answers.FlexAnswers.CountTotalAnswers();
        var answerLookup = new Dictionary<string, FlexAnswer>(totalAnswers);

        // Use an iterative approach instead of recursion for better performance
        var answersToProcess = new Stack<FlexAnswer>(FlexResponseSaveState.Answers.FlexAnswers);

        while (answersToProcess.Count > 0)
        {
            var answer = answersToProcess.Pop();
            answerLookup[answer.QuestionDataName] = answer;

            if (answer.ChildAnswers?.Any() == true)
            {
                foreach (var childAnswer in answer.ChildAnswers)
                {
                    answersToProcess.Push(childAnswer);
                }
            }
        }

        return answerLookup;
    }

    private void AddAnswerToLookup(FlexAnswer answer, Dictionary<string, FlexAnswer> answerLookup)
    {
        answerLookup[answer.QuestionDataName] = answer;

        // Add child answers to lookup
        if (answer.ChildAnswers?.Any() == true)
        {
            foreach (var childAnswer in answer.ChildAnswers)
            {
                AddAnswerToLookup(childAnswer, answerLookup);
            }
        }
    }

    private void InitializeFormDataEntry(string dataName, FlexQuestion question)
    {
        if (AnswerLookup.TryGetValue(dataName, out var existingAnswer))
        {
            FormData[dataName] = existingAnswer.Answer;

            if (!string.IsNullOrEmpty(existingAnswer.AnswerObject))
            {
                FormData[$"{dataName}Selected"] = existingAnswer.DeserializeAnswerObject(question);
            }
        }
        else
        {
            FormData[dataName] = null;
        }
    }

    private void AddQuestionWithChildren(FlexQuestion question)
    {
        question.EnsureVisibilityRuleContainer();
        LenderSurveyQuestions.Add(question);
        // Process child questions recursively
        ProcessChildQuestions(question);
    }

    private void ProcessChildQuestions(FlexQuestion parentQuestion)
    {
        if (parentQuestion.ChildQuestions?.Any() != true)
            return;

        foreach (var childQuestion in parentQuestion.ChildQuestions)
        {
            childQuestion.EnsureVisibilityRuleContainer();

            // Initialize form data for child question if it has existing answer
            InitializeFormDataEntry(childQuestion.DataName, childQuestion);

            // Add child question to the list
            //LenderSurveyQuestions.Add(childQuestion);

            // Recursively process nested child questions
            ProcessChildQuestions(childQuestion);
        }
    }

    private void GetFlexRating()
    {
        var firstCarrierOffer = FlexPublishedVersion.FlexVersion?.FlexVersionCarrierOffer?.FirstOrDefault();
        if (firstCarrierOffer != null)
        {
            NewFlexRatingContainer = firstCarrierOffer.FlexRatingContainer;
        }
    }

    private void InitializeValidationStates()
    {
        ValidationStates = LenderSurveyQuestions.ToDictionary(q => q.DataName, q => EvaluateInitialValidationState(q));
        //StateHasChanged();
    }

    private bool EvaluateInitialValidationState(FlexQuestion question)
    {
        // Calculated fields and non-required fields are always valid initially
        if (!question.FlexQuestionRequired || question.IsCalculatedField) return true;

        // For required fields, check if there's already an answer present
        if (FormData.TryGetValue(question.DataName, out var existingValue))
        {
            return FieldValidationExtensions.IsAnswerPresent(existingValue, question.DataType, question.DataName,
                AnswerLookup);
        }

        // No answer present for required field
        return false;
    }

    private void OnValidationStateChanged(Dictionary<string, bool> validationStates)
    {
        Console.WriteLine(
            $"Validation changed. Invalid fields: {string.Join(", ", validationStates.Where(v => !v.Value).Select(v => v.Key))}");
        ValidationStates = validationStates;
        _isFormValidCache = null;
        StateHasChanged(); // Force re-render to update FormStatusComponent
    }

    private FlexAnswerContainer ComposeFlexAnswerContainer(Guid flexResponseGuid)
    {
        var flexAnswerContainer = new FlexAnswerContainer
        {
            FlexAnswers = new List<FlexAnswer>(LenderSurveyQuestions.Count),
            IsCleared = IsCleared,
            OptionalExpiringDates = [],
            SelectedDeductibles = [],
            SelectedLimits = [],
            InsuredGuid = InsuredUser.InsuredGuid
        };

        foreach (var question in LenderSurveyQuestions)
        {
            var answer = CreateAnswerWithChildren(question, flexResponseGuid);

            if (answer != null)
            {
                flexAnswerContainer.FlexAnswers.Add(answer);
            }
        }

        return flexAnswerContainer;
    }

    private FlexAnswer? CreateAnswerWithChildren(FlexQuestion question, Guid flexResponseGuid)
    {
        var answer = CreateFlexAnswer(question);
        if (answer == null)
            return null;

        answer.FlexResponseGuid = flexResponseGuid;
        // Add child answers if they exist
        if (question.ChildQuestions?.Any() == true)
        {
            answer.ChildAnswers = [];
            foreach (var childQuestion in question.ChildQuestions)
            {
                var childAnswer = CreateAnswerWithChildren(childQuestion, flexResponseGuid);
                if (childAnswer != null) answer.ChildAnswers.Add(childAnswer);
            }
        }

        return answer;
    }

    private FlexAnswer? CreateFlexAnswer(FlexQuestion question)
    {
        if (!FormData.TryGetValue(question.DataName, out var formValue) || formValue == null)
            return null;

        return new FlexAnswer
        {
            QuestionDataName = question.DataName,
            Answer = question.DataType.ConvertAnswerByDataType(formValue),
            AnswerObject = ConvertToDataType(formValue, question),
            RowNumber = 0
        };
    }

    private string? ConvertToDataType(object? value, FlexQuestion question)
    {
        if (value == null) return null;

        try
        {
            return question.DataType switch
            {
                FlexDataTypeEnum.Combobox => FormData.TryGetSelectedValue<FlexFactor>(question.DataName),
                FlexDataTypeEnum.ContactInfo => FormData.TryGetSelectedValue<ContactInfo>(question.DataName),
                FlexDataTypeEnum.ZipCode => FormData.TryGetSelectedValue<ZipCodeInfo>(question.DataName),
                _ => null
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine(
                $"Error converting value '{value}' to type {question.DataType} for field {question.DataName}: {ex.Message}");
            return null;
        }
    }

    private async Task GetOrCreateFlexResponse()
    {
        var cacheKey = $"{InsuredUser.InsuredGuid}-new-flex-response";

        if (FlexResponse.FlexResponseGuid != Guid.Empty)
        {
            FlexResponse.FlexDefinitionId = FlexPublishedVersion.FlexDefinitionId;
            await HybridCache.SetAsync(cacheKey, FlexResponse, cancellationToken: CancellationToken.None);
            return;
        }

        FlexResponse = await HybridCache.GetOrCreateAsync(
            cacheKey,
            async _ =>
            {
                var newResponse = new FlexResponse
                {
                    FlexResponseGuid = Guid.NewGuid(),
                    FlexDefinitionId = FlexPublishedVersion.FlexDefinitionId
                };
                return await FlexResponseManager.SaveFlexResponse(newResponse);
            },
            cancellationToken: CancellationToken.None
        );
    }

    private async Task SaveForm()
    {
        // Implement save logic
        LoadingText = "Saving progress...";
        await GetOrCreateFlexResponse();
        IsCleared = false;
        SuccessMessage = "Your progress has been saved.";
        var success = await ProcessFormSubmission();
        if (success)
        {
            NavigationManager.NavigateTo($"/loans/loan-details/{FlexResponse.FlexResponseGuid}");
        }
    }

    private async Task SubmitForm()
    {
        if (!IsFormValid)
        {
            StateHasChanged();
            return;
        }

        LoanTransactionType = LoanTransactionTypeEnum.Open;
        SurveySubmittedBy = SurveySubmittedByEnum.Insured;
        SuccessMessage = "Your application has been submitted.";
        IsCleared = true;
        var success = await ProcessFormSubmission();
        if (success)
        {
            // Navigate to next page or show success message
            //TODO: Navigate preview page
            NavigationManager.NavigateTo($"/loans/loan-details/{FlexResponse.FlexResponseGuid}");
        }
    }

    private async Task<bool> ProcessFormSubmission()
    {
        try
        {
            IsSubmitting = true;

            await GetOrCreateFlexResponse();

            if (FlexResponse.FlexResponseGuid == Guid.Empty)
            {
                BootstrapToastService.ShowError("Failed to save flex response.");
                return false;
            }
            else
            {
                var flexAnswerContainer = ComposeFlexAnswerContainer(FlexResponse.FlexResponseGuid);
                var saveState = CreateFlexResponseSaveState(flexAnswerContainer);

                var success = await FlexResponseManager.SaveFlexResponseSaveState(saveState);

                var lenderResponse = CreateLenderResponse(flexAnswerContainer);
                LenderResponse savedLenderResponse = await FlexResponseManager.SaveLenderResponse(lenderResponse);

                var isSuccessful = (success && savedLenderResponse.FlexResponseGuid != Guid.Empty);
                BootstrapToastService.ShowMessage(
                    isSuccessful ? SuccessMessage : "Failed to save response state.",
                    isSuccessful ? ToastType.Success : ToastType.Error, "Form Submission"
                );
                return success;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error submitting form: {ex.Message}");
            BootstrapToastService.ShowError("An error occurred while submitting the form.");
            return false;
        }
        finally
        {
            IsSubmitting = false;
        }
    }

    private FlexResponseSaveState CreateFlexResponseSaveState(FlexAnswerContainer answers)
    {
        return new FlexResponseSaveState
        {
            Answers = answers,
            FlexResponseGuid = FlexResponse.FlexResponseGuid,
            FlexVersionGuid = FlexPublishedVersion.FlexVersion.FlexVersionGuid,
            WasUserSubmit = true,
            CurrentlySignedInGuid = InsuredUser.InsuredUserGuid,
            SavedBySecurityAccountGuid = InsuredUser.InsuredUserGuid,
            SavedData = answers.ToJson(),
            SavedAt = DateTimeOffset.Now,
            FlexResponseSaveStateGuid = Guid.NewGuid(),
            Status = "Submitted",
            PolicyProspectGuid = ActivePolicy.PolicyProspectGuid,
            IsRenewal = false,
            Questions = FlexPublishedVersion.FlexVersion.FlexQuestionContainer,
            Ratings = [NewFlexRatingContainer]
        };
    }

    private LenderResponse CreateLenderResponse(FlexAnswerContainer answers)
    {
        Dictionary<string, string> lenderResponseAnswers = answers.FlexAnswers.GetAnswersLenderResponse();

        return new LenderResponse
        {
            FlexResponseGuid = FlexResponse.FlexResponseGuid,
            PolicyProspectGuid = ActivePolicy.PolicyProspectGuid,
            BorrowerName = lenderResponseAnswers.GetValueOrDefault(DataNameConstant.BorrowerName, string.Empty),
            AddressSummary = lenderResponseAnswers.GetValueOrDefault(DataNameConstant.AddressSummary, string.Empty),
            AnticipatedClosingDate = lenderResponseAnswers.GetValueOrDefault(DataNameConstant.AnticipatedClosingDate)
                .ParseDateSafely(),
            LoanAmount = lenderResponseAnswers.GetValueOrDefault(DataNameConstant.LoanAmount).ParseDecimalSafely(),
            LoanNumber = lenderResponseAnswers.GetValueOrDefault(DataNameConstant.LoanNumber, string.Empty),
            SubmittedBy = (int)SurveySubmittedBy,
            LastUpdated = DateTimeOffset.UtcNow,
            LoanTransactionTypeId = (int)LoanTransactionType
        };
    }

    private void BackToLoansPage()
    {
        NavigationManager.NavigateTo("/loans/manage-loans/all");
    }

    private Task ClearForm()
    {
        // Reset form data
        FormData.Clear();
        ValidationStates.Clear();

        InitializeFormDataFromLoadedVersion();
        InitializeValidationStates();

        StateHasChanged();
        return Task.CompletedTask;
    }
}