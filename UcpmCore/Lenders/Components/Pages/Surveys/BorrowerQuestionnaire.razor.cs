using System.Text.Json;
using Microsoft.AspNetCore.Components;
using FlexLibrary;
using FlexLibrary.Models;
using Lenders.Extensions;
using Lenders.Managers.Loans;
using Lenders.Models;
using Lenders.Services;
using Microsoft.Extensions.Caching.Hybrid;
using ServerComponentLibrary.Components.Forms;
using ServerComponentLibrary.Components.Forms.Extensions;
using ServerComponentLibrary.Components.Inputs;
using ServerComponentLibrary.Components.Inputs.Models;
using ServerComponentLibrary.Managers;
using UcpmApi.Shared;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Flex;
using UcpmApi.Shared.Lender;
using UcpmApi.Shared.Lender.ResponseModels;
using UcpmApi.Shared.Survey;

namespace Lenders.Components.Pages.Surveys;

public partial class BorrowerQuestionnaire : ComponentBase
{
    private const int LenderFlexDefinitionId = 11;

    private const int BorrowerQuestionnairePageNumber = 6;

    [CascadingParameter(Name = "InsuredUserGlobal")]
    public InsuredUser InsuredUser { get; set; } = new();

    [Parameter]
    public Guid? FlexResponseGuid { get; set; }

    private FlexDynamicForm flexDynamicForm;
    private bool IsValidForm => flexDynamicForm?.IsFormValid() ?? false;
    private List<string> InvalidFields => flexDynamicForm.GetInvalidFields();
    private Dictionary<string, object?> FormData { get; set; } = new();
    private List<FlexQuestion> LenderSurveyQuestions { get; set; } = [];
    private NewFlexRatingContainer NewFlexRatingContainer { get; set; } = new();
    private Dictionary<string, bool> ValidationStates { get; set; } = new();
    private bool IsFormValid => ValidationStates.Values.All(v => v);
    private bool IsFormReadyForSubmission => IsFormValid && ValidationStates.Values.All(v => v);
    private LoanPolicyResponse ActivePolicy { get; set; } = new();

    [Inject]
    public FlexPublishedVersionManager FlexPublishedVersionManager { get; set; } = null!;

    [Inject]
    public LendersFlexResponseManager FlexResponseManager { get; set; } = null!;

    [Inject]
    public HybridCache HybridCache { get; set; } = null!;

    [Inject]
    public LendersPolicyManager LendersPolicyManager { get; set; } = null!;

    private FlexResponseSaveState FlexResponseSaveState { get; set; } = new();
    private FlexResponse FlexResponse { get; set; } = new();
    private FlexPublishedVersion FlexPublishedVersion { get; set; } = new();
    private bool IsLoading { get; set; }
    private string FormStatusTitle { get; set; } = "Loan Application Form Status";
    private bool IsSubmitting { get; set; }
    private bool IsCleared { get; set; }
    private LoanTransactionTypeEnum LoanTransactionType { get; set; } = LoanTransactionTypeEnum.Draft;
    private SurveySubmittedByEnum SurveySubmittedBy { get; set; } = SurveySubmittedByEnum.NotSubmitted;
    private string SuccessMessage { get; set; } = "Saved successfully.";

    protected override async Task OnInitializedAsync()
    {
        await LoadQuestionsAndPolicy();
    }

    private async Task ClearCache()
    {
        await HybridCache.RemoveAsync($"{InsuredUser.InsuredGuid}-new-flex-response");
    }

    protected override async Task OnParametersSetAsync()
    {
        IsLoading = true;

        try
        {
            if (InsuredUser.InsuredGuid != Guid.Empty)
            {
                ActivePolicy = await GetActivePolicy();
                await ClearCache();
                if (FlexResponseGuid.HasValue)
                {
                    FlexResponse.FlexResponseGuid = FlexResponseGuid.Value;

                    await GetSurveyResponse();

                    // Only initialize form data if we have survey response data
                    InitializeFormDataFromLoadedVersion();
                    InitializeValidationStates();
                }
            }
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadQuestionsAndPolicy()
    {
        try
        {
            IsLoading = true;

            // Load questions and policy in parallel for better performance
            var loadQuestionsTask = LoadFlexPublishedVersion();

            await Task.WhenAll(loadQuestionsTask);

            FlexPublishedVersion = await loadQuestionsTask;

            GetFlexRating();

            // Only initialize form data if we don't have a specific FlexResponseGuid
            // (OnParametersSetAsync will handle initialization when FlexResponseGuid is present)
            if (!FlexResponseGuid.HasValue)
            {
                InitializeFormDataFromLoadedVersion();
                InitializeValidationStates();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading questions and policy: {ex.Message}");
            FlexPublishedVersion = new FlexPublishedVersion();
            ActivePolicy = new LoanPolicyResponse();
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task<FlexPublishedVersion> LoadFlexPublishedVersion()
    {
        return await FlexPublishedVersionManager.GetFlexPublishedVersionById(LenderFlexDefinitionId);
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            //flexDynamicForm.IsFormValid();
        }
    }

    private async Task GetSurveyResponse()
    {
        try
        {
            if (FlexResponseSaveState.FlexResponseSaveStateGuid != Guid.Empty)
            {
                return;
            }

            FlexResponseSaveState =
                await FlexResponseManager.GetFlexResponseSaveState(FlexResponse.FlexResponseGuid);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading survey response: {ex.Message}");
        }
    }

    private async Task<LoanPolicyResponse> GetActivePolicy()
    {
        var activePolicies = await HybridCache.GetOrCreateAsync(
            $"{InsuredUser.InsuredGuid}-ActivePolicies",
            async cancel => await LendersPolicyManager.GetLenderActivePoliciesForInsured(InsuredUser.InsuredGuid),
            cancellationToken: CancellationToken.None
        );

        return activePolicies.OrderByDescending(d => d.PolicyEffectiveDate).First();
    }

    private void InitializeFormDataFromLoadedVersion()
    {
        // Add null check for FlexVersion and ensure FlexQuestionContainer is not null
        if (FlexPublishedVersion?.FlexVersion?.FlexQuestionContainer?.FlexSurvey?.NewFlexPages == null)
            return;

        var borrowerQuestionnairePage = FlexPublishedVersion.FlexVersion.FlexQuestionContainer.FlexSurvey.NewFlexPages
            .FirstOrDefault(q => q.PageNumber == BorrowerQuestionnairePageNumber);

        if (borrowerQuestionnairePage?.DataNamesOnPage == null)
            return;

        var dataNameCount = borrowerQuestionnairePage.DataNamesOnPage.Count;
        FormData = new Dictionary<string, object?>(dataNameCount);
        LenderSurveyQuestions = new List<FlexQuestion>(dataNameCount);

        var questionLookup = CreateQuestionLookup();
        var answerLookup = CreateAnswerLookup();

        foreach (string dataName in borrowerQuestionnairePage.DataNamesOnPage)
        {
            InitializeFormDataEntry(dataName, answerLookup);
            AddQuestionWithChildren(dataName, questionLookup);
        }
    }

    private Dictionary<string, FlexQuestion>? CreateQuestionLookup()
    {
        // Add null check to prevent accessing FlexQuestionContainer when it's null
        return FlexPublishedVersion?.FlexVersion?.FlexQuestionContainer?.FlexSurvey?.FlexQuestions
            ?.ToDictionary(q => q.DataName, q => q);
    }

    private Dictionary<string, FlexAnswer> CreateAnswerLookup()
    {
        var answerLookup = new Dictionary<string, FlexAnswer>();

        if (FlexResponseSaveState.Answers?.FlexAnswers == null)
            return answerLookup;

        foreach (var answer in FlexResponseSaveState.Answers.FlexAnswers)
        {
            AddAnswerToLookup(answer, answerLookup);
        }

        return answerLookup;
    }

    private void AddAnswerToLookup(FlexAnswer answer, Dictionary<string, FlexAnswer> answerLookup)
    {
        answerLookup[answer.QuestionDataName] = answer;

        // Add child answers to lookup
        if (answer.ChildAnswers?.Any() == true)
        {
            foreach (var childAnswer in answer.ChildAnswers)
            {
                AddAnswerToLookup(childAnswer, answerLookup);
            }
        }
    }

    private void InitializeFormDataEntry(string dataName, Dictionary<string, FlexAnswer> answerLookup)
    {
        FormData[dataName] = answerLookup.TryGetValue(dataName, out var existingAnswer)
            ? existingAnswer.Answer
            : null;
    }

    private void AddQuestionWithChildren(string dataName, Dictionary<string, FlexQuestion>? questionLookup)
    {
        if (questionLookup?.TryGetValue(dataName, out var question) != true)
            return;

        EnsureVisibilityRuleContainer(question);
        LenderSurveyQuestions.Add(question);

        // Process child questions recursively
        ProcessChildQuestions(question);
    }

    private void ProcessChildQuestions(FlexQuestion parentQuestion)
    {
        if (parentQuestion.ChildQuestions?.Any() != true)
            return;

        foreach (var childQuestion in parentQuestion.ChildQuestions)
        {
            EnsureVisibilityRuleContainer(childQuestion);

            // Initialize form data for child question if it has existing answer
            var answerLookup = CreateAnswerLookup();
            InitializeFormDataEntry(childQuestion.DataName, answerLookup);

            // Add child question to the list
            //LenderSurveyQuestions.Add(childQuestion);

            // Recursively process nested child questions
            ProcessChildQuestions(childQuestion);
        }
    }

    private static void EnsureVisibilityRuleContainer(FlexQuestion question)
    {
        question.VisibilityRuleContainer ??= new VisibilityRuleContainer
        {
            VisibilityRules = [],
            BooleanOpCode = "AND"
        };
    }

    private void GetFlexRating()
    {
        var firstCarrierOffer = FlexPublishedVersion.FlexVersion?.FlexVersionCarrierOffer?.FirstOrDefault();
        if (firstCarrierOffer != null)
        {
            NewFlexRatingContainer = firstCarrierOffer.FlexRatingContainer;
        }
    }

    private void InitializeValidationStates()
    {
        ValidationStates = LenderSurveyQuestions.ToDictionary(q => q.DataName, q => EvaluateInitialValidationState(q));
        //StateHasChanged();
    }

    private bool EvaluateInitialValidationState(FlexQuestion question)
    {
        // Calculated fields and non-required fields are always valid initially
        if (!question.FlexQuestionRequired || question.IsCalculatedField) return true;

        // For required fields, check if there's already an answer present
        if (FormData.TryGetValue(question.DataName, out var existingValue))
            return IsAnswerPresent(existingValue, question.DataType, question.DataName);

        // No answer present for required field
        return false;
    }

    private bool IsAnswerPresent(object? value, FlexDataTypeEnum dataType, string dataName)
    {
        // Create answer lookup for complex object validation
        var answerLookup = CreateAnswerLookup();
        return FieldValidationExtensions.IsAnswerPresent(value, dataType, dataName, answerLookup);
    }

    private void OnValidationStateChanged(Dictionary<string, bool> validationStates)
    {
        Console.WriteLine(
            $"Validation changed. Invalid fields: {string.Join(", ", validationStates.Where(v => !v.Value).Select(v => v.Key))}");
        ValidationStates = validationStates;
        StateHasChanged(); // Force re-render to update FormStatusComponent
    }

    private FlexAnswerContainer ComposeFlexAnswerContainer(Guid flexResponseGuid)
    {
        var flexAnswerContainer = new FlexAnswerContainer
        {
            FlexAnswers = [],
            IsCleared = IsCleared,
            OptionalExpiringDates = [],
            SelectedDeductibles = [],
            SelectedLimits = [],
            InsuredGuid = InsuredUser.InsuredGuid
        };

        foreach (var question in LenderSurveyQuestions)
        {
            var answer = CreateAnswerWithChildren(question, flexResponseGuid);

            if (answer != null) flexAnswerContainer.FlexAnswers.Add(answer);
        }

        return flexAnswerContainer;
    }

    private FlexAnswer? CreateAnswerWithChildren(FlexQuestion question, Guid flexResponseGuid)
    {
        var answer = CreateFlexAnswer(question);
        if (answer == null)
            return null;

        answer.FlexResponseGuid = flexResponseGuid;
        // Add child answers if they exist
        if (question.ChildQuestions?.Any() == true)
        {
            answer.ChildAnswers = [];
            foreach (var childQuestion in question.ChildQuestions)
            {
                var childAnswer = CreateAnswerWithChildren(childQuestion, flexResponseGuid);
                if (childAnswer != null) answer.ChildAnswers.Add(childAnswer);
            }
        }

        return answer;
    }

    private FlexAnswer? CreateFlexAnswer(FlexQuestion question)
    {
        if (!FormData.TryGetValue(question.DataName, out var formValue) || formValue == null)
            return null;

        return new FlexAnswer
        {
            QuestionDataName = question.DataName,
            Answer = ConvertAnswerByDataType(formValue, question.DataType),
            AnswerObject = ConvertToDataType(formValue, question),
            RowNumber = 0
        };
    }

    private string ConvertAnswerByDataType(object value, FlexDataTypeEnum dataType)
    {
        return dataType switch
        {
            FlexDataTypeEnum.Boolean => value?.ToString()?.ToLower() ?? "false",
            FlexDataTypeEnum.Number => value?.ToString() ?? "0",
            FlexDataTypeEnum.Money => value?.ToString() ?? "0",
            FlexDataTypeEnum.Date => value is DateTime date
                ? date.ToString("d")
                : value?.ToString() ?? "",
            _ => value?.ToString() ?? ""
        };
    }

    private string? ConvertToDataType(object? value, FlexQuestion question)
    {
        if (value == null) return null;

        try
        {
            return question.DataType switch
            {
                FlexDataTypeEnum.Combobox => TryGetSelectedValue<FlexFactor>(question.DataName),
                FlexDataTypeEnum.ContactInfo => TryGetSelectedValue<ContactInfo>(question.DataName),
                FlexDataTypeEnum.ZipCode => TryGetSelectedValue<ZipCodeInfo>(question.DataName),
                _ => null
            };
        }
        catch (Exception ex)
        {
            Console.WriteLine(
                $"Error converting value '{value}' to type {question.DataType} for field {question.DataName}: {ex.Message}");
            return null;
        }
    }

    private string? TryGetSelectedValue<T>(string dataName) where T : class
    {
        var selectedKey = $"{dataName}Selected";
        return FormData.TryGetValue(selectedKey, out var selectedValue) && selectedValue is T typedValue
            ? JsonSerializer.Serialize(typedValue)
            : null;
    }

    private async Task GetOrCreateFlexResponse()
    {
        var cacheKey = $"{InsuredUser.InsuredGuid}-new-flex-response";

        if (FlexResponse.FlexResponseGuid != Guid.Empty)
        {
            FlexResponse.FlexDefinitionId = FlexPublishedVersion.FlexDefinitionId;
            await HybridCache.SetAsync(cacheKey, FlexResponse, cancellationToken: CancellationToken.None);
            return;
        }

        FlexResponse = await HybridCache.GetOrCreateAsync(
            cacheKey,
            async _ =>
            {
                var newResponse = new FlexResponse
                {
                    FlexResponseGuid = Guid.NewGuid(),
                    FlexDefinitionId = FlexPublishedVersion.FlexDefinitionId
                };
                return await FlexResponseManager.SaveFlexResponse(newResponse);
            },
            cancellationToken: CancellationToken.None
        );
    }

    private async Task SaveForm()
    {
        // Implement save logic
        await GetOrCreateFlexResponse();

        var success = await ProcessFormSubmission();
        if (success)
        {
            Console.WriteLine("Form submitted successfully");
        }
    }

    private async Task SubmitForm()
    {
        if (!IsFormValid)
        {
            StateHasChanged();
            return;
        }

        LoanTransactionType = LoanTransactionTypeEnum.Open;
        SurveySubmittedBy = SurveySubmittedByEnum.Insured;
        SuccessMessage = "Your application has been submitted.";
        var success = await ProcessFormSubmission();
        if (success)
        {
            // Navigate to next page or show success message
            //TODO: Navigate preview page
            Console.WriteLine("Form submitted successfully");
        }
    }

    private async Task<bool> ProcessFormSubmission()
    {
        try
        {
            IsSubmitting = true;

            await GetOrCreateFlexResponse();

            if (FlexResponse.FlexResponseGuid == Guid.Empty)
            {
                BootstrapToastService.ShowError("Failed to save flex response.");
                return false;
            }
            else
            {
                var flexAnswerContainer = ComposeFlexAnswerContainer(FlexResponse.FlexResponseGuid);
                var saveState = CreateFlexResponseSaveState(flexAnswerContainer);

                var success = await FlexResponseManager.SaveFlexResponseSaveState(saveState);

                var lenderResponse = CreateLenderResponse(flexAnswerContainer);
                LenderResponse savedLenderResponse = await FlexResponseManager.SaveLenderResponse(lenderResponse);

                var isSuccessful = (success && savedLenderResponse.FlexResponseGuid != Guid.Empty);
                BootstrapToastService.ShowMessage(
                    isSuccessful ? SuccessMessage : "Failed to save response state.",
                    isSuccessful ? ToastType.Success : ToastType.Error, "Form Submission"
                );
                return success;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error submitting form: {ex.Message}");
            BootstrapToastService.ShowError("An error occurred while submitting the form.");
            return false;
        }
        finally
        {
            IsSubmitting = false;
        }
    }

    private FlexResponseSaveState CreateFlexResponseSaveState(FlexAnswerContainer answers)
    {
        return new FlexResponseSaveState
        {
            Answers = answers,
            FlexResponseGuid = FlexResponse.FlexResponseGuid,
            FlexVersionGuid = FlexPublishedVersion.FlexVersion.FlexVersionGuid,
            WasUserSubmit = true,
            CurrentlySignedInGuid = InsuredUser.InsuredUserGuid,
            SavedBySecurityAccountGuid = InsuredUser.InsuredUserGuid,
            SavedData = answers.ToJson(),
            SavedAt = DateTimeOffset.Now,
            FlexResponseSaveStateGuid = Guid.NewGuid(),
            Status = "Submitted",
            PolicyProspectGuid = ActivePolicy.PolicyProspectGuid,
            IsRenewal = false,
            Questions = FlexPublishedVersion.FlexVersion.FlexQuestionContainer,
            Ratings = [NewFlexRatingContainer]
        };
    }

    private LenderResponse CreateLenderResponse(FlexAnswerContainer answers)
    {
        Dictionary<string, string> lenderResponseAnswers = answers.FlexAnswers.GetAnswersLenderResponse();

        return new LenderResponse
        {
            FlexResponseGuid = FlexResponse.FlexResponseGuid,
            PolicyProspectGuid = ActivePolicy.PolicyProspectGuid,
            BorrowerName = lenderResponseAnswers.GetValueOrDefault(DataNameConstant.BorrowerName, string.Empty),
            AddressSummary = lenderResponseAnswers.GetValueOrDefault(DataNameConstant.AddressSummary, string.Empty),
            AnticipatedClosingDate = lenderResponseAnswers.GetValueOrDefault(DataNameConstant.AnticipatedClosingDate)
                .ParseDateSafely(),
            LoanAmount = lenderResponseAnswers.GetValueOrDefault(DataNameConstant.LoanAmount).ParseDecimalSafely(),
            LoanNumber = lenderResponseAnswers.GetValueOrDefault(DataNameConstant.LoanNumber, string.Empty),
            SubmittedBy = (int)SurveySubmittedBy,
            LastUpdated = DateTimeOffset.UtcNow,
            LoanTransactionTypeId = (int)LoanTransactionType
        };
    }

    private async Task ClearForm()
    {
        // Reset form data
        FormData.Clear();
        ValidationStates.Clear();

        //InitializeFormData();

        StateHasChanged();
    }

    private async Task PreviewForm()
    {
        // Implement form preview logic
        Console.WriteLine("Form preview requested");
    }
}