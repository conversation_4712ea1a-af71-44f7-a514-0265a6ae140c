using Microsoft.AspNetCore.Components;
using UcpmApi.Shared.Enums;

namespace Lenders.Components.Pages.Loans;

public partial class LoanDetailsPage : ComponentBase
{
    [Parameter]
    public Guid ResponseGuid { get; set; }

    private LoanTransactionTypeEnum LoanStatus { get; set; } = LoanTransactionTypeEnum.Open;

    private void OnLoanStatusChanged(LoanTransactionTypeEnum newStatus)
    {
        LoanStatus = newStatus;
    }
}