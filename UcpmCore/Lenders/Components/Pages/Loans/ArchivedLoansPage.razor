@page "/loans/archived"
@using Lenders.Components.Shared
@using UcpmApi.Shared.Enums
@layout AdminLayout
@rendermode InteractiveServer

<SubNavMenu/>
<div class="container py-4 px-0">
    <div class="mb-3 p-3 bg-body rounded shadow-sm">
        <div class="row px-2 align-items-start justify-content-between">
            <div class="col d-flex gap-1 mt-1 d-flex-row align-self-center justify-content-start px-2">
                <span class="h6">
                    <i class="bi bi-archive me-1"></i>
                    <span class="">
                        List of Archived Loans
                    </span>
                </span>
            </div>
        </div>
        <div class="separator separator-dashed my-2"></div>
        <div class="row  px-2 align-items-start justify-content-between">
            <div class="col d-flex gap-1 d-flex-row align-self-center justify-content-start px-0">
                <button class="btn btn-outline-secondary border-secondary-subtle btn-sm shadow-sm"
                        title="Reload Archived Loans"
                        @onclick="OnReloadHandler">
                    <i class="bi bi-arrow-clockwise "></i>
                </button>
                <div class="dropdown">
                    <button disabled="@(IsReloading)" aria-label="Sort Loans"
                            class="btn btn-sm btn-outline-secondary shadow-sm border-secondary-subtle dropdown-toggle"
                            type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        @if (SelectedSortType != null)
                        {
                            switch (SelectedSortType)
                            {
                                case LenderSortTypeEnum.LoanNumberAscending:
                                    <span>
                                        <i class="bi bi-sort-up me-1"></i> Loan Number Ascending
                                    </span>
                                    break;
                                case LenderSortTypeEnum.LoanNumberDescending:
                                    <span>
                                        <i class="bi bi-sort-down me-1"></i> Loan Number Descending
                                    </span>
                                    break;
                                case LenderSortTypeEnum.BorrowerName:
                                    <span>
                                        @if (BorrowersNameSortDescending)
                                        {
                                            <i class="bi bi-sort-alpha-down-alt me-1"></i>
                                        }
                                        else
                                        {
                                            <i class="bi bi-sort-alpha-up-alt me-1"></i>
                                        }
                                        <span>Borrower's Name</span>
                                    </span>
                                    break;
                                default:
                                    <span>
                                        <i class="bi bi-filter me-1"></i> Sort
                                    </span>
                                    break;
                            }
                        }
                        else
                        {
                            <span>
                                <i class="bi bi-filter me-1"></i> Sort
                            </span>
                        }
                    </button>
                    <ul class="dropdown-menu border-light-subtle shadow">
                        <li>
                            <button class="dropdown-item" type="button"
                                    @onclick="() => OnSortTypeChangedHandler(LenderSortTypeEnum.LoanNumber)">
                                @if (SelectedSortType == LenderSortTypeEnum.LoanNumberDescending)
                                {
                                    <i class="bi bi-sort-up me-1"></i>
                                    <span>Loan Number Ascending</span>
                                }
                                else
                                {
                                    <i class="bi bi-sort-down me-1"></i>
                                    <span>Loan Number Descending</span>
                                }
                            </button>
                        </li>
                        <li>
                            <button class="dropdown-item" type="button"
                                    @onclick="() => OnSortTypeChangedHandler(LenderSortTypeEnum.BorrowerName)">
                                @if (!BorrowersNameSortDescending)
                                {
                                    <i class="bi bi-sort-alpha-down-alt me-1"></i>
                                }
                                else
                                {
                                    <i class="bi bi-sort-alpha-up-alt me-1"></i>
                                }
                                <span>Borrower's Name</span>
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="col-6 align-self-center px-0">
                <SearchBox @bind-Value="@SearchText"
                           OnDebouncedInput="OnSearchTextChangedHandler"
                           DebounceMilliseconds="300"
                           Placeholder="Search borrowers or loan #"/>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <table class="table table-sm table-hover align-middle shadow-sm rounded">
                    <thead class="table-light border-bottom border-secondary-subtle fs-9">
                    <tr>
                        <th scope="col">Name</th>
                        <th scope="col">Loan #</th>
                        <th scope="col">Loan Date</th>
                        <th scope="col" class="text-center">Actions</th>
                    </tr>
                    </thead>
                    <tbody class="fs-9">
                    @if (IsReloading)
                    {
                        <tr>
                            <td colspan="4" class="text-center">
                                <LoaderOverlay LoadingText="Loading archived loans, please wait..."></LoaderOverlay>
                            </td>
                        </tr>
                    }
                    else
                    {
                        @if (NewArchivedLoansList.Any())
                        {
                            foreach (var newArchive in NewArchivedLoansList)
                            {
                                <tr>
                                    <td class="fw-bold align-self-center" title="Newly Archived Loan">
                                        <i class="bi bi-bookmark-check-fill me-1 text-warning"></i> @newArchive.BorrowerName
                                    </td>
                                    <td class="fw-bold align-self-center fw-medium">
                                        @newArchive.LoanNumber
                                    </td>
                                    <td class="fw-bold align-self-center">
                                        @newArchive.AnticipatedLoanClosingDate.ToString("MM/dd/yyyy")
                                    </td>
                                    <td class="align-self-center text-center">
                                        <button class="btn btn-sm shadow-sm btn-warning border-warning-subtle"
                                                @onclick="() => OnLoanReOpenHandler(newArchive)">
                                            <i class="bi bi-folder-symlink me-1 fa-rotate-180"></i> Reopen Loan
                                        </button>
                                    </td>
                                </tr>
                            }
                        }

                        if (FilteredArchivedLoans.Any())
                        {
                            foreach (var loan in FilteredArchivedLoans)
                            {
                                <tr>
                                    <td class="fw-medium align-self-center">
                                        @loan.BorrowerName
                                    </td>
                                    <td class="align-self-center fw-medium">
                                        @loan.LoanNumber
                                    </td>
                                    <td class="align-self-center">
                                        @loan.AnticipatedLoanClosingDate.ToString("MM/dd/yyyy")
                                    </td>
                                    <td class="align-self-center text-center">
                                        <button class="btn btn-sm shadow-sm btn-warning border-warning-subtle"
                                                @onclick="() => OnLoanReOpenHandler(loan)">
                                            <i class="bi bi-folder-symlink me-1"></i> Reopen Loan
                                        </button>
                                    </td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="6" class="text-center">
                                    <div class="fw-medium mb-0 fs-9 alert alert-warning text-center">
                                        No archived loans found.
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                    </tbody>
                </table>
            </div>
            <div class="col-12">
                <Pagination
                    OnPageChanged="HandlePageChange"
                    TotalPages="_paginationState.TotalPages"
                    OnPageSizeChangedCallback="HandlePageSizeChange"
                    TotalItems="_paginationState.TotalItems">
                </Pagination>
            </div>
        </div>
    </div>
    <ConfirmModal @ref="ConfirmModal" Icon="ConfirmIcon.Question" 
                  CancelButtonText="No, Cancel"
                    ConfirmButtonText="Yes, Reopen"
                  OnConfirmation="HandleConfirmModalResult">
        <p>
            Are you sure you want to reopen this loan?
        </p>
        <p class="fs-11 text-secondary-emphasis mb-0 fw-medium">
            @SelectedLoan.BorrowerName
        </p>
        <span class="text-secondary fs-9 fw-medium">
            Loan Number: @SelectedLoan.LoanNumber
        </span>
    </ConfirmModal>
   
    @if (IsUnArchiving)
    {
        <LoaderOverlay LoadingText="Reopening loan, please wait..."></LoaderOverlay>
    }
</div>
