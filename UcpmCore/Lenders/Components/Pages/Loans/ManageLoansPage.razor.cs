using Lenders.Managers.Loans;
using Lenders.Services;
using Lenders.Shared;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Lender.RequestModels;
using UcpmApi.Shared.Lender.ResponseModels;

namespace Lenders.Components.Pages.Loans;

public class ManageLoansPageBase : LenderComponentBase, IAsyncDisposable
{
    private const string AllViewType = "all";

    [Parameter]
    public string? View { get; set; }

    [Inject]
    public LendersPolicyLoansManager LendersPolicyLoansManager { get; set; } = null!;

    [Inject]
    public IJSRuntime JsRuntime { get; set; }

    protected LoanPolicyResponse SelectedPolicy { get; set; } = new();
    public List<LoanQuoteResponse> AllQuoteLoans { get; set; } = [];
    protected bool IsAllLoansView { get; set; }
    protected bool IsAllPolicyLoansLoading { get; set; }
    protected DisplayTypeEnum CurrentDisplayType { get; set; } = DisplayTypeEnum.LoanList;
    private DisplayTypeEnum SelectedDisplayType { get; set; } = DisplayTypeEnum.LoanList;

    protected Task HandleSelectedPolicy(LoanPolicyResponse policy)
    {
        SelectedPolicy = policy;
        return Task.CompletedTask;
    }

    protected override Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Register beforeunload event to prevent accidental navigation
            JsRuntime.InvokeVoidAsync("setupBeforeUnload", "⚠️ You have unsaved changes. Are you sure you want to leave?");
        }

        return base.OnAfterRenderAsync(firstRender);
    }

    public async ValueTask DisposeAsync()
    {
        await JsRuntime.InvokeVoidAsync("removeBeforeUnload");
    }

    protected override async Task OnParametersSetAsync()
    {
        try
        {
            if (InsuredUser.InsuredGuid == Guid.Empty)
            {
                //Console.WriteLine("InsuredUser.InsuredGuid is empty. Cannot load loans.");
                // BootstrapToastService.ShowError("Insured user is not set. Please log in again.");
                return;
            }

            InitializeViewSettings();

            if (IsAllLoansView)
            {
                await LoadAllPolicyLoansAsync();
            }
            else
            {
                CurrentDisplayType = SelectedDisplayType;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error during parameter set: {ex}");
            BootstrapToastService.ShowError("An error occurred while loading the loans. Please try again later.");
        }
    }

    private void InitializeViewSettings()
    {
        if (string.IsNullOrEmpty(View))
        {
            IsAllLoansView = false;
        }
        else
        {
            IsAllLoansView = View == AllViewType;
        }

        if (IsAllLoansView)
        {
            CurrentDisplayType = DisplayTypeEnum.LoanList;
        }
    }

    private async Task LoadAllPolicyLoansAsync()
    {
        if (AllQuoteLoans.Count > 0) return;

        IsAllPolicyLoansLoading = true;

        try
        {
            var loansTask = LendersPolicyLoansManager.GetLenderResponsesForPolicy(CreateLoanQuotesRequest());
            var policyLoansTask = LendersPolicyLoansManager.GetLenderPoliciesForManageLoans(CreateLoanQuotesRequest());

            var results = await Task.WhenAll(loansTask, policyLoansTask);

            AllQuoteLoans = new List<LoanQuoteResponse>(results[0].Count + results[1].Count);
            AllQuoteLoans.AddRange(results[0]);
            AllQuoteLoans.AddRange(results[1]);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading policy loans: {ex.Message}");
            AllQuoteLoans = [];
        }
        finally
        {
            IsAllPolicyLoansLoading = false;
        }
    }

    private LoanQuotesRequest CreateLoanQuotesRequest()
    {
        return new LoanQuotesRequest
        {
            PolicyProspectGuid = Guid.Empty,
            InsuredGuid = InsuredUser.InsuredGuid,
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            ShowAll = true,
            SortType = LenderSortTypeEnum.DateDescending
        };
    }

    protected Task HandleDisplayTypeChanged(DisplayTypeEnum displayType)
    {
        if (View == AllViewType)
        {
            CurrentDisplayType = DisplayTypeEnum.LoanList;
            return Task.CompletedTask;
        }

        CurrentDisplayType = displayType;
        SelectedDisplayType = displayType;
        return Task.CompletedTask;
        /*if (displayType == DisplayTypeEnum.LoanList)
        {
            IsAllLoansView = false;
            await LoadAllPolicyLoansAsync();
        }
        else if (displayType == DisplayTypeEnum.SecuritizeLoans)
        {
            IsAllLoansView = true;
            // Load securitize loans logic can be added here if needed
        }*/
    }
}