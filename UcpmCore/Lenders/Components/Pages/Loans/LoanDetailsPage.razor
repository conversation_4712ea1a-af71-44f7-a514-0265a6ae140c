@page "/loans/loan-details/{ResponseGuid:guid}"
@using UcpmApi.Shared.Enums
@layout AdminLayout
@rendermode InteractiveServer
<PageTitle>Loan Application Details</PageTitle>
<div class="container p-0">
    <div class="bg-body border border-light-subtle rounded-bottom">
        <div class="row justify-content-between align-items-center">
            <div class="col d-flex">
                <button type="button" title="Back" class="btn btn-sm lender-btn-primary my-1 shadow-sm mx-2"
                        onclick="history.back()">
                    <i class="bi bi-arrow-return-left"></i>
                </button>
                <h6 class="mb-0 text-start p-2">
                    <i class="bibi bi-file-earmark-medical fs-5"></i>
                    Loan Application Details
                </h6>
            </div>
            <div class="col-auto">
                <span
                    class="badge bg-danger-subtle border border-danger-subtle text-danger-emphasis rounded-pill fw-medium fs-10 me-2">
                    Note here
                </span>
            </div>
        </div>
    </div>
</div>
<div class="container py-4">
    <div class="row">
        <div class="col-8">
            <div class="card shadow-sm border-light-subtle">
                <div class="card-body">
                    <h6 class="mb-0">Loan Application Details</h6>
                    
                    <FlexAnswerDisplayExample></FlexAnswerDisplayExample>
                </div>
            </div>
        </div>
        <div class="col-4">
            <div class="sticky-top" style="top: 5rem;">
                <div class="card shadow-sm border-light-subtle">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <h6 class="mb-0 fw-semibold">
                                    Summary
                                </h6>
                            </div>
                            <div class="col-12 my-2">
                                <div class="fs-9 text-secondary-emphasis fw-medium">
                                    Bob Smith
                                </div>
                                <div class="fw-medium fs-11 text-secondary">Borrower Name</div>
                            </div>
                            <div class="col-12 my-2">
                                <div class="fs-9 text-secondary-emphasis fw-medium">
                                    #12345
                                </div>
                                <div class="fw-medium fs-11 text-secondary">
                                    Loan Number
                                </div>
                            </div>
                            <div class="col-6 my-2 d-flex flex-column">
                                <div class="d-flex align-items-center">
                                    <span class="fs-9 fw-semibold text-secondary me-1 align-self-start">$</span>
                                    <span class="fs-4 fw-bold text-dark me-2 lh-1 ls-n2">12,000,000</span>
                                </div>
                                <div class="fw-medium fs-11 text-secondary">Loan Amount</div>
                            </div>
                            <div class="col-6 my-2">
                                <div class="fs-9 text-secondary-emphasis fw-semibold">
                                    12/31/2024
                                </div>
                                <div class="fw-medium fs-11 text-secondary">
                                    Anticipated Loan Date
                                </div>
                            </div>
                        </div>
                        <div class="separator separator-dashed my-2"></div>
                        <div class="row">
                            <div class="col-12 d-flex justify-content-between">
                                <h6 class="mb-0 fw-semibold">
                                    Status
                                </h6>
                                <i class="bi bi-circle-fill @LoanStatus.GetColor() mx-2 "></i>
                            </div>
                            <div class="col-12 my-2">
                                <div class="row border border-dashed rounded border-light-subtle shadow-sm justify-content-center">
                                    <div class="col-6 py-2">
                                        <div class="dropdown">
                                            <button class="btn btn-sm w-100 @LoanStatus.GetButtonClass() shadow-sm" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                @LoanStatus.GetDescription()
                                            </button>
                                            <ul class="dropdown-menu border border-light-subtle shadow-sm">
                                                @foreach (var status in LoanTransactionTypeEnumExtensions.GetLoanTransactionTypesDictionary())
                                                {
                                                    <li>
                                                        <button class="dropdown-item" type="button" @onclick="() => OnLoanStatusChanged(status.Key)">
                                                            @status.Value
                                                        </button>
                                                    </li>
                                                }
                                            </ul>
                                        </div>
                                        <div class="fw-medium fs-11 text-secondary">
                                            Set loan status
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>