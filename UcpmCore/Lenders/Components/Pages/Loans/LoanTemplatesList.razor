@using Lenders.Components.Shared
@using BlazorStrap.V5

<LoanTemplateListFilter OnSearchTextChanged="OnSearchTextChangedHandler"
                OnReload="OnReloadHandler" 
                @bind-SearchText="_searchText"
                OnSortTypeChanged="OnSortTypeChangedHandler">
</LoanTemplateListFilter>

<div class="row">
    <div class="col-12">
        <table class="table table-sm table-hover align-middle shadow-sm rounded">
            <thead class="table-light border-bottom border-secondary-subtle fs-9">
                <tr>
                    <th class="align-self-center" scope="col"><i class="bi bi-shield-lock-fill me-1"></i> TemplateName Name</th>
                    <th scope="col">TemplateDescription</th>
                    <th scope="col">DateLastUpdated</th>
                    <th scope="col" class="text-center col-2">Actions</th>
                </tr>
            </thead>
            <tbody class="fs-9 align-self-center">
                @if (IsReloading)
                {
                    <tr>
                        <td colspan="5" class="text-center">
                            <LoaderOverlay LoadingText="Loading loans, please wait..."></LoaderOverlay>
                        </td>
                    </tr>
                }
                else
                {
                    if (FilteredTemplates.Any())
                    {
                        foreach (var template in FilteredTemplates)
                        {
                            <tr>
                                <td class="fw-medium align-self-center">
                                    @template.TemplateName
                                </td>
                                <td class="align-self-center fs-10">
                                    @template.TemplateDescription
                                </td>
                                <td class="align-self-center fw-medium">
                                    @template.DateLastUpdated.ToString("MM/dd/yyyy")
                                </td>
                                <td class="align-self-center text-center">
                                </td>
                            </tr>
                        }
                    }
                    else
                    {
                        <tr>
                            <td colspan="6" class="text-center">
                                <div class="fw-medium mb-0 alert alert-warning">
                                    No loans found to securitize.
                                </div>
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
    </div>

    <div class="col-12">
        <Pagination OnPageChanged="HandlePageChange"
                    TotalPages="_paginationState.TotalPages"
                    OnPageSizeChangedCallback="HandlePageSizeChange"
                    CurrentPage="_paginationState.CurrentPage"
                    TotalItems="_paginationState.TotalItems">
        </Pagination>
    </div>
</div>