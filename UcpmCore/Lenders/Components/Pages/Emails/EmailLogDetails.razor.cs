using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using UcpmApi.Shared;

namespace Lenders.Components.Pages.Emails;

public partial class EmailLogDetails : ComponentBase
{
    [Parameter]
    public DirectSendLog DirectSendLog { get; set; } = new();

    [Parameter]
    public EventCallback OnBackToLogs { get; set; }

    [Parameter]
    public List<DirectSendLog> DirectSendLogs { get; set; } = [];

    [Inject]
    public IJSRuntime JsRuntime { get; set; } = null!;

    private int _currentIndex, _totalCount;
    private int CurrentIndexDisplay => _currentIndex + 1;
    private int _previousIndex = -1, _nextIndex = -1;

    private async Task PrintEmail()
    {
        await JsRuntime.InvokeVoidAsync("printElement", "emailContent");
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JsRuntime.InvokeVoidAsync("showToolTips");
        }
    }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        if (DirectSendLogs.Count > 0)
        {
            _totalCount = DirectSendLogs.Count;
            _currentIndex = DirectSendLogs.IndexOf(DirectSendLog);
            _previousIndex = _currentIndex > 0 ? _currentIndex - 1 : -1;
            _nextIndex = _currentIndex < _totalCount - 1 ? _currentIndex + 1 : -1;
            if (_currentIndex < 0 || _currentIndex >= _totalCount)
            {
                DirectSendLog = new DirectSendLog(); // Reset if index is out of bounds
            }
            else
            {
                DirectSendLog = DirectSendLogs[_currentIndex];
            }
        }
    }

    private async Task HandlePreviousEmail()
    {
        if (_previousIndex >= 0)
        {
            DirectSendLog = DirectSendLogs[_previousIndex];
            _currentIndex = _previousIndex;
            _previousIndex = _currentIndex > 0 ? _currentIndex - 1 : -1;
            _nextIndex = _currentIndex < _totalCount - 1 ? _currentIndex + 1 : -1;
            await InvokeAsync(StateHasChanged);
        }
    }

    private async Task HandleNextEmail()
    {
        if (_nextIndex >= 0)
        {
            DirectSendLog = DirectSendLogs[_nextIndex];
            _currentIndex = _nextIndex;
            _previousIndex = _currentIndex > 0 ? _currentIndex - 1 : -1;
            _nextIndex = _currentIndex < _totalCount - 1 ? _currentIndex + 1 : -1;
            await InvokeAsync(StateHasChanged);
        }
    }

    private async Task HandleBackToLogs()
    {
        await OnBackToLogs.InvokeAsync();
    }
}