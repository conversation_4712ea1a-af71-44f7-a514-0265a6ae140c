using System.Text.Json;

namespace EnvRiskMgmt.Extensions;

public static class DistributedCacheExtensions
{
    //set constants for cache keys
    public const string QuestionnaireViewModelKey = "QuestionnaireViewModel";
    public const string AgentProfileViewModelKey = "AgentProfileViewModel";
    public const string CustomizeViewModelKey = "CustomizeViewModel";
    public const string ConsultingServicesViewModelKey = "ConsultingServicesViewModel";
    public const string AssessmentViewModelKey = "AssessmentViewModel";
    public const string ProjectViewModelKey = "ProjectViewModel";
    public const string RecommendedCoverageViewKey = "RecommendedCoverageView";
    public const string InsuredNameQueryStringKey = "insuredName";
    public const string NextStepsViewModelKey = "NextStepsViewModel";
    public const string SolutionsViewModelKey = "SolutionsViewModel";
    public const string InviteLinkKey  = "InviteLink";
    public const string PasscodeKey = "PassCode";
    public const string PageKey = "Page";
    public const string NavMenuKey = "NavMenu";
    public const string PathResponseActionKey = "PathResponseAction";
    public const string SelectedPolicyProspect = "PolicyProspect";

    /// <summary>
    /// Retrieves an object from the distributed cache.
    /// </summary>
    /// <typeparam name="T">The type of the object to retrieve.</typeparam>
    /// <param name="cache">The distributed cache instance.</param>
    /// <param name="key">The key of the cached object.</param>
    /// <returns>
    /// The object of type <typeparamref name="T"/> if found in the cache; otherwise, the default value of <typeparamref name="T"/>.
    /// </returns>
    public static async Task<T?> GetObjectAsync<T>(this IDistributedCache cache, string key, Guid inviteLinkGuid)
    {
        string? json = await cache.GetStringAsync($"{key}{inviteLinkGuid}");
        return json == null ? default : JsonSerializer.Deserialize<T>(json);
    }
    
    /// <summary>
    /// Retrieves an object from the distributed cache.
    /// </summary>
    /// <typeparam name="T">The type of the object to retrieve.</typeparam>
    /// <param name="cache">The distributed cache instance.</param>
    /// <param name="key">The key of the cached object.</param>
    /// <returns>
    /// The object of type <typeparamref name="T"/> if found in the cache; otherwise, the default value of <typeparamref name="T"/>.
    /// </returns>
    public static T? GetObject<T>(this IDistributedCache cache, string key, Guid inviteLinkGuid)
    {
        string? json = cache.GetString($"{key}{inviteLinkGuid}");
        return json == null ? default : JsonSerializer.Deserialize<T>(json);
    }

    /// <summary>
    /// Retrieves an object from the distributed cache.
    /// </summary>
    /// <typeparam name="T">The type of the object to retrieve.</typeparam>
    /// <param name="cache">The distributed cache instance.</param>
    /// <param name="key">The key of the cached object.</param>
    /// <returns>
    /// The object of type <typeparamref name="T"/> if found in the cache; otherwise, the default value of <typeparamref name="T"/>.
    /// </returns>
    public static async Task SetObjectAsync<T>(this IDistributedCache cache, string key, Guid inviteLinkGuid, T value, int fromMinutes = 15)
    {
        DistributedCacheEntryOptions options = new()
        {
            SlidingExpiration = TimeSpan.FromMinutes(fromMinutes)
        };
        string jsonString = JsonSerializer.Serialize(value);
        await cache.SetStringAsync($"{key}{inviteLinkGuid}", jsonString, options);
    }


    /// <summary>
    /// Removes an object from the distributed cache.
    /// </summary>
    /// <param name="cache">The distributed cache instance.</param>
    /// <param name="key">The key of the cached object to remove.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    public static async Task RemoveObjectAsync(this IDistributedCache cache, string key, Guid inviteLinkGuid)
    {
        await cache.RemoveAsync($"{key}{inviteLinkGuid}");
    }

    /// <summary>
    /// Removes all predefined objects from the distributed cache.
    /// </summary>
    /// <param name="cache">The distributed cache instance.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    public static async Task RemoveAllObjectsAsync(this IDistributedCache cache, Guid inviteLinkGuid)
    {
        await cache.RemoveObjectAsync(QuestionnaireViewModelKey, inviteLinkGuid);
        await cache.RemoveObjectAsync(AgentProfileViewModelKey, inviteLinkGuid);
        await cache.RemoveObjectAsync(CustomizeViewModelKey, inviteLinkGuid);
        await cache.RemoveObjectAsync(ConsultingServicesViewModelKey, inviteLinkGuid);
        await cache.RemoveObjectAsync(AssessmentViewModelKey, inviteLinkGuid);
        await cache.RemoveObjectAsync(NextStepsViewModelKey, inviteLinkGuid);
        await cache.RemoveObjectAsync(SolutionsViewModelKey, inviteLinkGuid);
        await cache.RemoveObjectAsync(ProjectViewModelKey, inviteLinkGuid);
        await cache.RemoveObjectAsync(RecommendedCoverageViewKey, inviteLinkGuid);
        await cache.RemoveObjectAsync(InsuredNameQueryStringKey, inviteLinkGuid);
        await cache.RemoveObjectAsync(PageKey, inviteLinkGuid);
        await cache.RemoveObjectAsync(NavMenuKey, inviteLinkGuid);
        await cache.RemoveObjectAsync(PathResponseActionKey, inviteLinkGuid);
    }
}