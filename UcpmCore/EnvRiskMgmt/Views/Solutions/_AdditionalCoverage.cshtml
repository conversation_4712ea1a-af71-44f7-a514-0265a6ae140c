@using Newtonsoft.Json
@using UcpmApi.Shared.Kato
@using UcpmApi.Shared.Extensions
@model SolutionsModel

<div class="row" id="additionalCoverage">
    <div class="col-12">
        <div class="alert alert-info">
            These coverages and definitions will enhance your policy and broaden the coverage that is being provided.
        </div>
        <table class="table table-bordered mt-2">
            <thead>
                <tr>
                    <th colspan="3" class="bg-secondary text-light">Coverages that can be added for additional premium.</th>
                    <th class="bg-secondary text-light">Keys</th>
                    <th class="bg-secondary text-light">Premium</th>
                    <th class="bg-secondary text-light">Select</th>
                </tr>
            </thead>
            <tbody id="addOnsAppend">
                @foreach (var part in Model.OptionalCoverage.Where(c => c.IsMarketStandard != true))
                {
                    <partial name="_CoveragePart" model="part" />
                    foreach (var item in part.DefinedTerms)
                    {
                        <partial name="_IkeaDefinedTerm" model="item" />
                        foreach (var element in item.Options.Where(w => w.IkeaDisplayTypeId != 1))
                        {
                            <partial name="_IkeaDefinedTermOption" model="element" />
                        }
                    }
                }
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="4">
                        <p class="text-end">
                            <span class="fw-bold">Total Premium</span><br />
                            <small id="additional-coverage-minimum" class="minimumPremium" data-minimum-premium="@Model.SolutionsViewModel.MinimumPremium">Subject to a minimum premium @Model.SolutionsViewModel.MinimumPremium.ToCurrencyFormat("c0")</small>
                        </p>
                    </td>
                    <td>
                        <p class="fw-bold totalPremium" data-quickrater-premium="@Model.SolutionsViewModel.PricingModel.QuickRaterPremium">@Model.SolutionsViewModel.PricingModel.QuickRaterPremium.ToCurrencyFormat("C0")</p>
                    </td>
                    <td></td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>
