@using Newtonsoft.J<PERSON>
@using UcpmApi.Shared.Kato
@using UcpmApi.Shared.Extensions
@model SolutionsModel

<div id="loadingFinalPolicy" style="display: none">
    <div class="d-flex  justify-content-center mb-3">
        <div class="flex-column">
            <h2 class="order-1" style="display: block;">Summarizing Policy</h2>
            <div class="spinner"></div>
        </div>
    </div>
</div>

<div class="tab-content" id="finalPolicy">
    <div class="alert alert-info">
        Based on the selections you have made, here is how your policy will be built.
    </div>
    <table class="table table-bordered mt-2">
        <thead>
            <tr>
                <th class="bg-secondary text-light" colspan="3">Your Policy</th>
                <th class="bg-secondary text-light">Keys</th>
                <th class="bg-secondary text-light">Premium</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var part in Model.SolutionsViewModel.CoverageParts.Where(x => x.IncludedByDefault))
            {
                <partial name="_IkeaCoveragePartTable" model="part" />
            }
            @foreach (var part in Model.SolutionsViewModel.CoverageParts.Where(x => !x.IncludedByDefault && !x.IsMarketStandard && x.Checked))
            {
                <partial name="_IkeaCoveragePartTable" model="part" />
            }
            @foreach (var part in Model.SolutionsViewModel.CoverageParts.Where(x => x.IsMarketStandard && !x.IncludedByDefault && x.Checked))
            {
                <partial name="_IkeaCoveragePartTable" model="part" />
            }
        </tbody>
        <tfoot>
            <tr>
                <td colspan="4">
                    <p class="text-end">
                        <span class="fw-bold">Total Premium</span><br />
                        <small id="final-policy-coverage-minimum" class="minimumPremium" data-minimum-premium="@Model.SolutionsViewModel.MinimumPremium">Subject to a minimum premium @Model.SolutionsViewModel.MinimumPremium.ToCurrencyFormat("c0")</small>
                    </p>
                </td>
                <td>
                    <p class="fw-bold totalPremium" data-quickrater-premium="@Model.SolutionsViewModel.PricingModel.QuickRaterPremium">@Model.SolutionsViewModel.PricingModel.QuickRaterPremium.ToCurrencyFormat("C0")</p>
                </td>
            </tr>
        </tfoot>
    </table>
</div>
