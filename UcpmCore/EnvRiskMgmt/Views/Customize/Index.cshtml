@using EnvRiskMgmt.Components.CustomizePackage
@using UcpmApi.Shared.Kato
@model CustomizeViewModel
@{
    Layout = "_MainLayOut";
    ViewData["Title"] =  "Customize";
}

@section Profile
{
    @await Component.InvokeAsync("AgentProfile")
}

@await Html.PartialAsync("_BenchmarkChartModal")
<div class="container bg-light">
    <!-- nav arrows -->
    @await Component.InvokeAsync("NavMenu", new { page = "Customize", invLink = ViewBag.InviteLink })
    <!-- end nav arrows -->

    <div class="row">
        <p class="col-12">
            Purchase Options: The last step of this process is to make a decision on the limits and deductibles for your coverage. Purchasing higher limits is always in your best interest. Pick from the dropdowns and you will see pricing update automatically. If most cases an annual policy is better, but if you would like to pursue something that is specific to a certain project, you can select that option. Once you have made your decisions, click the coverage boxes (they turn blue) and then next.
        </p>
    </div>
    
     @if(!Model.IsProjectSpecific) 
     {
        <div class="row">
            <div class="col-12">
                @await Component.InvokeAsync("FlexQuestion", Model.IsProjectSpecificFlexQuestion)
            </div>
        </div>
     }
    <div class="row my-2">
        <div class="col-md-6 col-12">
            @await Component.InvokeAsync("CustomizePackage", new { model = Model })
        </div>
        @if(Model.IsProjectSpecific) 
        {
            <div class="col-md-6 col-12">
                @await Component.InvokeAsync("CustomizePackage", new { model = Model, package = CustomizePackageEnum.ProjectCoverage })
            </div>
        }
        @if (Model.UserWantsProfessional && Model.WeCanOfferProfessional)
        {
            <div class="col-md-6 col-12">
                @await Component.InvokeAsync("CustomizePackage", new { model = Model, package = CustomizePackageEnum.ProfessionalLiability })
            </div>
        }
    </div>
    <div class="row my-2 mt-3">
        <div class="container text-center">
            <div class="row align-items-center">
                <div class="col text-start">
                    <button id="backButton" onclick="navigateToUrl('/solutions')" type="button" class="btn btn-primary">
                        <i class="bi bi-arrow-left-circle"></i> Back
                    </button>
                </div>
                <div class="col text-center align-middle align-content-center">
                    <partial name="_HelpIcon"/>
                </div>
                <div class="col text-end align-middle">
                    <button class="btn btn-primary float-end"  data-message="Loading Consulting Services..." data-action-url="@Url.Action("SetAndUpdateNextPolicyNumber", "Customize")" id="purchaseBtn">
                        Next <i class="bi bi-arrow-right-circle"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@Html.HiddenFor(m => m.PricingModel.FlexResponseGuid)
@Html.HiddenFor(m => m.PricingModel.SolutionsPageBasePremium)
@await Html.PartialAsync("_HelpVideoModal", "https://api-files.sproutvideo.com/file/ea91d3b01c16edc563/b1df81808b731dc8/1080.mp4")

@section Scripts
{
    <script>
        $(document).ready(function () {
            CustomizeCoverage.init();
        });
    </script>
}