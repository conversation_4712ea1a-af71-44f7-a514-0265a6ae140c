using UcpmApi.Shared.Ikea;

namespace EnvRiskMgmt.ApiServices.Interfaces;

public interface IIkeaPolicyService
{
    Task<IEnumerable<IkeaCoveragePart>> IkeaCoveragePart(List<Guid> flexInsuredExposureGuid);
    Task<IEnumerable<IkeaDefinedTerm>> IkeaDefinedTerm(List<Guid> flexInsuredExposureGuid);
    Task<IEnumerable<IkeaDefinedTermOption>> IkeaDefinedTermOption(List<Guid> flexInsuredExposureGuid);
    Task<IEnumerable<IkeaCoveragePart>> AllIkeaCoveragePart();
    Task<IEnumerable<IkeaDefinedTerm>> AllIkeaDefinedTerm();
    Task<IEnumerable<IkeaDefinedTermOption>> AllIkeaDefinedTermOption();
    Task<bool> SaveCoverage(IEnumerable<IkeaCoveragePartFlexInsuredExposure> model, Guid carrierSubmissionGuid);
    Task<bool> SaveDefinedTerm(IEnumerable<IkeaDefinedTermFlexInsuredExposure> model, Guid carrierSubmissionGuid);
    Task<bool> SaveDefinedTermOption(IEnumerable<IkeaDefinedTermOptionFlexInsuredExposureAvailableOption> model, Guid carrierSubmissionGuid);
    Task<IEnumerable<IkeaDefinedTerm>> GetIkeaAllDefinedTerm();
    Task<bool> QuickSaveCoverage(Guid coveragePartGuid, Guid carrierSubmissionGuid);
    Task<bool> QuickDeleteCoverage(Guid coveragePartGuid, Guid carrierSubmissionGuid);
    Task<bool> QuickSaveDefinedTerm(Guid definedTermGuid, Guid carrierSubmissionGuid);
    Task<bool> QuickDeleteDefinedTerm(Guid definedTermGuid, Guid carrierSubmissionGuid);
    Task<bool> QuickSaveDefinedTermOption(Guid definedTermGuidOption, Guid carrierSubmissionGuid);
    Task<bool> QuickDeleteDefinedTermOption(Guid definedTermGuidOption, Guid carrierSubmissionGuid);
}