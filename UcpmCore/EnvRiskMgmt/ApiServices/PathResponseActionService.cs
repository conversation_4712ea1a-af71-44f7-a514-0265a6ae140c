using UcpmApi.Shared.Pathfinder;

namespace EnvRiskMgmt.ApiServices;

public class PathResponseActionService(IHttpClientFactory client) : BaseService(client), IPathResponseActionService
{
    public async Task<bool> SavePathResponseAction(PathResponseAction pathResponseAction)
    {
        
        var isSuccess = await PostAsync("PathResponseAction", pathResponseAction);
        return isSuccess;
    }
}