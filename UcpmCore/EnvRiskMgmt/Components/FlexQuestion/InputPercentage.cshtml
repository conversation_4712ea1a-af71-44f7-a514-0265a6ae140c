@using EnvRiskMgmt.Components.FlexQuestion
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using UcpmApi.Shared.Enums
@model UcpmApi.Shared.Kato.FlexQuestionViewModel
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<input type="hidden" asp-for="@Model.QuestionText" />
<input type="hidden" asp-for="@Model.QuestionId" />
<input type="hidden" asp-for="@Model.DataName" />
<input type="hidden" asp-for="@Model.DataType" />
<input type="hidden" asp-for="@Model.QuestionIndex" />
<input type="hidden" asp-for="@Model.RowNumber" />
<input type="hidden" asp-for="@Model.IsRequired" />

@if (Model.ViewStyle == FlexQuestionViewStyleEnum.NoLabel)
{
    @if (Model.IsRequired)
    {
        <input type="text" style="width:250px;" asp-for="QuestionTextAnswer" data-type="@Model.DataType.ToString()"
               class="form-control percentage" aria-describedby="@Model.QuestionId" autocomplete="off" required>
    }
    else
    {
        <input type="text" style="width:250px;" asp-for="QuestionTextAnswer" data-type="@Model.DataType.ToString()"
               class="form-control percentage" aria-describedby="@Model.QuestionId" autocomplete="off">
    }
}
else
{
    <div class="@Model.DisplayWidth.ToBootstrapColumnWidth()">
        @if (Model.IsRequired)
        {          
            <label class="form-label">
                @Model.QuestionText
                <span class="text-danger">*</span>
            </label>
            <input type="text" asp-for="QuestionTextAnswer" data-type="@Model.DataType.ToString()"
                   class="form-control percentage" aria-describedby="@Model.QuestionId" autocomplete="off" required>
        }
        else
        {
            <label asp-for="QuestionId" class="col-form-label">@Model.QuestionText</label>
            <input type="text" asp-for="QuestionTextAnswer" data-type="@Model.DataType.ToString()"
                   class="form-control percentage" aria-describedby="@Model.QuestionId" autocomplete="off">
        }
    </div>
}