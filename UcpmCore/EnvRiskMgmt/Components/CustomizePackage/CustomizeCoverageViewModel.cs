using Microsoft.AspNetCore.Mvc.Rendering;
using UcpmApi.Shared.Policy;

namespace EnvRiskMgmt.Components.CustomizePackage;

public class CoverageBaseView
{
    public Guid PackageGuid { get; set; }
    public Guid InviteLinkGuid { get; set; }
}

public class ProfessionalLiabilityView : CoverageBaseView
{
    public decimal ProfessionalTotalCost { get; set; }
    public SelectList? LimitsSelectList { get; set; }
    public string? SelectedLimitProfessional { get; set; }
    public SelectList? DeductibleSelectList { get; set; }
    public string? SelectedDeductibleProfessional { get; set; }
    public Guid CarrierMaximumOfferGuid { get; set; }
    public List<BenchmarkChartView> BenchmarkChartViews { get; set; }
    public CustomizePackageEnum Package { get; set; } = CustomizePackageEnum.ProfessionalLiability;
}

public class RiskConsultingPackageView : CoverageBaseView
{
    public CustomizePackageEnum Package { get; set; } = CustomizePackageEnum.RiskConsulting;
    public decimal RiskPrice { get; set; } = 750;
    public bool NoCostNXUS { get; set; }
}

public class NxusCoverageView : CoverageBaseView
{
    public decimal NxusTotalCost { get; set; }
    public SelectList? LimitsSelectList { get; set; }
    public string? SelectedLimit { get; set; }
    public SelectList? DeductibleSelectList { get; set; }
    public string? SelectedDeductible { get; set; }
    public Guid CarrierMaximumOfferGuid { get; set; }
    public CustomizePackageEnum Package { get; set; } = CustomizePackageEnum.Nxus;
    public decimal Premium { get; set; }
    public decimal Taxes { get; set; }
    public decimal Fees { get; set; }
    public List<BenchmarkChartView> BenchmarkChartViews { get; set; }
}

public class ProjectCoverageView : CoverageBaseView
{
    public decimal ProjectTotalCost { get; set; }
    public SelectList? LimitsSelectList { get; set; }
    public string? SelectedLimit { get; set; }
    public SelectList? DeductibleSelectList { get; set; }
    public string? SelectedDeductible { get; set; }
    public Guid CarrierMaximumOfferGuid { get; set; }
    public CustomizePackageEnum Package { get; set; } = CustomizePackageEnum.ProjectCoverage;
    public decimal Premium { get; set; }
    public decimal Taxes { get; set; }
    public decimal Fees { get; set; }
    public List<BenchmarkChartView> BenchmarkChartViews { get; set; }
}

public class BenchmarkChartView
{
    public decimal LimitAggregate { get; set; }

    public List<NearestNeighbor> NearestNeighborsList { get; set; }
}

public class UpdatePremiumOptionResult : CoverageBaseView
{
    public new string? TotalCost { get; set; }
}

public class UpdatedNxusPremiumOptionResult : CoverageBaseView
{
    public new string? TotalCost { get; set; }
    public decimal Premium { get; set; }
    public decimal Taxes { get; set; }
    public decimal Fees { get; set; }
}