using UcpmApi.Shared.Adobe;
using UcpmApi.Shared.Flex;
using UcpmApi.Shared.Helpers;
using UcpmApi.Shared.Lookups;
using UcpmApi.Shared.Security;


namespace EnvRiskMgmt.Controllers;

public class FinalizeController : BaseController
{
    private readonly IInsuredService _insuredService;
    private readonly IFlexResponseService _flexResponseService;
    private readonly IExternalApiService _externalApiService;
    private readonly IFeatureService _featureService;
    private const string PageName = "Finalize";

    public FinalizeController(IInsuredService insuredService, IFlexResponseService flexResponseService,
        IExternalApiService externalApiService, IFeatureService featureService, IDistributedCache distributedCache,
        IPathResponseActionService pathResponseActionService) : base(pathResponseActionService, distributedCache)
    {
        _insuredService = insuredService;
        _flexResponseService = flexResponseService;
        _externalApiService = externalApiService;
        _featureService = featureService;
    }

    [AllowAnonymous]
    public async Task<IActionResult> Index()
    {
        //init log path response action
        InitPathResponseActionLog("Index", PageName);
        //set visit page
        SetVisitPage(HttpContext.GetRequestPath());

        //get invite link guid and passcode
        Guid ilg = await GetInviteLinkGuid();
        if (ilg == Guid.Empty) return RedirectToAction("Index", "Home");

        // get finalize
        FinalizeViewModel model = await _insuredService.FinalizeTab(ilg);
        if (PathResponseActionLog != null) PathResponseActionLog.FlexResponseGuid = model.FlexResponseGuid;

        //save flex response log
        FlexResponseLog flexResponseLog = new()
        {
            LogData = "EnvRiskMgmt Finalize Page",
            LogTypeId = (int)FlexResponseLogTypeEnum.Timer,
            FlexResponseGuid = model.FlexResponseGuid
        };
        await _flexResponseService.AddFlexResponseLog(flexResponseLog);

        //save log path response action
        await SavePathResponseActionLog();
        ViewBag.InviteLink = ilg.ToString();
        return View(model);
    }

    public async Task<IActionResult> Esign(string email)
    {
        //init log path response action
        InitializePathResponseActionLogFromCache(nameof(Esign), PageName);

        List<CarrierSubmission> submissions = await _insuredService.GetSubmissionsFromInvite(INVITE_LINK_GUID);
        CarrierSubmission subForAgent =
            await _insuredService.CarrierSubmissionWithAgentInfo(submissions.FirstOrDefault()
                .CarrierSubmissionGuid);
        Agent agentInfo = subForAgent.Agent;
        Insured insured = subForAgent.Insured;

        CarrierSubmission sub =
            submissions?.FirstOrDefault(fd => fd.PolicyStatusId == (int)PolicyStatusEnum.QuotedSent) ?? new();
        List<DocModel> docs = await _insuredService.GetAllDocsBySourceGuid(sub.CarrierSubmissionGuid);
        DocModel doc = docs?.FirstOrDefault(fd => !string.IsNullOrEmpty(fd.CloudUrl) && fd.FileName == "App.pdf");
        EnvironmentEnumModel environment = await _featureService.GetEnvironmentModel();

        if (doc != null && !(environment.Environment == EnvironmentEnum.Dev ||
                             environment.Environment == EnvironmentEnum.Local))
        {
            //Word of caution ESing is wired to send an email to the agent, and does so through Adobe. Please use caution when testing or working with
            using TempPath tempPath = new(Path.GetTempPath());
            string path = Path.Combine(tempPath.Path, doc.FileName);

            HttpClient httpClient = new();
            HttpResponseMessage result = await httpClient.GetAsync(doc.CloudUrl);
            using (FileStream fs = new(path, FileMode.CreateNew))
            {
                await result.Content.CopyToAsync(fs);
            }

            ExternalApiToken apiToken =
                await _externalApiService.GetExternalApiToken("-", "AdobeEsign", "AdobeEsign") ?? new();
            apiToken.AccessToken = await AdobeModel.RefreshToken(apiToken);
            AdobeAgreementInfo agreement = await AdobeModel.RunAdobeESign(apiToken, path, insured, email);
            agreement = agreement == null ? new(true) : agreement;
        }

        // save log path response action
        if (PathResponseActionLog?.FlexResponseGuid != Guid.Empty)
        {
            await SavePathResponseActionLog();
        }

        return Ok(new { Success = $"A email has been sent to {email} requesting an e-signature." });
    }

    [HttpPost]
    public async Task<IActionResult> SaveFinalize(FinalizeViewModel model)
    {
        //init log path response action
        InitPathResponseActionLog(nameof(SaveFinalize), PageName);
        if (PathResponseActionLog != null) PathResponseActionLog.FlexResponseGuid = model.FlexResponseGuid;

        model.CurrentlySignedInGuid = AgentProfile?.AgentGuid ?? Guid.Empty;
        //save finalize
        bool isSuccess = await _flexResponseService.SaveFinalize(model);

        //save log path response action
        await SavePathResponseActionLog();
        return isSuccess
            ? Ok(new
            {
                success = true,
                message = "Success"
            })
            : NotFound(new
            {
                success = false,
                message = "Failed"
            });
    }

    [HttpPost]
    public async Task<IActionResult> SendNotification()
    {
        // This notification will be sent from the ePayPolicyInsuredImportProcess web job after the payment is processed.
        //if (AGENT_PROFILE != null)
        //{
        //    DirectSendLog directSendLog = await _insuredService.SendPremiumPaymentProcessedNotificationByAgentGuid(AGENT_PROFILE.AgentGuid, new List<string>());
        //    if (directSendLog != null && directSendLog.DirectSendLogId != 0)
        //    {
        //        await _insuredService.SaveDirectSendLog(directSendLog);

        //        return Ok(new
        //        {
        //            success = true,
        //            message = "Success"
        //        });
        //    }
        //}

        //return NotFound(new
        //{
        //    success = false,
        //    message = "Failed"
        //});
        return Ok();
    }
}