using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Shared.ApiClientBase.Interfaces;
using UcpmApi.Shared.Apis;

namespace UcpmControllerLibraryEx.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PathfinderSynthesiaPolicyController : ControllerBase
    {
        private readonly PathfinderSynthesiaPolicyApi _pathfinderSynthesiaApi;
        private readonly IErrorComponent _errorComponent;
        private readonly IHttpContextAccessor _contextAccessor;

        public PathfinderSynthesiaPolicyController(PathfinderSynthesiaPolicyApi pathfinderSynthesiaApi,
            IErrorComponent errorComponent,
            IHttpContextAccessor contextAccessor)
        {
            _pathfinderSynthesiaApi = pathfinderSynthesiaApi;
            _errorComponent = errorComponent;
            _contextAccessor = contextAccessor;
        }
    }
}
