using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UcpmApi.Shared;
using UcpmApi.Shared.Api.Carrier;
using UcpmApi.Shared.ApiClientBase.Interfaces;
using UcpmApi.Shared.Apis;
using UcpmApi.Shared.Carrier;
using UcpmComponentLibraryEx.Helpers;

namespace UcpmControllerLibrary.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CarrierSubmissionFootnoteController : ControllerBase
    {
        private readonly CarrierSubmissionFootnoteApi _carrierSubFootnoteApi;
        private readonly IErrorComponent _errorComponent;
        private readonly IHttpContextAccessor _contextAccessor;

        public CarrierSubmissionFootnoteController(CarrierSubmissionFootnoteApi carrierSubFootnoteApi,
            IErrorComponent errorComponent,
            IHttpContextAccessor contextAccessor)
        {
            _carrierSubFootnoteApi = carrierSubFootnoteApi;
            _errorComponent = errorComponent;
            _contextAccessor = contextAccessor;
        }

        [HttpGet("GetBySubmission")]
        public async Task<IActionResult> GetBySubmission(Guid carrierSubmissionGuid)
        {
            string jwtToken = Utilities.GetJwtToken(_contextAccessor);

            IEnumerable<CarrierSubmissionFootnote> result = await _carrierSubFootnoteApi.GetBySubmission(carrierSubmissionGuid, _errorComponent, jwtToken);

            return result == null ? BadRequest(new List<CarrierSubmissionFootnote>()) : Ok(result);
        }

        [HttpPost("AddCarrierSubmissionFootnotes")]
        public async Task<IActionResult> AddCarrierSubmissionFootnotes([FromBody] List<CarrierSubmissionFootnote> footnotesToAdd)
        {
            string jwtToken = Utilities.GetJwtToken(_contextAccessor);
            bool result = await _carrierSubFootnoteApi.AddCarrierSubmissionFootnotes(footnotesToAdd, _errorComponent, jwtToken);
            return Ok(result);
        }

        [HttpPut("UpdateCarrierSubmissionFootnotes")]
        public async Task<IActionResult> UpdateCarrierSubmissionFootnotes(Guid carrierSubmissionGuid, [FromBody] List<CarrierSubmissionFootnote> footnotesToSave)
        {
            string jwtToken = Utilities.GetJwtToken(_contextAccessor);
            bool result = await _carrierSubFootnoteApi.UpdateCarrierSubmissionFootnotes(carrierSubmissionGuid, footnotesToSave, _errorComponent, jwtToken);
            return Ok(result);
        }

    }
}
