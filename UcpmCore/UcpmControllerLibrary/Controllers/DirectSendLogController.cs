using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Shared;
using UcpmApi.Shared.Api.Mail;
using UcpmApi.Shared.ApiClientBase.Interfaces;
using UcpmApi.Shared.Apis;
using UcpmApi.Shared.Mail;
using UcpmComponentLibraryEx.Helpers;
using UcpmTools.StringUtilities;

namespace UcpmControllerLibraryEx.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DirectSendLogController : ControllerBase
    {
        private readonly DirectSendLogApi _directSendLogApi;
        private readonly IErrorComponent _errorComponent;
        private readonly IHttpContextAccessor _contextAccessor;

        public DirectSendLogController(DirectSendLogApi directSendLogApi,
            IErrorComponent errorComponent,
            IHttpContextAccessor contextAccessor)
        {
            _directSendLogApi = directSendLogApi;
            _errorComponent = errorComponent;
            _contextAccessor = contextAccessor;
        }

        [HttpGet("GetByRegardingGuid")]
        public async Task<IActionResult> GetByRegardingGuid(Guid regardingGuid)
        {
            string jwtToken = Utilities.GetJwtToken(_contextAccessor);
            IEnumerable<DirectSendLog> directSendLogs = await _directSendLogApi.GetByRegardingGuid(regardingGuid, _errorComponent, jwtToken);

            return directSendLogs == null ? BadRequest() : Ok(directSendLogs);
        }

        [HttpGet("GetDirectSendLogs")]
        public async Task<IActionResult> GetDirectSendLogs()
        {
            string jwtToken = Utilities.GetJwtToken(_contextAccessor);
            IEnumerable<DirectSendLog> directSendLogs = await _directSendLogApi.GetDirectSendLogs(_errorComponent, jwtToken);

            return directSendLogs == null ? BadRequest() : Ok(directSendLogs);
        }

        [HttpGet("GetEmailsByPolicy")]
        public async Task<IActionResult> GetEmailsByPolicy(Guid policyProspectGuid)
        {
            string jwtToken = Utilities.GetJwtToken(_contextAccessor);
            IEnumerable<EmailFileViewModel> emailFiles = await _directSendLogApi.GetEmailsByPolicy(policyProspectGuid, _errorComponent, jwtToken);

            return emailFiles == null ? BadRequest() : Ok(emailFiles);
        }


    }
}
