using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UcpmApi.Shared;
using UcpmApi.Shared.ApiClientBase.Interfaces;
using UcpmApi.Shared.Apis;
using UcpmApi.Shared.Flex;
using UcpmComponentLibraryEx.Helpers;

namespace UcpmControllerLibrary.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class FlexResponseScheduledRatingReasonController : ControllerBase
    {
        private readonly FlexResponseScheduledRatingReasonApi _flexResponseScheduledRatingReasonApi;
        private readonly IErrorComponent _errorComponent;
        private readonly IHttpContextAccessor _contextAccessor;

        public FlexResponseScheduledRatingReasonController(FlexResponseScheduledRatingReasonApi flexResponseScheduledRatingReasonApi, IErrorComponent errorComponent, IHttpContextAccessor httpContextAccessor)
        {
            _flexResponseScheduledRatingReasonApi = flexResponseScheduledRatingReasonApi;
            _errorComponent = errorComponent;
            _contextAccessor = httpContextAccessor;
        }

        [HttpGet("GetForFlexResponse")]
        public async Task<IActionResult> GetForFlexResponse(Guid flexResponseGuid)
        {
            string jwtToken = Utilities.GetJwtToken(_contextAccessor);

            IEnumerable<FlexResponseScheduledRatingReason> response = await _flexResponseScheduledRatingReasonApi.GetForFlexResponse(flexResponseGuid, _errorComponent, jwtToken);
            return response == null ? BadRequest(response) : Ok(response);
        }

        [HttpPost("UpdateFlexResponse")]
        public async Task<IActionResult> UpdateFlexResponse(ScheduledRatingReasonUpdateModel model)
        {
            string jwtToken = Utilities.GetJwtToken(_contextAccessor);

            bool response = await _flexResponseScheduledRatingReasonApi.UpdateFlexResponse(model, _errorComponent, jwtToken);
            return Ok(response);
        }      

    }
}
