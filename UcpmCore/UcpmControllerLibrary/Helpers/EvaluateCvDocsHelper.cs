using Microsoft.AspNetCore.Http;
using Sprache;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using UcpmApi.BusinessModel.ChatGpt;
using UcpmApi.Shared;
using UcpmApi.Shared.Api.Coverage;
using UcpmApi.Shared.Api.Mail;
using UcpmApi.Shared.Api.Vetting;
using UcpmApi.Shared.ApiClientBase.Interfaces;
using UcpmApi.Shared.Apis;
using UcpmApi.Shared.ChatGpt;
using UcpmApi.Shared.Coverage;
using UcpmApi.Shared.Doc;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Guids;
using UcpmApi.Shared.Mail;
using UcpmApi.Shared.Vetting;

namespace UcpmControllerLibrary.Helpers
{
    public class EvaluateCvDocsHelper
    {
        private readonly ProcessInstanceFormApi _processInstanceFormApi;
        private readonly ProcessInstanceApi _processInstanceApi;
        private readonly ProcessInstanceDocApi _processInstanceDocApi;
        private readonly ProcessRequirementGroupApi _processRequirementGroupApi;
        private readonly AssessedPolicyApi _assessedPolicyApi;
        private readonly FormApi _formApi;
        private readonly NewFormRequestApi _newFormRequestApi;
        private readonly FormCoverageIssueApi _formCoverageIssueApi;
        private readonly CarriersApi _carriersApi;
        private readonly IssueRequirementApi _issueRequirementApi;
        private readonly DocApi _docApi;
        private readonly AssessedApi _assessedApi;
        private readonly MessageApi _messageApi;
        private readonly DirectSendApi _directSendApi;
        private readonly IErrorComponent _errorComponent;
        private readonly string _jwtToken;

        public EvaluateCvDocsHelper(
            ProcessInstanceFormApi processInstanceFormApi,
            ProcessInstanceApi processInstanceApi,
            ProcessRequirementGroupApi processRequirementGroupApi,
            AssessedPolicyApi assessedPolicyApi,
            FormApi formApi,
            NewFormRequestApi newFormRequestApi,
            FormCoverageIssueApi formCoverageIssueApi,
            IssueRequirementApi issueRequirementApi,
            DocApi docApi,
            ApiKeyApi apiKeyApi,
            AssessedApi assessedApi,
            CarriersApi carriersApi,
            MessageApi messageApi,
            DirectSendApi directSendApi,
            ProcessInstanceDocApi processInstanceDocApi,
            IErrorComponent errorComponent,
            string jwtToken)
        {
            _processInstanceFormApi = processInstanceFormApi;
            _processInstanceApi = processInstanceApi;
            _processInstanceDocApi = processInstanceDocApi;
            _processRequirementGroupApi = processRequirementGroupApi;
            _assessedPolicyApi = assessedPolicyApi;
            _formApi = formApi;
            _newFormRequestApi = newFormRequestApi;
            _formCoverageIssueApi = formCoverageIssueApi;
            _issueRequirementApi = issueRequirementApi;
            _docApi = docApi;
            _assessedApi = assessedApi;
            _carriersApi = carriersApi;
            _messageApi = messageApi;
            _directSendApi = directSendApi;
            _errorComponent = errorComponent;
            _jwtToken = jwtToken;
        }

        public async Task<EvaluateCvDocsResult> RunDocEvaluations(EvaluateCvDocs evaluateCvDocs)
        {
            if (evaluateCvDocs == null)
            {
                return new EvaluateCvDocsResult
                {
                    Success = false,
                    Message = "Evaluation data is null."
                };
            }

            EvaluateCvDocsResult evaluateCvDocsResult = new();
            evaluateCvDocs.ProcessStatus = (int)ProcessStatusIdEnum.PolicyNeedsResearch;

            IEnumerable<DocModel> allDocs = await _docApi.GetAllDocsBySourceGuid(evaluateCvDocs.AssessedGuid, _errorComponent, _jwtToken);

            if (!allDocs.Any(d => d.FileName.Contains(".pdf")))
            {
                SetDocumentMissingStatus(evaluateCvDocs);
                return evaluateCvDocsResult;
            }

            List<DocModel> docs = allDocs.Where(d => d.FileName.Contains(".pdf")).ToList();
            List<string> cloudUrls = docs
                .Where(d => !string.IsNullOrEmpty(d.CloudUrl))
                .Select(d => d.CloudUrl)
                .ToList();

            await ProcessDeclarations(evaluateCvDocs, cloudUrls);
            await ProcessForms(evaluateCvDocs, cloudUrls);
            await SaveFormsToProcessInstance(evaluateCvDocs);
            await VerifyDecPage(evaluateCvDocs);
            await VerifyFormsPage(evaluateCvDocs);

            if (evaluateCvDocs.FormsPass && evaluateCvDocs.AllDeclarationsFound && evaluateCvDocs.FormsInDataBase)
            {
                evaluateCvDocs.ProcessStatus = (int)ProcessStatusIdEnum.PolicyProcessed;
            }
            else if (evaluateCvDocs.FormsFound && evaluateCvDocs.AllDeclarationsFound && !evaluateCvDocs.FormsInDataBase)
            {
                evaluateCvDocs.ProcessStatus = (int)ProcessStatusIdEnum.FullPolicyUploadedUnknownForms;
            }
            else if (evaluateCvDocs.FormsFound && !evaluateCvDocs.AllDeclarationsFound && !evaluateCvDocs.FormsInDataBase)
            {
                evaluateCvDocs.ProcessStatus = (int)ProcessStatusIdEnum.UnknownForms;
            }
            else if (!evaluateCvDocs.FormsFound || evaluateCvDocs.NoDeclarationsFound)
            {
                evaluateCvDocs.ProcessStatus = (int)ProcessStatusIdEnum.RequiredDataMissing;
            }
            else
            {
                evaluateCvDocs.ProcessStatus = (int)ProcessStatusIdEnum.PolicyNeedsResearch;
            }

            ProcessInstance? processInstance = await _processInstanceApi.GetProcessInstanceByGuid(evaluateCvDocs.ProcessInstanceGuid, _errorComponent, _jwtToken);
            if (processInstance == null || processInstance.ProcessInstanceGuid == Guid.Empty)
            {
                return evaluateCvDocsResult;
            }

            processInstance.ProcessStatusId = evaluateCvDocs.ProcessStatus;

            AssessedPolicy assessedPolicy = new()
            {
                AssessedPolicyGuid = Guid.NewGuid(),
                ProcessInstanceGuid = processInstance.ProcessInstanceGuid,
                ExpirationDate = evaluateCvDocs.AssumedPolicyExpirationDate,
                EffectiveDate = evaluateCvDocs.AssumedPolicyEffectiveDate,
                CarrierName = evaluateCvDocs.CarrierName,
                PolicyNumber = evaluateCvDocs.PolicyNumber,
                AggregateLimit = evaluateCvDocs.AggregateLimit,
                OccuranceLimit = evaluateCvDocs.OccuranceLimit,
                Deductible = evaluateCvDocs.Deductible,
            };

            List<Task<bool>> updateDocTasks = docs.Select(doc =>
            {
                ProcessInstanceDoc processInstanceDoc = new()
                {
                    ProcessInstanceGuid = evaluateCvDocs.ProcessInstanceGuid,
                    DocGuid = doc.DocGuid,
                    ProcessDocStatusId = evaluateCvDocs.DocStatus,
                };

                return _processInstanceDocApi.UpdateProcessInstanceDoc(processInstanceDoc, _errorComponent, _jwtToken);
            }).ToList();

            Task<bool> updateProcessInstanceTask = _processInstanceApi.UpdateProcessInstanceStatusAndAddHistory(processInstance, _errorComponent, _jwtToken);
            Task<bool> addAssessedPolicyTask = _assessedPolicyApi.AddAssessedPolicy(assessedPolicy, _errorComponent, _jwtToken);

            List<Task<bool>> allTasks = updateDocTasks
                .Append(updateProcessInstanceTask)
                .Append(addAssessedPolicyTask)
                .ToList();

            bool[] taskResults = await Task.WhenAll(allTasks);

            if (taskResults.Any(r => !r))
            {
                StringBuilder message = new();
                message.Append("Evaluation finalization failed.");

                for (int i = 0; i < updateDocTasks.Count; i++)
                {
                    if (!taskResults[i])
                    {
                        message.Append($" Document update failed for DocGuid: {docs[i].DocGuid}");
                    }
                }

                if (!taskResults[updateDocTasks.Count])
                {
                    message.Append(" A problem occurred updating the process instance status.");
                }

                if (!taskResults[updateDocTasks.Count + 1])
                {
                    message.Append(" A problem occurred adding the AssessedPolicy record.");
                }

                processInstance.ProcessStatusId = (int)ProcessStatusIdEnum.PolicyNeedsResearch;
                await _processInstanceApi.UpdateProcessInstanceStatusAndAddHistory(processInstance, _errorComponent, _jwtToken);

                return new EvaluateCvDocsResult
                {
                    Success = false,
                    Message = message.ToString()
                };
            }

            evaluateCvDocsResult.Success = true;

            if (!evaluateCvDocs.LimitsAndDatesPass || !evaluateCvDocs.FormsPass)
            {
                await SendEmailAndZoomNotificationToAgentAsync(processInstance.ProcessInstanceGuid);
            }

            return evaluateCvDocsResult;
        }

        public async Task SaveFormsToProcessInstance(EvaluateCvDocs evaluateCvDocs)
        {
            evaluateCvDocs.ProcessStatus = (int)ProcessStatusIdEnum.PolicyNeedsResearch;
            if (evaluateCvDocs.FormsResponse.Forms.Any())
            {
                List<string> formText = GetFormTextFromResponse(evaluateCvDocs.FormsResponse);
                DocModel doc = new()
                {
                    DocGuid = Guid.Empty,
                    FileName = "FormOcrResults.txt",
                    FolderGuid = evaluateCvDocs.AssessedGuid,
                    EmployeeGuid = Guid.Empty,
                    DocTypeId = 13,
                    FileData = formText.SelectMany(s => Encoding.UTF8.GetBytes(s + Environment.NewLine)).ToArray()
                };

                await _docApi.UploadDoc(doc.FileName, doc.FileData, doc.FolderGuid, Guid.Empty, doc.NodeType, doc.DocTypeId, _errorComponent, _jwtToken);

                List<Form> possibleForms = await FindPossibleFormMatches(evaluateCvDocs.FormsResponse.Forms);

                if (possibleForms.Any())
                {
                    evaluateCvDocs.FormsInDataBase = true;
                    await _processInstanceFormApi.AddFormsToProcessInstance(possibleForms, evaluateCvDocs.ProcessInstanceGuid, _errorComponent, _jwtToken);
                    await _processInstanceFormApi.BulkAddScores(evaluateCvDocs.ProcessInstanceGuid, _errorComponent, _jwtToken);
                }
                else
                {
                    evaluateCvDocs.FormsInDataBase = false;
                }

                if (!evaluateCvDocs.FormsInDataBase)
                {
                    await AddUnknownForms(evaluateCvDocs.FormsResponse.Forms, evaluateCvDocs.ProcessInstanceGuid);
                }
            }
        }



        public async Task VerifyDecPage(EvaluateCvDocs evaluateCvDocs)
        {
            if (evaluateCvDocs.DeclarationsResponse != null && IsDeclarationsResponseEmpty(evaluateCvDocs.DeclarationsResponse))
            {
                evaluateCvDocs.NoDeclarationsFound = true;
                return;
            }
            await UploadOcrDeclarationsResult(evaluateCvDocs);
            await ProcessDeclarationsData(evaluateCvDocs);
        }

        public async Task VerifyFormsPage(EvaluateCvDocs evaluateCvDocs)
        {
            if (evaluateCvDocs.FormsFound)
            {
                List<string> cleanedFormList = CleanFormsList(evaluateCvDocs.FormsResponse.Forms);

                List<FormCoverageIssue> formCoverageIssues = (await _formCoverageIssueApi.GetByFormNumbers(cleanedFormList, _errorComponent, _jwtToken)).ToList();

                if (formCoverageIssues.Count > 0)
                {
                    List<bool> issueRequirementsMet = [];
                    IEnumerable<ProcessRequirementGroup> processRequirementGroups = await _processRequirementGroupApi.GetProcessRequirementGroupsByProcess(evaluateCvDocs.ProcessId, _errorComponent, _jwtToken);
                    foreach (ProcessRequirementGroup processRequirementGroup in processRequirementGroups)
                    {
                        List<IssueRequirement> issueRequirements = (await _issueRequirementApi.GetByRequirementGroupGuid(processRequirementGroup.RequirementGroupGuid, _errorComponent, _jwtToken)).ToList();

                        CoverageEvaluation coverageEvaluation = new()
                        {
                            FormsThatNeedAnalysis = formCoverageIssues,
                            IssueRequirements = issueRequirements
                        };
                        coverageEvaluation.FormCheckIssueRequirement();

                        issueRequirementsMet.Add(coverageEvaluation.CoveragePasses);
                    }

                    evaluateCvDocs.FormsPass = !issueRequirementsMet.Contains(false);
                }
                else
                {
                    evaluateCvDocs.FormsPass = false;
                }
            }
            else
            {
                evaluateCvDocs.DocStatus = (int)ProcessDocStatusEnum.UserDocErrorCannotOcr;
            }
        }

        private async Task<bool> SendEmailAndZoomNotificationToAgentAsync(Guid processInstanceGuid)
        {
            Assessed assessed = await _assessedApi.GetAssessedByProcessInstanceGuid(processInstanceGuid, _errorComponent, _jwtToken);
            string employeeEmail = "<EMAIL>"; // Hardcoded
            string message = $"There is an unverified Policy/Dec Page for {assessed.AssessedName}. Please use the PT online tool to verify the selections!";

            DirectSend directSend = new()
            {
                ExtraData = new Dictionary<string, string>()
                {
                    {"AssessedName", assessed.AssessedName }
                },
                NodeGuid = EmployeeList.Ethan,
                NodeType = NodeTypeEnum.Employee,
                TemplateId = (int)DirectSendTemplateEnum.CVUnverifiedDecPage,
                Targets =
                [
                    new DirectSendTarget()
                    {
                         TargetEmail = employeeEmail,
                         TargetMode = RecipientTypeEnum.Normal,
                         NodeTypeId = (int)NodeTypeEnum.Employee,
                         UserGuid = EmployeeList.Ethan
                    }
                ]

            };
            MessageModel directMessage = new()
            {
                AccountGuid = EmployeeList.Ethan,
                MessageData = message,
                RichTextList = []
            };

            try
            {
                await _messageApi.CreateNewDirectMessage(directMessage, _errorComponent, _jwtToken);
                await _directSendApi.SendEmail(directSend, _errorComponent, _jwtToken);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        private static async Task ProcessDeclarations(EvaluateCvDocs evaluateCvDocs, List<string> cloudUrls)
        {
            ChatGptHelper chatGptHelper = new();
            DeclarationsResponse declarationsResponse = await chatGptHelper.GetDeclarationsResponse(cloudUrls);

            if (declarationsResponse != null)
            {
                evaluateCvDocs.DeclarationsResponse = declarationsResponse;
            }
            else
            {
                evaluateCvDocs.NoDeclarationsFound = true;
                evaluateCvDocs.AllDeclarationsFound = false;
            }
        }

        private static async Task ProcessForms(EvaluateCvDocs evaluateCvDocs, List<string> cloudUrls)
        {
            ChatGptHelper chatGptHelper = new();
            FormsResponse formsResponse = await chatGptHelper.GetFormsResponse(cloudUrls);

            if (formsResponse != null && formsResponse.Forms != null && formsResponse.Forms.Count > 0)
            {
                evaluateCvDocs.FormsFound = true;
                evaluateCvDocs.FormsResponse = formsResponse;
            }
            else
            {
                evaluateCvDocs.FormsFound = false;
            }
        }

        private static List<string> GetDecTextFromResponse(DeclarationsResponse declarationsResponse)
        {
            StringBuilder builder = new();

            builder.AppendLine($"CarrierName: {declarationsResponse.CarrierName}");
            builder.AppendLine($"IssuingCompany: {declarationsResponse.IssuingCompany}");
            builder.AppendLine($"InsuredName: {declarationsResponse.InsuredName}");
            builder.AppendLine($"PolicyNumber: {declarationsResponse.PolicyNumber}");
            builder.AppendLine($"PolicyEffectiveDate: {declarationsResponse.PolicyEffectiveDate}");
            builder.AppendLine($"PolicyExpirationDate: {declarationsResponse.PolicyExpirationDate}");
            builder.AppendLine("CoverageParts:[");

            if (declarationsResponse.CoverageParts != null)
            {
                foreach (CoverageResponse coverage in declarationsResponse.CoverageParts)
                {
                    builder.AppendLine($"OccurrenceLimit: {coverage.OccurrenceLimit}");
                    builder.AppendLine($"AggregateLimit: {coverage.AggregateLimit}");
                    builder.AppendLine($"Deductible: {coverage.Deductible}");
                }
            }

            builder.AppendLine("]");
            builder.AppendLine($"Premium: {declarationsResponse.Premium}");

            return builder.ToString().Split(new[] { "\r\n", "\n" }, StringSplitOptions.RemoveEmptyEntries).ToList();
        }

        private static List<string> GetFormTextFromResponse(FormsResponse formsResponse)
        {
            StringBuilder builder = new();

            foreach (FormDetailsResponse form in formsResponse.Forms)
            {
                builder.AppendLine($"{form.FormNumber} | {form.FormDescription}");
            }

            return builder.ToString().Split(new[] { "\r\n", "\n" }, StringSplitOptions.RemoveEmptyEntries).ToList();
        }

        private static void SetDocumentMissingStatus(EvaluateCvDocs evaluateCvDocs)
        {
            evaluateCvDocs.ProcessStatus = (int)ProcessStatusIdEnum.RequiredDataMissing;
            evaluateCvDocs.DocStatus = (int)ProcessDocStatusEnum.NotProcessed;
        }

        private static string ExtractFormNumber(FormDetailsResponse input)
        {
            return $"{input.FormNumber} {input.FormDescription}";
        }

        private static string CleanFormNumber(string formNumber)
        {
            formNumber = Regex.Replace(formNumber, @"[\n\r]", "").Trim();
            formNumber = formNumber.Replace("-", " ");
            formNumber = formNumber.Replace("/", " ");
            formNumber = Regex.Replace(formNumber, @"[()]", "");
            formNumber = Regex.Replace(formNumber, @"\s+", " ").Trim();

            return formNumber;
        }

        private static List<string> CleanFormsList(IReadOnlyList<FormDetailsResponse> formsList)
        {
            return formsList
                .Select(input =>
                {
                    string formNumber = ExtractFormNumber(input);
                    Console.WriteLine($"Extracted Form Number: {formNumber}");

                    if (string.IsNullOrEmpty(formNumber))
                    {
                        return string.Empty;
                    }

                    return CleanFormNumber(formNumber);
                })
                .Where(cleaned => !string.IsNullOrEmpty(cleaned))
                .ToList();
        }

        private async Task UploadOcrDeclarationsResult(EvaluateCvDocs evaluateCvDocs)
        {
            List<string> decPageText = GetDecTextFromResponse(evaluateCvDocs.DeclarationsResponse);
            DocModel doc = new()
            {
                DocGuid = Guid.Empty,
                FileName = "DecOcrResults.txt",
                FolderGuid = evaluateCvDocs.AssessedGuid,
                EmployeeGuid = Guid.Empty,
                DocTypeId = 13,
                FileData = decPageText.SelectMany(s => Encoding.UTF8.GetBytes(s + Environment.NewLine)).ToArray()
            };

            await _docApi.UploadDoc(doc.FileName, doc.FileData, doc.FolderGuid, Guid.Empty, doc.NodeType, doc.DocTypeId, _errorComponent, _jwtToken);
        }
        private static bool IsDeclarationsResponseEmpty(DeclarationsResponse? declarations) =>
            declarations == null ||
            declarations.CoverageParts == null ||
            (string.IsNullOrWhiteSpace(declarations.CarrierName) &&
             string.IsNullOrWhiteSpace(declarations.InsuredName) &&
             string.IsNullOrWhiteSpace(declarations.PolicyNumber) &&
             string.IsNullOrWhiteSpace(declarations.PolicyEffectiveDate) &&
             string.IsNullOrWhiteSpace(declarations.PolicyExpirationDate) &&
             (declarations.CoverageParts.Count == 0 || declarations.Premium == 0));

        private async Task ProcessDeclarationsData(EvaluateCvDocs evaluateCvDocs)
        {
            DeclarationsResponse declarations = evaluateCvDocs.DeclarationsResponse;

            evaluateCvDocs.AssumedPolicyEffectiveDate = ParseDateOrDefault(declarations.PolicyEffectiveDate);
            evaluateCvDocs.AssumedPolicyExpirationDate = ParseDateOrDefault(declarations.PolicyExpirationDate);

            CoverageResponse? firstCoverage = declarations.CoverageParts.FirstOrDefault(c => c.OccurrenceLimit > 0 && c.AggregateLimit > 0);
            evaluateCvDocs.OccuranceLimit = firstCoverage?.OccurrenceLimit ?? 0;
            evaluateCvDocs.AggregateLimit = firstCoverage?.AggregateLimit ?? 0;
            evaluateCvDocs.Deductible = firstCoverage?.Deductible ?? 0;

            evaluateCvDocs.CarrierName = declarations.CarrierName;
            evaluateCvDocs.IssuingCompany = declarations.IssuingCompany;
            evaluateCvDocs.PolicyNumber = declarations.PolicyNumber;

            evaluateCvDocs.UnknownCarrier = !string.IsNullOrEmpty(evaluateCvDocs.CarrierName) &&
                (await _carriersApi.GetCarrierByName(evaluateCvDocs.CarrierName, _errorComponent, _jwtToken))?.CarrierGuid == Guid.Empty;

            evaluateCvDocs.LimitsAndDatesPass = evaluateCvDocs.OccuranceLimit > 0 &&
                evaluateCvDocs.AggregateLimit > 0 &&
                evaluateCvDocs.AssumedPolicyEffectiveDate != DateTime.MinValue &&
                evaluateCvDocs.AssumedPolicyExpirationDate != DateTime.MinValue;

            evaluateCvDocs.AllDeclarationsFound = evaluateCvDocs.LimitsAndDatesPass && !string.IsNullOrEmpty(evaluateCvDocs.PolicyNumber);

            evaluateCvDocs.DocStatus = evaluateCvDocs.LimitsAndDatesPass
                ? (int)ProcessDocStatusEnum.OcrProcessed
                : (int)ProcessDocStatusEnum.BadLimits;
        }

        private static DateTime ParseDateOrDefault(string? dateStr) =>
            DateTime.TryParse(dateStr, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDate)
                ? parsedDate
                : DateTime.MinValue;

        public async Task<List<Form>> FindPossibleFormMatches(IReadOnlyList<FormDetailsResponse> forms)
        {
            List<Form> matchingForms;
            IEnumerable<Form> possibleForms = await _formApi.GetAllFormsForOcrInCplProgram(_errorComponent, _jwtToken);

            List<string> cleanedForms = CleanFormsList(forms);
            HashSet<string> cleanedFormsSet = new(cleanedForms, StringComparer.OrdinalIgnoreCase);

            matchingForms = possibleForms
                .Where(f => f.FormNumber != null && cleanedFormsSet.Contains(f.FormNumber))
                .GroupBy(f => f.FormNumber, StringComparer.OrdinalIgnoreCase)
                .Select(g => g.First())
                .ToList();

            return matchingForms;
        }
        private async Task<bool> AddUnknownForms(IReadOnlyList<FormDetailsResponse> formsFound, Guid processInstanceGuid)
        {
            List<NewFormRequest> forms = [];

            foreach (FormDetailsResponse form in formsFound)
            {
                forms.Add(new NewFormRequest()
                {
                    NewFormRequestGuid = Guid.NewGuid(),
                    FormNumber = form.FormNumber,
                    FormDescription = form.FormDescription,
                    ProgramGuid = Guid.Empty,
                    CarrierGuid = CarrierList.CoverageVerifierDefault,
                    ProcessInstanceGuid = processInstanceGuid,
                    SubmittedAtZoned = DateTimeOffset.Now
                });
            }

            bool success = await _newFormRequestApi.AddNewFormRequests(forms, _errorComponent, _jwtToken);
            return success;
        }
    }
}
