using Mater.Shared;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Components;

namespace Mater.Components.Pages
{
    public class LoginBase : MaterComponentBase
    {
        [CascadingParameter]
        private HttpContext HttpContext { get; set; } = default!;

        [SupplyParameterFromQuery]
        private string? ReturnUrl { get; set; }

        protected override async Task OnInitializedAsync()
        {
            if (HttpMethods.IsGet(HttpContext.Request.Method) && !await IsUserAuthenticated())
            {
                await HttpContext.ChallengeAsync(OpenIdConnectDefaults.AuthenticationScheme, new AuthenticationProperties { RedirectUri = ReturnUrl });
            }
        }

        protected override async Task OnParametersSetAsync()
        {
            if (await IsUserAuthenticated())
            {
                NavManager.NavigateTo("/Dashboard");
            }

            await base.OnParametersSetAsync();
        }
    }
}
