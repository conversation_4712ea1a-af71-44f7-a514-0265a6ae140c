using FlexLibrary;
using Mater.Extensions;
using Mater.Shared;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.JSInterop;
using ServerComponentLibrary.Managers;
using ServerComponentLibrary.Services;
using UcpmApi.Shared;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Extensions;
using UcpmApi.Shared.Flex;
using UcpmApi.Shared.Mail;
using UcpmApi.Shared.Pulse;

namespace Mater.Components.Pages.Flex.AccountInfo;

public class AccountInfoPageBase : MaterComponentBase
{
    [Parameter]
    public required Guid FlexResponseGuid { get; set; }

    [Inject]
    protected FlexResponseSaveStateManager FlexResponseSaveStateManager { get; set; }

    [Inject]
    private DocManager DocManager { get; set; } = null!;

    [Inject]
    private CarrierSubmissionFlexResponseManager CarrierSubmissionFlexResponseManager { get; set; } = null!;

    [Inject]
    public FlexRatingManager FlexRatingManager { get; set; } = null!;

    [Inject]
    protected CarrierSubmissionSubjectivityManager CarrierSubmissionSubjectivityManager { get; set; }
    [Inject]
    protected CarrierSubmissionManager CarrierSubmissionManager { get; set; }

    [Inject]
    protected StateRequirementManager StateRequirementManager { get; set; }

    [Inject]
    protected StateManager StateManager { get; set; }

    [Inject]
    protected AgentManager AgentManager { get; set; }

    [Inject]
    protected DirectSendManager? DirectSendManager { get; set; }

    private List<string> DataNamesOnInsuredInfoPage { get; set; } = ["FirstNamedInsured", "MailingAddress", "City", "State", "Zip"]; // add more data names as needed

    public FlexResponseSaveState ResponseSaveState { get; set; }

    private List<FlexAnswer> InsuredInfoAnswers { get; set; } = [];

    private List<FlexQuestion> InsuredInfoQuestions { get; set; } = [];

    protected List<DocModel> RelevantDocuments { get; set; } = [];

    private readonly List<string> _includedDocTypes = ["Quote.pdf", "Policy.pdf", "App.pdf", "SL Affidavit.pdf", "Warranty Statement.pdf", "Binder Work.docx"];

    public Dictionary<FlexQuestion, FlexAnswer?> InsuredInfoLabelsAndAnswers { get; set; } = [];

    public FlexOption? SelectedOption { get; set; }

    public List<CarrierSubmissionSubjectivity> CarrierSubmissionSubjectivities { get; set; }

    public List<Guid> SubjectivityUploadingList { get; set; } = [];

    public bool AreSubjectivitiesUploading { get; set; } = false;

    public bool IsDownloadingAll { get; set; } = false;

    public List<DocModel> SubjectivityDocuments { get; set; } = [];

    public bool IsBinding { get; set; } = false;

    protected static readonly List<string> AllowedExtensions = [".pdf", ".png", ".jpeg", ".jpg"];

    private const long MaxFileSize = 50 * 1024 * 1024;

    private PolicyProspect? PolicyProspect { get; set; }

    private string PolicyNumber { get; set; } = string.Empty;
    public Guid CarrierSubmissionGuid { get; set; }
    private Guid InvoiceGuid { get; set; }
    
    protected override async Task OnInitializedAsync()
    {
        DataIsLoading = true;
        ResponseSaveState = await FlexResponseSaveStateManager.GetFlexResponseSaveState(FlexResponseGuid);
        await GetPolicyDetails();
        await GetInsuredInfoQuestionsAndAnswers();
        await GetRelevantDocuments();
        await GetStateForms();
        await GetSelectedOption();
        await GetSubjectivities();

        DataIsLoading = false;

        await base.OnInitializedAsync();
    }

    private async Task GetPolicyDetails()
    {
        List<CarrierSubmission> carrierSubmissions = await CarrierSubmissionManager!.GetCarrierSubmissionsByPolicyProspect(ResponseSaveState.PolicyProspectGuid);
        PolicyNumber = carrierSubmissions.FirstOrDefault()?.UcpmPolicyNumber ?? string.Empty;
        CarrierSubmissionGuid = carrierSubmissions.FirstOrDefault()?.CarrierSubmissionGuid ?? Guid.Empty;
        InvoiceGuid = carrierSubmissions.FirstOrDefault()?.CarrierSubmissionOption.FirstOrDefault()?.Invoice.FirstOrDefault()?.InvoiceGuid ?? Guid.Empty;
    }

    private async Task GetSubjectivities()
    {
        CarrierSubmissionSubjectivities = await CarrierSubmissionSubjectivityManager.GetAllCarrierSubmissionSubjectivityByCarrierSubmissionGuid(CarrierSubmissionGuid);
        foreach (CarrierSubmissionSubjectivity carrierSubmissionSubjectivity in CarrierSubmissionSubjectivities)
        {
            if (carrierSubmissionSubjectivity.SubjectivityStatusId == (int)SubjectivityStatusEnum.Received)
            {
                List<DocModel> existingDocs = await DocManager.GetDocsBySourceGuid(carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid);
                DocModel? firstDoc = existingDocs.FirstOrDefault();
                if (firstDoc != null)
                {
                    SubjectivityDocuments.Add(firstDoc);
                }
            }
        }
    }

    private async Task GetSelectedOption()
    {
        FlexRating flexRating = await FlexRatingManager.GetPricing(FlexResponseGuid, false);
        SelectedOption = flexRating.Options.FirstOrDefault(o => o.IsSelected);
    }

    private async Task GetRelevantDocuments()
    {
        if (CarrierSubmissionGuid != Guid.Empty)
        {
            IEnumerable<DocModel> documents =
                await DocManager.GetDocsBySourceGuid(CarrierSubmissionGuid);
            RelevantDocuments = documents.Where(d => _includedDocTypes.Any(t => d.FileName.Contains(t, StringComparison.OrdinalIgnoreCase))).ToList();

        }
    }

    private async Task GetStateForms()
    {
        FlexAnswer? stateAnswer = InsuredInfoAnswers.FirstOrDefault(q => q.QuestionDataName == "State");
        if (!string.IsNullOrEmpty(stateAnswer?.Answer))
        {
            List<State> states = await StateManager.GetAllStates();
            State? state = states.FirstOrDefault(s => s.StateName == stateAnswer.Answer);
            if (state != null)
            {
                IEnumerable<StateRequirement> stateForms = await StateRequirementManager.GetByStateCode(state.Code);
                IEnumerable<StateRequirement> aplStateForms = stateForms?.Where(s => s.PolicyTypeId == 0 || s.PolicyTypeId == (int)PolicyTypeEnum.PollutionLiabilityInsurance) ?? [];
                foreach (StateRequirement stateForm in aplStateForms)
                {
                    RelevantDocuments.Add(new DocModel { FileName = stateForm.RequirementName, CloudUrl = stateForm.RequirementUrl });
                }
            }
        }
    }

    private Task GetInsuredInfoQuestionsAndAnswers()
    {
        InsuredInfoQuestions = ResponseSaveState.Questions.FlexSurvey.FlexQuestions
            .Where(q => DataNamesOnInsuredInfoPage.Contains(q.DataName)).OrderBy(q => q.QuestionIndex).ToList();

        InsuredInfoAnswers = ResponseSaveState.Answers.FlexAnswers
            .Where(a => DataNamesOnInsuredInfoPage.Contains(a.QuestionDataName)).ToList();

        // get the question and answer labels
        InsuredInfoLabelsAndAnswers = InsuredInfoQuestions
            .ToDictionary(q => q, q =>
                InsuredInfoAnswers.FirstOrDefault(a => a.QuestionDataName == q.DataName));
        return Task.CompletedTask;
    }

    public async Task DownloadAll()
    {
        try
        {
            IsDownloadingAll = true;
            using HttpResponseMessage r = await DocManager.DownloadAllByUrls(RelevantDocuments.Select(x => x.CloudUrl));
            byte[] fileBytes = await r.Content.ReadAsByteArrayAsync();
            string? fileName = r.Content.Headers.ContentDisposition.FileName;
            await JSRuntime.InvokeVoidAsync("downloadFile", fileBytes, fileName);
        }
        finally
        {
            IsDownloadingAll = false;
        }
    }

    public async Task HandleCloseAccount()
    {
        UpdateCarrierSubmissionModel model = new()
        {
            CarrierSubmissionGuid = CarrierSubmissionGuid,
            PolicyStatusId = (int)PolicyStatusEnum.Closed
        };

        bool result = await CarrierSubmissionManager.UpdatePolicyStatusBySubmissionGuid(model);
        if (result)
        {
            ResponseSaveState.Status = "Closed";
            ToastService.ShowToast("Account has been closed.", ToastLevelEnum.Success, 8000);
        }
        else
        {
            ToastService.ShowToast("An error occurred while closing the account. Please try again.", ToastLevelEnum.Error, 8000);
        }

        StateHasChanged();
    }

    public async Task ValidateAndUploadFile(InputFileChangeEventArgs args, CarrierSubmissionSubjectivity carrierSubmissionSubjectivity)
    {
        if (args.File != null)
        {
            IBrowserFile file = args.File;
            string extension = Path.GetExtension(file.Name).ToLowerInvariant();
            if (!AllowedExtensions.Contains(extension))
            {
                ToastService.ShowToast($"Invalid file type selected. Allowed file types are: {string.Join(", ", AllowedExtensions)}.", ToastLevelEnum.Error, 8000);
                return;
            }
            if (file.Size > MaxFileSize)
            {
                ToastService.ShowToast($"File size exceeds the maximum of {MaxFileSize / (1024 * 1024)} MB.", ToastLevelEnum.Error, 8000);
                return;
            }
            await UploadSubjectivity(args, carrierSubmissionSubjectivity);
        }
        else
        {
            ToastService.ShowToast("No file was selected.", ToastLevelEnum.Warning, 8000);
            return;
        }
    }

    public async Task UploadSubjectivity(InputFileChangeEventArgs args, CarrierSubmissionSubjectivity carrierSubmissionSubjectivity)
    {
        SubjectivityUploadingList.Add(carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid);
        StateHasChanged();

        IBrowserFile uploadedFile = args.File;
        DocModel newDoc = await ProcessUploadedFile(uploadedFile, carrierSubmissionSubjectivity);

        DocModel? oldDoc = SubjectivityDocuments.Find(d => d.SourceGuid == carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid);
        if (oldDoc != null)
        {
            SubjectivityDocuments.Remove(oldDoc);
        }
        SubjectivityDocuments.Add(newDoc);
        await UploadSubjectivities(newDoc);

        SubjectivityUploadingList.Remove(carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid);
        StateHasChanged();
    }

    public async Task UploadSubjectivities(DocModel subjectivityDocument)
    {
        AreSubjectivitiesUploading = true;

        CarrierSubmissionSubjectivity? carrierSubmissionSubjectivity = CarrierSubmissionSubjectivities.Find(s => s.CarrierSubmissionSubjectivityGuid == subjectivityDocument.SourceGuid);
        DocModel existingDoc = await DocManager.GetDocBySourceAndName(subjectivityDocument.SourceGuid, subjectivityDocument.FileName);
        if (existingDoc != null && existingDoc.DocGuid != Guid.Empty)
        {
            await DocManager.ArchiveDoc(existingDoc);
        }

        existingDoc = await DocManager.UploadDoc(subjectivityDocument);
        if (existingDoc == null || existingDoc.DocGuid == Guid.Empty)
        {
            ToastService.ShowToast("A problem occurred while uploading the document. The document was not uploaded.", ToastLevelEnum.Error, 8000);
            return;
        }

        if (carrierSubmissionSubjectivity != null)
        {
            carrierSubmissionSubjectivity.SubjectivityStatusId = (int)SubjectivityStatusEnum.Received;
            carrierSubmissionSubjectivity.LinkedDocGuid = existingDoc.DocGuid;

            bool result = await UpdateSubjectivity(carrierSubmissionSubjectivity);
            if (result)
            {
                ToastService.ShowToast("Subjectivity has been uploaded!", ToastLevelEnum.Success, 8000);
            }
            else
            {
                ToastService.ShowToast("A problem occurred while updating the subjectivity. The document was not uploaded.", ToastLevelEnum.Error, 8000);
            }
        }

        AreSubjectivitiesUploading = false;
    }

    public bool GetIsSubjectivityLoading(Guid carrierSubmissionSubjectivityGuid)
    {
        return SubjectivityUploadingList.Contains(carrierSubmissionSubjectivityGuid);
    }

    public string GetPlanName(int? materPlanId)
    {
        if (materPlanId == null)
        {
            return string.Empty;
        }

        return ((AplPlanEnum)materPlanId).ToDescriptionString();
    }

    public string GetPlanDescription(int? materPlanId)
    {
        if (materPlanId == null)
        {
            return string.Empty;
        }

        return ((AplPlanEnum)materPlanId).GetPlanDescription();
    }

    public bool GetIsAllSubjectivitiesTemporarilyUploaded()
    {
        
        return CarrierSubmissionSubjectivities.All(s => SubjectivityDocuments.Any(d => d.SourceGuid == s.CarrierSubmissionSubjectivityGuid));
    }

    private async Task<bool> UpdateSubjectivity(CarrierSubmissionSubjectivity carrierSubmissionSubjectivity)
    {
        CarrierSubmissionSubjectivity subjectivityToUpdate = new()
        {
            CarrierSubmissionSubjectivityGuid = carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid,
            Note = carrierSubmissionSubjectivity.Note,
            SubjectivityTextOverride = carrierSubmissionSubjectivity.SubjectivityTextOverride,
            LinkedDocGuid = carrierSubmissionSubjectivity.LinkedDocGuid,
            RecordCreatedZoned = DateTimeOffset.Now,
            SubjectivityStatusId = carrierSubmissionSubjectivity.SubjectivityStatusId,
        };

        return await CarrierSubmissionSubjectivityManager.UpdateCarrierSubmissionSubjectivity(carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid, subjectivityToUpdate);
    }

    private async Task<DocModel> ProcessUploadedFile(IBrowserFile file, CarrierSubmissionSubjectivity carrierSubmissionSubjectivity)
    {
        DocModel doc = new()
        {
            DocGuid = Guid.Empty,
            FileName = file.Name,
            FolderGuid = carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid,
            SourceGuid = carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid,
            EmployeeGuid = Guid.Empty,
            DocTypeId = (int)DocTypeEnum.Custom
        };

        using (Stream fileStream = file.OpenReadStream(MaxFileSize))
        {
            using MemoryStream stream = new();
            byte[] buffer = new byte[81920];
            int read = 0;

            while ((read = await fileStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
            {
                stream.Write(buffer, 0, read);
            }

            doc.FileData = stream.ToArray();
        }

        return doc;
    }



    public async Task SetPolicyStatusToOrdered()
    {
        IsBinding = true;
        UpdateCarrierSubmissionModel model = new()
        {
            CarrierSubmissionGuid = CarrierSubmissionGuid,
            PolicyStatusId = (int)PolicyStatusEnum.Ordered
        };
        await CarrierSubmissionManager.SetAndUpdateNextPolicyNumber(CarrierSubmissionGuid);
        bool success = await GenerateBinderWorkDoc();
        if (success)
        {
            success = await SendBinderWorkDocumentEmail();
            if (success)
            {
                bool result = await CarrierSubmissionManager.UpdatePolicyStatusBySubmissionGuid(model);
                if (result)
                {
                    ToastService.ShowToast("Subjectivities has been uploaded and a policy is in progress. Check back here for policy document.", ToastLevelEnum.Success, 8000);
                }
                else
                {
                    ToastService.ShowToast("A problem occurred updating the policy status.", ToastLevelEnum.Error, 8000);
                }
            }
            else
            {
                ToastService.ShowToast("A problem occurred while generating the binder document email.", ToastLevelEnum.Error, 8000);
            }
        }
        else
        {
            ToastService.ShowToast("A problem occurred while generating the binder document.", ToastLevelEnum.Error, 8000);
        }
        IsBinding = false;
        StateHasChanged();
    }

    private async Task<bool> SendBinderWorkDocumentEmail()
    {
        string insuredName = InsuredInfoAnswers.FirstOrDefault(x => x.QuestionDataName == "InsuredName")?.Answer ?? string.Empty;
        DocModel? binderDoc = RelevantDocuments.FirstOrDefault(d => d.FileName == "Binder Work.docx");
        Agent agent = await AgentManager.GetAgent(AccountGuid);

        DirectSend directSend = new()
        {
            ExtraData = new Dictionary<string, string>()
                {
                    { "AgentFirstName", base.UserFirstName },
                    { "AgentLastName", base.UserLastName },
                    { "PolicyNumber", PolicyNumber },
                    { "InsuredName", insuredName },
                },
            NodeGuid = AccountGuid,
            NodeType = NodeTypeEnum.Agent,
            TemplateId = (int)DirectSendTemplateEnum.BinderWorkEmail,
            Attachments = binderDoc != null ? [binderDoc.DocPath] : [],
            Targets =
            [
                new DirectSendTarget()
                    {
                        TargetEmail = agent.AgentEmail,
                        TargetMode = RecipientTypeEnum.Normal,
                        NodeTypeId = (int)NodeTypeEnum.Agent,
                        UserGuid = base.AccountGuid
                    }
            ]
        };

        DirectSendLog log = await DirectSendManager!.SendEmail(directSend);
        return true;
    }

    private async Task<bool> GenerateBinderWorkDoc()
    {
        DocModel doc = await DocManager!.GenerateBinderWorkDoc(InvoiceGuid);

        if (doc != null && doc.DocGuid != Guid.Empty)
        {
            if (RelevantDocuments.Any(d => d.FileName == "Binder Work.docx"))
            {
                RelevantDocuments.Remove(RelevantDocuments.First(d => d.FileName == "Binder Work.docx"));
            }

            RelevantDocuments.Add(doc);
            return true;
        }
        return false;
    }
}