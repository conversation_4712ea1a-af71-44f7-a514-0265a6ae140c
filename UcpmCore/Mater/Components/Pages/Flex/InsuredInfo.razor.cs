using FlexLibrary;
using Mater.Shared;
using Microsoft.AspNetCore.Components;
using ServerComponentLibrary.Managers;
using UcpmApi.Shared;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Flex;

namespace Mater.Components.Pages
{
    public class InsuredInfoBase : MaterComponentBase
    {
        [Parameter]
        public required Guid FlexResponseGuid { get; set; }

        [Inject]
        FlexResponseLogManager FlexResponseLogManager { get; set; }

        [Inject]
        protected PathResponseActionManager PathResponseActionManager { get; set; }

        protected override async Task OnInitializedAsync()
        {
            DataIsLoading = true;

            await base.OnInitializedAsync();
            await AddFlexResponseLog();

            DataIsLoading = false;
        }

        protected override Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                _ = LogPathResponseAction();
            }

            return base.OnAfterRenderAsync(firstRender);
        }

        private async Task AddFlexResponseLog()
        {
            FlexResponseLog flexResponseLog = new()
            {
                LogData = "Mater Insured Info Page",
                LogTypeId = (int)FlexResponseLogTypeEnum.Timer,
                FlexResponseGuid = FlexResponseGuid
            };

            bool succeded = await FlexResponseLogManager.AddLog(flexResponseLog);
        }

        //log action
        public async Task LogPathResponseAction(bool b = true)
        {
            await PathResponseActionManager.SavePathResponseAction(FlexResponseGuid, AccountGuid, "InsuredInfo", "InsuredInfo",
                b);
        }
    }
}
