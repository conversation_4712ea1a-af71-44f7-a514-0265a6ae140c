using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using System.Security.Claims;
using UcpmApi.Shared.Api.Security;
using UcpmApi.Shared.ApiClientBase.Interfaces;
using UcpmApi.Shared.Auth;
using UcpmApi.Shared.Enums;

namespace Mater.Middleware;


public class TokenManagementMiddleware
{
    private readonly RequestDelegate _next;

    public TokenManagementMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (context.User.Identity is { IsAuthenticated: true })
        {
            IEnumerable<Claim> userClaims = context.User.Claims;
            string? tokenExpirationClaim = userClaims.FirstOrDefault(c => c.Type == ClaimTypes.Expiration)?.Value;
            string? jwtToken = userClaims.FirstOrDefault(c => c.Type == "JwtToken")?.Value;
            string? userName = context.User.Identity.Name;

            if (tokenExpirationClaim != null && DateTimeOffset.TryParse(tokenExpirationClaim, out DateTimeOffset tokenExpiration))
            {
                if (tokenExpiration < DateTimeOffset.UtcNow)
                {
                    await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
                    context.Response.Redirect("/Account/Login");
                    return;
                }

                if (tokenExpiration < DateTimeOffset.UtcNow.AddMinutes(5) && !string.IsNullOrEmpty(jwtToken))
                {
                    UserApi userApi = context.RequestServices.GetRequiredService<UserApi>();
                    IErrorComponent errorComponent = context.RequestServices.GetRequiredService<IErrorComponent>();

                    ApplicationUserRefreshJwtRequest refreshRequest = new()
                    {
                        JwtToken = jwtToken,
                        AccountTypeId = (int)SecurityAccountTypeEnum.Agent,
                        UserName = userName,
                    };

                    ApplicationUser newJwtToken = await userApi.RefreshJwt(refreshRequest, errorComponent);

                    if (newJwtToken != null && !string.IsNullOrWhiteSpace(newJwtToken.JwtToken))
                    {
                        List<Claim> claims =
                        [
                            new("JwtToken", newJwtToken.JwtToken),
                            new(ClaimTypes.Name, userName),
                            new(ClaimTypes.Expiration, DateTimeOffset.UtcNow.AddMinutes(60).ToString()),
                            .. userClaims.Where(c => c.Type != "JwtToken" && c.Type != ClaimTypes.Expiration),
                        ];

                        ClaimsIdentity newIdentity = new(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                        ClaimsPrincipal newPrincipal = new(newIdentity);

                        await context.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, newPrincipal, new AuthenticationProperties { IsPersistent = true });
                    }
                    else
                    {
                        await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
                    }
                }
            }
        }

        await _next(context);
    }
}