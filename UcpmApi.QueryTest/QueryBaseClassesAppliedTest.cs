using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UcpmApi.Query;
using UcpmApi.Query.Acc;

namespace UcpmApi.QueryTest
{
    [TestClass]
    public class QueryBaseClassesAppliedTest
    {
        [TestMethod]
        public void VerifyQueriesAreInherited()
        {
            //We are not using this, except to force the load of the DLL into the test environment
            FeeTaxQuery feeTaxQuery = new (null);
            Assert.IsNotNull(feeTaxQuery);

            string rootNamespace = "UcpmApi.Query";
            string testNamespace = "UcpmApi.QueryTest";


            // Get all types in the current assembly
            var allTypes = AppDomain.CurrentDomain.GetAssemblies()
                .SelectMany(a => a.GetTypes())
                .Where(t => t.Namespace != null
                    && t.Namespace.StartsWith(rootNamespace)
                    && !t.Namespace.StartsWith(testNamespace)
                    && t.Name.EndsWith("Query")
                    )
                .ToList();


            Assert.IsTrue(allTypes.Count != 0, "No types found in the current assembly");

            // Filter types that are derived from BaseQuery
            var derivedTypes = allTypes
                .Where(t => t.IsClass && !t.IsAbstract && typeof(BaseQuery).IsAssignableFrom(t))
                .ToList();

            var errors = allTypes
                .Select(t => $"{t.Namespace}.{t.Name}")
                .Except(derivedTypes.Select(t => $"{t.Namespace}.{t.Name}"))
                .ToList();

            Assert.AreEqual(0, errors.Count, $"The following types are not derived from BaseQuery: {string.Join(", ", errors)}");
        }
    }
}
