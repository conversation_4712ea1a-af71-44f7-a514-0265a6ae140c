using EnvironmentInspector;
using EnvironmentInspector.Enums;
using EnvironmentInspector.Factories;
using LLBLGenHelper;
using LLBLGenHelper.Interfaces;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using ORMStandard.DatabaseSpecific;
using OrmStandardHelper;
using UcpmApi.Logging;
using UcpmApi.Query.Doc;

namespace UcpmApi.QueryTest.Doc;

[TestClass]
public class DocTypeQueryTest
{
    private DocTypeQuery _docTypeQuery;
    [TestInitialize]
    public void SetUp()
    {
        AppEnvironment appEnvironment =
            new AppEnvironmentFactory(DataAccessAdapter.ConnectionStringKeyName, ForCompanyEnum.Ucpm)
                .GetAppEnvironmentFromEnvironmentVariables("UcpmAPI-Tests");
        ConfigurationHelper.ConfigureOrm(appEnvironment);
        _docTypeQuery = new DocTypeQuery(new QuickAdapterAsync(new DataAccessAdapter(),new DataLogger()));
    }
    
    
    [TestMethod]
    public void GetDocTypeById_ReturnsCorrectDocType()
    {
        // Arrange
        // Act
        var result = _docTypeQuery.GetDocTypeById(20);

        // Assert
        Assert.AreEqual(20, result.DocTypeId);
    }
}