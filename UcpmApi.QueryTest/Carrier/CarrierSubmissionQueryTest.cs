using EnvironmentInspector;
using EnvironmentInspector.Enums;
using EnvironmentInspector.Factories;
using LLBLGenHelper;
using LLBLGenHelper.Interfaces;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using OrmStandardHelper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UcpmApi.Logging;
using UcpmApi.Query.Carrier;

namespace UcpmApi.QueryTest.Carrier;

[TestClass]
public class CarrierSubmissionQueryTest
{
    private Mock<IQuickAdapterReader> _mockQuickAdapterReader;
    private CarrierSubmissionQuery _mockCarrierSubmissionQuery;
    private CarrierSubmissionQuery _carrierSubmissionQuery;

    [TestInitialize]
    public void Initialize()
    {
        AppEnvironment appEnvironment =
            new AppEnvironmentFactory(DataAccessAdapter.ConnectionStringKeyName, ForCompanyEnum.Ucpm)
                .GetAppEnvironmentFromEnvironmentVariables("UcpmAPI-Tests");
        ConfigurationHelper.ConfigureOrm(appEnvironment);

        _mockQuickAdapterReader = new Mock<IQuickAdapterReader>();
        _mockCarrierSubmissionQuery =
            new CarrierSubmissionQuery(_mockQuickAdapterReader.Object);
        _carrierSubmissionQuery = new CarrierSubmissionQuery(new QuickAdapterReader(new DataAccessAdapter(), new DataLogger()));
    }

    [TestMethod]
    public async Task GetSubmissionsMissingSlDueDate_FetchesCorrect()
    {
        // Arrange
        // Act
        IEnumerable<CarrierSubmissionEntity> result = await _mockCarrierSubmissionQuery.GetSubmissionsMissingSlDueDate();

        // Assert
        Assert.IsNotNull(result);
    }

    [TestMethod, Ignore]
    public void MyTestMethod()
    {
        Task<ORMStandard.HelperClasses.EntityCollection<CarrierSubmissionEntity>> yoski = _mockCarrierSubmissionQuery.GetCarrierSubmissionsByPolicyGuid(Guid.Parse("a25bfe41-239b-43bf-b601-0073dae32ffc"));
        ORMStandard.HelperClasses.EntityCollection<CarrierSubmissionEntity> broski = yoski.Result;
        foreach (CarrierSubmissionEntity item in broski)
        {
            Console.WriteLine(item.PolicyStatus.Priority);
        }
        Assert.AreEqual(broski.First().PolicyProspectGuid, Guid.Parse("a25bfe41-239b-43bf-b601-0073dae32ffc"));
    }

    [TestMethod]
    public async Task GetOrphanedCarrierSubmissions_FetchesCorrect()
    {
        // Arrange
        int limit = 5;

        // Act
        IEnumerable<CarrierSubmissionEntity> result = await _mockCarrierSubmissionQuery.GetOrphanedCarrierSubmissions(limit);

        // Assert
        Assert.IsNotNull(result);
    }

    [TestMethod]
    public void ViaCarrierSubmissionGetCarrierMaxOffer()
    {
        IQuickAdapterReader Iquick = new QuickAdapterReader(new DataAccessAdapter(), new DataLogger());
        CarrierSubmissionQuery subQuery = new(Iquick);
       // 8bc90d24-9632-40f1-a771-820e9d7297c3
       var nxusSubGuid = Guid.Parse("8bc90d24-9632-40f1-a771-820e9d7297c3");
       //6a33ad44-3d4f-432e-8426-62abd2de47eb
        Guid subGuid = Guid.Parse("E7295A35-3B50-47DA-99A5-DC2B591A5F82");
        CarrierSubmissionEntity sub = subQuery.GetByGuid(subGuid);
        CarrierSubmissionEntity maxOffer = subQuery.SubmissionToMaxOffer(nxusSubGuid);
       // CarrierMaximumOfferEntity value = maxOffer.PolicyProspect.Package.Program.CarrierProgram.SelectMany(sm => sm.CarrierMaximumOffer).Where(w => w.CarrierProgram.CarrierGuid == maxOffer.CarrierGuid).FirstOrDefault(fd => fd.VariationName == "Nxus");

        Assert.IsNotNull(maxOffer);
    }
    
    [TestMethod,Ignore]
    public async Task GetCarrierSubmissionsByPackage_ShouldReturnSubmissions()
    {
        // Arrange
        Guid packageGuid = Guid.Parse("fa3a7673-1824-42cc-9549-fe2b513f7b87");
        // Act
        var result = await _carrierSubmissionQuery.GetCarrierSubmissionsByPackage(packageGuid);
    
        // Assert
        if (result != null)
        {
            Assert.IsTrue(result.First().PolicyProspect.Package.PrimaryRepEmployee.EmployeeGuid!=Guid.Empty);
        }
        //Assert.IsNotNull(result);
        //Assert.AreEqual(1, result.Count);
    }
}//