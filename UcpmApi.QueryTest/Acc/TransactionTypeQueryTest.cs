using System.Linq;
using System.Threading.Tasks;
using EnvironmentInspector;
using EnvironmentInspector.Enums;
using EnvironmentInspector.Factories;
using LLBLGenHelper;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using OrmStandardHelper;
using UcpmApi.Logging;
using UcpmApi.Query.Acc;

namespace UcpmApi.QueryTest.Acc;

[TestClass]
public class TransactionTypeQueryTest
{
    private TransactionTypeQuery _transactionTypeQuery;
    private Mock<LinqMetaData> _mockLinqMetaData;

    [TestInitialize]
    public void Setup()
    {
        AppEnvironment appEnvironment =
            new AppEnvironmentFactory(DataAccessAdapter.ConnectionStringKeyName, ForCompanyEnum.Ucpm)
                .GetAppEnvironmentFromEnvironmentVariables("UcpmAPI-Tests");
        ConfigurationHelper.ConfigureOrm(appEnvironment);
        _transactionTypeQuery =
            new TransactionTypeQuery(new QuickAdapterAsync(new DataAccessAdapter(), new DataLogger()));
        _mockLinqMetaData = new Mock<LinqMetaData>();
    }

    [TestMethod]
    public void GetTransactionTypeById_ReturnsCorrectTransactionType()
    {
        // Arrange
        // active transaction type
        var transactionType = new TransactionTypeEntity
        {
            TransactionTypeId = 1,
            TamCode = "*EN",
            TransactionDescription = "Endorsement",
            NewtonCompanyCode = "B**",
            Active = true
        };
        // inactive transaction type
        var transactionType2 = new TransactionTypeEntity
        {
            TransactionTypeId = 12,
            TamCode = "CRP",
            TransactionDescription = "Payment",
            NewtonCompanyCode = "B**",
            Active = false
        };

        // Act
        // get active transaction type
        var result = _transactionTypeQuery.GetTransactionTypeById(1);
        // get inactive transaction type
        var result2 = _transactionTypeQuery.GetTransactionTypeById(12, false);

        // Assert
        // active transaction type
        Assert.IsNotNull(result);
        Assert.AreEqual(transactionType.TransactionTypeId, result.TransactionTypeId);
        Assert.AreEqual(transactionType.TamCode, result.TamCode);
        Assert.AreEqual(transactionType.TransactionDescription, result.TransactionDescription);
        Assert.AreEqual(transactionType.NewtonCompanyCode, result.NewtonCompanyCode);
        Assert.AreEqual(transactionType.Active, result.Active);
        // inactive transaction type
        Assert.IsNotNull(result2);
        Assert.AreEqual(transactionType2.TransactionTypeId, result2.TransactionTypeId);
        Assert.AreEqual(transactionType2.TamCode, result2.TamCode);
        Assert.AreEqual(transactionType2.TransactionDescription, result2.TransactionDescription);
        Assert.AreEqual(transactionType2.NewtonCompanyCode, result2.NewtonCompanyCode);
        Assert.AreEqual(transactionType2.Active, result2.Active);
    }

    [TestMethod]
    public void GetTransactionTypeById_ReturnsNullWhenNotFound()
    {
        // Arrange

        // Act
        var result = _transactionTypeQuery.GetTransactionTypeById(-12);

        // Assert
        Assert.IsNull(result);
    }

    [TestMethod]
    public async Task GetActiveTransactionTypes_ReturnsActiveTransactionTypes()
    {
        // Arrange
        var transactionTypes = new EntityCollection<TransactionTypeEntity>
        {
            new TransactionTypeEntity { TransactionTypeId = 1, Active = true },
            new TransactionTypeEntity { TransactionTypeId = 2, Active = true }
        };

        // Act
        var result = await _transactionTypeQuery.GetActiveTransactionTypes();

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(19, result.Count);
        Assert.IsTrue(result.All(t => t.Active));
    }

    [TestMethod]
    public void GetTransactionByPtCode_ReturnsCorrectTransactionType()
    {
        // Arrange
        var transactionType = new TransactionTypeEntity
        {
            TransactionTypeId = 3,
            PolicyTrackerCode = "AUD",
            TransactionDescription = "Audit Premium",
            NewtonCompanyCode = "B**",
            Active = true
        };
        // Act
        var result = _transactionTypeQuery.GetTransactionByPtCode(transactionType.PolicyTrackerCode);

        // Assert
        Assert.AreEqual(transactionType.PolicyTrackerCode, result.PolicyTrackerCode);
        Assert.IsTrue(result.Active);
        Assert.AreEqual(transactionType.TransactionDescription, result.TransactionDescription);
        Assert.AreEqual(transactionType.NewtonCompanyCode, result.NewtonCompanyCode);
    }

    [TestMethod]
    public void GetTransactionByPtCode_ReturnsNullWhenNotFound()
    {
        // Arrange
        var policyTrackerCode = "PFEE";
        // Act
        var result = _transactionTypeQuery.GetTransactionByPtCode(policyTrackerCode);

        // Assert
        Assert.IsNull(result);
    }
}