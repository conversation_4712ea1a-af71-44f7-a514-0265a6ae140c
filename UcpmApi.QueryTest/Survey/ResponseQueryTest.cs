using EnvironmentInspector;
using EnvironmentInspector.Enums;
using EnvironmentInspector.Factories;
using LLBLGenHelper;
using LLBLGenHelper.Interfaces;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using OrmStandardHelper;
using SD.LLBLGen.Pro.ORMSupportClasses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UcpmApi.Logging;
using UcpmApi.Query.Survey;

namespace UcpmApi.QueryTest.Survey;

[TestClass]
public class ResponseQueryTest
{
    private ResponseQuery _responseQuery;
    private Mock<IQuickAdapterAsync> _mockQuickAdapterAsync;
    private ResponseQuery _mockResponseQuery;
    private Mock<IResponseQueryWrapper> _mockResponseQueryWrapper;

    [TestInitialize]
    public void Initialize()
    {
        AppEnvironment appEnvironment =
            new AppEnvironmentFactory(DataAccessAdapter.ConnectionStringKeyName, ForCompanyEnum.Ucpm)
                .GetAppEnvironmentFromEnvironmentVariables("UcpmAPI-Tests");
        ConfigurationHelper.ConfigureOrm(appEnvironment);

        _mockQuickAdapterAsync = new Mock<IQuickAdapterAsync>();
        _mockResponseQuery = new ResponseQuery(_mockQuickAdapterAsync.Object);
        _responseQuery =
            new ResponseQuery(new QuickAdapterAsync(new DataAccessAdapter(), new DataLogger()));
        _mockResponseQueryWrapper = new Mock<IResponseQueryWrapper>();
    }

    [TestMethod]
    public async Task TestGetResponsesToRenew()
    {
        int responseToFetch = 10;
        EntityCollection<ResponseEntity> responses = await _responseQuery.GetResponsesToRenew(responseToFetch);

        Assert.IsNotNull(responses);
    }
    [TestMethod, Ignore()]
    public async Task GetResponseWithoutLinkingGuid_ReturnsExpectedResponses_WhenCalledWithValidParameters()
    {
        // Arrange
        // parameter responsesToBeAdded is not used in the method
        IEnumerable<ResponseEntity> getResponseParameters =
        [
            new() { ClonedFromResponseGuid = Guid.Parse("13832ced-26da-471b-bcce-0000a5c4acc5") },
            new() { ClonedFromResponseGuid = Guid.Parse("110463c8-4634-4830-a567-0000ced5b1eb") },
            new() { ClonedFromResponseGuid = Guid.Parse("e187a97e-349f-478e-be22-000176ef9fcc") },
            new() { ClonedFromResponseGuid = Guid.Parse("e41f94e6-9415-42b4-8044-0001fbfc2c1a") },
            new() { ClonedFromResponseGuid = Guid.Parse("cc5f1092-6f08-4c98-8347-0002033bb9de") }
        ];

        _mockQuickAdapterAsync.Setup(a =>
                a.FetchEntityCollectionAsync(It.IsAny<QueryParameters>(), "", ""))
            .Returns(Task.CompletedTask);
        _mockResponseQueryWrapper.Setup(a => a.GetResponseWithoutLinkingGuid(getResponseParameters))
            .Returns(Task.FromResult(new EntityCollection<ResponseEntity>()));

        // Act
        // mock call
        EntityCollection<ResponseEntity> result = await _mockResponseQuery.GetResponseWithoutLinkingGuid(getResponseParameters);
        EntityCollection<ResponseEntity> mockResult = await _mockResponseQueryWrapper.Object.GetResponseWithoutLinkingGuid(getResponseParameters);
        // real call
        EntityCollection<ResponseEntity> result2 = await _responseQuery.GetResponseWithoutLinkingGuid(getResponseParameters);

        // Assert
        // mock result
        _mockQuickAdapterAsync.Verify(a =>
            a.FetchEntityCollectionAsync(It.IsAny<QueryParameters>(), "", ""), Times.Once);
        _mockResponseQueryWrapper.Verify(a => a.GetResponseWithoutLinkingGuid(getResponseParameters), Times.Once);
        Assert.IsFalse(result.Any());
        // real result
        Assert.IsTrue(result2.Any());
        Assert.AreEqual(5, result2.Count);
        foreach (ResponseEntity entity in result2)
        {
            Assert.IsNotNull(entity.ResponseGuid);
            Assert.IsTrue(getResponseParameters.Any(a => a.ClonedFromResponseGuid == entity.ResponseGuid));
        }
    }

    [TestMethod, Ignore()]
    public async Task GetResponseWithoutLinkingGuid_ReturnsEmptyCollection_WhenNoMatchingResponsesFound()
    {
        // Arrange
        List<ResponseEntity> responsesToBeAdded = [new ResponseEntity { ClonedFromResponseGuid = Guid.NewGuid() }];
        _mockQuickAdapterAsync.Setup(a =>
                a.FetchEntityCollectionAsync(It.IsAny<QueryParameters>(), "", ""))
            .Returns(Task.CompletedTask);

        // Act
        // mock call
        EntityCollection<ResponseEntity> result = await _mockResponseQuery.GetResponseWithoutLinkingGuid(responsesToBeAdded);
        // real call
        EntityCollection<ResponseEntity> result2 = await _responseQuery.GetResponseWithoutLinkingGuid(responsesToBeAdded);

        // Assert
        // mock result
        _mockQuickAdapterAsync.Verify(a =>
            a.FetchEntityCollectionAsync(It.IsAny<QueryParameters>(), "", ""), Times.Once);
        Assert.IsFalse(result.Any());
        // real result
        Assert.IsFalse(result2.Any());
    }

    [TestMethod, Ignore()]
    public async Task GetByLinkingGuidWithoutPrefetch_ReturnsExpectedResponses_WhenCalledWithValidParameters()
    {
        // Arrange
        Guid responseLinkingGuid = Guid.Parse("354748bc-2764-474e-be15-fcd543235940");
        EntityCollection<ResponseEntity> expectedResponses = [new ResponseEntity(), new ResponseEntity()];
        _mockQuickAdapterAsync.Setup(a => a.FetchEntityCollectionAsync(It.IsAny<QueryParameters>(), "", ""))
            .Returns(Task.CompletedTask);
        _mockResponseQueryWrapper.Setup(a => a.GetByLinkingGuidWithoutPrefetch(responseLinkingGuid));

        // Act
        // mock call
        EntityCollection<ResponseEntity> result = await _mockResponseQuery.GetByLinkingGuidWithoutPrefetch(responseLinkingGuid);
        EntityCollection<ResponseEntity> mockResult = await _mockResponseQueryWrapper.Object.GetByLinkingGuidWithoutPrefetch(responseLinkingGuid);
        // real call
        EntityCollection<ResponseEntity> result2 = await _responseQuery.GetByLinkingGuidWithoutPrefetch(responseLinkingGuid);

        // Assert
        _mockQuickAdapterAsync.Verify(a => a.FetchEntityCollectionAsync(It.IsAny<QueryParameters>(), "", ""),
            Times.Once);
        _mockResponseQueryWrapper.Verify(a => a.GetByLinkingGuidWithoutPrefetch(responseLinkingGuid), Times.Once);

        CollectionAssert.DoesNotContain(expectedResponses, result);
        Assert.IsTrue(result2.Any());
    }


    [TestMethod]
    public async Task GetByLinkingGuidWithoutPrefetch_ReturnsEmptyCollection_WhenNoMatchingResponsesFound()
    {
        // Arrange
        _mockQuickAdapterAsync.Setup(a => a.FetchEntityCollectionAsync(It.IsAny<QueryParameters>(), "", ""))
            .Returns(Task.CompletedTask);

        // Act
        EntityCollection<ResponseEntity> result = await _mockResponseQuery.GetByLinkingGuidWithoutPrefetch(Guid.NewGuid());

        // Assert
        Assert.IsFalse(result.Any());
    }
}

#region Wrapper

public interface IResponseQueryWrapper
{
    Task<EntityCollection<ResponseEntity>> GetByLinkingGuidWithoutPrefetch(Guid linkingGuid);

    Task<EntityCollection<ResponseEntity>>
        GetResponseWithoutLinkingGuid(IEnumerable<ResponseEntity> responsesToBeAdded);
}

public class ResponseQueryWrapper : IResponseQueryWrapper
{
    private readonly ResponseQuery _responseQuery;

    public ResponseQueryWrapper(ResponseQuery responseQuery)
    {
        _responseQuery = responseQuery;
    }

    public async Task<EntityCollection<ResponseEntity>> GetByLinkingGuidWithoutPrefetch(Guid linkingGuid)
    {
        return await _responseQuery.GetByLinkingGuidWithoutPrefetch(linkingGuid);
    }

    public async Task<EntityCollection<ResponseEntity>> GetResponseWithoutLinkingGuid(
        IEnumerable<ResponseEntity> responsesToBeAdded)
    {
        return await _responseQuery.GetResponseWithoutLinkingGuid(responsesToBeAdded);
    }
}

#endregion