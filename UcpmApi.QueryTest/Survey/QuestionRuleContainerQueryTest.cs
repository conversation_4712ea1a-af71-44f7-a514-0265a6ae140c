using System;
using System.Threading.Tasks;
using EnvironmentInspector;
using EnvironmentInspector.Enums;
using EnvironmentInspector.Factories;
using LLBLGenHelper;
using LLBLGenHelper.Interfaces;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using OrmStandardHelper;
using SD.LLBLGen.Pro.ORMSupportClasses;
using UcpmApi.Logging;
using UcpmApi.Query.Survey;
using UcpmApi.Shared.Enums;

namespace UcpmApi.QueryTest.Survey;

[TestClass]
public class QuestionRuleContainerQueryTest
{
    private Mock<IQuickAdapterAsync> _mockQuickAdapterAsync;
    private QuestionRuleContainerQuery _mockQuery;
    private QuestionRuleContainerQuery _questionRuleContainerQuery;

    [TestInitialize]
    public void SetUp()
    {
        AppEnvironment appEnvironment =
            new AppEnvironmentFactory(DataAccessAdapter.ConnectionStringKeyName, ForCompanyEnum.Ucpm)
                .GetAppEnvironmentFromEnvironmentVariables("UcpmAPI-Tests");
        ConfigurationHelper.ConfigureOrm(appEnvironment);

        _mockQuickAdapterAsync = new Mock<IQuickAdapterAsync>();
        _mockQuery = new QuestionRuleContainerQuery(_mockQuickAdapterAsync.Object);
        _questionRuleContainerQuery =
            new QuestionRuleContainerQuery(new QuickAdapterAsync(new DataAccessAdapter(), new DataLogger()));
    }

    [TestMethod]
    public async Task GetVisibilityContainer_ReturnsContainer_WhenContainerExists()
    {
        // Arrange
        var questionGuid = Guid.Parse("532759fc-5aba-40cf-aee0-c93327145025");
        var questionRuleContainerGuid = Guid.Parse("ce9d73db-ebd3-44b2-8630-00005acb5f92");
        var expectedContainer = new QuestionRuleContainerEntity
        {
            QuestionRuleContainerGuid = Guid.NewGuid(),
            QuestionGuid = questionGuid,
            RuleTypeId = (int)QuestionRuleTypeEnum.QuestionVisibility
        };

        _mockQuickAdapterAsync.Setup(m => m.FetchEntityCollectionAsync(It.IsAny<QueryParameters>(), "", ""))
            .Returns(Task.FromResult(expectedContainer));

        // Act
        var result = await _mockQuery.GetVisibilityContainer(questionGuid);
        var result2 = await _questionRuleContainerQuery.GetVisibilityContainer(questionGuid);

        // Assert
        // mock result
        Assert.AreEqual(expectedContainer.QuestionGuid, result.QuestionGuid);
        Assert.AreEqual(expectedContainer.RuleTypeId, result.RuleTypeId);
        Assert.IsNotNull(result.QuestionRuleContainerGuid);
        // real result
        Assert.AreEqual(expectedContainer.QuestionGuid, result2.QuestionGuid);
        Assert.AreEqual(expectedContainer.RuleTypeId, result2.RuleTypeId);
        Assert.AreEqual(result2.QuestionRuleContainerGuid, questionRuleContainerGuid);
    }

    [TestMethod]
    public async Task GetVisibilityContainer_ReturnsNewContainer_WhenContainerDoesNotExist()
    {
        // Arrange
        var questionGuid = Guid.NewGuid();

        _mockQuickAdapterAsync.Setup(m => m.FetchEntityCollectionAsync(It.IsAny<QueryParameters>(), "", ""))
            .Returns(Task.FromResult(new EntityCollection<QuestionRuleContainerEntity>()));
        
        // Act
        var result = await _mockQuery.GetVisibilityContainer(questionGuid);
        var result2 = await _questionRuleContainerQuery.GetVisibilityContainer(questionGuid);
        
        // Assert
        // mock result
        Assert.AreEqual(questionGuid, result.QuestionGuid);
        Assert.AreEqual((int)QuestionRuleTypeEnum.QuestionVisibility, result.RuleTypeId);
        Assert.IsNotNull(result.QuestionRuleContainerGuid);
        // real result
        Assert.AreEqual(questionGuid, result2.QuestionGuid);
        Assert.AreEqual((int)QuestionRuleTypeEnum.QuestionVisibility, result2.RuleTypeId);
        Assert.IsNotNull(result2.QuestionRuleContainerGuid);
    }
}