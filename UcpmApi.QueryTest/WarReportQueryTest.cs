using EnvironmentInspector;
using EnvironmentInspector.Enums;
using EnvironmentInspector.Factories;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using OrmStandardHelper;
using SD.LLBLGen.Pro.ORMSupportClasses;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using LLBLGenHelper;
using UcpmApi.Logging;
using UcpmApi.Query.Cache;

namespace UcpmApi.QueryTest.Cache.WarReportQueryTests
{
    [TestClass]
    public class UnitTest1
    {
        [TestInitialize]
        public void Initialize()
        {
            // Needs to pull configuration per environment.
            // Note, visual studio runs under the administrator, so these would need to be machine level set for debugging.
            // Additionally, VS won't see a change until restarted. However, for the dev-ops, this reduces the 
            // complexity of configuration to a batch of "set key=value" statements.
            // Inject ORM assembly
            AppEnvironmentFactory appEnvFactory = new(DataAccessAdapter.ConnectionStringKeyName, ForCompanyEnum.Ucpm);
            AppEnvironment appEnv = appEnvFactory.GetAppEnvironmentFromEnvironmentVariables();
            ConfigurationHelper.ConfigureOrm(appEnv);

            RuntimeConfiguration.SetDependencyInjectionInfo(
                [typeof(EmployeeEntity).Assembly],
                null
                );
        }

        [TestMethod]
        public async Task VerifyPrefetchWorksAsync()
        {
            WarReportQuery query = new(new QuickAdapterAsync(new DataAccessAdapter(),new DataLogger()));
            IEnumerable<WarReportProductEntity> result = await query.GetWarReports(Guid.Parse("121781AC-547F-4C9E-A064-4CF27DDBF530"));
            foreach (WarReportProductEntity warReportProduct in result)
            {
                Assert.IsTrue(warReportProduct.Product != null);
            }
        }
    }
}
