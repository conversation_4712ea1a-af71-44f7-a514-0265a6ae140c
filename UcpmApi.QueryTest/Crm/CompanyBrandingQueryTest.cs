using EnvironmentInspector;
using EnvironmentInspector.Enums;
using EnvironmentInspector.Factories;
using LLBLGenHelper;
using LLBLGenHelper.Interfaces;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using ORMStandard.DatabaseSpecific;
using OrmStandardHelper;
using System;
using UcpmApi.Logging;
using UcpmApi.Query.Crm;

namespace UcpmApi.QueryTest.Crm;

[TestClass]
public class CompanyBrandingQueryTest
{
    private Mock<IQuickAdapterAsync> _mockQuickAdapterAsync;
    private Mock<DataLogger> _mockDataLogger;
    private CompanyBrandingQuery _mockCompanyBrandingQuery;
    private CompanyBrandingQuery _companyBrandingQuery;

    [TestInitialize]
    public void SetUp()
    {
        AppEnvironment appEnvironment =
            new AppEnvironmentFactory(DataAccessAdapter.ConnectionStringKeyName, ForCompanyEnum.Ucpm)
                .GetAppEnvironmentFromEnvironmentVariables("UcpmAPI-Tests");
        ConfigurationHelper.ConfigureOrm(appEnvironment);
        _mockQuickAdapterAsync = new Mock<IQuickAdapterAsync>();
        _mockCompanyBrandingQuery = new CompanyBrandingQuery(_mockQuickAdapterAsync.Object);
        _companyBrandingQuery = new CompanyBrandingQuery(new QuickAdapterAsync(new DataAccessAdapter(), new DataLogger()));
    }


    [TestMethod]
    public void GetCompanyBrandingByCompanyGuid_ReturnsCompanyBranding_WithCorrectCompanyGuid()
    {
        // Arrange
        Guid companyGuid = Guid.Parse("10c0ec68-aedb-4e6a-903e-8e1623486569");

        // Act
        ORMStandard.EntityClasses.CompanyBrandingEntity mockResult = _mockCompanyBrandingQuery.GetCompanyBrandingByCompanyGuid(companyGuid);

        // Assert
        Assert.IsNotNull(mockResult);
    }

    [TestMethod]
    public void GetCompanyBrandingByCompanyGuid_ReturnsCompanyBranding_WithNoCompanyGuid()
    {
        // Arrange
        Guid companyGuid = Guid.Empty;

        // Act
        ORMStandard.EntityClasses.CompanyBrandingEntity mockResult = _mockCompanyBrandingQuery.GetCompanyBrandingByCompanyGuid(companyGuid);

        // Assert
        Assert.IsNotNull(mockResult);
    }


}