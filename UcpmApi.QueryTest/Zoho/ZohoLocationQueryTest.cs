using EnvironmentInspector;
using EnvironmentInspector.Enums;
using EnvironmentInspector.Factories;
using LLBLGenHelper;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using OrmStandardHelper;
using SD.LLBLGen.Pro.ORMSupportClasses;
using UcpmApi.Query.Zoho;

namespace UcpmApi.QueryTest.Zoho
{
    [TestClass]
    public class ZohoLocationQueryTest
    {
        private ZohoLocationQuery _zohoLocationQuery;

        [TestInitialize]
        public void Initialize()
        {
            AppEnvironment appEnvironment =
                new AppEnvironmentFactory(DataAccessAdapter.ConnectionStringKeyName, ForCompanyEnum.Ucpm)
                    .GetAppEnvironmentFromEnvironmentVariables("UcpmAPI-Tests");
            ConfigurationHelper.ConfigureOrm(appEnvironment);

            EnvironmentDetection environmentDetection = new(new DataAccessAdapter());
            RuntimeConfiguration.SetDependencyInjectionInfo(
                [typeof(AgentEntity).Assembly],
                null);

            _zohoLocationQuery = new ZohoLocationQuery();
        }

        [TestMethod]
        public void TestCarrierCount()
        {
            int count = _zohoLocationQuery.CountNeverSentToZoho();
            Assert.IsTrue(count >= 0); //Only fails if exception is thrown
        }

    }
}
