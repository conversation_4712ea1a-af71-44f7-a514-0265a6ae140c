using EnvironmentInspector;
using EnvironmentInspector.Enums;
using EnvironmentInspector.Factories;
using LLBLGenHelper;
using LLBLGenHelper.Interfaces;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using OrmStandardHelper;
using System;
using System.Linq;
using UcpmApi.Logging;
using UcpmApi.Query.Vetting;

namespace UcpmApi.QueryTest.Vetting;

[TestClass]
public class EvaluatorQueryTest
{

    private IQuickAdapterAsync _quickAdapterAsync;
    private IQuickAdapterReader _quickAdapterReader;
    private EvaluatorQuery _evaluatorQuery;

    [TestInitialize]
    public void Setup()
    {
        AppEnvironment appEnvironment =
            new AppEnvironmentFactory(DataAccessAdapter.ConnectionStringKeyName, ForCompanyEnum.Ucpm)
                .GetAppEnvironmentFromEnvironmentVariables("UcpmAPI-Tests");
        ConfigurationHelper.ConfigureOrm(appEnvironment);
        _quickAdapterAsync = new QuickAdapterAsync(new DataAccessAdapter(), new DataLogger());
        _quickAdapterReader = new QuickAdapterReader(new DataAccessAdapter(), new DataLogger());
        _evaluatorQuery = new EvaluatorQuery(_quickAdapterReader);
    }

    [TestMethod]
    public void GetEvaluatorsTest()
    {
        IQueryable<EvaluatorEntity> evaluators = _evaluatorQuery.GetEvaluators();

        //Assert
        Assert.IsNotNull(evaluators);
        Assert.IsTrue(evaluators.Any());
        //assert that the evaluator entity has been prefetched
        foreach (EvaluatorEntity evaluator in evaluators)
        {
            Assert.IsNotNull(evaluator.EvaluatorType);
        }
    }

    [TestMethod]
    public void GetEvaluatorByNameShouldReturnData()
    {
        EvaluatorEntity evaluatorEntity = _evaluatorQuery.GetEvaluatorByName("Test Group");

        Assert.IsNotNull(evaluatorEntity);
        Assert.IsTrue(evaluatorEntity.EvaluatorGuid != Guid.Empty);
    }
}