
using EnvironmentInspector;
using EnvironmentInspector.Enums;
using EnvironmentInspector.Factories;
using LLBLGenHelper;
using LLBLGenHelper.Interfaces;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using OrmStandardHelper;
using SD.LLBLGen.Pro.ORMSupportClasses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UcpmApi.BusinessModel.Crm;
using UcpmApi.BusinessModel.Filesystem;
using UcpmApi.Command.Doc;
using UcpmApi.Command.Flex;
using UcpmApi.Command.History;
using UcpmApi.Command.Security;
using UcpmApi.Converter;
using UcpmApi.Logging;
using UcpmApi.Query.Activity;
using UcpmApi.Query.Carrier;
using UcpmApi.Query.Crm;
using UcpmApi.Query.Doc;
using UcpmApi.Query.Employee;
using UcpmApi.Query.Flex;
using UcpmApi.Query.Lookups;
using UcpmApi.Query.Policy;
using UcpmApi.Query.Security;
using UcpmApi.Shared;
using UcpmApi.Shared.Helpers;
using UcpmApi.UcpmWebApi.Helpers;

namespace UcpmApi.ConverterTest
{
    [TestClass]
    public class InvoiceConverterTests
    {
        private QuickAdapterAsync _quickAdapterAsync;
        private QuickAdapterReader _quickAdapterReader;
        private BuildBaseTesterClass _buildBaseTesterClass;

        [TestInitialize]
        public void Initialize()
        {
            AppEnvironmentFactory appEnvFactory = new(DataAccessAdapter.ConnectionStringKeyName, ForCompanyEnum.Ucpm);
            AppEnvironment appEnv = appEnvFactory.GetAppEnvironmentFromEnvironmentVariables();
            ConfigurationHelper.ConfigureOrm(appEnv);

            RuntimeConfiguration.SetDependencyInjectionInfo([typeof(InvoiceEntity).Assembly], null);
            _quickAdapterAsync = new(new DataAccessAdapter(), new DataLogger());
            _quickAdapterReader = new(new DataAccessAdapter(), new DataLogger());

            _buildBaseTesterClass = new BuildBaseTesterClass();
        }

        [TestMethod]
        public async Task TestGetInvoiceWithDetailsByGuidShouldReturnData()
        {
            Guid invoiceGuid = Guid.Parse("4B4DB42B-3BA1-4C7B-AB90-ABB406CF9A6D");
            Invoice invoice = await _buildBaseTesterClass.InvoiceConverter.GetInvoiceWithDetailsByGuid(invoiceGuid);

            Assert.IsNotNull(invoice);
            Assert.IsTrue(invoice.InvoiceGuid != Guid.Empty);
            Assert.IsTrue(!string.IsNullOrEmpty(invoice.AgencyName));
        }

        [TestMethod]
        public async Task TestGenerateInvoiceHtmlShouldReturnData()
        {
            Guid invoiceGuid = Guid.Parse("4B4DB42B-3BA1-4C7B-AB90-ABB406CF9A6D");
            Invoice invoice = await _buildBaseTesterClass.InvoiceConverter.GetInvoiceWithDetailsByGuid(invoiceGuid);

            InvoiceGenerationHelper invoiceGenerationHelper = new(invoice);
            (string BodyHtml, string HeaderHtml, string FooterHtml) result = invoiceGenerationHelper.GenerateInvoiceHtml();

            Assert.IsNotNull(result);
            Assert.IsNotNull(result.BodyHtml);
            Assert.IsNotNull(result.HeaderHtml);
            Assert.IsNotNull(result.FooterHtml);
        }

        [TestMethod]
        public async Task TestGetInvoiceDocsForFullyPaidInvoicesShouldReturnValidData()
        {
            List<(Guid AgentGuid, Guid InsuredGuid, Guid InvoiceGuid, string DocLink)> fullyPaidInvoices =
            [
                (Guid.Parse("0D023176-780F-44BF-B9D0-8BCC546D35BC"),Guid.Parse("9F8AD5A7-1AA6-4836-A0AE-A8268CCECE8F"), Guid.Parse("4B4DB42B-3BA1-4C7B-AB90-ABB406CF9A6D"), ""),
            ];

            List<(Guid AgentGuid, Guid InsuredGuid, Guid InvoiceGuid, string DocLink)> updatedPaidInvoices =
                await InsuredEPayPolicyInvoiceDocHelper.GetInvoiceDocsForFullyPaidInvoices(_buildBaseTesterClass.DocConverter,
                    _buildBaseTesterClass.DocInfoConverter,
                    _buildBaseTesterClass.InvoiceConverter,
                    fullyPaidInvoices);

            Assert.IsTrue(updatedPaidInvoices.Any(u => !string.IsNullOrEmpty(u.DocLink)));
        }
    }
}



