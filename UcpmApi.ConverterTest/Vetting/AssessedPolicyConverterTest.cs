using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using ORMStandard.EntityClasses;
using UcpmApi.Shared.Vetting;
using UcpmApi.Converter;
using System;

namespace UcpmApi.Tests
{
    [TestClass]
    public class AssessedPolicyConverterTests
    {
        [TestMethod]
        public void ToView_WithValidEntity_ReturnsAssessedPolicy()
        {
            // Arrange
            var entity = new AssessedPolicyEntity
            {
                AssessedPolicyGuid = Guid.NewGuid(),
                ProcessInstanceGuid = Guid.NewGuid(),
                PolicyNumber = "12345",
                EffectiveDate = DateTime.Now,
                ExpirationDate = DateTime.Now
            };

            // Act
            var result = AssessedPolicyConverter.ToView(entity);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(entity.AssessedPolicyGuid, result.AssessedPolicyGuid );
            Assert.AreEqual(entity.ProcessInstanceGuid, result.ProcessInstanceGuid);
            Assert.AreEqual(entity.PolicyNumber, result.PolicyNumber);
            Assert.AreEqual(entity.EffectiveDate, result.EffectiveDate);
            Assert.AreEqual(entity.ExpirationDate, result.ExpirationDate);
        }

        [TestMethod]
        public void ToView_WithNullEntity_ReturnsNull()
        {
            // Arrange
            AssessedPolicyEntity entity = null;

            // Act
            var result = AssessedPolicyConverter.ToView(entity);

            // Assert
            Assert.AreEqual(result.AssessedPolicyGuid, Guid.Parse("00000000-0000-0000-0000-000000000000"));
            Assert.AreEqual(result.EffectiveDate, DateTime.Parse("01/01/0001 12:00:00 am"));
            Assert.AreEqual(result.ExpirationDate, DateTime.Parse("01/01/0001 12:00:00 am"));
            Assert.IsNull(result.PolicyNumber);
            Assert.IsNull(result.Employee);
        }
    }
}
