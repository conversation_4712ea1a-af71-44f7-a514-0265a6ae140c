using System;
using EnvironmentInspector;
using EnvironmentInspector.Enums;
using EnvironmentInspector.Factories;
using LLBLGenHelper;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using OrmStandardHelper;
using SD.LLBLGen.Pro.ORMSupportClasses;
using UcpmApi.Converter;
using UcpmApi.Logging;
using UcpmApi.Query.Lookups;
using UcpmApi.Shared;

namespace UcpmApi.ConverterTest
{
    [TestClass]
    public class UnitTest1
    {
        [TestInitialize]
        public void Initialize()
        {
            // Needs to pull configuration per environment.
            // Note, visual studio runs under the administrator, so these would need to be machine level set for debugging.
            // Additionally, VS won't see a change until restarted. However, for the dev-ops, this reduces the 
            // complexity of configuration to a batch of "set key=value" statements.
            // Inject ORM assembly
            AppEnvironmentFactory appEnvFactory = new(DataAccessAdapter.ConnectionStringKeyName, ForCompanyEnum.Ucpm);
            AppEnvironment appEnv = appEnvFactory.GetAppEnvironmentFromEnvironmentVariables();
            ConfigurationHelper.ConfigureOrm(appEnv);

            RuntimeConfiguration.SetDependencyInjectionInfo(
                [typeof(DesktopSoftwareVersionEntity).Assembly],
                null
                );
        }

        [TestMethod]
        public void TestMethod1()
        {
            DesktopSoftwareVersionQuery query = new(new QuickAdapterAsync(new DataAccessAdapter(),new DataLogger()));
            DesktopSoftwareVersionEntity r = query.GetUserVersion(Uri.UnescapeDataString("KRILLIN-Yakety Yak"), 1);
            Assert.IsNotNull(r);
            Assert.IsFalse(r.IsNew);
            DesktopSoftwareVersion asView = DesktopSoftwareVersionConverter.ToView(r);
            Assert.IsNotNull(asView);
        }
    }
}
