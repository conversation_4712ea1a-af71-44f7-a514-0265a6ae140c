using EnvironmentInspector;
using EnvironmentInspector.Enums;
using EnvironmentInspector.Factories;
using LLBLGenHelper;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ORMStandard.DatabaseSpecific;
using OrmStandardHelper;
using System;
using System.Threading.Tasks;
using UcpmApi.Converter;
using UcpmApi.Converter.Ikea;
using UcpmApi.Logging;
using UcpmApi.Query.Crm;
using UcpmApi.Query.Ikea;

namespace UcpmApi.ConverterTest.Ikea
{
    [TestClass]
    public class IkeaProjectOwnerConverterTest
    {
        private IkeaProjectOwnerConverter _ikeaProjectOwnerConverter;
        [TestInitialize]
        public void Initialize()
        {
            AppEnvironmentFactory appEnvFactory = new(DataAccessAdapter.ConnectionStringKeyName, ForCompanyEnum.Ucpm);
            AppEnvironment appEnv = appEnvFactory.GetAppEnvironmentFromEnvironmentVariables();
            ConfigurationHelper.ConfigureOrm(appEnv);
            QuickAdapterAsync quickAdapterAsync = new(new DataAccessAdapter(), new DataLogger());
            QuickAdapterReader quickAdapterReader = new(new DataAccessAdapter(), new DataLogger());
            FlexResponseSaveStateConverter flexResponseSaveStateConverter = new(new Query.Flex.FlexResponseSaveStateQuery(quickAdapterReader));
            IkeaCoveragePartQuery ikeaCoveragePartQuery = new(quickAdapterAsync);
            ProjectOwnerQuery projectOwnerQuery = new(quickAdapterAsync);

            _ikeaProjectOwnerConverter = new IkeaProjectOwnerConverter(new Query.Kato.KatoQuery(quickAdapterReader), new Query.Security.InviteLinkQuery(quickAdapterReader), flexResponseSaveStateConverter, ikeaCoveragePartQuery, projectOwnerQuery);

        }

        [TestMethod]
        public async Task GetIkeaOwnersByInviteLinkGuid_ShouldReturnNonZeroResult()
        {
            Guid inviteLinkGuid = Guid.Parse("4915ec29-59b3-4ec5-9de9-1dfe0613f754");

            Shared.Cache.ProjectOwnerDebug owners = await _ikeaProjectOwnerConverter.GetQualifyingOwners(inviteLinkGuid);
            Assert.IsTrue(owners.ProjectOwnerList.Count > 0);
        }
    }
}
