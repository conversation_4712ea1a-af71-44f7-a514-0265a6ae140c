using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EnvironmentInspector;
using EnvironmentInspector.Enums;
using EnvironmentInspector.Factories;
using LLBLGenHelper;
using LLBLGenHelper.Interfaces;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ORMStandard.DatabaseSpecific;
using OrmStandardHelper;
using UcpmApi.Logging;
using UcpmApi.Converter.Coverage;
using UcpmApi.Query.Coverage;
using UcpmApi.Shared.Coverage;

namespace UcpmApi.ConverterTest.Coverage
{
    [TestClass]
    public class FormCoverageIssueConverterTest
    {
        private IQuickAdapterAsync _quickAdapterAsync;
        private FormCoverageIssueQuery _formCoverageIssueQuery;
        private FormCoverageIssueConverter _formCoverageIssueConverter;

        [TestInitialize]
        public void Initialize()
        {
            AppEnvironment appEnvironment =
                new AppEnvironmentFactory(DataAccessAdapter.ConnectionStringKeyName, ForCompanyEnum.Ucpm)
                    .GetAppEnvironmentFromEnvironmentVariables("UcpmAPI-Tests");
            ConfigurationHelper.ConfigureOrm(appEnvironment);
            _quickAdapterAsync = new QuickAdapterAsync(new DataAccessAdapter(), new DataLogger());
            _formCoverageIssueQuery = new FormCoverageIssueQuery(_quickAdapterAsync);
            _formCoverageIssueConverter = new FormCoverageIssueConverter(_formCoverageIssueQuery);
        }

        [TestMethod]
        public async Task GetByFormNumbersShouldReturnResults()
        {
            List<string> formNumbers = new List<string>()
            {
                "WSG 084 05 11",
                "SL 17887 08 04",
                "ENV 1502 06 12",
                "LD 5S23l 04 22",                
                "ENV 1200 03 10",
                "ENV 1220 05 20",
                "ENV 1230 03 10",
                "ENV 3103 12 10",
                "ENV 3123 08 04",
                "ENV 3137 08 04",
                "ENV 3143 03 05",
                "ENV 3146 09 22",
                "ENV 3147 10 12",
                "ENV 3154 04 20",
                "ENV 3236 03 15",
                "ENV 3239 10 21",
                "ENV 3244 04 18",
                "ENV 3250 12 18",
                "ENV 3251 12 18",
                "ENV 3253 12 18",
                "ENV 5100 06 11",
                "ENV 5102 10 04",
                "ENV 5519 04 04",
                "ALL 20887 10 06",
                "ALL 21101 11 06",
                "ENV 9950 01 15",
                "ENVM 484 03 22",
                "IL P001 01 04",
                "MA 608255e 04 15",
                "SL 34255b 04 23",
                "TRIA24a 08 20"
            };

            IEnumerable<FormCoverageIssue> forms = await _formCoverageIssueConverter.GetByFormNumbers(formNumbers);

            Assert.IsNotNull(forms);
            Assert.IsTrue(forms.Any());
        }
    }
}
