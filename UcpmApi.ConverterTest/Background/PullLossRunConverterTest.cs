using EnvironmentInspector;
using EnvironmentInspector.Enums;
using EnvironmentInspector.Factories;
using LLBLGenHelper;
using LLBLGenHelper.Interfaces;
using Microsoft.AspNetCore.Components;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;
using ORMStandard.Linq;
using OrmStandardHelper;
using SD.LLBLGen.Pro.ORMSupportClasses;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using UcpmApi.Command.Policy;
using UcpmApi.Converter.Background;
using UcpmApi.Logging;
using UcpmApi.Query.Background;
using UcpmApi.Query.Carrier;
using UcpmApi.Query.Crm;
using UcpmApi.Query.Policy;
using UcpmApi.Shared.Background;

namespace UcpmApi.ConverterTest.Background
{
    [TestClass]
    public class PullLossRunConverterTest
    {
        [Inject]
        IErrorLogger _errorLogger { get; set; }

        private InsuredQuery _insuredQuery;
        private InvoiceQuery _invoiceQuery;
        private PolicyProspectQuery _prospectQuery;
        private PullLossRunConverter _pullLossRunConverter;
        private PullLossRunsQuery _pullLossRunsQuery;

        [TestInitialize]
        public void Initialize()
        {
            AppEnvironmentFactory appEnvFactory = new(DataAccessAdapter.ConnectionStringKeyName, ForCompanyEnum.Ucpm);
            AppEnvironment appEnv = appEnvFactory.GetAppEnvironmentFromEnvironmentVariables();
            ConfigurationHelper.ConfigureOrm(appEnv);

            RuntimeConfiguration.SetDependencyInjectionInfo([typeof(DocEntity).Assembly], null);

            QuickAdapterAsync quickAdapterAsync = new(new DataAccessAdapter(), new DataLogger());
            QuickAdapterReader quickAdapterReader = new(new DataAccessAdapter(), new DataLogger());
            LinqMetaData linqMetaData = new(new DataAccessAdapter());
            InsuredQuery insuredQuery = new(quickAdapterReader);
            PolicyChangeLogQuery policyLogQuery = new(quickAdapterAsync);
            PolicyProspectQuery policyQuery = new(quickAdapterReader, insuredQuery, policyLogQuery);
            PolicyProspectCommand policyCommand = new(_errorLogger, quickAdapterAsync, policyQuery);
            PullLossRunsQuery lossRunQuery = new(quickAdapterAsync);
            InvoiceQuery invoiceQuery = new(quickAdapterAsync);
            UnderwriterQuery underwriterQuery = new(quickAdapterAsync);

            _insuredQuery = insuredQuery;
            _prospectQuery = policyQuery;
            _pullLossRunConverter = new(lossRunQuery, invoiceQuery, quickAdapterAsync);
            _pullLossRunsQuery = lossRunQuery;
            _invoiceQuery = new(quickAdapterAsync);
        }



        [TestMethod]
        public async Task TestNintyDays()
        {
            List<PullLossRun> results = await _pullLossRunConverter.ConvertPullLossRunForAutoRenewals(Shared.Enums.GeneratedTypeEnum.PullLossRunsForOnlineRenewals);
            var toView = results.Select(s => new { s.SubmissionUpForRenewal.CarrierSubmissionGuid, s.SubmissionUpForRenewal.PolicyProspect.RenewalOfPolicyGuid, s.Insured.Name }).OrderBy(ob => ob.CarrierSubmissionGuid).ToList();
            Assert.IsNotNull(results);
        }

        [TestMethod]
        public async Task TestSixtyDays()
        {
            List<PullLossRun> results = await _pullLossRunConverter.ConvertPullLossRunForAutoRenewals(Shared.Enums.GeneratedTypeEnum.PullLossRunsFirstMailRenewal);
            var toView = results.Select(s => new { s.SubmissionUpForRenewal.CarrierSubmissionGuid, s.SubmissionUpForRenewal.PolicyProspect.RenewalOfPolicyGuid, s.Insured.Name }).OrderBy(ob => ob.CarrierSubmissionGuid).ToList();
            Assert.IsNotNull(results);
        }

        [TestMethod]
        public async Task TestThirtyDays()
        {
            List<PullLossRun> results = await _pullLossRunConverter.ConvertPullLossRunForAutoRenewals(Shared.Enums.GeneratedTypeEnum.PullLossRunsSecondMailRenewal);
            var toView = results.Select(s => new { s.SubmissionUpForRenewal.CarrierSubmissionGuid, s.SubmissionUpForRenewal.PolicyProspect.RenewalOfPolicyGuid, s.Insured.Name }).OrderBy(ob => ob.CarrierSubmissionGuid).ToList();
            Assert.IsNotNull(results);
        }

        [TestMethod]
        public async Task TestFour()
        {
            EntityCollection<CarrierSubmissionEntity> results = await _pullLossRunsQuery.GetPullLossRunContent(Shared.Enums.GeneratedTypeEnum.PullLossRunsFirstMailRenewal);

            Assert.IsNotNull(results);
        }

        [TestMethod]
        public async Task SpeedTest()
        {
            List<Guid> insuredsWithManyPackages = [
                Guid.Parse("AF2AC508-6CBA-4A26-9D25-C25C8756623B"),
                Guid.Parse("CC68D406-1A17-4E86-B0E4-51D6891ADE3A"),
                Guid.Parse("A77DC2D9-4E3A-42AE-8B74-A6E811861272"),
                Guid.Parse("09975F5D-7E7E-4A72-ADFB-884AF3430F68"),
                Guid.Parse("C5F1FEF8-C231-4601-9DA9-6A38E67E92B4"),
                Guid.Parse("D5E03EF5-BAEA-43FD-BA5C-194A79E07518"),
                Guid.Parse("ECFCACBC-1201-4C6E-ABAD-5B22281DD259"),
                Guid.Parse("47C710BB-E663-4884-8A5C-7BE778CC6D08"),
                Guid.Parse("C55439E4-BAE7-4A9B-8813-062CF8F2AE92"),
                ];

            List<Guid> lastCarrierSubmissionForInsureds = [
                Guid.Parse("888787D1-2E5E-435A-96AE-52DD7E92815C"),
                Guid.Parse("DDE7880B-1718-435C-8D66-DC4581AC91CC"),
                Guid.Parse("C1FB765A-FD25-4FF5-946E-998DBE010696"),
                Guid.Parse("D7B841CC-FA83-44FE-AC75-8229289103ED"),
                Guid.Parse("C23E95B7-6D98-437C-AA2B-FC5EC673BC77"),
                Guid.Parse("66E5B19E-87A8-483C-934E-B7F71D077EF8"),
                Guid.Parse("56DAFFAB-A1F1-4934-A3BE-69EE21F93F4B"),
                Guid.Parse("0D63C95A-08CB-4C08-8C0F-B1487EFEC77D"),
                Guid.Parse("BDAEFCEC-D0B2-4260-977A-536891ACD95A"),
                ];

            Stopwatch stopwatch = Stopwatch.StartNew();

            EntityCollection<CarrierSubmissionEntity> submissions = await _pullLossRunsQuery.TestSpeed(lastCarrierSubmissionForInsureds);
            List<PullLossRun> lossRuns = _pullLossRunConverter.AddSubmissionUpToInsured(submissions);
            List<Guid> insuredGuids = submissions.Select(s => s.PolicyProspect).Select(s => s.Package).Select(s => s.InsuredAgentHistory).Select(s => s.InsuredGuid).Distinct().ToList();
            EntityCollection<InvoiceEntity> invoices = await _invoiceQuery.GetInvoicesByInsured(insuredGuids);

            foreach (PullLossRun lossRun in lossRuns)
            {
                lossRun.PriorInvoices = [];
                List<InvoiceEntity> matchingInvoices = invoices.Where(w => w.CarrierSubmissionOption.CarrierSubmission.PolicyProspect.Package.InsuredAgentHistory.InsuredGuid == lossRun.Insured.InsuredGuid).ToList();
                _pullLossRunConverter.ProcessInvoiceUp(matchingInvoices, lossRun);
            }

            stopwatch.Stop();
            string elapsedTime = $"Elapsed time: {stopwatch.ElapsedMilliseconds} ms";

            Assert.AreNotEqual(0, stopwatch.ElapsedMilliseconds);
        }
    }
}
