using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using MoreLinq;
using System.ComponentModel.DataAnnotations;
using System.Text;
using UcpmApi.Shared;
using UcpmApi.Shared.Carrier;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Policy;
using UCPMFederated.Binders;

namespace UCPMFederated.Models
{
    public class AccountDetailModel
    {
        [Display(Name = "Insured Name")]
        public string InsuredName { get; set; }
        [Display(Name = "Physical Address")]
        public string Address1 { get; set; }
        [Display(Name = "Physical Address 2")]
        public string Address2 { get; set; }
        [Display(Name = "City")]
        public string City { get; set; }
        [Display(Name = "State")]
        public string StateCode { get; set; }
        [Display(Name = "Zip")]
        public string ZipCode { get; set; }

        [Display(Name = "Mailing Address")]
        public string MailingAddress1 { get; set; }
        [Display(Name = "Mailing Address 2")]
        public string MailingAddress2 { get; set; }
        [Display(Name = "Mailing City")]
        public string MailingCity { get; set; }
        [Display(Name = "Mailing State")]
        public string MailingState { get; set; }
        [Display(Name = "Mailing Zip")]
        public string MailingZip { get; set; }

        [Display(Name = "Country Code")]
        public string CountryCode { get; set; }
        [Display(Name = "Risk County")]
        public string RiskCounty { get; set; }


        [DataType(DataType.Date, ErrorMessage = "Date only")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        [Display(Name = "Effective Date")]
        public DateTime EffectiveDate { get; set; }
        [Display(Name = "Expiration Date")]
        [DataType(DataType.Date, ErrorMessage = "Date only")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime ExpirationDate { get; set; }
        [Display(Name = "Need By Date")]
        [DataType(DataType.Date, ErrorMessage = "Date only")]
        [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
        public DateTime NeedByDate { get; set; }
        public string TypeSelected { get; set; }
        public string DocTypeSelected { get; set; }

        [Display(Name = "Program Or PolicyType?")]
        public SelectList ProgramOrPolicyType { get; set; }

        [Display(Name = "Premium")]
        [DisplayFormat(DataFormatString = "{0:C2}", ApplyFormatInEditMode = true)]
        [ModelBinder(BinderType = typeof(DecimalBinder))]
        public decimal Premium { get; set; }


        [Display(Name = "Inspection Fee")]
        [DisplayFormat(DataFormatString = "{0:C2}", ApplyFormatInEditMode = true)]
        [ModelBinder(BinderType = typeof(DecimalBinder))]
        public decimal AgencyFee { get; set; }

        [Display(Name = "Total Due")]
        [DisplayFormat(DataFormatString = "{0:C2}", ApplyFormatInEditMode = true)]
        public decimal TotalDue { get; set; }
        [Display(Name = "Policy Sub Type")]
        public Guid PolicySubTypeGuid { get; set; }
        [Display(Name = "Policy Number")]
        public string PolicyNumber { get; set; }
        public Guid InvoiceGuid { get; set; }
        public Guid PolicyProspectGuid { get; set; }
        public Guid InsuredGuid { get; set; }
        [Display(Name = "Limit Aggregate")]
        public decimal LimitAggregate { get; set; }
        [Display(Name = "Limit Occurrence")]
        public decimal LimitOccurrence { get; set; }
        public string Status { get; set; }
        public string DefaultTab { get; set; }
        public List<StateRequirement> StateRequirements { get; set; }
        public SelectList PolicySubTypeList { get; set; }
        public SelectList CountryList { get; set; }
        public SelectList StateList { get; set; }
        public SelectList LimitList { get; set; }
        public SelectList DueDiligenceList { get; set; }
        public SelectList UnderWriterList { get; set; }
        public SelectList SubmissionTypesList { get; set; }
        public SelectList DocTypeList { get; set; }
        [Required(ErrorMessage = "Please select a document type.")]
        public int DocTypeId { get; set; }
        public string ReadOnly { get; set; }
        [Display(Name = "Account Number")]
        public string PartnerAccountNumber { get; set; }
        public Guid CarrierSubmissionGuid { get; set; }
        public IEnumerable<DocModel> Docs { get; set; }
        public DocModel MostRecentEndorsementDoc { get; set; }
        public DocModel MostRecentPolicyDoc { get; set; }
        public DocModel MostRecentDiligentSearchDoc { get; set; }
        public IEnumerable<Endorsement> Endorsements { get; set; }
        [Display(Name = "Due Diligence Complete")]
        public int DueDiligenceStatusId { get; set; }
        [Display(Name = "Is Tax Exempt?")]
        public bool IsTaxExempt { get; set; }
        [Display(Name = "Tax Exempt Reason")]
        public string TaxExemptReason { get; set; }

        [Display(Name = "File")]
        public IFormFile FormFile { get; set; }
        [Display(Name = "Subject")]
        public string Subject { get; set; }

        [Display(Name = "Body")]
        public string Body { get; set; }
        public string ActiveTab { get; set; }
        [Required, Display(Name = "Risk Description")]
        public string RiskDescription { get; set; }
        public IEnumerable<DirectSendLog> Logs { get; set; }
        public bool IsReviewer { get; set; }
        public bool HasRiskDescription { get; set; }
        public List<InvoiceItem> InvoiceItems { get; set; }
        public int ItemsPerPage { get; set; }

        [Display(Name = "Underwriter")]
        public Guid UnderWriterID { get; set; }
        [Display(Name = "Submission Type")]
        public int RenewalStatusID { get; set; }

        public AccountDetailModel()
        {
            EffectiveDate = DateTime.Now;
            ExpirationDate = DateTime.Now.AddYears(1);
            NeedByDate = DateTime.Now;
        }

        public AccountDetailModel(FederatedInsuredDetail response, List<PolicySubType> policySubTypes, List<Country> countries, List<State> states, List<LimitsRequested> limits, List<DocType> docTypes, IEnumerable<DocModel> docs,
            IEnumerable<Endorsement> endorsements, IEnumerable<DirectSendLog> logs, IEnumerable<DueDiligenceStatus> dueDiligences, string tab, List<InvoiceItem> invoiceItems, bool isReviewer,
            IEnumerable<Underwriter> underwriters, IEnumerable<RenewalStatus> submissionTypes)
        {
            ActiveTab = tab;
            InsuredName = response.InsuredName;
            Address1 = response.Address1;
            Address2 = response.Address2;
            City = response.City;
            StateCode = response.StateCode;
            ZipCode = response.ZipCode;
            MailingAddress1 = response.MailingAddress1;
            MailingAddress2 = response.MailingAddress2;
            MailingCity = response.MailingCity;
            MailingState = response.MailingState;
            MailingZip = response.MailingZip;
            CountryCode = response.CountryCode;
            RiskCounty = response.RiskCounty;
            EffectiveDate = response.EffectiveDate.DateTime;
            ExpirationDate = response.ExpirationDate.DateTime;
            NeedByDate = response.NeedByDate.DateTime;
            Premium = response.Premium;
            PolicySubTypeGuid = response.PolicySubTypeGuid;
            TotalDue = response.TotalDue;
            InvoiceGuid = response.InvoiceGuid;
            IsTaxExempt = response.IsTaxExempt;
            TaxExemptReason = response.TaxExemptReason;
            StateRequirements = response.StateRequirements;
            PolicyProspectGuid = response.PolicyProspectGuid;
            CarrierSubmissionGuid = response.CarrierSubmissionGuid;
            InsuredGuid = response.InsuredGuid;
            PolicyNumber = response.PolicyNumber;
            AgencyFee = response.AgencyFee;
            LimitAggregate = response.LimitAggregate;
            LimitOccurrence = response.LimitOccurrence;
            Status = response.Status;
            DueDiligenceStatusId = response.DueDiligenceStatusId;

            UnderWriterID = response.UnderWriterID;
            RenewalStatusID = response.RenewalStatusID;

            if (Status != "Quoted" && Status != "Open")
            {
                ReadOnly = "readonly";
            }
            PartnerAccountNumber = response.PartnerAccountNumber;
            List<PolicySubType> sortedPolicySubTypes = policySubTypes.OrderBy(p => p.SortOrder).ToList();
            PolicySubTypeList = new SelectList(sortedPolicySubTypes, "PolicySubTypeGuid", "PolicySubTypeName");
            CountryList = new SelectList(countries, "CountryCodeIsoAlpha2", "CountryName");
            StateList = new SelectList(states, "Code", "StateName");
            LimitList = new SelectList(limits, "LimitsRequestedValue", "ValueFormatted");
            DocTypeList = new SelectList(docTypes, "DocTypeId", "DocTypeName");
            DueDiligenceList = new SelectList(dueDiligences, "DueDiligenceStatusId", "DueDiligenceStatusName");
            DocTypeList = new SelectList(docTypes, "DocTypeId", "DocTypeName");
            DueDiligenceList = new SelectList(dueDiligences, "DueDiligenceStatusId", "DueDiligenceStatusName");
            UnderWriterList = new SelectList(underwriters, "UnderwriterId", "UnderwriterName");
            SubmissionTypesList = new SelectList(submissionTypes, "RenewalStatusId", "Status");

            Docs = docs;
            Endorsements = endorsements;
            Logs = logs;

            MostRecentEndorsementDoc = docs.Where(d => d.DocTypeId == (int)DocTypeEnum.Endorsement).OrderByDescending(d => d.LastWrite).FirstOrDefault();
            MostRecentPolicyDoc = docs.Where(d => d.DocTypeId == (int)DocTypeEnum.Policy).OrderByDescending(d => d.LastWrite).FirstOrDefault();
            MostRecentDiligentSearchDoc = docs.Where(d => d.DocTypeId == (int)DocTypeEnum.DiligentSearch).OrderByDescending(d => d.LastWrite).FirstOrDefault();

            Subject = $"{InsuredName}";
            StringBuilder builder = new();
            builder.AppendLine($"Effective Date: {EffectiveDate:d}");
            builder.AppendLine($"Account Number: {PartnerAccountNumber}");
            builder.AppendLine($"Premium: {Premium:c0}");
            builder.AppendLine($"Policy Number: {PolicyNumber}");
            Body = builder.ToString();
            InvoiceItems = invoiceItems;
            IsReviewer = isReviewer;
            HasRiskDescription = response.StateCode == "MN" || response.StateCode == "TX" || response.StateCode == "CA" || response.StateCode == "NY";
            RiskDescription = HasRiskDescription ? response.RiskDescription : " ";
        }
    }
}
