using UcpmApi.Shared;

namespace UCPMFederated.Models
{
    public class SLLandingPageModel
    {
        public string DefaultTab { get; set; }
        public string AllActive { get; set; }
        public string QuotedActive { get; set; }
        public string BindRequestedActive { get; set; }
        public string BoundActive { get; set; }
        public string SubmissionActive { get; set; }
        public string QuotedShow { get; set; }
        public string BindRequestedShow { get; set; }
        public string BoundShow { get; set; }
        public string SubmissionShow { get; set; }
        public string ActiveShow { get; set; }
        public AccountByStatusModel AllAccounts { get; set; }
        public AccountByStatusModel QuotedAccounts { get; set; }
        public AccountByStatusModel SubmittedAccounts { get; set; }
        public AccountByStatusModel BindRequestedAccounts { get; set; }
        public AccountByStatusModel BoundAccounts { get; set; }
        public AccountByStatusModel HeldAccounts { get; set; }

        private static readonly string _Active = "Active";
        public SLLandingPageModel(IEnumerable<FederatedInsuredOverview> insureds, string defaultTab)
        {
            AllAccounts = new AccountByStatusModel(insureds.ToList(), "All");
            QuotedAccounts = new AccountByStatusModel(insureds.Where(i => i.RequestStatus == "Quoted").ToList(), "Quoted");
            SubmittedAccounts = new AccountByStatusModel(insureds.Where(i => i.RequestStatus == "Submitted").ToList(), "Submitted");
            BindRequestedAccounts = new AccountByStatusModel(insureds.Where(i => i.RequestStatus == "Bind Requested").ToList(), "Bind Requested");
            BoundAccounts = new AccountByStatusModel(insureds.Where(i => i.RequestStatus == "Bound").ToList(), "Bound");
            HeldAccounts = new AccountByStatusModel(insureds.Where(i => i.RequestStatus == "Held").ToList(), "Held");
            DefaultTab = defaultTab;

            if (DefaultTab == "Quoted")
            {
                QuotedActive = _Active;
                QuotedShow = "show";
            }

            if (DefaultTab == "BindRequested")
            {
                BindRequestedActive = _Active;
                BindRequestedShow = "show";
            }

            if (DefaultTab == "Bound")
            {
                BoundActive = _Active;
                BoundShow = "show";
            }

            if (DefaultTab == "Submitted")
            {
                SubmissionActive = _Active;
                SubmissionShow = "show";
            }

            if (DefaultTab == "All")
            {
                AllActive = _Active;
                ActiveShow = "show";
            }
        }
    }
}
