using Ardalis.ApiEndpoints;
using Asp.Versioning;
using EnvironmentInspector.Enums;
using Lender.Api.Helpers.Auth;
using Lender.Api.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System;
using System.Threading;
using System.Threading.Tasks;
using UcpmApi.Shared;

namespace Lender.Api.Endpoints.Loans
{
    public class Post : EndpointBaseAsync
        .WithRequest<Loan>
        .WithActionResult<LoanResponse>
    {
        private readonly LenderService _lenderService;
        private readonly IEnvironmentDetection _environmentDetection;


        public Post(LenderService lenderService, IEnvironmentDetection environmentDetection)
        {
            _lenderService = lenderService;
            _environmentDetection = environmentDetection;
        }



        // [ApiKeyAuthentication]
        [HttpPost("/api/v{version:apiVersion}/loans")]
        [ApiVersion("1.0")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(LoanResponse))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized, Type = typeof(ProblemDetails))]
        [SwaggerOperation(
            Summary = "Creates a new Loan",
            Description = "Creates a new Loan",
            OperationId = "Loan.Create",
            Tags = new[] { "Loans" })
        ]
        public override async Task<ActionResult<LoanResponse>> HandleAsync([FromBody] Loan request, CancellationToken cancellationToken = new CancellationToken())
        {
            // Try to get the Insured Guid from the authenticated user. If the attempt fails, the method
            // throws which should trigger our exception handler endpoint to log the issue and send a
            // ProblemDetails response.
            Guid insuredGuid = ApiEndpointsHelpers.GetInsuredGuidFromIdentity(request, User.Identity?.Name);
            EnvironmentEnum environment = _environmentDetection.GetEnvironment();
            LoanResponse response = await _lenderService.CreateLoan(request, insuredGuid, environment);
            if (response == default)
            {
                return BadRequest();
            }

            Uri uri = new($"{Request.Scheme}://{Request.Host}{Request.Path.Value}/{response.LoanGuid}");
            return Created(uri, response);
        }
    }
}