using Microsoft.AspNetCore.Mvc;
using System;
using System.Security.Authentication;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Lender.Api.Helpers.Auth
{
    public static class ApiEndpointsHelpers
    {
        public static Guid GetInsuredGuidFromIdentity<TRequest>(TRequest requestBody, string userIdentityName)
        {
            if (Guid.TryParse(userIdentityName, out Guid insuredGuid))
            {
                return insuredGuid;
            }

            JsonSerializerOptions options = new()
            {
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            string requestBodyString = JsonSerializer.Serialize(requestBody, options);
            throw new AuthenticationException($"There was a problem while trying to authenticate you to " +
                                              $"the service. **Request Body:** `{requestBodyString}`");
        }

        public static (Guid, ProblemDetails) GetGuidOrReturnProblemDetails(object loanGuidObject)
        {
            string inputString = loanGuidObject?.ToString() ?? string.Empty;
            bool conversionSucceeded = Guid.TryParse(inputString, out Guid result);

            if (conversionSucceeded)
            {
                return (result, null);
            }

            ProblemDetails problems = new()
            {
                Type = "https://tools.ietf.org/html/rfc7231#section-6.5.1",
                Title = "One or more validation errors occurred.",
                Detail = "Please include the `loanGuid` of the Loan you would like to update in the last section of " +
                         "the URL. Example: /loans/00000000-0000-0000-0000-000000000000"
            };

            return (Guid.Empty, problems);
        }
    }
}