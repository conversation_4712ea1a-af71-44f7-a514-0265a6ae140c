using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using PolicyTrackerBlazor.Client.PolicyAuth.Requirements;
using UcpmComponentLibraryEx.Services.Interfaces;

namespace PolicyTrackerBlazor.Client.PolicyAuth.Handlers
{
    public class DefaultAuthHandler : AuthorizationHandler<AuthenticatedRequirement>
    {
        private readonly IAuthenticationService _authenticationService;

        private NavigationManager _NavManager { get; set; }
        public DefaultAuthHandler(IAuthenticationService currentUserService, NavigationManager navManager)
        {
            _authenticationService = currentUserService;
            _NavManager = navManager;
        }
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, AuthenticatedRequirement requirement)
        {
            Uri currentUri = new(_NavManager.Uri);

            Guid accountGuid = _authenticationService?.CurrentUser?.AccountGuid ?? Guid.Empty;
            if (accountGuid == Guid.Empty)
            {
                await Task.CompletedTask;
                return;
            }

            if (_authenticationService?.CurrentUser?.JwtTokenExpiration <= DateTime.UtcNow)
            {
                await Task.CompletedTask;
                await _authenticationService.Logout();
                return;
            }

            context.Succeed(requirement);
            await Task.CompletedTask;
        }
    }
}
