using UcpmApi.Shared.Carrier;
using UcpmApi.Shared.Policy;
using UcpmComponentLibraryEx.Models;
using UcpmComponentLibraryEx.Services.Interfaces;
using UcpmTools.StringUtilities;

namespace PolicyTrackerBlazor.Client.Managers
{
    public class ProgramManager : ManagerBase
    {
        public ProgramManager(HttpClient httpClient, IAuthenticationService userAuth)
        {
            HttpClient = httpClient;
            UserAuth = userAuth;
        }

        public async Task<ProgramModel> GetProgramByGuid(Guid programGuid)
        {
            QueryStringBuilder query = new("/Program/GetProgramByGuid");
            query.Add(nameof(programGuid), programGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<ProgramModel>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<IEnumerable<ProgramModel>> GetProgramsNotInCarrierProgram(Guid carrierGuid)
        {
            QueryStringBuilder query = new("/Program/GetProgramsNotInCarrierProgram");
            query.Add(nameof(carrierGuid), carrierGuid);

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.GetAsync<IEnumerable<ProgramModel>>(query.ToString(), UserAuth?.CurrentUser?.JwtToken);
        }

        public async Task<bool> UpdateProgram(ProgramModel program) 
        {
            QueryStringBuilder query = new("/Program/UpdateProgram");

            UcpmComponentApiBase apiBase = new(HttpClient);
            return await apiBase.PutAsync<bool, ProgramModel>(query.ToString(), program, UserAuth?.CurrentUser?.JwtToken);
        }
    }
}
