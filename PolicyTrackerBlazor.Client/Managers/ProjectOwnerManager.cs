using UcpmApi.Shared.Cache;
using UcpmApi.Shared.Crm;
using UcpmComponentLibraryEx.Models;
using UcpmComponentLibraryEx.Services.Interfaces;
using UcpmTools.StringUtilities;

namespace PolicyTrackerBlazor.Client.Managers;

public class ProjectOwnerManager: ManagerBase
{
    public ProjectOwnerManager(HttpClient httpClient, IAuthenticationService userAuth)
    {
        HttpClient = httpClient;
        UserAuth = userAuth;
    }
    public  async Task<List<DuplicateProjectOwnerViewModel>> GetDuplicateProjectOwners()
    {
        QueryStringBuilder query = new("/ProjectOwner/GetDuplicateProjectOwners");
        UcpmComponentApiBase apiBase = new(HttpClient);
        return await apiBase.GetAsync<List<DuplicateProjectOwnerViewModel>>(query.ToString(), UserAuth.CurrentUser.JwtToken);
    }
    
    public async Task<MergeProjectOwnerResponse> MergeProjectOwners(MergeProjectOwnerRequest mergeProjectOwnerRequest)
    {
        QueryStringBuilder query = new("/ProjectOwner/MergeProjectOwners");
        UcpmComponentApiBase apiBase = new(HttpClient);
        var response = await apiBase.PostAsync<MergeProjectOwnerResponse, MergeProjectOwnerRequest>(query.ToString(), mergeProjectOwnerRequest, UserAuth.CurrentUser.JwtToken);
        return response;
    }
}