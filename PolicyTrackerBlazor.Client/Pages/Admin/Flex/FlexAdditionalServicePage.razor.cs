using Microsoft.AspNetCore.Components;
using PolicyTrackerBlazor.Client.Managers;
using PolicyTrackerBlazor.Client.Shared;
using UcpmApi.Shared.Flex;
using UcpmComponentLibraryEx.Services;

namespace PolicyTrackerBlazor.Client.Pages.Admin.Flex;

public class FlexAdditionalServicePageBase : PolicyTrackerBlazorComponentBase
{
    [Inject] public required FlexAdditionalServiceManager FlexAdditionalServiceManager { get; set; }

    [Inject] public required ToastService ToastService { get; set; }

    public IEnumerable<FlexAdditionalService> FlexAdditionalServices { get; set; } = [];
    public IEnumerable<FlexAdditionalService> SearchFlexAdditionalServices { get; set; } = [];

    private FlexAdditionalService FlexAdditionalService { get; set; } = new();

    protected FlexAdditionalServiceModalBase FlexAdditionalServiceModalBase { get; set; } = new();

    public int SelectedFlexAdditionalServiceId { get; set; }

    public bool IsForArchive { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await GetFlexServices();
        await base.OnInitializedAsync();
    }

    private async Task GetFlexServices()
    {
        try
        {
            DataIsLoading = true;
            FlexAdditionalServices = await FlexAdditionalServiceManager.GetAdditionalServices();
            SearchFlexAdditionalServices = FlexAdditionalServices;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            ToastService.ShowToast(ex.Message, ToastLevelEnum.Error);
        }
        finally
        {
            DataIsLoading = false;
        }
    }

    public async Task OnSaveFlexService(FlexAdditionalService flexAdditionalService)
    {
        if (flexAdditionalService.FlexAdditionalServiceId == 0)
        {
            await GetFlexServices();
        }
        else
        {
            // update flex additional service from the list
            var flexAdditionalServiceToUpdate = FlexAdditionalServices.FirstOrDefault(r =>
                r.FlexAdditionalServiceId == flexAdditionalService.FlexAdditionalServiceId);
            if (flexAdditionalServiceToUpdate != null)
            {
                flexAdditionalServiceToUpdate.FlexAdditionalServiceName =
                    flexAdditionalService.FlexAdditionalServiceName;
                flexAdditionalServiceToUpdate.Cost = flexAdditionalService.Cost;
                flexAdditionalServiceToUpdate.IsArchived = IsForArchive;
            }

            StateHasChanged();
        }
    }

    public async Task HandleEditFlexService(FlexAdditionalService flexAdditionalService)
    {
        await FlexAdditionalServiceModalBase.ShowModal(flexAdditionalService);
        StateHasChanged();
    }

    protected async Task OpenFlexServiceModal()
    {
        await FlexAdditionalServiceModalBase.ShowModal(null);
    }

    public Task OnDeleteFlexService(int flexAdditionalServiceId)
    {
        SelectedFlexAdditionalServiceId = flexAdditionalServiceId;
        IsForArchive = true;
        return Task.CompletedTask;
    }

    public Task OnActivateFlexService(FlexAdditionalService flexAdditionalService)
    {
        FlexAdditionalService = flexAdditionalService;
        IsForArchive = false;
        return Task.CompletedTask;
    }

    public async Task OnConfirmUpdateStatus()
    {
        try
        {
            DataIsLoading = true;
            // archive or activate flex service
            if (IsForArchive)
            {
                await ArchiveFlexService();
            }
            else
            {
                await ActivateFlexService();
            }
        }
        catch (Exception ex)
        {
            ToastService.ShowToast(ex.Message, ToastLevelEnum.Error);
        }
        finally
        {
            DataIsLoading = false;
        }
    }

    private async Task ArchiveFlexService()
    {
        var isSuccess = await FlexAdditionalServiceManager.DeleteFlexService(SelectedFlexAdditionalServiceId);
        if (isSuccess)
        {
            ToastService.ShowToast("Flex Service archive successfully", ToastLevelEnum.Success);
            await GetFlexServices();
        }
        else
        {
            ToastService.ShowToast("Failed to archive Flex Service", ToastLevelEnum.Error);
        }
    }
    
    private async Task ActivateFlexService()
    {
        bool isSuccess = await FlexAdditionalServiceManager.SaveFlexService(FlexAdditionalService);
        if (isSuccess)
        {
            ToastService.ShowToast("Flex Service activated successfully", ToastLevelEnum.Success);
            await OnSaveFlexService(FlexAdditionalService);
        }
        else
        {
            ToastService.ShowToast("Failed to activate Flex Service", ToastLevelEnum.Error);
        }
    }

    public async Task OnSearch(string? searchTerm)
    {
        if (string.IsNullOrEmpty(searchTerm))
        {
            SearchFlexAdditionalServices = FlexAdditionalServices;
        }
        else
        {
            await Task.FromResult(SearchFlexAdditionalServices = FlexAdditionalServices.Where(r =>
                r.FlexAdditionalServiceName.IndexOf(searchTerm, StringComparison.OrdinalIgnoreCase) >= 0 ||
                r.Cost.ToString().IndexOf(searchTerm, StringComparison.OrdinalIgnoreCase) >= 0
            ).ToList());
        }

        StateHasChanged();
    }
}