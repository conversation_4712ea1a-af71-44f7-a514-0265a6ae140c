using BlazorStrap.V5;
using Microsoft.AspNetCore.Components;
using PolicyTrackerBlazor.Client.Managers;
using PolicyTrackerBlazor.Client.Shared;
using UcpmApi.Shared;
using UcpmApi.Shared.Crm;
using UcpmComponentLibraryEx.Managers;
using UcpmComponentLibraryEx.Services;
using UcpmTools;

namespace PolicyTrackerBlazor.Client.Pages.Admin.NotificationTypes
{
    public class NotificationTypeModalBase : PolicyTrackerBlazorComponentBase
    {
        [Inject]
        protected ToastService ToastService { get; set; }

        [Inject]
        private AgentEnvNotificationTypeManager AgentEnvNotificationTypeManager { get; set; }

        [Inject]
        private DirectSendTemplateManager DirectSendTemplateManager { get; set; }

        [Parameter]
        public BSModal NotificationModal { get; set; } = new();

        [Parameter]
        public EventCallback OnPerformActionClick { get; set; }

        [Parameter]
        public AgentEnvNotificationType NotificationType { get; set; } = new();

        public AgentEnvNotificationType NotificationTypeModel { get; set; } = new();

        public List<DirectSendTemplate> DirectSendTemplates { get; set; } = [];

        public bool IsLoading { get; set; } = false;

        public string ButtonLabel { get; set; } = "Add";

        protected override async Task OnInitializedAsync()
        {
            IEnumerable<DirectSendTemplate> result = await DirectSendTemplateManager.GetDirectSendTemplates();
            DirectSendTemplates = result.OrderBy(x => x.DirectSendDescription).ToList();
        }

        protected override void OnParametersSet()
        {
            QuickReflection.CopyProps(NotificationType, NotificationTypeModel);
            ButtonLabel = GetActionName();
        }

        public async Task ShowModal()
        {
            await NotificationModal.ShowAsync();
        }

        public async Task PerformAction()
        {
            try
            {
                IsLoading = true;
                ButtonLabel = "Loading";
                bool isSuccess = false;
                isSuccess = NotificationType.AgentEnvNotificationTypeId != null ?
                        await AgentEnvNotificationTypeManager.UpdateAgentEnvNotificationType(NotificationTypeModel) :
                        await AgentEnvNotificationTypeManager.AddAgentEnvNotificationType(NotificationTypeModel);

                if (isSuccess)
                {
                    await NotificationModal.HideAsync();
                    ToastService.ShowToast($"The notification has been {GetActionName()}ed!", ToastLevelEnum.Success);
                    await OnPerformActionClick.InvokeAsync();
                }
                else
                {
                    ToastService.ShowToast($"Failed to {GetActionName()} the notification!", ToastLevelEnum.Error);
                }
            }
            catch (Exception)
            {
                ToastService.ShowToast($"Something went wrong while {GetActionName()}ing the notification.", ToastLevelEnum.Error);
            }
            finally
            {
                IsLoading = false;
                ButtonLabel = GetActionName();
            }
        }

        public string GetActionName()
        {
            return NotificationType.AgentEnvNotificationTypeId != null ? "Edit" : "Add";
        }
    }
}
