@using PolicyTrackerBlazor.Client.Components.SearchInput
@using PolicyTrackerBlazor.Client.Components.Loading
@inherits TeamSettingsBase
@page "/Admin/Tools/Teams"
<div class="p-1">
    <Breadcrumb StartIndex="1" />
</div>
<div class="container">
    <div class="text-center">
        <h4>Team Settings</h4>
    </div>
    <div class="py-1">
        <div class="row align-items-center ">
            <div class="col-md-9 col-xxl-10">
                <PTSearchInput Placeholder="Search team settings..." ValueChanged="OnSearch" Disabled="@DataIsLoading"/>
            </div>
            <div class="col-md-3 col-xxl-2 ms-auto text-sm-end text-md-end text-xxl-end text-xl-end text-center">
                <BSButton Color="BSColor.Primary" Size="Size.Small" Class="shadow-sm" OnClick="AddTeam" Target="teamFormModal">
                    <i class="bi bi-plus-lg"></i> Add Team
                </BSButton>
            </div>
        </div>
    </div>
    @if (!DataIsLoading)
    {
        <div class="table-responsive">
            <table class="table table-striped table-hover shadow-sm table-sm">
                <thead class="table-light">
                <tr>
                    <th scope="col" class="text-center">Team</th>
                    <th scope="col" class="text-center">Team Members</th>
                    <th scope="col" class="text-end col-2">Actions</th>
                </tr>
                </thead>
                <tbody>
                @if (SearchTeams.Count == 0)
                {
                    <tr>
                        <td colspan="3" class="text-center">😒 No teams found!</td>
                    </tr>
                }
                else
                {
                    foreach (var team in SearchTeams)
                    {
                        <tr>
                            <td class="text-center align-content-center">@team.TeamName</td>
                            <td class="text-center align-content-center">
                                <BSButton Class="btn btn-link" OnClick="@(() => ViewTeamMembers(team.TeamGuid))">
                                    @team.MemberCount
                                </BSButton>
                            </td>
                            <td class="text-end align-content-center">
                                <div class="btn-group btn-group-sm" role="group">
                                    <BSButton Color="BSColor.Primary" IsOutlined="true"
                                              Class="d-inline-flex shadow-sm" OnClick="@(() => ShowModalForEdit(team))">
                                        <i class="bi bi-pen-fill d-md-none d-xxl-none d-xl-none d-sm-none "></i>
                                        <span class="d-none d-sm-grid d-md-grid d-xxl-grid d-xl-grid"> Edit </span>
                                    </BSButton>
                                    <BSButton Color="BSColor.Danger" IsOutlined="true"
                                              Class="d-inline-flex shadow-sm" Target="confirmDeleteModal" OnClick="@(() => OpenDeleteModal(team))">
                                        <i class="bi bi-trash3-fill d-md-none d-xxl-none d-xl-none d-sm-none"></i>
                                        <span class="d-none d-sm-grid d-md-grid d-xxl-grid d-xl-grid"> Delete </span>
                                    </BSButton>
                                </div>
                            </td>
                        </tr>
                    }
                }
                </tbody>
            </table>
        </div>
    }
    else
    {
        <div class="row justify-content-center">
            <div class="col">
                <ContentLoader/>
            </div>
        </div>
    }
</div>

<BSModal DataId="teamFormModal" HideOnValidSubmit="true" IsStaticBackdrop="true" @ref="TeamFormModal">
    <BSForm Model="FormTeam" OnValidSubmit="SaveTeam">
        <DataAnnotationsValidator/>
        <BSModalHeader>@ModalTitle</BSModalHeader>
        <BSModalContent>
            <div class="mb-3">
                <BSLabel>Team Name</BSLabel>
                <BSInput InputType="InputType.Text" @bind-Value="FormTeam.TeamName"/>
                <BSFeedback For="@(() => FormTeam.TeamName)"/>
            </div>
        </BSModalContent>
        <BSModalFooter>
            <BSButton Size="Size.Small" Class="shadow-sm" Target="teamFormModal">
                <i class="bi bi-x-lg"></i> Cancel
            </BSButton>
            <BSButton Size="Size.Small" Class="shadow-sm" IsSubmit="true" Color="BSColor.Primary">
                <i class="bi bi-floppy2-fill"></i> Save
            </BSButton>
        </BSModalFooter>
    </BSForm>
</BSModal>

<PTConfirmationModal Icon="PTIcon.Exclamation" 
                     DataId="confirmDeleteModal" 
                     OnConfirmation="ConfirmedDeleteTeam" >
    <p class="small">
        Are you sure you want to delete this team?
    </p>
</PTConfirmationModal>