@using PolicyTrackerBlazor.Client.Components.Loading
@using PolicyTrackerBlazor.Client.Components.SearchInput
@inherits FeatureBase
@page "/Admin/Tools/Features"
<div class="p-1">
    <Breadcrumb StartIndex="1" />
</div>
<div class=container-fluid>
    <div class="text-center">
        <h4>Features</h4>
    </div>
    @if (!DataIsLoading)
    {
        <div class="topnav">
            <div class="w-100 d-flex justify-content-end">
                <BSButton Class="btn btn-primary shadow-sm" OnClick="OpenNewFeatureModal">
                    <i class="bi bi-plus-lg"></i> Add New Feature
                </BSButton>
            </div>
        </div>
        <BSNav IsTabs="true">
            <BSNavItem>
                <TabLabel>Active (@FeaturesBase.Count)</TabLabel>
                <TabContent>
                    <div class="col-4">
                        <PTSearchInput Class="input-group my-2" Placeholder="Search Features..." ValueChanged="OnSearch" Disabled="@DataIsLoading"/>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover shadow-sm table-sm">
                            <thead class="table-light">
                            <tr>
                                <th scope="col">Feature Group</th>
                                <th scope="col">Feature Name</th>
                                <th scope="col">Live Date</th>
                                <th scope="col">Sunset Date</th>
                                <th scope="col">Last Edit</th>
                                <th scope="col" class="text-center">Advanced</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach (Feature feature in Features)
                            {
                                <tr class="align-middle">
                                    <td>@feature.FeatureGroup</td>
                                    <td>
                                        @feature.FeatureName <br>
                                        <small class="text-muted">Desc: @feature.FeatureDescription | Created: @feature.DateCreatedZoned.ToString("MM-dd-yyyy")</small>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control" value="@feature.FeatureLiveDateZoned.ToString("MM-dd-yyyy")" @onchange="@(e => UpdateLiveDate(e, @feature.FeatureId))">
                                    </td>
                                    <td>
                                        <input type="text" class="form-control" value="@feature.FeatureSunsetDateZoned.ToString("MM-dd-yyyy")" @onchange="@(e => UpdateSunsetDate(e, @feature.FeatureId))">
                                    </td>
                                    <td>@feature.EmployeeName</td>
                                    <td class="text-center align-middle">
                                        <div class="btn-group btn-group-sm">
                                            <BSButton Color="BSColor.Primary" IsOutlined="true" Class="btn d-flex btn-sm shadow-sm"
                                                      Size="Size.Small" OnClick="@(() => OpenEditModal(feature))">
                                                <i class="bi bi-pen-fill"></i> Advanced
                                            </BSButton>
                                        </div>
                                    </td>
                                </tr>
                            }
                            </tbody>
                        </table>
                    </div>
                </TabContent>
            </BSNavItem>
            <BSNavItem>
                <TabLabel>Retired (@RetiredFeatures.Count)</TabLabel>
                <TabContent>
                    <div class="col-4">
                        <PTSearchInput Class="input-group my-2" Placeholder="Search Features..." ValueChanged="OnSearchRetired" Disabled="@DataIsLoading"/>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover shadow-sm table-sm">
                            <thead class="table-light">
                            <tr>
                                <th scope="col">Feature Group</th>
                                <th scope="col">Feature Name</th>
                                <th scope="col">Live Date to Sunset Date</th>
                                <th scope="col" class="text-center">Last Edit</th>
                                <th scope="col" class="text-center">Retire(d)?</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach (Feature feature in RetiredFeatures)
                            {
                                <tr class="align-middle">
                                    <td>@feature.FeatureGroup</td>
                                    <td>
                                        @feature.FeatureName<br>
                                        <small>@feature.FeatureDescription</small>
                                    </td>
                                    <td>@feature.FeatureLiveDateZoned.ToString("MM-dd-yyyy") to @feature.FeatureSunsetDateZoned.ToString("MM-dd-yyyy")</td>
                                    <td>@feature.EmployeeName</td>
                                    <td class="text-center align-middle col-2">
                                        <div class="btn-group btn-group-sm">
                                            <BSButton Size="Size.Small" Color="BSColor.Warning"
                                                      Class="btn d-flex btn-sm shadow-sm" OnClick="@(() => UpdateRetiredStatus(feature.FeatureId))">
                                                <i class="bi bi-tags-fill"></i> Tag me back in coach
                                            </BSButton>
                                        </div>
                                    </td>
                                </tr>
                            }
                            </tbody>
                        </table>
                    </div>
                </TabContent>
            </BSNavItem>
        </BSNav>
    }
    else
    {
        <div class="row justify-content-center">
            <div class="col">
                <ContentLoader/>
            </div>
        </div>
    }
</div>
<div class="modal" tabindex="-1" style="display: @(AddFeatureModalIsVisible ? "block" : "none");">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Feature</h5>
                <BSButton type="button" class="btn-close" data-dismiss="modal" aria-label="Close" OnClick="@(() => ModalClose("addFeatureModal"))"></BSButton>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="featureName">Name:</label>
                    <input type="text" id="featureName" class="form-control" @bind="NewFeatureDetails.FeatureName">
                </div>
                <div class="mb-3">
                    <label for="featureDesc">Feature Description:</label>
                    <input type="text" id="featureDesc" class="form-control" @bind="NewFeatureDetails.FeatureDescription">
                </div>
                <div class="mb-3">
                    <label for="liveDate">Live Date:</label>
                    <input type="date" id="liveDate" class="form-control" @bind="NewFeatureDetails.FeatureLiveDateZoned">
                </div>
                <div class="mb-3">
                    <label for="sunsetDate">Sunset Date:</label>
                    <input type="date" id="sunsetDate" class="form-control" @bind="NewFeatureDetails.FeatureSunsetDateZoned">
                </div>
                <div class="mb-3">
                    <label for="featureGroup">Feature Group:</label>
                    <BSInput InputType="InputType.Select" @bind-Value="NewFeatureDetails.FeatureGroupId" id="featureGroup">
                        <option value="">-- Select --</option>
                        @foreach (var featureGroup in FeatureGroups)
                        {
                            <option value="@featureGroup.FeatureGroupId">@featureGroup.FeatureGroupName</option>
                        }
                    </BSInput>
                </div>
                <div class="mb-3">
                    <InputCheckbox id="IsAvailableToWholesaler" class="form-check-input" @bind-Value="NewFeatureDetails.IsAvailableToWholesaler"/>
                    <label class="form-check-label" for="IsAvailableToWholesaler">Available to Wholesaler</label>
                </div>
                <div class="mb-3">
                    <InputCheckbox id="VisibleOnHomeScreen" class="form-check-input" @bind-Value="NewFeatureDetails.VisibleOnHomeScreen"/>
                    <label class="form-check-label" for="VisibleOnHomeScreen">Visible On Home Screen</label>
                </div>
            </div>
            <div class="modal-footer">
                <BSButton class="btn-secondary d-flex justify-content-lg-center" OnClick="@(() => ModalClose("addFeatureModal"))">
                    Cancel
                </BSButton>
                <BSButton class="btn-primary d-flex justify-content-lg-center" OnClick="@(() => AddNewFeature())">
                    Save
                </BSButton>
            </div>
        </div>
    </div>
</div>

<div class="modal" tabindex="-1" style="display: @(EditFeatureModalIsVisible ? "block" : "none");">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Feature Name</h5>
                <BSButton type="button" class="btn-close" data-dismiss="modal" aria-label="Close" OnClick="@(() => ModalClose("editFeatureModal"))"></BSButton>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="imageUrl">Image Url:</label>
                    <input type="text" id="imageUrl" class="form-control" @bind="EditFeatureDetails.ImageUrl">
                </div>
                <div class="mb-3">
                    <label for="featureDescription">Feature Description:</label>
                    <input type="text" id="featureDescription" class="form-control" @bind="EditFeatureDetails.FeatureDescription">
                </div>
                <div class="mb-3">
                    <label for="featureMenuText">Feature Menu Text:</label>
                    <input type="text" id="featureMenuText" class="form-control" @bind="EditFeatureDetails.MenuText">
                </div>
                <div class="mb-3">
                    <InputCheckbox id="IsAvailableToWholesaler2" class="form-check-input" @bind-Value="EditFeatureDetails.IsAvailableToWholesaler"/>
                    <label class="form-check-label" for="IsAvailableToWholesaler2">Available to Wholesaler</label>
                </div>
                <div class="mb-3">
                    <InputCheckbox id="VisibleOnHomeScreen2" class="form-check-input" @bind-Value="EditFeatureDetails.VisibleOnHomeScreen"/>
                    <label class="form-check-label" for="VisibleOnHomeScreen2">Visible On Home Screen</label>
                </div>
                @if (IsProgrammer)
                {
                    <div class="row">
                        <div class="col mb-3">
                            <label for="featureGroup">Feature Group:</label>
                            <BSInput InputType="InputType.Select" @bind-Value="EditFeatureDetails.FeatureGroupId">
                                <option value="null">-- Select --</option>
                                @foreach (FeatureGroup featureGroup in FeatureGroups)
                                {
                                    <option value="@featureGroup.FeatureGroupId">@featureGroup.FeatureGroupName</option>
                                }
                            </BSInput>
                        </div>
                        <div class="col mb-3">
                            <label for="targetController">Target Controller:</label>
                            <input type="text" id="targetController" class="form-control" @bind="EditFeatureDetails.ControllerName">
                        </div>
                        <div class="col mb-3">
                            <label for="targetAction">Target Action:</label>
                            <input type="text" id="targetAction" class="form-control" @bind="EditFeatureDetails.ActionName">
                        </div>
                    </div>
                }

                <div class="mb-3">
                    <label for="externalFeatureUrl">External Feature Url:</label>
                    <input type="text" id="externalFeatureUrl" class="form-control" @bind="EditFeatureDetails.OffsiteFeatureUrl">
                </div>
            </div>
            <div class="modal-footer">
                <BSButton class="btn-secondary d-flex justify-content-lg-center" OnClick="@(() => ModalClose("editFeatureModal"))">
                    Cancel
                </BSButton>
                <BSButton class="btn-primary d-flex justify-content-lg-center" OnClick="@(() => UpdateFeatureDetails())">
                    Save
                </BSButton>
            </div>
        </div>
    </div>
</div>