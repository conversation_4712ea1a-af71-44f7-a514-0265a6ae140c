@page "/Admin/Tools/Roles/List"
@using PolicyTrackerBlazor.Shared.Models.Tools
@using PolicyTrackerBlazor.Client.Components.SearchInput
@using PolicyTrackerBlazor.Client.Components.Loading
@inherits RolesBase
<PageTitle>Role List</PageTitle>

<div class="p-1">
    <Breadcrumb StartIndex="1" />
</div>
<div class="container-fluid">
    <div class="text-center">
        <h4>Role List</h4>
    </div>
    <div class="py-1">
        <div class="row align-items-center ">
            <div class="col-md-9 col-xxl-10">
                <PTSearchInput Placeholder="Search roles..." ValueChanged="OnSearch" Disabled="@DataIsLoading"/>
            </div>
            <div class="col-md-3 col-xxl-2 ms-auto text-sm-end text-md-end text-xxl-end text-xl-end text-center">
                <BSButton Color="BSColor.Primary" Size="Size.Small" Class="shadow-sm focus-ring" Target="new-role-modal">
                    <i class="bi bi-plus-lg"></i> New Role
                </BSButton>
            </div>
        </div>
    </div>
    @if (!DataIsLoading)
    {
        <div class="table-responsive">
            <table class="table table-striped table-hover shadow-sm table-sm">
                <thead class="table-light">
                <tr>
                    <th scope="col">ID</th>
                    <th scope="col">Role Name and Type</th>
                    <th scope="col">Permissions Assigned to Role</th>
                    <th scope="col">Actions</th>
                </tr>
                </thead>
                <tbody>
                @if (SearchResults.Any())
                {
                    @foreach (var role in SearchResults)
                    {
                        <RoleItem @bind-SelectedRole="SelectedRole" OnEditButtonClick="GetPermissionByAccountId" Role="@role"></RoleItem>
                    }
                }
                else
                {
                    <tr class="role-item fs-6">
                        <td scope="row" class="text-center fs-5" colspan="4">
                            😒 No Roles found!
                        </td>
                    </tr>
                }
                </tbody>
            </table>
        </div>
    }
    else
    {
        <div class="row justify-content-center">
            <div class="col">
                <ContentLoader/>
            </div>
        </div>
    }
</div>

@* role permission edit modal form *@
<BSModal DataId="role-edit-modal" IsScrollable="true">
    <Header>Edit Role Permissions</Header>
    <Content>
        @foreach (var permission in PermissionsByAccountId ?? [])
        {
            <div class="form-check">
                <input class="form-check-input" @bind="@permission.IsSelected" type="checkbox" value="" id="@permission?.PermissionId">
                <label class="form-check-label" for="@permission!.PermissionId">
                    @permission?.PermissionName
                </label>
            </div>
        }
    </Content>
    <Footer Context="modal">
        <BSButton MarginStart="Margins.Auto" Size="Size.Small" Class="shadow-sm" @onclick="modal.HideAsync">
            <i class="bi bi-x-lg"></i> Close
        </BSButton>
        <BSButton Color="BSColor.Primary" Size="Size.Small" Class="shadow-sm" IsDisabled="SaveButtonDisabled" OnClick="UpdateSelectedRoles">
            <i class="bi bi-floppy2-fill"></i>  @SaveButtonLabel
        </BSButton>
    </Footer>
</BSModal>

@* new role modal form *@
<BSModal DataId="new-role-modal" OnShow="@(() => OnNewRole(new RoleModel()))" HideOnValidSubmit="true" IsStaticBackdrop="true">
    <BSForm Model="NewRole" OnValidSubmit="@OnSaveRole">
        <DataAnnotationsValidator/>
        <BSModalHeader>New Role</BSModalHeader>
        <BSModalContent>
            <div class="mb-3">
                <BSLabel>Role Name</BSLabel>
                <BSInput InputType="InputType.Text" @bind-Value="@NewRole.RoleName"/>
                <BSFeedback For="@(() => NewRole.RoleName)"/>
            </div>
            <div class="mb-3">
                <BSLabel>Account Type</BSLabel>
                <BSInput InputType="InputType.Select" placeholder="Select Account" @bind-Value="@NewRole.AccountTypeId" InputSize="Size.Medium">
                    <option value="0">select account</option>
                    @foreach (var accountType in NewRole.AccountTypes)
                    {
                        <option value="@accountType.AccountTypeId">@accountType.AccountType</option>
                    }
                </BSInput>
                <BSFeedback For="@(() => NewRole.AccountTypeId)"/>
            </div>
        </BSModalContent>
        <BSModalFooter>
            <BSButton Target="new-role-modal" Size="Size.Small" Class="shadow-sm">
                <i class="bi bi-x-lg"></i> Cancel
            </BSButton>
            <BSButton IsSubmit="true" Size="Size.Small" Class="shadow-sm" IsDisabled="SaveButtonDisabled" Color="BSColor.Primary">
                <i class="bi bi-floppy2-fill"></i> @SaveButtonLabel
            </BSButton>
        </BSModalFooter>
    </BSForm>
</BSModal>