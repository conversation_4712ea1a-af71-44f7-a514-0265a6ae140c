@using UcpmApi.Shared.Planning

<h5 class="text-start">Cards</h5>
<ol>
    @foreach (Backlog other in SprintList)
    {
        var hasQuestions = other.Questions.Any();
        <li>
            <a class="text-break @((other.Estimates.Any(s => s.EmployeeGuid == EmployeeGuid)) ? "text-success" : "")" @onclick="@(()=> SwapBacklogHandler(other))">
                @other.Title
                @if (hasQuestions)
                {
                    <span class="text-info">(Q)</span> <!-- This indicates the backlog has questions. Replace with an icon or badge if preferred. -->
                }
            </a>
        </li>
    }
</ol>

@code {

    [Parameter]
    public Guid EmployeeGuid { get; set; }

    [Parameter]
    public List<Backlog> SprintList { get; set; }

    [Parameter]
    public EventCallback<Backlog> SwapBacklog { get; set; }

    private async Task SwapBacklogHandler(Backlog backlog)
    {
        await SwapBacklog.InvokeAsync(backlog);
    }
}
