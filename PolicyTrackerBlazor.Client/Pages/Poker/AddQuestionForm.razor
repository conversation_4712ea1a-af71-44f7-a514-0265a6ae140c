@using UcpmApi.Shared.Planning

<EditForm Model="@NewQuestion" OnValidSubmit="@SubmitQuestion">
    <div class="input-group">
        <InputText id="questionText" class="form-control" @bind-Value="NewQuestion.QuestionText" />
        <button type="submit" class="btn btn-outline-primary">Submit Question</button>
    </div>
    
</EditForm>

@code {
    private BacklogQuestion NewQuestion { get; set; } = new BacklogQuestion();

    // This EventCallback should be invoked to notify the parent component.
    [Parameter]
    public EventCallback<BacklogQuestion> OnAddQuestion { get; set; }

    // This method handles the form submission and invokes the EventCallback.
    private async Task SubmitQuestion()
    {
        await OnAddQuestion.InvokeAsync(NewQuestion);
        NewQuestion = new BacklogQuestion(); // Reset the form after submitting
    }
}
