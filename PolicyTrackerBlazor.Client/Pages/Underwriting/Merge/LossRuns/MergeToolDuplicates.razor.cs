using Microsoft.AspNetCore.Components;
using PolicyTrackerBlazor.Client.Shared;
using UcpmApi.Shared;
using UcpmApi.Shared.Policy;
using UcpmComponentLibraryEx.Managers;
using UcpmComponentLibraryEx.Services;

namespace PolicyTrackerBlazor.Client.Pages.Underwriting.Merge.LossRuns;

public class LossRunsDuplicateMergeToolModalBase : PolicyTrackerBlazorComponentBase
{
    [Inject]
    public ToastService ToastService { get; set; }

    [Inject]
    public PolicyManager PolicyManager { get; set; }

    [Inject]
    public NavigationManager NavigationManager { get; set; }

    [Parameter, SupplyParameterFromQuery]
    public Guid PolicyProspectGuid { get; set; } = new();

    [Parameter]
    public PolicyProspect Policy { get; set; } = new();

    public Claim? SelectedClaim { get; set; }

    protected bool Loading { get; set; } = true;

    protected IEnumerable<IGrouping<string, Claim>> DuplicateClaimsByClaimNumber { get; set; } = [];

    public ClaimEditor ClaimEditor { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            Loading = true;
            Policy = await PolicyManager.GetPolicyProspectByGuid(PolicyProspectGuid);
            DuplicateClaimsByClaimNumber = Policy.Claims.GroupBy(claim => claim.ClaimNumber).Where(claim => claim.Count() > 1);

            await base.OnInitializedAsync();
        }
        catch (Exception)
        {
            ToastService.ShowToast("Failed to load policy.", ToastLevelEnum.Error);
        }
        finally
        {
            Loading = false;
        }
    }

    public async Task HandleSelect(Claim claim)
    {
        SelectedClaim = claim;
        await ClaimEditor.ShowModal();
        StateHasChanged();
    }

    public void HandleSuccessfulMerge()
    {
        NavigationManager.NavigateTo("/admin/underwriting/tools/merge/LossRunsDuplicateMergeTool");
    }
}

