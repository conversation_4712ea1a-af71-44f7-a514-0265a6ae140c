@inherits CoverageIssuesBase
@page "/Admin/DcatTools/CarrierList/CarrierProgramList/{CarrierGuid:guid}/Forms/{ProgramGuid:guid}/CoverageIssues/{FormGuid:guid}"
@using PolicyTrackerBlazor.Client.Components.Loading
@using UcpmApi.Shared.Carrier
@using UcpmApi.Shared.Policy

<PageTitle>Coverage Issues</PageTitle>
<div class="p-1">
    <Breadcrumb StartIndex="1" ContainsGuidSegmentPairs="true" StartGuidPairingIndex="3" />
</div>
<div class="container-fluid">
    <h3 class="text-center">Coverage Issues</h3>
    
    @if (DataIsLoading)
    {
        <div class="row justify-content-center">
            <div class="col">
                <ContentLoader/>
            </div>
        </div>
    }
    else
    {

        if (Forms != null && Forms.Any())
        {
            <h5 class="text-center">Carrier: @Forms.FirstOrDefault()?.CarrierName</h5>
            <h5 class="text-center">Program: @Forms.FirstOrDefault()?.ProgramName</h5>
            <h5 class="text-center">Form: @Forms.FirstOrDefault(f => f.FormGuid == FormGuid)?.FormNumber</h5>
        }
            <BSRow>
                <BSCol Class="col-4">
                    <div style="max-height: 500px; overflow-y: auto;">
                        <BSTable Class="table table-striped table-hover table-bordered" IsBordered="false" style="border-collapse: separate; border-spacing: 0">
                            <BSTHead Class="table-dark" style="position: sticky; top: 0; z-index: 1;">
                                <BSTR>
                                    <BSTD Class="col">Coverage Issue</BSTD>

                                </BSTR>
                            </BSTHead>
                            <BSTBody>
                            @foreach (CoverageIssue form in MatchingCoverage.OrderBy(c => c.CoverageIssueSortOrder))
                                {
                                    <BSTR>
                                        <BSTD>@form.CoverageIssueName</BSTD>
                                    </BSTR>
                                }
                            </BSTBody>
                        </BSTable>
                    </div>
                    <div class="d-flex justify-content-end mt-2">
                        <BSButton Color="BSColor.Danger" class="me-3"><i class="fa fa-arrow-left" /> Remove</BSButton>
                        <BSButton Color="BSColor.Success">Add <i class="fa fa-arrow-right" /></BSButton>
                    </div>
                </BSCol>


                <BSCol>
                    <div style="max-height: 500px; overflow-y: auto;">
                        <BSTable Class="table table-striped table-hover table-bordered" IsBordered="false" style="border-collapse: separate; border-spacing: 0">
                            <BSTHead Class="table-dark">
                                <BSTR>
                                    <BSTD Class="col">Coverage Issue Name</BSTD>
                                    <BSTD Class="col">Priority Value</BSTD>
                                    <BSTD Class="col">Short Text</BSTD>
                                    <BSTD Class="col">Verified</BSTD>
                                </BSTR>
                            </BSTHead>
                            <BSTBody>
                                @if (FormCoverageIssue != null && FormCoverageIssue.Any())
                                {
                                    @foreach (FormCoverageIssue form in FormCoverageIssue.OrderBy(f => f.CoverageIssue.MetaIssueId))
                                    {
                                        <BSTR>
                                            <BSTD>@form.CoverageIssue.CoverageIssueName</BSTD>
                                            <BSTD>@form.IssuePriority.PriorityName</BSTD>
                                            <BSTD>@form.CoverageIssueStatus.ShortText</BSTD>
                                            <BSTD>@form.VerifiedByUcpmDateZoned.ToString("MM/dd/yyyy")</BSTD>
                                        </BSTR>
                                    }
                                }
                                else
                                {
                                    <BSTR>
                                        <BSTD colspan="4" class="text-center">No data available</BSTD>
                                    </BSTR>
                                }
                            </BSTBody>
                        </BSTable>
                    </div>
                </BSCol>

            </BSRow>
            <div class="row-12 d-flex justify-content-end align-items-end">
                <div class="col d-flex flex-column align-items-end">
                    <BSButton Color="BSColor.Secondary" class="btn mb-2">Set Issue Status</BSButton>
                    <div class="d-flex justify-content-end">
                        <BSButton Color="BSColor.Secondary" class="me-3"><i class='bi bi-floppy'></i> Save</BSButton>
                        <BSButton Class="ms-1 me-1" IsOutlined="true" Color="BSColor.Secondary" Size="Size.Small" OnClick="BackToPrevPage"><i class="bi bi-arrow-return-left"></i> Back</BSButton>
                    </div>
                </div>
            </div>
    }
 </div>
