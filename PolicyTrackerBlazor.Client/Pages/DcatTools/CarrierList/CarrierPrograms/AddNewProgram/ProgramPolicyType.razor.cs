using Microsoft.AspNetCore.Components;
using Microsoft.Identity.Client;
using Microsoft.JSInterop;
using PolicyTrackerBlazor.Client.Shared;
using System.ComponentModel;
using UcpmApi.Shared;
using UcpmApi.Shared.Carrier;
using UcpmApi.Shared.Enums;
using UcpmComponentLibraryEx.Managers;
using UcpmComponentLibraryEx.Services;

namespace PolicyTrackerBlazor.Client.Pages.DcatTools.CarrierList.CarrierPrograms.AddNewProgram;

public class PolicyProgramTypeBase : PolicyTrackerBlazorComponentBase
{
    [Parameter]
    public Guid CarrierGuid { get; set; }

    [Parameter]
    public Guid CarrierProgramGuid { get; set; }

    [Parameter]
    public Guid ProgramGuid { get; set; }
    [Parameter]
    public Guid CarrierPolicyPoolGuid { get; set; }
    [Parameter]
    public int PolicyTypeId { get; set; }

    [Inject]
    public required CarrierProgramManager CarrierProgramManager { get; set; }
    [Inject]
    public required CarrierPolicyPoolBatchManager CarrierPolicyPoolBatchManager { get; set; }
    [Inject]
    protected IssuingCompanyManager? IssuingCompanyManager { get; set; }
    [Inject]
    public required EmployeeManager EmployeeManager { get; set; }
    [Inject]
    public required CarrierMaximumOfferManager CarrierMaximumOfferManager { get; set; }
    [Inject]
    public required CarrierMaximumOfferCommissionManager CarrierMaximumOfferCommissionManager { get; set; }
    [Inject]
    public required PolicyTypeManager PolicyTypeManager { get; set; }
  
    public List<CarrierProgram>? CarrierPrograms { get; set; }
    protected IEnumerable<CarrierProgram>? GetAllCarrierPrograms { get; set; }
    protected IEnumerable<CarrierProgram>? CarrierPolicy { get; set; }
    public IEnumerable<CarrierPolicyPool> CarrierPolicyPoolList { get; set; }
    public IEnumerable<PolicyNumberingRule> PolicyNumberingRule { get; set; }
    public CarrierProgram CarrierProgramToAdd { get; set; } = new();
    protected SetDefaultIssuingCompanyModal? SetDefaultIssuingCompanyModal { get; set; }
    protected EditCarrierFeesAndTaxesOverridesModal? EditCarrierFeesAndTaxesOverridesModal { get; set; }
    protected CarrierMaximumOffer BindedCarrierMaximumOffer { get; set; }
    protected IEnumerable<IssuingCompany> IssuingCompanies { get; set; } = [];
    public IEnumerable<Employee> Employees { get; set; }
    public IEnumerable<PolicyNumberingRule> CarrierPolicyPoolBatch { get; set; }
    public bool OfferIsNew { get; set; }
    public CarrierMaximumOfferCommission DefaultCommission { get; set; }
    public CarrierMaximumOfferCommission RenewalCommission { get; set; } 
    public CarrierMaximumOfferCommission ProgramCommission { get; set; } 
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        DataIsLoading = true;
        CarrierPrograms = (await CarrierProgramManager.GetAllCarrierProgramsByCarrierGuid(CarrierGuid)).ToList();
        GetAllCarrierPrograms = await CarrierProgramManager.GetClaimsAndPolicyByGuid(CarrierProgramGuid);
        CarrierPolicyPoolBatch = await CarrierPolicyPoolBatchManager.GetAllPolicyNumberingRule();
        IssuingCompanies = await IssuingCompanyManager!.GetIssuingCompaniesByCarrierGuid(CarrierGuid);
        PolicyNumberingRule = await CarrierPolicyPoolBatchManager.GetAllPolicyNumberingRule();
        CarrierPolicyPoolList = await CarrierPolicyPoolBatchManager.GetPolicyPoolByCarrierGuid(CarrierGuid);
        Employees = await EmployeeManager.GetAllEmployeesByPermissionWithSelect((int)PermissionEnum.SaveOfferEditor);
        await SetBindedOffer();
        await GetCommissionData();
        DataIsLoading = false;
        
    }

    private async Task SetBindedOffer() 
    {
        List<CarrierMaximumOffer> offersByProgram = await CarrierMaximumOfferManager.GetByCarrierProgram(CarrierProgramGuid) ?? new List<CarrierMaximumOffer>();
        BindedCarrierMaximumOffer = offersByProgram.FirstOrDefault(o => o.PolicyTypeId == PolicyTypeId);
        if (BindedCarrierMaximumOffer == null)
        {
            OfferIsNew = true;
            BindedCarrierMaximumOffer = new CarrierMaximumOffer();
            BindedCarrierMaximumOffer.PolicyTypeId = PolicyTypeId;
            BindedCarrierMaximumOffer.CarrierProgramGuid = CarrierProgramGuid;
            BindedCarrierMaximumOffer.CarrierMaximumOfferGuid = Guid.NewGuid();
            BindedCarrierMaximumOffer.PolicyNumberingRuleId = PolicyNumberingRule.FirstOrDefault().PolicyNumberingRuleId;
        }
    }

    private async Task GetCommissionData() 
    { 
        List<CarrierMaximumOfferCommission> commissions = await CarrierMaximumOfferCommissionManager.GetByOffer(BindedCarrierMaximumOffer.CarrierMaximumOfferGuid);

        DefaultCommission = commissions.FirstOrDefault(c => c.CarrierCommissionComponentId == (int)CarrierMaximumOfferCommissionEnum.DefaultCommGross)
            ?? new CarrierMaximumOfferCommission 
            { VerifiedOnZoned = DateTimeOffset.Now, CarrierMaximumOfferGuid = BindedCarrierMaximumOffer.CarrierMaximumOfferGuid, CarrierCommissionComponentId = (int)CarrierMaximumOfferCommissionEnum.DefaultCommGross };

        RenewalCommission = commissions.FirstOrDefault(c => c.CarrierCommissionComponentId == (int)CarrierMaximumOfferCommissionEnum.DefaultRenewal)
            ?? new CarrierMaximumOfferCommission 
            { VerifiedOnZoned = DateTimeOffset.Now, CarrierMaximumOfferGuid = BindedCarrierMaximumOffer.CarrierMaximumOfferGuid, CarrierCommissionComponentId = (int)CarrierMaximumOfferCommissionEnum.DefaultRenewal };

        ProgramCommission = commissions.FirstOrDefault(c => c.CarrierCommissionComponentId == (int)CarrierMaximumOfferCommissionEnum.ProgramCommGross)
            ?? new CarrierMaximumOfferCommission 
            { VerifiedOnZoned = DateTimeOffset.Now, CarrierMaximumOfferGuid = BindedCarrierMaximumOffer.CarrierMaximumOfferGuid, CarrierCommissionComponentId = (int)CarrierMaximumOfferCommissionEnum.ProgramCommGross };
    }
   
    public async Task SavePolicyType() 
    {

        if (!ValidateCommissionValues()) 
        {
            ToastService.ShowToast("Please ensure all commission values are in decimal notation (ex. 0.12)", ToastLevelEnum.Error);
            return;
        }

        try
        {
            if (OfferIsNew)
            {
                await CarrierMaximumOfferManager.SaveCarrierMaximumOfferViaDcatTools(BindedCarrierMaximumOffer);
            }
            else
            {
                await CarrierMaximumOfferManager.UpdateCarrierMaximumOfferViaDcatTools(BindedCarrierMaximumOffer);
            }

            await SaveCommissionOnOffer();

            ToastService.ShowToast("Successfully saved policy type for carrier program.", ToastLevelEnum.Success);
            NavManager.NavigateTo($"/Admin/DcatTools/CarrierList/CarrierProgramList/{CarrierGuid}/CarrierProgramDetails/{CarrierProgramGuid}");
        }
        catch(Exception ex)  
        {
            ToastService.ShowToast("Error occurred when trying to save.", ToastLevelEnum.Error);
        }
    }
    public async Task SaveCommissionOnOffer() 
    {
        List<CarrierMaximumOfferCommission> commissionsToSave = new List<CarrierMaximumOfferCommission>
        {
            DefaultCommission,
            ProgramCommission
        };

        if (BindedCarrierMaximumOffer.UseRenewalSplit) 
        { 
            commissionsToSave.Add(RenewalCommission);
        }
        
        bool success = await CarrierMaximumOfferCommissionManager.SaveCommissions(commissionsToSave);
    }
    protected async Task BackToPrevPage()
    {
        await JSRuntime.InvokeVoidAsync("history.back");
    }

    protected async Task ShowSetDefaultIssuingCompanyModal()
    {
        await SetDefaultIssuingCompanyModal!.ShowModal();
    }

    protected async Task ShowEditCarrierFeesAndTaxesOverridesModal()
    {
        await EditCarrierFeesAndTaxesOverridesModal!.ShowModal();
    }
    private bool ValidateCommissionValues() 
    {
        List<CarrierMaximumOfferCommission> commissionsToSave = new List<CarrierMaximumOfferCommission>
        {
            DefaultCommission,
            ProgramCommission,
            RenewalCommission,
        };
        
        if(commissionsToSave.Where(c => c.DefaultCommission > 1).Any()) 
        {
            return false;
        }

        return true;
    }
}