@using PolicyTrackerBlazor.Client.Components.Loading
@inherits DcatTemplateProgramSnippetEditorBase;
@page "/Admin/DcatTools/ProgramList/DcatTemplateProgramSnippetEditor/{ProgramGuid:guid}"

<PageTitle>DCAT Template Program Snippet Editor</PageTitle>
<div class="p-1">
    <Breadcrumb StartIndex="1" ContainsGuidSegmentPairs="true" StartGuidPairingIndex="3" />
</div>
<div class="container-fluid">
    <div class="text-center">
        <h4>DCAT Template Program Snippet Editor</h4>
        <h3>Program: @Program?.ProgramName</h3>
    </div>
    <div class="py-1">
        <div class="row align-items-center ">
            <div class="col-12">
                
            </div>
        </div>
    </div>

    @if (DataIsLoading)
    {
        <div class="row justify-content-center">
            <div class="col">
                <ContentLoader/>
                <ContentLoader/>
                <ContentLoader/>
            </div>
        </div>
    }
    else
    {
        <EditForm @ref="UpdateDcatTemplateProgramSnippetForm" Model="DcatTemplateProgramSnippet" FormName="UpdateDcatTemplateProgramSnippetForm">
            <DataAnnotationsValidator />
            <BSContainer>
                <BSRow MarginBottom="Margins.Small">
                    <BSLabel Class="form-label">HTML Template:</BSLabel>
                    <BSCol>
                        <BSInput InputType="InputType.TextArea" @bind-Value="@DcatTemplateProgramSnippet.HtmlTemplate" style="height: 550px" />
                    </BSCol>
                </BSRow>
                <BSRow MarginBottom="Margins.Small">
                    <BSLabel Class="form-label">Future HTML Template:</BSLabel>
                    <BSCol>
                        <BSInput InputType="InputType.TextArea" @bind-Value="@DcatTemplateProgramSnippet.FutureHtmlTemplate" style="height: 550px"/>
                    </BSCol>
                </BSRow>
                <CustomButton Class="btn btn-outline-primary btn-sm float-end" ButtonLabel="Save" ButtonLoadingLabel="Saving" ButtonEvent="HandleValidSubmit"/>
            </BSContainer>
        </EditForm>
    }
</div>
