@page "/Pulse/KanbanView"
@using PolicyTrackerBlazor.Client.Components.Loading
@using PolicyTrackerBlazor.Client.Components.SearchInput
@inherits KanbanViewBase
@attribute [Authorize(Roles = "Employee")]

@if (!DataIsLoading)
{
    <div class="pulse-page-wrapper">
    <div class="row">
        <div class="col-6">
            <h3>Kanban View</h3>
        </div>
        <div class="col-6 text-end">
            <button class="btn btn-primary" type="button" @onclick="@GoToInsuredList">Insured List</button>
        </div>
    </div>
    <br>
    <div class="pulse-page-wrapper">
        @if (UserAuth.CurrentUser.Roles.Contains("Pulse Admin"))
        {
            <div class="row">
                <div class="col-3">
                    <BSLabel for="">Program</BSLabel>
                    <select class="form-control form-select" @bind="SelectedProgram">
                        <option value="">Select</option>
                        @foreach (var program in ProgramList)
                        {
                            <option value="@program">@program</option>
                        }
                    </select>
                </div>
                <div class="col-3">
                    <BSLabel for="">Pulse Users</BSLabel>
                    <select class="form-control form-select" @bind="SelectedAgent">
                        <option value="">Select</option>
                        @foreach (var agent in AgentList)
                        {
                            <option value="@agent">@agent</option>
                        }
                    </select>
                </div>
                <div class="col-3">
                    <div class="float-end">
                        <BSLabel for=""> Search</BSLabel>
                        <PTSearchInput Placeholder="Search Insured..." ValueChanged="OnSearch" Disabled="@DataIsLoading" Value="@SearchTerm" />
                    </div>
                </div>
            </div>
        }
        @if (UserAuth.CurrentUser.Roles.Contains("Pulse User"))
        {
            <div class="d-flex justify-content-start input-group row align-items-center">
                <div class="col-6">
                    <PTSearchInput Placeholder="Search Insured..." ValueChanged="OnSearch" Disabled="@DataIsLoading" Value="@SearchTerm" />
                </div>
                <div class="col-6">
                    <BSButton Class="form-control btn-primary" style="max-width:150px" data-bs-toggle="collapse" data-bs-target="#collapseAdvancedSearch" aria-expanded="false" aria-controls="collapseAdvancedSearch">
                        Advanced Search
                    </BSButton>
                </div>
            </div>
            <div class="collapse multi-collapse" id="collapseAdvancedSearch">
                <div class="card card-body">
                    <div class="col-12">
                        <div class="row">
                            <div class="col-3">
                                <BSLabel for="">Program</BSLabel>
                                <select class="form-control form-select" @bind="SelectedProgram">
                                    <option value="">Select</option>
                                    @foreach (var program in ProgramList)
                                    {
                                        <option value="@program">@program</option>
                                    }
                                </select>
                            </div>
                            <div class="col-3">
                                <BSLabel for="">Agents</BSLabel>
                                <select class="form-control form-select" @bind="SelectedAgent">
                                    <option value="">Select</option>
                                    @foreach (var agent in AgentList)
                                    {
                                        <option value="@agent">@agent</option>
                                    }
                                </select>
                            </div>
                            <div class="col-3">
                                <BSLabel for="">Status</BSLabel>
                                <select class="form-control form-select" @bind="SelectedStatus">
                                    <option value="">Select</option>
                                    @foreach (var status in StatusList)
                                    {
                                        <option value="@status">@status</option>
                                    }
                                </select>
                            </div>
                            <div class="col-3">
                                <BSLabel for="">By Days</BSLabel>
                                <select class="form-control form-select" @bind="SelectedBand">
                                    <option value="">Select</option>
                                    @foreach (var band in BandList)
                                    {
                                        <option value="@band">@band</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <br>
                        <div class="col-3">
                            <div class="col-12">
                                <div class="row">
                                    <div class="col-6">
                                        <BSLabel for="">Effective Date</BSLabel>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" @bind="IncludeDate" /> Include
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <input type="date" class="form-control" @bind="EffectiveDate" disabled="@(!IncludeDate)" />
                        </div>
                    </div>
                </div>
                <br>
            </div>
        }
        <div class="pulse-policies-container">
            @foreach (PulseSubStatusEnum pulseSubStatus in PulseSubStatusesSorted)
            {
                <div class="d-flex flex-column m-2">
                    @{
                        var model = PulsePoliciesGroup.GetModel($"ListId{pulseSubStatus}");
                        var totalCountPerStatus = model?.Items.Count() ?? 0;
                    }
                    <div class="policies-column-title">
                        @AddSpacesBeforeCapitals(pulseSubStatus)
                        @if (TotalCountPerStatus.TryGetValue(pulseSubStatus, out var status))
                        {
                            <span class="ms-2 badge text-bg-secondary">
                                @totalCountPerStatus / @status.ToString()
                            </span>
                        }
                    </div>
                    <div class="policies-column">
                        <SortableList Id="@($"ListId{pulseSubStatus}")" GroupModel="PulsePoliciesGroup" Context="policyDetails">
                            <SortableItemTemplate>
                                <div class="card my-3 pulse-tile" @onclick="() => GoToAccountDetails(policyDetails.PolicyProspectGuid)">
                                    <div class="card-body">
                                        <div class="row justify-content-between">
                                            <div class="col-11">
                                                <span class="card-title fw-bold fs-6">@policyDetails.InsuredName</span>
                                            </div>
                                            <div class="d-flex col-1 align-items-center justify-content-center">
                                            </div>
                                        </div>
                                        <span class="card-subtitle mb-2 text-muted">
                                            @policyDetails.EffectiveDate.ToString("MM/dd/yyyy")
                                        </span>
                                        <span class="card-text">@policyDetails.PrimaryRep</span><br />
                                        <span class="card-text">@policyDetails.AgentName</span><br />
                                        <span class="card-text">@policyDetails.ProgramName</span><br />
                                    </div>
                                </div>
                            </SortableItemTemplate>
                        </SortableList>
                    </div>
                </div>
            }
        </div>
    </div>
    </div>
}
else
{
    <div class="row justify-content-center">
        <div class="col-12">
            <ContentLoader />
            <ContentLoader />
            <ContentLoader />
        </div>
    </div>
}
