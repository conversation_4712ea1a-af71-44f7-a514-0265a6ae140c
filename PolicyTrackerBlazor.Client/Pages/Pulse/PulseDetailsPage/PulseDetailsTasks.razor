@inherits PulseDetailsTasksBase
<div class="container text-center py-2">
    <div class="row align-items-start justify-content-between">
        <div class="col-6 align-self-center px-0">
            <div class="input-group input-group-sm">
                <span class="input-group-text shadow-sm border-end-0">
                    <i class="bi bi-search text-secondary"></i>
                </span>
                <input type="text" disabled="@(IsLoadingActivities || IsReloading)" id="txtSearchTasks"
                       class="form-control shadow-sm border-start-0" aria-label=""
                       @oninput="OnSearchTextChangedHandler"
                       placeholder="search tasks..." aria-describedby="">
            </div>
        </div>
        <div class="col text-end align-self-center px-0">
            <BSButton IsDisabled="@(IsReloading || IsLoadingActivities)" Color="BSColor.Primary"
                      OnClick="OnAddNewTaskClicked" Size="Size.Small"
                      Class="shadow-sm border-primary-subtle"
                      IsOutlined="true" Target="@TaskFormModalId" title="Add New Task">
                <i class="bi bi-journal-plus"></i> Add Task
            </BSButton>
            <div class="btn-group btn-group-sm" role="group" aria-label="">
                <button type="button" disabled="@(IsReloading || IsLoadingActivities)"
                        class="btn btn-outline-secondary border-secondary-subtle "
                        title="Refresh Tasks"
                        @onclick="OnRefreshTasksClick">
                    @if (IsReloading)
                    {
                        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    }
                    else
                    {
                        <i class="bi bi-arrow-clockwise"></i>
                    }
                </button>
                <button disabled="@(IsReloading || IsLoadingActivities)" type="button"
                        class="btn border-secondary-subtle @(ShowCompletedTasks ? "btn-outline-secondary" : "btn-secondary")"
                        title="@(ShowCompletedTasks ? "Hide Completed Tasks" : "Show Completed Tasks"))"
                        @onclick="OnShowCompletedTasksClick">
                    <i class="bi @(ShowCompletedTasks ? "bi-eye-fill" : "bi-eye-slash-fill") "></i>
                </button>
                <button disabled="@(IsReloading || IsLoadingActivities)" type="button"
                        class="btn border-secondary-subtle @(IsGridView ? "btn-outline-secondary" : "btn-secondary")"
                        title="@(IsGridView ? "Lis View" : "Grid View")"
                        @onclick="OnToggleGridView">
                    <i class="bi @(IsGridView ? "bi-grid" : "bi-list-task") "></i>
                </button>
            </div>
        </div>
    </div>
</div>

@* If the view is grid, render the grid view, otherwise render the list view *@
@if (!IsGridView)
{
    if (FilteredActivities.Any())
    {
        <div class="p-1 gap-4 py-md-2 align-items-center justify-content-center">
            <div class="list-group">
                @foreach (var activityModel in FilteredActivities)
                {
                    <div
                        class="list-group-item list-group-item-action  gap-3 py-3 @(activityModel.TaskStatusId == (int)TaskStatusEnum.Complete ? "bg-success-subtle" : "") "
                        aria-current="true">
                        <div class="d-flex gap-2 w-100 justify-content-between">
                            <div>
                                <div class="mb-1 d-flex gap-2 justify-content-start">
                                    <h6 class="align-self-center mb-1 me-1"> Who:</h6>
                                    @if (activityModel.ScheduledFor == Guid.Empty)
                                    {
                                        <span
                                            class="badge shadow-sm d-flex fw-semibold align-items-center p-1 pe-2 text-secondary-emphasis bg-secondary-subtle border border-secondary-subtle rounded-pill">
                                        <i class="bi bi-person-circle me-1 fs-6"></i>N/A</span>
                                    }
                                    else
                                    {
                                        <span
                                            class="badge shadow-sm fw-medium d-flex align-items-center p-1 pe-2 text-success-emphasis bg-success-subtle border border-success-subtle rounded-pill">
                                        @if (!string.IsNullOrEmpty(activityModel.ScheduledForEmployee.WebPhotoUrl))
                                            {
                                                <img class="rounded-circle me-1" width="22" height="22"
                                                     src="@activityModel.ScheduledForEmployee.WebPhotoUrl"
                                                     loading="lazy" alt="">
                                            }
                                            else
                                            {
                                                if (!string.IsNullOrEmpty(activityModel.ScheduledForEmployee.EmailFooterImgUrl))
                                                {
                                                    <img class="rounded-circle me-1" width="18" height="18"
                                                         src="@activityModel.ScheduledForEmployee.EmailFooterImgUrl"
                                                         loading="lazy" alt="">
                                                }
                                                else
                                                {
                                                    <i class="bi bi-person-circle me-1 fs-6"></i>
                                                }
                                            }
                                            @activityModel.ScheduledForEmployee.EmployeeName
                                    </span>
                                    }
                                </div>
                                <div class="mb-1 d-flex gap-2 justify-content-start">
                                    <h6 class="align-self-center mb-1 me-1"> When:</h6>
                                    <span class="text-muted ms-1">
                                    @activityModel.StartTimeZoned.ToString("MM/dd/yyyy")
                                </span>
                                </div>
                                <div class="mb-2 d-flex gap-2 justify-content-start">
                                    <h6 class="align-self-center mb-1 me-1"> Description:</h6>
                                    @if (activityModel.IsHighPriority)
                                    {
                                        <div class="fw-medium fs-8 text-danger-emphasis ms-1 align-self-center">
                                            @activityModel.Regarding
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="ms-1 fs-8 mb-0 align-self-center">@activityModel.Regarding</div>
                                    }
                                </div>
                                <div class="mb-2 d-flex gap-2 justify-content-start">
                                    <h6 class="align-self-center mb-1 me-1"> Status:</h6>
                                    <TaskStatusBadge TaskStatusId="activityModel.TaskStatusId"/>
                                    <div class="dropdown">
                                        <ul class="dropdown-menu bg-light-subtle shadow border border-success-subtle">
                                            <li>
                                            <span class="dropdown-item text-muted py-0 px-2 fs-10">
                                                Update Status
                                            </span>
                                            </li>
                                            <li>
                                                <hr class="dropdown-divider">
                                            </li>
                                            @foreach (var taskStatus in TaskStatusEnumExtensions.GetTaskStatusDictionary([activityModel.TaskStatusId]))
                                            {
                                                <li>
                                                    <button type="button"
                                                            @onclick="() => OnClickStatus(activityModel, taskStatus.Key)"
                                                            class="dropdown-item fw-medium fs-10">@taskStatus.Value</button>
                                                </li>
                                            }
                                        </ul>
                                    </div>
                                </div>
                                @if (activityModel.TaskStatusId != (int)TaskStatusEnum.Complete)
                                {
                                    <div class="mb-1 d-flex gap-2 justify-content-start">
                                        <h6 class="align-self-center mb-1 me-1"> Actions:</h6>
                                        <div class="dropdown">
                                            <button
                                                class="btn border @(activityModel.IsHighPriority ? "border-danger-subtle" : "border-light-subtle") shadow-sm btn-sm dropdown-toggle"
                                                type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                Actions
                                            </button>
                                            <ul class="dropdown-menu shadow border border-light-subtle">
                                                <li>
                                                    <BSButton Class="dropdown-item" Target="@TaskFormModalId"
                                                              title="Edit task"
                                                              OnClick="() => OnTaskSelected(activityModel)">
                                                        <i class="bi bi-feather me-1 text-primary"></i> Edit Task
                                                    </BSButton>
                                                </li>
                                                <li>
                                                    <button
                                                        disabled="@IsPersonalTaskQueueOrAssignedToCurrentEmployee(activityModel)"
                                                        @onclick="() => OnClickAssign(activityModel)"
                                                        title="Assign to me"
                                                        class="dropdown-item" type="button">
                                                        <i class="bi bi-person-fill-down me-1 text-primary-emphasis"></i>
                                                        Take Task
                                                        <br/>
                                                        @if (IsPersonalTaskQueue(activityModel))
                                                        {
                                                            <div style="font-size:10px;"
                                                                 class="text-warning-emphasis text-center fw-medium">
                                                                Personal Task
                                                            </div>
                                                        }
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
}
else
{
    <table class="table tasks-table table-sm">
        <thead class="table-light">
        <tr>
            <th scope="col">Description</th>
            <th scope="col" class="text-center">When</th>
            <th scope="col" class="">Who</th>
            <th scope="col" class="text-center">Status</th>
            <th scope="col" class="text-center"></th>
        </tr>
        </thead>
        <tbody>
        @if (IsLoadingActivities)
        {
            <tr>
                <td colspan="4" class="text-center">
                    <Loading LoadingClass="f-9" MarginTop="mt-2" SpinnerType="SpinnerType.Grow"
                             SpinnerColor="BSColor.Secondary"></Loading>
                </td>
            </tr>
        }
        else
        {
            if (FilteredActivities.Any())
            {
                foreach (var activityModel in FilteredActivities)
                {
                    <tr>
                        <th scope="row" title="@activityModel.Regarding"
                            class="col-3 @(activityModel.IsHighPriority ? "bg-danger-subtle fw-medium" : "")">
                            @if (activityModel.IsHighPriority)
                            {
                                <div class="fs-9 fw-medium text-danger-emphasis">@activityModel.Regarding</div>
                            }
                            else
                            {
                                <div class="fs-9 fw-medium ellipsis">@activityModel.Regarding</div>
                            }
                        </th>
                        <td class="text-center fs-9 @(activityModel.IsHighPriority ? "bg-danger-subtle fw-medium" : "")">
                            @activityModel.StartTimeZoned.ToString("MM/dd/yyyy")
                        </td>
                        <td class="@(activityModel.IsHighPriority ? "bg-danger-subtle fw-semibold" : "")">
                            @if (activityModel.ScheduledFor == Guid.Empty)
                            {
                                <span
                                    class="badge shadow-sm fw-medium align-items-center px-2 text-secondary-emphasis bg-secondary-subtle border border-secondary-subtle rounded-pill">
                            N/A
                        </span>
                            }
                            else
                            {
                                <span
                                    class="badge shadow-sm fw-medium align-items-center px-2 text-success-emphasis bg-success-subtle border border-success-subtle rounded-pill">
                                    @activityModel.ScheduledForEmployee.DisplayName
                        </span>
                            }
                        </td>
                        <td class="text-center @(activityModel.IsHighPriority ? "bg-danger-subtle fw-semibold" : "")">
                            <div class="dropdown">
                                <TaskStatusBadge Ellipsis="true"
                                                 Title="@(((TaskStatusEnum)activityModel.TaskStatusId).GetDescription())"
                                                 TaskStatusId="activityModel.TaskStatusId"/>
                                <ul class="dropdown-menu bg-light-subtle shadow border border-success-subtle">
                                    <li>
                                <span class="dropdown-item text-muted py-0 px-2 fs-10">
                                    @(((TaskStatusEnum)activityModel.TaskStatusId).GetDescription())
                                </span>
                                    </li>
                                    <li>
                                        <hr class="dropdown-divider">
                                    </li>
                                    @foreach (var taskStatus in TaskStatusEnumExtensions.GetTaskStatusDictionary([activityModel.TaskStatusId]))
                                    {
                                        <li>
                                            <button type="button"
                                                    @onclick="() => OnClickStatus(activityModel, taskStatus.Key)"
                                                    class="dropdown-item fw-medium fs-10">@taskStatus.Value</button>
                                        </li>
                                    }
                                </ul>
                            </div>
                        </td>
                        <td class="text-center @(activityModel.IsHighPriority ? "bg-danger-subtle fw-semibold" : "")">
                            @if (activityModel.TaskStatusId != (int)TaskStatusEnum.Complete)
                            {
                                <div class="dropdown">
                                    <button
                                        class="btn border @(activityModel.IsHighPriority ? "border-danger-subtle" : "border-light-subtle")  shadow-sm btn-sm dropdown-toggle"
                                        type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu shadow border border-light-subtle">
                                        <li>
                                            <BSButton Class="dropdown-item" Target="@TaskFormModalId"
                                                      title="Edit task"
                                                      OnClick="() => OnTaskSelected(activityModel)">
                                                <i class="bi bi-feather me-1 text-primary"></i> Edit Task
                                            </BSButton>
                                        </li>
                                        <li>
                                            <button
                                                disabled="@IsPersonalTaskQueueOrAssignedToCurrentEmployee(activityModel)"
                                                @onclick="() => OnClickAssign(activityModel)" title="Assign to me"
                                                class="dropdown-item" type="button">
                                                <i class="bi bi-person-fill-down me-1 text-primary-emphasis"></i> Take
                                                Task
                                                <br/>
                                                @if (IsPersonalTaskQueue(activityModel))
                                                {
                                                    <div style="font-size:10px;"
                                                         class="text-warning-emphasis text-center fw-medium">Personal
                                                        Task
                                                    </div>
                                                }
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            }
                        </td>
                    </tr>
                }
            }
            else
            {
                <tr>
                    <td colspan="4" class="text-center">No tasks available.</td>
                </tr>
            }
        }
        </tbody>
    </table>
}
@if (!IsLoadingActivities)
{
    <CascadingValue Value="Employees.ToList()" Name="EmployeesReference">
        <TaskFormModal ModalDataId="@TaskFormModalId" Activity="SelectedActivity"
                       OnActivityUpdated="HandleOnActivityUpdated"></TaskFormModal>
    </CascadingValue>
}

