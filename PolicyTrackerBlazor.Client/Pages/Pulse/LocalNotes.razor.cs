using Microsoft.AspNetCore.Components;
using PolicyTrackerBlazor.Client.Managers;
using PolicyTrackerBlazor.Client.Shared;
using UcpmApi.Shared.DropdownItem;
using UcpmApi.Shared.Pulse;
using UcpmComponentLibraryEx.Managers;

namespace PolicyTrackerBlazor.Client.Pages.Pulse;

public class LocalNotesBase : PolicyTrackerBlazorComponentBase
{
    [CascadingParameter(Name = "PulsePolicyDetailsViewModel")]
    public PulsePolicyDetailsViewModel PulsePolicyDetailsViewModel { get; set; } = new();

    [Parameter] public List<Note> Notes { get; set; } = [];
    [Parameter] public List<Note> PreviousNotes { get; set; } = [];
    [Parameter] public List<EmployeeDropdownItem> EmployeeDropdownItems { get; set; } = [];
    [Parameter] public Guid LinkingGuid { get; set; }
    [Inject] public UcpmComponentLibraryEx.Managers.EmployeeManager EmployeeManager { get; set; } = null!;
    [Inject] public NoteManager NoteManager { get; set; } = null!;
    protected SingleNotePartial NoteModal { get; set; } = null!;
    protected string ActiveTab = "current";
    protected bool AddNoteEnabled { get; set; } = true;
    protected bool IsLoading { get; set; }

    protected async Task ShowNote(Note note, bool isPrev = false)
    {
        await NoteModal.LoadModal(note, isPrev);
    }

    protected async Task CreateNote()
    {
        await NoteModal.LoadModal(new Note()
        {
            EnteredByList = EmployeeDropdownItems,
            StartTimeZoned = DateTime.Now,
            RegardingAndNotes = string.Empty,
            ActionByGuid = UserAuth.CurrentUser.AccountGuid,
            LinkingGuid = PulsePolicyDetailsViewModel.PolicyProspect.PolicyProspectGuid,
            FirstName = UserAuth.CurrentUser.FirstName
        });
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (PulsePolicyDetailsViewModel.PolicyProspect.PolicyProspectGuid != Guid.Empty)
        {
            LinkingGuid = PulsePolicyDetailsViewModel.PolicyProspect.PolicyProspectGuid;
        }

        IsLoading = true;
        await LoadNotes();
        IsLoading = false;
    }

    protected void SetActiveTab(string tab)
    {
        ActiveTab = tab;
        AddNoteEnabled = tab != "previous";
    }

    private async Task LoadNotes()
    {
        if (PulsePolicyDetailsViewModel.PolicyProspect.PolicyProspectGuid == Guid.Empty)
        {
            return;
        }

        PreviousNotes = await NoteManager.GetNotes(PulsePolicyDetailsViewModel.PolicyProspect.RenewalOfPolicyGuid);
        Notes = await NoteManager.GetNotes(PulsePolicyDetailsViewModel.PolicyProspect.PolicyProspectGuid);
        EmployeeDropdownItems = await EmployeeManager.GetTaskUsers();
    }

    protected async Task OnNoteCreated(Note? note)
    {
        if (note != null)
        {
            Notes = await NoteManager.GetNotes(PulsePolicyDetailsViewModel.PolicyProspect.PolicyProspectGuid);
        }
    }

    protected async Task OnNoteDeleted(Note? note)
    {
        Notes = await NoteManager.GetNotes(PulsePolicyDetailsViewModel.PolicyProspect.PolicyProspectGuid);
    }
}