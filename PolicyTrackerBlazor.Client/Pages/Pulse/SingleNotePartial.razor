@inherits SingleNoteViewOnlyPartialBase

<BSModal Size="Size.Medium" @ref="NoteModal">
    <BSModalHeader>
        @if (Note != null)
        {
            @if (Note.ActivityHistoryGuid == Guid.Empty)
            {
                <h5 class="modal-title">Add Note</h5>
            }
            else
            {
                <h5 class="modal-title">Edit Note</h5>
            }
        }
    </BSModalHeader>
    <BSModalContent>

        <fieldset>
            @if (Note != null)
            {
                @if (Note.EnteredByList.Any())
                {
                    <div class="mb-3">
                        <label class="form-label">Entered By</label>
                        <select @bind="Note.ActionByGuid" class="form-select" disabled="@IsPrevious">
                            <option value="@UserAuth.CurrentUser.AccountGuid">@UserAuth.CurrentUser.FirstName  @UserAuth.CurrentUser.LastName</option>
                            @foreach (var item in Note.EnteredByList)
                            {
                                <option value="@item.EmployeeGuid">@item.EmployeeName</option>
                            }
                        </select>
                    </div>
                }
                @if (Note.IsAutogenerated)
                {
                    <div class="form-check mb-3">
                        <input type="checkbox" checked="@Note.IsAutogenerated" disabled>
                        <label class="form-check-label">
                            Note was autogenerated
                        </label>
                    </div>
                }
                <div class="mb-3">
                    <label class="form-label">Date Entered</label>
                    <InputDate class="form-control" @bind-Value="Note.StartTimeZoned" disabled="@IsPrevious" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Regarding</label>
                    <textarea @bind="Note.RegardingAndNotes" class="form-control" rows="5" disabled="@IsPrevious"></textarea>
                </div>
            }
        </fieldset>
    </BSModalContent>
    @if (!IsPrevious)
    {
        <BSModalFooter>
            <BSButton Color="BSColor.Danger" Size="Size.Small" IsDisabled="IsProcessing" Class="shadow-sm" @onclick="DeleteNote">
                <span role="status">@DeleteButtonLabel</span>
            </BSButton>
            <BSButton Color="BSColor.Primary" Size="Size.Small" IsDisabled="IsProcessing" Class="shadow-sm" @onclick="SaveNote">
                <span role="status">@SaveButtonLabel</span>
            </BSButton>
        </BSModalFooter>
    }
</BSModal>