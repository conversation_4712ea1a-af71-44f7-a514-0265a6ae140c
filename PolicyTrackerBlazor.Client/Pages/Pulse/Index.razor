@page "/Pulse"
@page "/Pulse/Index"
@using Microsoft.AspNetCore.Authorization
@using PolicyTrackerBlazor.Client.Pages.Pulse
@using PolicyTrackerBlazor.Client.Components.Loading
@inherits IndexBase
@attribute [Authorize(Roles = "Employee")]

@if (!DataIsLoading)
{
    <div class="row">
        <div class="col-12">
            <div class="row">
                <div class="col-6">
                    
                </div>
                <div class="col-6 text-end">
                    
                </div>
            </div>
            <div class="row">
                <div class="col-3">
                    <h3>Require Review</h3>
                </div>
                <div class="col-9 text-end">
                    <button class="btn btn-primary" type="button" @onclick="@GoToInsuredList">Insured List</button>
                    <a href="Pulse/KanbanView" class="dashboard-kanban-button">
                        <i class="bi bi-kanban me-2"></i> View as Kanban
                    </a>              
                </div>
            </div>
            <br/>
            <table class="table table-bordered">
                <thead class="table-dark">
                    <tr>
                        <th></th>
                        @foreach (var item in DateBands)
                        {
                            <th>@item.BandName</th>
                        }
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in RequireReviewList.GroupBy(g => g.Status))
                    {
                        <tr>
                            <th colspan="7" class="table-secondary">@item.Key</th>
                        </tr>
                        @foreach (var subItem in item.GroupBy(i => i.SubStatus).OrderBy(d => d.Key))
                        {
                            var firstSub = subItem.First();
                            <tr>
                                <td>@subItem.Key</td>
                                @foreach (var band in DateBands)
                                {
                                    <td>
                                        <a @onclick="() => LoadBandDetails(band.BandName, firstSub.PulseSubStatusId, firstSub.PolicyStatusId)">
                                            @(subItem.Count(s => s.DateBandName == band.BandName))
                                        </a>
                                    </td>
                                }
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>

    <div class="row mt-3 mb-2 align-items-end">       
        <div class="col-12 col-md">
            <label class="form-label">Programs</label>
            <select class="form-select" @onchange="@ChangeProgram">
                <option value="" selected>Select...</option>
                @foreach (var item in ProgramSelectList)
                {
                    <option value="@item">@item</option>
                }
            </select>
        </div>
        <div class="col-12 col-md">
            <label class="form-label">Pulse Users</label>
            <select class="form-select" @onchange="@ChangeBroker">
                <option value="" selected>Select...</option>
                @foreach (var item in PulseUsersSelectList)
                {
                    <option value="@item">@item</option>
                }
            </select>
        </div>
        <div class="col-12 col-md">
            <label class="form-label">Search</label>
            <input class="form-control" @oninput="@HandleChange" placeholder="Search insured name">
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <table class="table table-bordered">
                <thead class="table-dark">
                    <tr>
                        <th></th>
                        @foreach (var item in DateBands)
                        {
                            <th>@item.BandName</th>
                        }
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in FilteredDashboard.GroupBy(g => g.Status))
                    {
                        <tr>
                            <th colspan="7" class="table-secondary">@item.Key</th>
                        </tr>
                        @foreach (var subItem in item.GroupBy(i => i.SubStatus).OrderBy(d=> d.Key))
                        {
                            var firstSub = subItem.First();
                            <tr>
                                <td>@subItem.Key</td>
                                @foreach (var band in DateBands)
                                {
                                    <td>
                                        <a @onclick="() => LoadBandDetails(band.BandName, firstSub.PulseSubStatusId, firstSub.PolicyStatusId)">
                                            @(subItem.Count(s => s.DateBandName == band.BandName))
                                        </a>
                                    </td>
                                }
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
}
else
{
    <div class="row justify-content-center">
        <div class="col">
            <ContentLoader/>
            <ContentLoader/>
            <ContentLoader/>
        </div>
    </div>
}
<BandDetailsModal @ref="BandDetails" />