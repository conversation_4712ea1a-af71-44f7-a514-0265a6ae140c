using Microsoft.AspNetCore.Components;
using PolicyTrackerBlazor.Client.Shared;
using PolicyTrackerBlazor.Shared.Extensions;
using PolicyTrackerBlazor.Shared.Models;
using UcpmApi.Shared.Vetting;
using UcpmComponentLibraryEx.Managers;
using UcpmComponentLibraryEx.Services;

namespace PolicyTrackerBlazor.Client.Pages.JunkDrawer.CoverageVerifier.IssueRequirements;

public class IssueRequirementsBase : PolicyTrackerBlazorComponentBase
{
    [Inject]
    public required IssueRequirementManager IssueRequirementManager { get; set; }

    [Inject]
    public required ToastService ToastService { get; set; }

    public string SearchText { get; set; } = string.Empty;

    public bool IsPageLoading { get; set; }

    public List<IssueRequirementModel> SearchResults { get; set; } = [];

    public List<IssueRequirementModel> IssueRequirementModels { get; set; } = [];

    public IssueRequirementModal IssueRequirementModal { get; set; } = new IssueRequirementModal();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            IsPageLoading = true;
            await LoadIssueRequirements();
        }
        catch (Exception)
        {
            ToastService.ShowToast("An error occurred while loading the issue requirements.", ToastLevelEnum.Error);
        }
        finally
        {
            IsPageLoading = false;
        }

        await base.OnInitializedAsync();
    }

    /// <summary>
    /// Handles the search event and updates the search results based on the entered search text.
    /// </summary>
    /// <param name="e">The event arguments containing the search text.</param>
    public async Task OnSearch(ChangeEventArgs e)
    {
        SearchText = e.Value?.ToString() ?? string.Empty;
        if (string.IsNullOrEmpty(SearchText))
        {
            SearchResults = IssueRequirementModels;
        }
        else
        {
            await Task.FromResult(SearchResults = IssueRequirementModels.Where(ir =>
                 ir.CoverageIssueName.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                 ir.RequirementDescription.IndexOf(SearchText, StringComparison.OrdinalIgnoreCase) >= 0
             ).ToList());
        }

        StateHasChanged();
    }

    /// <summary>
    /// Loads the Issue Requirements from the API.
    /// </summary>
    private async Task LoadIssueRequirements()
    {
        IEnumerable<IssueRequirement> issueRequirements = await IssueRequirementManager.GetIssueRequirements();
        IssueRequirementModels = [.. issueRequirements.ToModels().OrderBy(j => j.CoverageIssueName)];
        SearchResults = IssueRequirementModels;
    }

    public async Task HandleEditButtonClick(IssueRequirementModel issueRequirement)
    {
        await IssueRequirementModal.ShowModal(issueRequirement);
    }

    public async Task HandleAddButtonClick()
    {
        await IssueRequirementModal.ShowModal(new IssueRequirementModel());
    }

    public async Task HandleActionClick()
    {
        try
        {
            IsPageLoading = true;
            await LoadIssueRequirements();
        }
        catch (Exception)
        {
            ToastService.ShowToast("An error occurred while loading the issue requirements.", ToastLevelEnum.Error);
        }
        finally
        {
            IsPageLoading = false;
        }
    }
}
