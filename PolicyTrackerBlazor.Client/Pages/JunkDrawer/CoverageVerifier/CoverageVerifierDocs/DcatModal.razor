@inherits DcatModalBase
@using UcpmApi.Shared.Dcat;

<BSModal DataId="CoverageVerifierDcatModal" Size="Size.Large" IsStaticBackdrop="true" @ref="DcatModal">
    <BSModalHeader>
        <h5 class="modal-title">Coverage Issues</h5>
    </BSModalHeader>
    <BSModalContent>
        <BSTable Class="table table-striped table-hover table-bordered" IsBordered="false">
            <BSTHead Class="table-dark">
                <BSTR>
                    <BSTD Class="small">CoverageIssueName</BSTD>
                    <BSTD Class="small">Group Name</BSTD>
                    <BSTD Class="small">Coverage Issue Description</BSTD>
                    <BSTD Class="small">Status</BSTD>
                </BSTR>
            </BSTHead>
            <BSTBody>
                @if (DataIsLoading)
                {
                    <span class="spinner-border spinner-border-sm ms-2" role="status" aria-hidden="true"></span>
                }
                else
                {
                    @foreach (CoverageIssueModel issue in CoverageIssueModel)
                    {
                        string statusBackground = "small " + issue?.CoverageIssues?.FirstOrDefault()?.BackgroundColor ?? "";
                        <BSTR>
                            <BSTD Class="small">@(issue?.CoverageIssueName)</BSTD>
                            <BSTD Class="small">@(issue?.GroupName)</BSTD>
                            <BSTD Class="small">@(issue?.CoverageIssueDescription)</BSTD>
                            <BSTD Class="@statusBackground">@(issue?.CoverageIssues?.FirstOrDefault()?.ShortText)</BSTD>
                        </BSTR>
                    }
                }
            </BSTBody>
        </BSTable>
    </BSModalContent>
    <BSModalFooter>
        <BSButton Class="btn btn-secondary" IsSubmit="false" OnClick="CloseModal">Close</BSButton>
    </BSModalFooter>
</BSModal>
