@using PolicyTrackerBlazor.Client.Components.Loading
@using PolicyTrackerBlazor.Client.Components.SearchInput
@inherits PathfinderBase
@page "/admin/junkdrawer/Pathfinder"

<PageTitle>Pathfinder</PageTitle>
<div class="p-1">
    <Breadcrumb StartIndex="1" />
</div>
<div class="container-fluid">
    <div class="text-center">
        <h4>Pathfinder - Account Logins to Pathfinder</h4>
    </div>
    <BSNav IsTabs="true">
        <BSNavItem>
            <TabLabel>Logins</TabLabel>
            <TabContent>
                <div class="row">
                    <div class="col-12 my-1">
                        <PTSearchInput Placeholder="Search logins..." ValueChanged="OnSearch" Disabled="@DataIsLoading" />
                    </div>
                    <!-- Date Filters -->
                    <div class="w-100 d-flex flex-column flex-lg-row justify-content-between gap-3 my-2">
                        <div class="col-md-3">
                            <label for="lStartDate">Start Date</label>
                            <input type="date" id="lStartDate" class=form-control @bind="StartDate" @oninput="OnStartDateChange" />
                        </div>
                        <div class="col-md-3">
                            <label for="lEndDate">End Date</label>
                            <input type="date" id="lEndDate" class=form-control @bind="EndDate" @oninput="OnEndDateChange" />
                        </div>
                    </div>
                    <div>
                        @if (IsPageLoading)
                        {
                            <div class="row justify-content-center">
                                <div class="col">
                                    <ContentLoader />
                                </div>
                            </div>
                        }
                        else
                        {
                            @if (!AccountLoginList.Any())
                            {
                                <div class="alert alert-info" role="alert">
                                    No account logins found.
                                </div>
                            }
                            else
                            {
                                if (SearchResults.Any())
                                {
                                    <PathfinderAccountLoginAuditTable AccountLoginList="SearchResults" />
                                    <PaginationComponent ItemsCount="@AccountLoginList.Count()"
                                                         PageItemCount="@PageLimit"
                                                         ActivePageNumber="@ActivePageNumber"
                                                         OnChange="@HandlePageChange" />
                                }
                                else
                                {
                                    <div class="alert alert-warning" role="alert">
                                        No search results found.
                                    </div>
                                }
                            }
                        }
                    </div>
                </div>
            </TabContent>
        </BSNavItem>
        <BSNavItem>
            <TabLabel>Recordings</TabLabel>
            <TabContent>
                <div class="row">
                    <div class="col-12 my-1">
                        <PTSearchInput Placeholder="Search recordings..." ValueChanged="OnSearchPathRecording" Disabled="@DataIsLoading" />
                    </div>
                    <!-- Date Filters -->
                    <div class="w-100 d-flex flex-column flex-lg-row justify-content-between gap-3 my-2">
                        <div class="col-md-3">
                            <label for="rStartDate">Start Date</label>
                            <input type="date" id="rStartDate" class=form-control @bind="StartDateRecording" @oninput="OnStartDateChangeRecordings" />
                        </div>
                        <div class="col-md-3">
                            <label for="rEndDate">End Date</label>
                            <input type="date" id="rEndDate" class=form-control @bind="EndDateRecording" @oninput="OnEndDateChangeRecordings" />
                        </div>
                    </div>
                    <div>
                        @if (IsPageLoading)
                        {
                            <div class="row justify-content-center">
                                <div class="col">
                                    <ContentLoader />
                                </div>
                            </div>
                        }
                        else
                        {
                            @if (!PathRecordingList.Any())
                            {
                                <div class="alert alert-info" role="alert">
                                    No recordings found.
                                </div>
                            }
                            else
                            {
                                if (SearchResultsRecording.Any())
                                {
                                    <PathfinderPathRecodingTable PathRecordingList="SearchResultsRecording" />
                                    <PaginationComponent ItemsCount="@PathRecordingList.Count()"
                                                         PageItemCount="@PageLimit"
                                                         ActivePageNumber="@ActivePageNumber"
                                                         OnChange="@HandlePageChangeRecording" />
                                }
                                else
                                {
                                    <div class="alert alert-warning" role="alert">
                                        No search results found.
                                    </div>
                                }
                            }
                        }
                    </div>
                </div>
            </TabContent>
        </BSNavItem>
        <BSNavItem>
            <TabLabel>Applications</TabLabel>
            <TabContent>
                <div class="row">
                    <div class="col-12 my-1">
                        <PTSearchInput Placeholder="Search applications..." ValueChanged="OnSearchApplications" Disabled="@DataIsLoading" />
                    </div>
                    <!-- Date Filters -->
                    <div class="w-100 d-flex flex-column flex-lg-row justify-content-between gap-3 my-2">
                        <div class="col-md-3">
                            <label for="aStartDate">Start Date</label>
                            <input type="date" id="aStartDate" class=form-control @bind="StartDateApplication" @oninput="OnStartDateChangeApplications" />
                        </div>
                        <div class="col-md-3">
                            <label for="aEndDate">End Date</label>
                            <input type="date" id="aEndDate" class=form-control @bind="EndDateApplication" @oninput="OnEndDateChangeApplications" />
                        </div>
                    </div>
                    <div>
                        @if (IsPageLoading)
                        {
                            <div class="row justify-content-center">
                                <div class="col">
                                    <ContentLoader />
                                </div>
                            </div>
                        }
                        else
                        {
                            @if (!ApplicationList.Any())
                            {
                                <div class="alert alert-info" role="alert">
                                    No applications found.
                                </div>
                            }
                            else
                            {
                                if (SearchResultsApplication.Any())
                                {
                                    <PathfinderApplicationsTable ApplicationList="SearchResultsApplication" />
                                    <PaginationComponent ItemsCount="@ApplicationList.Count()"
                                                            PageItemCount="@PageLimit"
                                                            ActivePageNumber="@ActivePageNumber"
                                                            OnChange="@HandlePageChangeApplications" />
                                }
                                else
                                {
                                    <div class="alert alert-warning" role="alert">
                                        No search results found.
                                    </div>
                                }
                            }
                        }
                    </div>
                </div>
            </TabContent>
        </BSNavItem>
    </BSNav>
</div>