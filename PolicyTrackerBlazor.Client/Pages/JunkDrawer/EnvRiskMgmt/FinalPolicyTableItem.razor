@inherits FinalPolicyTableItemBase

@{
    string cellText = string.IsNullOrWhiteSpace(IkeaCoveragePart.Description) ? IkeaCoveragePart.Title : IkeaCoveragePart.Description;
}

<tr>
    @if (IkeaCoveragePart.DefinedTerms.Any(d => d.Options.Any(o => o.Checked && o.IkeaDisplayTypeId != 0)))
    {
        <td colspan="3">
            <a data-bs-toggle="collapse"
               data-bs-target=".<EMAIL>"
               aria-expanded="false" class="collapsed nested-table-row-toggle">
                @cellText
            </a>
        </td>
    }
    else
    {
        <td colspan="3">
            <p class="nested-table-row-no-toggle">@cellText</p>
        </td>
    }

    <td class="d-flex justify-content-between">
        @if (IkeaCoveragePart.Icons.Contains("&"))
        {
            <i class="bi bi-circle-fill fs-4 text-darkcyan drop-shadow me-1"></i>
        }
        @if (IkeaCoveragePart.Icons.Contains("C"))
        {
            <i class="bi bi-circle-fill fs-4 text-darkorange drop-shadow me-1"></i>
        }
        @if (IkeaCoveragePart.Icons.Contains("$"))
        {
            <button type="button" class="btn btn-sm btn-secondary">
                <i class="bi bi-copy h5" data-bs-toggle="tooltip"
                   data-bs-title="Matching price from your current policy">
                </i>
            </button>
        }
        @if (IkeaCoveragePart.Icons.Contains("%"))
        {
            <button type="button" class="btn btn-sm btn-secondary">
                <i class="bi bi-copy h5" data-bs-toggle="tooltip"
                   data-bs-title="Coverage that matches what you are required to carry for @IkeaCoveragePart.Title">
                </i>
            </button>
        }
        @if (IkeaCoveragePart.Icons.Contains("#"))
        {
            <i class="bi bi-circle-fill fs-4 text-lightcyan drop-shadow me-1"></i>
        }
        @if (IkeaCoveragePart.Icons.Contains("="))
        {
            <i class="bi bi-circle-fill fs-4 text-lightblue drop-shadow me-1"></i>
        }
    </td>
    <td>@IkeaCoveragePart.Premium</td>
</tr>
@foreach (var term in IkeaCoveragePart.DefinedTerms.Where(t => t.Options.Any(o => o.Checked) || t.Checked).OrderBy(t => t.Title))
{
    term.IkeaCoveragePartGuid = IkeaCoveragePart.IkeaCoveragePartGuid;
    <FinalPolicyIkeaDefinedTerm IkeaDefinedTerm="term"></FinalPolicyIkeaDefinedTerm>
}
