using Microsoft.AspNetCore.Components;
using PolicyTrackerBlazor.Client.Shared;
using UcpmApi.Shared;

namespace PolicyTrackerBlazor.Client.Pages.JunkDrawer.EnvRiskMgmt;

public class EnvRiskMgmtInviteLinkItemBase : PolicyTrackerBlazorComponentBase
{
    [Parameter]
    public CPLApiInviteLinkModel? EnvRiskMgmtInviteLink { get; set; }

    [Parameter]
    public EventCallback<CPLApiInviteLinkModel> OnGoToPageClicked { get; set; }

    [Parameter]
    public EventCallback<Guid> OnViewButtonClick { get; set; }

    [Parameter]
    public EventCallback<Guid> OnViewLogButtonClick { get; set; }

    [Parameter]
    public EventCallback<Guid> OnViewQualifyingProjectOwners { get; set; }

    [Parameter]
    public EventCallback<Guid> OnViewPolicyMap { get; set; }

    public async void HandleViewPricing()
    {
        if (OnViewButtonClick.HasDelegate)
        {
            await OnViewButtonClick.InvokeAsync(EnvRiskMgmtInviteLink?.FlexResponseGuid ?? Guid.NewGuid());
        }
    }
    public async void HandleViewQualifyingProjectOwners()
    {
        if (OnViewQualifyingProjectOwners.HasDelegate)
        {
            await OnViewQualifyingProjectOwners.InvokeAsync(EnvRiskMgmtInviteLink?.InviteLinkGuid ?? Guid.NewGuid());
        }
    }
    public async void HandleViewLog()
    {
        if (OnViewLogButtonClick.HasDelegate)
        {
            await OnViewLogButtonClick.InvokeAsync(EnvRiskMgmtInviteLink?.FlexResponseGuid ?? Guid.NewGuid());
        }
    }

    public async Task HandleGoToPage()
    {
        if (OnGoToPageClicked.HasDelegate)
        {
            await OnGoToPageClicked.InvokeAsync(EnvRiskMgmtInviteLink);
        }
    }

    public async Task HandleViewPolicyMap()
    {
        if (OnViewPolicyMap.HasDelegate)
        {
            await OnViewPolicyMap.InvokeAsync(EnvRiskMgmtInviteLink?.InviteLinkGuid ?? Guid.NewGuid());
        }
    }
}
