@inherits EnvRiskMgmtBase
@page "/Admin/JunkDrawer/EnvRiskMgmt"

<PageTitle>Environment Risk Management</PageTitle>
<div class="p-1">
    <Breadcrumb StartIndex="1" />
</div>
<div class="container-fluid">
    <div class="text-center">
        <h4>Environment Risk Management - Invite Links</h4>
    </div>
    <div class="row">
        <div class="w-100 d-flex flex-column flex-lg-row justify-content-between gap-3">
            <form class="mb-3">
                <input @onchange="OnSearch" value="@SearchText" class="form-control" type="search" placeholder="🔍 Search an invite link" aria-label="Search">
            </form>
        </div>
        <div>
            @if (IsPageLoading)
            {
                <Loading />
            }
            else
            {
                <EnvRiskMgmtInviteLinkTable 
                    EnvRiskMgmtInviteLinks="@SearchResults" 
                    OnGoToPageClicked="@GoToEnvRiskMgmt" 
                    OnLogButtonClick="@OnLogButtonClick" 
                    OnViewButtonClick="@OnViewButtonClick" 
                    OnViewQualifyingProjectOwners="@OnViewQualifyingProjectOwners"
                    OnViewPolicyMap="@OnViewPolicyMap" />
                <PaginationComponent ItemsCount="@EnvRiskMgmtInviteLinks.Count()"
                                     PageItemCount="@PageLimit"
                                     ActivePageNumber="@ActivePageNumber"
                                     OnChange="@HandlePageChange" />
            }
        </div>
    </div>
</div>

<EnvRiskMgmtInviteLinkPremiumModal @ref="EnvRiskMgmtInviteLinkPremiumModal" />
<EnvRiskMgmtInviteLinkLogModal @ref="EnvRiskMgmtInviteLinkLogModal" />
<EnvRiskMgmtQualifyingProjectOwnerDebugModal @ref="EnvRiskMgmtQualifyingProjectOwnerDebugModalBase" />
<EnvRiskMgmtPolicyMapModal @ref="EnvRiskMgmtPolicyMapModal" />
