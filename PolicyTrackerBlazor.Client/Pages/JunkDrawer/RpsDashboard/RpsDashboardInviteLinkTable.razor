@using UcpmApi.Shared.Security
@inherits RpsDashboardInviteLinkTableBase;

<BSTable Class="table table-striped table-hover table-bordered" IsBordered="false">
    <BSTHead Class="table-dark">
        <BSTR>
            <BSTD Class="col-2">Insured Name</BSTD>
            <BSTD Class="col-2">Agent Name</BSTD>
            <BSTD Class="col-2">Class of Business</BSTD>
            <BSTD Class="col-1">Actions</BSTD>
        </BSTR>
   </BSTHead>
    <BSTBody>
        @foreach (RpsInviteLink inviteLink in InviteLinks)
        {
            <RpsDashboardInviteLinkItem InviteLink="inviteLink" OnGoToPageClicked="@OnGoToPageClicked" />
        }
    </BSTBody>
</BSTable>
