using Microsoft.AspNetCore.Components;
using PolicyTrackerBlazor.Client.Shared;
using UcpmApi.Shared;
using UcpmApi.Shared.Coverage;
using UcpmApi.Shared.Policy;
using UcpmComponentLibraryEx.Managers;

namespace PolicyTrackerBlazor.Client.Pages.FlexApp
{
    public class AddRemoveFormsBase : PolicyTrackerBlazorComponentBase
    {
        [Inject]
        public FormManager FormManager { get; set; }
        [Inject]
        public SubmissionFormManager SubmissionFormManager { get; set; }
        [Inject]
        public CarrierSubmissionFlexResponseManager CarrierSubmissionFlexResponseManager { get; set; }
        [Inject]
        public CarrierSubmissionManager CarrierSubmissionManager { get; set; }

        [Parameter]
        public Guid FlexResponseGuid { get; set; }

        public Guid CarrierSubmissionGuid { get; set; }
        public string SearchText { get; set; } = string.Empty;
        public IEnumerable<Form> FilteredForms { get; set; }
        public IEnumerable<Form> NotAddedForms { get; set; }
        public IEnumerable<SubmissionForm> AddedForms { get; set; }

        protected override async Task OnInitializedAsync()
        {
            await LoadSubmissionForms();
            await base.OnInitializedAsync();
        }

        private async Task LoadSubmissionForms()
        {
            DataIsLoading = true;

            IEnumerable<CarrierSubmissionFlexResponse> carrierSubmissionFlexResponse = await CarrierSubmissionFlexResponseManager.GetByFlexResponse(FlexResponseGuid);
            CarrierSubmissionGuid = carrierSubmissionFlexResponse.FirstOrDefault().CarrierSubmissionGuid;

            CarrierSubmission submission = await CarrierSubmissionManager.GetFormsForDcat(CarrierSubmissionGuid);
            DateTime policyEffectiveDate = submission.PolicyProspect.EffectiveDateZoned.Date;
            ProgramModel program = submission.PolicyProspect.Package.Program;
            IEnumerable<Form> forms = await FormManager.GetFormsByCarrierAndProgramSortedByUse(submission.CarrierGuid,
                program.ProgramGuid, policyEffectiveDate);

            AddedForms = await SubmissionFormManager.GetSubmissionFormsByCarrierSubmission(submission.CarrierSubmissionGuid);
            AddedForms = AddedForms.OrderBy(o => o.SortOrder).ToList();

            List<Guid> addedGuids = AddedForms.Select(m => m.FormGuid).ToList();

            NotAddedForms = forms.Where(m => !addedGuids.Contains(m.FormGuid)).OrderBy(o => o.FormNumber).ToList();
            FilteredForms = NotAddedForms;

            DataIsLoading = false;

            StateHasChanged();
        }

        private async Task AddExistingForms(Guid formGuid, Guid carrierSubmissionGuid)
        {
            IEnumerable<SubmissionForm> existingForm = await SubmissionFormManager.GetSubmissionFormsByCarrierSubmission(carrierSubmissionGuid);
            SubmissionForm formToAdd = existingForm.FirstOrDefault(f => f.FormGuid == formGuid);

            if (formToAdd == null)
            {
                int nextSortOrder = existingForm.Count() == 0 ? 1 : existingForm.Last().SortOrder + 1;
                formToAdd = new SubmissionForm
                {
                    CarrierSubmissionGuid = carrierSubmissionGuid,
                    FormGuid = formGuid,
                    SortOrder = nextSortOrder
                };

                await SubmissionFormManager.SaveAddedSubmissionForms(new List<SubmissionForm> { formToAdd });
            }

            await LoadSubmissionForms();
        }

        private async Task RemoveSubmissionForm(Guid formGuid, Guid carrierSubmissionGuid)
        {
            List<Guid> formsToRemove = new();

            if (!formsToRemove.Contains(formGuid))
            {
                formsToRemove.Add(formGuid);
            }

            AddedForms = AddedForms.Where(f => f.FormGuid != formGuid).ToList();

            Form form = await FormManager.GetFormByGuid(formGuid);
            NotAddedForms = NotAddedForms.Append(form).OrderBy(o => o.FormNumber).ToList();

            IEnumerable<SubmissionForm> existingForm = await SubmissionFormManager.GetSubmissionFormsByCarrierSubmission(carrierSubmissionGuid);
            SubmissionForm formToRemove = existingForm.FirstOrDefault(f => f.FormGuid == formGuid);

            if (formToRemove != null)
            {
                await SubmissionFormManager.SaveRemovedSubmissionForms(new List<SubmissionForm> { formToRemove });
            }

            formsToRemove.Clear();

            StateHasChanged();
        }

        public async Task OnAddFormClicked(Guid formGuid)
        {
            await AddExistingForms(formGuid, CarrierSubmissionGuid);
        }

        public async Task OnRemoveFormClicked(Guid formGuid)
        {
            await RemoveSubmissionForm(formGuid, CarrierSubmissionGuid);
        }

        public async Task OnGoBackToSubmissionFormsButtonClicked()
        {
            NavManager.NavigateTo($"/FlexApp/SubmissionForms/{FlexResponseGuid}");
        }

        public void OnSearchClicked()
        {
            if (!string.IsNullOrEmpty(SearchText))
            {
                FilteredForms = NotAddedForms.Where(f => f.FormNumber.ToLower().Contains(SearchText.ToLower()) ||
                    f.FormDescription.ToLower().Contains(SearchText.ToLower()));
            }
            else
            {
                FilteredForms = NotAddedForms;
            }

            StateHasChanged();
        }

        public void OnClearSearchClicked()
        {
            SearchText = string.Empty;
        }

        public async Task OnSortOrderChanged(Guid formGuid, int newSortOrderNum)
        {
            SubmissionForm submissionForm = new()
            {
                CarrierSubmissionGuid = CarrierSubmissionGuid,
                FormGuid = formGuid,
                SortOrder = newSortOrderNum
            };

            if (submissionForm != null)
            {
                await SubmissionFormManager.UpdateSubmissionForm(submissionForm);
            }

            await LoadSubmissionForms();
        }
    }
}
