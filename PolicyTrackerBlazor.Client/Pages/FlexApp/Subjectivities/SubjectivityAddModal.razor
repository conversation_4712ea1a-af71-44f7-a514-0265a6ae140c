@using PolicyTrackerBlazor.Shared.Models;
@using PolicyTrackerBlazor.Shared.Models.Subjectivity
@inherits SubjectivityAddModalBase


<BSModal DataId="SubjectivityModal" HasCloseButton="false" IsStaticBackdrop="true" Size="Size.Large" IsScrollable="true" @ref="Modal">
    <BSModalHeader>
        <h5 class="d-flex gap-2 align-items-center">Add Subjectivity</h5>
    </BSModalHeader>
    <BSModalContent>
        <div class="container">
            @if (AdditionalFlexSubjectivities.Any())
            { 
                @foreach (AdditionalFlexSubjectivityModel additionalSubjectivity in AdditionalFlexSubjectivities)
                {
                    <div class="form-check">
                        <input class="form-check-input" @bind="@additionalSubjectivity.IsSelected" type="checkbox">
                        <label class="form-check-label" >
                            @additionalSubjectivity.Subjectivity
                        </label>
                    </div>
                }
            }
            <div class="m-3">
                <BSLabel>Additional Subjectivities</BSLabel>
                <BSInput InputType="InputType.TextArea" @bind-Value="SubjectivityAddModel.AdditionalSubjectivity"/>
            </div>
        </div>
    </BSModalContent>
    <BSModalFooter>
        <BSButton Class="btn btn-secondary" OnClick="HideModal">Close</BSButton>
        <CustomButton Class="btn btn-primary float-end" ButtonEvent="HandleSave" ButtonLabel="Save" ButtonLoadingLabel="Saving" SpinnerType="SpinnerType"/>
    </BSModalFooter>
</BSModal>
