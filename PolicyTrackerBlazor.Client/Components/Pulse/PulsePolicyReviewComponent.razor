@using UcpmApi.Shared.Pulse
@inject UcpmComponentLibraryEx.Services.Interfaces.IAuthenticationService UserAuthenticationService

<h5>Policy Review</h5>

@{
    var quoteReviewedOrCreated = PolicyReview.QuoteReviewedOrCreated ? "Yes" : "No";
    var dcatReviewedOrCreated = PolicyReview.DcatReviewedOrCreated ? "Yes" : "No";
    var agentUpdateRequired = PolicyReview.AgentUpdateRequired ? "Yes" : "No";
}

<div style="padding: 0% 5% 0% 5%">
    <div class="row">
        <p>Did you review the Quote? <b>@quoteReviewedOrCreated</b></p>
    </div>

    @if (ProductName.ToLower().Contains("cpl"))
    {
        <div class="row">
            <p>Did you review the DCAT?  <b>@dcatReviewedOrCreated</b></p>
        </div>
    }

    <div class="row">
        <p>Does the agent need to be updated?  <b>@agentUpdateRequired</b></p>
    </div>
</div>


@code {
    [Parameter]
    public PulsePolicyReview PolicyReview { get; set; } = null!;
    [Parameter]
    public string ProductName { get; set; } = null!;
}
