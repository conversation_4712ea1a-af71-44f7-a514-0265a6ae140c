using Microsoft.AspNetCore.Components;
using PolicyTrackerBlazor.Shared.Extensions;
using static Microsoft.Azure.Pipelines.WebApi.PipelinesResources;

namespace PolicyTrackerBlazor.Client.Components
{
    public partial class Breadcrumb
    {
        private List<BreadcrumbSegment> Segments { get; set; } = new();
        [Parameter]
        public int StartIndex { get; set; }
        [Parameter]
        public int IgnoreLastIndexCount { get; set; }
        [Parameter]
        public bool ContainsGuidSegmentPairs { get; set; } = false;
        [Parameter]
        public int StartGuidPairingIndex { get; set; }

        protected override void OnInitialized()
        {
            var uri = new Uri(NavigationManager.Uri);
            var pathSegments = uri.AbsolutePath.Split('/', StringSplitOptions.RemoveEmptyEntries);
            var endIndex = pathSegments.Length - IgnoreLastIndexCount;

            if (ContainsGuidSegmentPairs) 
            {
                for (int i = StartIndex; i < StartGuidPairingIndex; i++)
                {
                    var href = "/" + string.Join("/", pathSegments.Take(i + 1));
                    Segments.Add(new BreadcrumbSegment { Href = href, Title = pathSegments[i].ToWords() });
                }

                for(int i = StartGuidPairingIndex; i < pathSegments.Length; i+=2) 
                {
                    var href = "/" + string.Join("/", pathSegments.Take(i + 2));
                    Segments.Add(new BreadcrumbSegment { Href = href, Title = pathSegments[i].ToWords() });
                }
            } 
            else 
            {
                for (int i = StartIndex; i < endIndex; i++)
                {
                    var href = "/" + string.Join("/", pathSegments.Take(i + 1));
                    Segments.Add(new BreadcrumbSegment { Href = href, Title = pathSegments[i].ToWords() });
                }
            }
        }

        private class BreadcrumbSegment
        {
            public string Href { get; set; }
            public string Title { get; set; }
        }
    }
}