using BackgroundProc;
using UcpmApi.Shared.ApiClientBase;
using UcpmApi.Shared.Apis;
using UcpmApi.Shared.Zoho.ZohoTransfer;

namespace ZohoApiWrapper.UcpmApi
{
    public class ZohoCompanyApiWrapper
    {
        private readonly HttpWrapper _wrapper;
        private readonly ZohoCompanyApi _companyApi;
        private readonly MemoryErrorLogger _memoryErrorLogger;

        public ZohoCompanyApiWrapper(ApiConfiguration apiConfig)
        {
            _wrapper = ApiConfiguration.GetConfiguredHttpWrapper(apiConfig.UcpmApiUri);
            _memoryErrorLogger = new MemoryErrorLogger();
            _companyApi = new ZohoCompanyApi(_wrapper, _memoryErrorLogger);
        }

        public async Task<ZohoPendingCount> CreateCsv(int maxToExport, ErrorComponent errorComponent, string bearer)
        {
            ZohoPendingCount result = await _companyApi.CreateCsvAndReturnPath(maxToExport, errorComponent, bearer);

            return result;
        }

        public async Task<ZohoPendingCount> ZippedCsvToSync(string zippedCsvPath, ErrorComponent errorComponent, string bearer)
        {
            ZohoPendingCount pending = new ZohoPendingCount { OutputPath = zippedCsvPath };
            ZohoPendingCount result = await _companyApi.ZippedCsvToSync(pending, errorComponent, bearer);

            return result;
        }

    }

}
