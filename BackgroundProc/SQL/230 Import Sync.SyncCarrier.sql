truncate table Sync.SyncCarrier

-- SELECT CarrierGuid, RecordId, "0001-01-01" as A, "0001-01-01" as B, "0001-01-01" as C, "0001-01-01" as D  FROM THIS WHERE length(CarrierGuid) > 0 

bulk insert Sync.SyncCarrier
from 'd:\data\Carriers.csv'
WITH ( DATAFILETYPE = 'char', FIELDQUOTE = '"', FIRSTROW = 2, FIELDTERMINATOR = ',', ROWTERMINATOR = '\n', TABLOCK )

alter table Sync.SyncCarrier with check
check constraint FK_SyncCarrier_Carrier;
