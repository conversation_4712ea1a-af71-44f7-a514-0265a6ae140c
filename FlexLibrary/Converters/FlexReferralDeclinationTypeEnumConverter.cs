using Newtonsoft.Json;
using System;

namespace FlexLibrary
{
    public class FlexReferralDeclinationTypeEnumConverter : JsonConverter
    {
        public override bool CanConvert(Type t)
        {
            return t == typeof(FlexReferralDeclinationTypeEnum) || t == typeof(FlexReferralDeclinationTypeEnum?);
        }

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null)
            {
                return FlexReferralDeclinationTypeEnum.Normal;
            }

            string value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "normal":
                    return FlexReferralDeclinationTypeEnum.Normal;
                case "referral":
                    return FlexReferralDeclinationTypeEnum.Referral;
                case "declination":
                    return FlexReferralDeclinationTypeEnum.Declination;
                default:
                    return FlexReferralDeclinationTypeEnum.Normal;
            }
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            FlexReferralDeclinationTypeEnum value = (FlexReferralDeclinationTypeEnum)untypedValue;
            switch (value)
            {
                case FlexReferralDeclinationTypeEnum.Normal:
                    serializer.Serialize(writer, "normal");
                    return;
                case FlexReferralDeclinationTypeEnum.Referral:
                    serializer.Serialize(writer, "referral");
                    return;
                case FlexReferralDeclinationTypeEnum.Declination:
                    serializer.Serialize(writer, "declination");
                    return;
                default:
                    serializer.Serialize(writer, "normal");
                    return;
            }
        }

        public static readonly FlexReferralDeclinationTypeEnumConverter Singleton = new FlexReferralDeclinationTypeEnumConverter();
    }
}
