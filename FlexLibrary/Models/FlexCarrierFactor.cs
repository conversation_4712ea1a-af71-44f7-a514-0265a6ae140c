using Newtonsoft.Json;
using System;

namespace FlexLibrary.Models
{
    public partial class FlexCarrierFactor
    {
        [JsonProperty("Value")]
        public decimal Value { get; set; }

        [JsonProperty("MinModifier")]
        public decimal MinModifier { get; set; }

        [JsonProperty("MaxModifier")]
        public decimal MaxModifier { get; set; }
        [JsonProperty("FactorTypeCode")]
        public string FactorTypeCode { get; set; }
        [JsonProperty("CarrierMaximumOfferGuid")]
        public Guid CarrierMaximumOfferGuid { get; set; }
    }
}
