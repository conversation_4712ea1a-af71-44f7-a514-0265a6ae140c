using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace FlexLibrary
{
    public partial class FlexAnswer
    {
        [<PERSON>son<PERSON>roperty("QuestionDataName")]
        public string QuestionDataName { get; set; }

        [JsonProperty("Answer")]
        public string Answer { get; set; } = string.Empty;
        [JsonProperty("AnswerObject")]
        public string AnswerObject { get; set; } = string.Empty;
        [JsonProperty("RowNumber")]
        public int RowNumber { get; set; }
        [<PERSON>sonProperty("FlexResponseGuid")]
        public Guid FlexResponseGuid { get; set; }
        [JsonProperty("ChildAnswers")]
        public List<FlexAnswer> ChildAnswers { get; set; } = new List<FlexAnswer>();

        [JsonProperty("SavedBySecurityAccountGuid")]
        public Guid SavedBySecurityAccountGuid { get; set; }
    }

}
