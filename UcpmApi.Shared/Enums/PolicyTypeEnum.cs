namespace UcpmApi.Shared.Enums
{
    public enum PolicyTypeEnum
    {
        AutoCommercial = 23,   //AUT
        BuildersRiskInstallation,   //BRI
        DirectorsAndOfficersLiability,   //D&O
        GeneralLiabilityMonoline,   //GLM
        GeneralLiabilityPollutionProf,   //GL+
        OwnersAndContractorsProtectiveOCP,   //OCP
        PollutionLiabilityInsurance,   //PLI
        PollutionProfessionalCombined,   //PPC
        ProductsPollutionMonoline,   //PPM
        ProfessionalLiabilityInsuranceEO,   //E&O
        PropertyEquipment,   //PEQ
        RailroadProtectiveLiability,   //RPL
        Excess,   //XS
        WorkersCompensation,   //WC_
        Other,   //OTH
        EnergyPackage = 47,   //EG+
        Package = 50,   //PKG
        Crime,   //CRI
        ReuseMe = 55,   //XS3
        HNOAuto = 61,   //HNO
        Cyber = 63,   //CYB
        MotorTruckCargo = 66,   //MOT
        Property,   //PL
        ProductsLiability,   //PRL
        OwnersProtectiveProfessionalIndemnityOPPI,   //OPP
        OwnerControlledInsuranceProgramOCIP,   //OCI
        ContractorControlledInsuranceProgramCCIP,   //CCI
        AutoLiabilityOnly,   //ALI
        AutoPhysicalDamageOnly,   //APD
        ReuseMe1,   //XS4
        ReuseMe2,   //XS5
        ReuseMe3,   //XS6
        ReuseMe4,   //XS7
        CommercialProperty,   //CPR
        ResidentialProperty,   //RPR
    }
}
