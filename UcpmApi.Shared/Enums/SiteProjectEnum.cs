using System.ComponentModel.DataAnnotations;

namespace UcpmApi.Shared.Enums
{
    public enum SiteProjectEnum
    {
        [Display(Name = "policytracker")]
        PolicyTracker = 1,
        [Display(Name = "portal")]
        Portal,
        [Display(Name = "gateway")]
        Gateway,
        [Display(Name = "brochure")]
        Brochure,
        [Display(Name = "lenders")]
        Lenders,
        [Display(Name = "payment")]
        Payment,
        [Display(Name = "cerc")]
        Cerc,
        [Display(Name = "erp")]
        Erp,
        [Display(Name = "chat")]
        Chat,
        [Display(Name = "survey")]
        SurveyEngine,
        [Display(Name = "insurate-gateway")]
        InsurateGateway,
        [Display(Name = "eco-api")]
        EcoApi,
        [Display(Name = "eco-client")]
        EcoClient,
        [Display(Name = "coverage")]
        CoverageVerifier,
        [Display(Name = "roz")]
        RozNews,
        [Display(Name = "pathfinder-blazor")]
        PathfinderBlazor = 16,
        [Display(Name = "coverage-blazor")]
        CoverageVerifierBlazor = 17,
        [Display(Name = "policy-tracker-blazor")]
        PolicyTrackerBlazor = 18,
        [Display(Name = "insure-solutions-hub")]
        InsureSolutionsHub = 20,
        [Display(Name = "rps-pollution-coverage")]
        RPSPollutionCoverage = 21,
        [Display(Name = "mater")]
        Mater = 22,
    }
}
