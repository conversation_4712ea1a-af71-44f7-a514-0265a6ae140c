//using Swashbuckle.AspNetCore.Annotations;

namespace Lender.Shared.Enums
{
    /// <summary>
    /// <p>
    /// Values here actually correlate to Survey.LoanTransactionType in the db.
    /// One exception, `Securitized` below is in the db as `Sold`. They mean
    /// the same thing, but this api is for our customers and they prefer
    /// `Securitized` over `Sold`.
    /// </p>
    /// <p>
    /// <em><b>Important:</b></em> `Referral` and `Deleted` are intentionally left out of this enum as we do
    /// not want our public API users to have access to those status options. 
    /// See `Lender.Shared.Enums.InternalLoanStatusEnum` if you want those last two values for something in this solution.
    /// </p>
    /// </summary>
    //[SwaggerSchema("Allowable values for `Loan.LoanStatus`.")]
    public enum LoanStatusEnum
    {
        Open = 1,
        Funded,
        Securitized,
        Archived,
        Unfunded = 7
    }
}
