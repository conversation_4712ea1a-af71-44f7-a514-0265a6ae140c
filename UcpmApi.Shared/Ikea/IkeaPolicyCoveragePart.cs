using ApiTools.Base;
using CsvHelper.Configuration.Attributes;
using System;
using System.Collections.Generic;
using System.Text;
using UcpmApi.Shared.Coverage;
using UcpmApi.Shared.Enums;

namespace UcpmApi.Shared.Ikea
{
    public class IkeaPolicyCoveragePart : BaseViewModel, IBaseViewModel
    {
        public Guid PolicyProspectGuid { get; set; }
        public Guid IkeaCoveragePartGuid { get; set; }
        public Guid CoverageIssueGuid { get; set; }
        public IkeaPolicyCoveragePartTypeEnum Type { get; set; }
        public string Title { get; set; }
        public string CoverageIssueName { get; set; }
        public string ShortText { get; set; }
        public string Description { get; set; }
        public bool HasCoverageIssueStatus { get; set; }
        public string Premium { get; set; }
        public List<IkeaDefinedTerm> DefinedTerms { get; set; }
        public bool Checked { get; set; }
        public bool IncludedByDefault { get; set; }
        public bool IsCercQualifying { get; set; }
        public bool IsMarketStandard { get; set; }
        public bool IsCommonTypeOfWorkCoverage { get; set; }
        public bool IsEnvRiskMgmtVisible { get; set; }
        public int SortOrder { get; set; }
        public HashSet<string> Icons { get; set; }
    }
}
