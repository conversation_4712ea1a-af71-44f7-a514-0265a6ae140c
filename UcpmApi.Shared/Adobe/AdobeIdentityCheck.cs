using ApiTools.Base;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;
using UcpmApi.Shared.Api;

namespace UcpmApi.Shared.Adobe
{
    public class AdobeIdentityCheck : BaseViewModel, IBaseViewModel
    {
        [JsonPropertyName("alternateEmail")]
        public string AlternateEmail { get; set; }

        [JsonPropertyName("nameMatchCriteria")]
        public string NameMatchCriteria { get; set; }

        public AdobeIdentityCheck() { }
    }
}
