using ApiTools.Base;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;
using UcpmApi.Shared.Api;

namespace UcpmApi.Shared.Adobe
{
    public class AdobePredicate : BaseViewModel, IBaseViewModel
    {
        [JsonPropertyName("fieldLocationIndex")]
        public decimal FieldLocationIndex { get; set; }

        [JsonPropertyName("fieldName")]
        public string FieldName { get; set; }

        [JsonPropertyName("operator")]
        public string Operator { get; set; }

        [JsonPropertyName("value")]
        public string Value { get; set; }

        public AdobePredicate() { }
    }
}
