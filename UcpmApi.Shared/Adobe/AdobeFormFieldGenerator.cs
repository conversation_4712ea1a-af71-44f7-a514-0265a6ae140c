using ApiTools.Base;
using System.Text.Json.Serialization;

namespace UcpmApi.Shared.Adobe
{
    public class AdobeFormFieldGenerator : BaseViewModel, IBaseViewModel
    {
        [JsonPropertyName("formFieldDescription")]
        public AdobeFormFieldDescription FormFieldDescription { get; set; }

        [Json<PERSON>ropertyName("formFieldNamePrefix")]
        public string FormFieldNamePrefix { get; set; }

        [JsonPropertyName("generatorType")]
        public string GeneratorType { get; set; }

        [JsonPropertyName("participantSetName")]
        public string ParticipantSetName { get; set; }

        [JsonPropertyName("anchorTextInfo")]
        public AdobeAnchorTextInfo AnchorTextInfo { get; set; }

        [JsonPropertyName("linked")]
        public bool Linked { get; set; }

        public AdobeFormFieldGenerator() { }
    }
}
