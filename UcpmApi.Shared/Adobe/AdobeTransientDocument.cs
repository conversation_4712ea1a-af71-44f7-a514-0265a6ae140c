using ApiTools.Base;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;
using UcpmApi.Shared.Api;

namespace UcpmApi.Shared.Adobe
{
    public class AdobeTransientDocument : BaseViewModel, IBaseViewModel
    {
        [JsonPropertyName("transientDocumentId")]
        public string AdobeTransientDocumentId { get; set; }

        public AdobeTransientDocument()
        {

        }
    }
}
