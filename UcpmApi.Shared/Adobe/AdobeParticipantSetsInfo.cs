using ApiTools.Base;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;
using UcpmApi.Shared.Api;

namespace UcpmApi.Shared.Adobe
{
    public class AdobeParticipantSetsInfo : BaseViewModel, IBaseViewModel
    {
        [JsonPropertyName("order")]
        public decimal Order { get; set; }

        [Json<PERSON>ropertyName("role")]
        public string Role { get; set; }

        [<PERSON><PERSON><PERSON>ropertyName("electronicSealId")]
        public string ElectronicSealId { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("label")]
        public string Label { get; set; }

        [JsonPropertyName("memberInfos")]
        public List<AdobeParticipantInfo> MemberInfos { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("privateMessage")]
        public string PrivateMessage { get; set; }

        [JsonPropertyName("providerParticipationInfo")]
        public AdobeProviderParticipationInfo ProviderParticipationInfo { get; set; }

        [JsonPropertyName("visiblePages")]
        public List<string> VisiblePages { get; set; }

        public AdobeParticipantSetsInfo() { }
    }

    public enum AdobeESignRoleEnum
    {
        SIGNER,
        APPROVER,
        ACCEPTOR,
        CERTIFIED_RECIPIENT,
        FORM_FILLER,
        DELEGATE_TO_SIGNER,
        DELEGATE_TO_APPROVER,
        DELEGATE_TO_ACCEPTOR,
        DELEGATE_TO_CERTIFIED_RECIPIENT,
        DELEGATE_TO_FORM_FILLER,
        NOTARY_SIGNER,
        ELECTRONIC_SEALER,
    }
}
