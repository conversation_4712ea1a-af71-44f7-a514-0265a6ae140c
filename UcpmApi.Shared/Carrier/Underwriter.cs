using ApiTools.Base;
using System;
using System.Collections.Generic;
using System.Security.Policy;
using System.Text;
using UcpmApi.Shared.Enums;

namespace UcpmApi.Shared.Carrier
{
    public class Underwriter : BaseViewModel, IBaseViewModel
    {
        public Guid UnderwriterId { get; set; }
        public string UnderwriterName { get; set; }
        public Guid CarrierGuid { get; set; }
        public string UnderwriterEmail { get; set; }
        public string Phone { get; set; }
        public string PhoneExt { get; set; }
        public string Fax { get; set; }
        public string Mobile { get; set; }
        public string Title { get; set; }
        public bool HasLeftFirm { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsGeneralContact { get; set; }
        public DateTimeOffset PasswordLastUpdatedZoned { get; set; }
        public bool OptOutFromInquery { get; set; }
        public Guid MostRecentProducerEmployeeGuid { get; set; }
        public string AlternateEmail { get; set; }
        public bool MobileIsPreferred { get; set; }
        public int TimeZoneId { get; set; }
        public string StateCode { get; set; }
        public string UnderwritingSplitNotes { get; set; }

        public Underwriter()
        {

        }
    }
}
