namespace UcpmApi.Shared;

public class CarrierMaximumOffer : BaseViewModel
{
    public Guid CarrierMaximumOfferGuid { get; set; }
    public Guid CarrierProgramGuid { get; set; }
    public int PolicyTypeId { get; set; }
    public decimal MinimumPremium { get; set; }
    public string MaximumOfferYaml {  get; set; }
    public bool IsDeleted { get; set; }
    public Guid? OccurrenceGuid { get; set; }
    public Guid? AggregateGuid { get; set; }
    public Guid? DeductibleRequestedGuid { get; set; }
    public int MaximumPolicyTerm { get; set; }
    public bool DisplayOnline { get; set; }
    public Guid? IssuingCompanyGuid { get; set; }
    public string PolicyNumberTemplate { get; set; }
    public int NextPolicyNumber { get; set; }
    public bool IsDefaultFlatRate { get; set; }
    public Guid? CarrierPolicyPoolGuid { get; set; }
    public int VariationNumber { get; set; }
    public string VariationName { get; set; }
    public bool UseUcpmApplication  { get; set; }
    public bool UseOfferRating { get; set; }
    public int PolicyNumberingRuleId { get; set; }
    public PolicyType PolicyType { get; set; }
    public bool AllowWholesalers { get; set; }
    public bool UseRenewalSplit { get; set; }
}