using System;
using System.Collections.Generic;
using System.Text;
using UcpmApi.Shared.Coverage;

namespace UcpmApi.Shared.Carrier
{
    public class CarrierSubmissionIssueOverride : BaseViewModel, IBaseViewModel
    {
        public Guid CarrierSubmissionGuid { get; set; } 
        public Guid CoverageIssueGuid { get; set; }
        public Guid CreatedByEmployeeGuid { get; set; }
        public DateTime RecordCreatedZoned { get; set; }
        public Guid NewCoverageIssueStatusGuid { get; set; }
        public string ReasonOverridden {  get; set; }
        public string? CustomIssueStatus { get; set; }
        public CoverageIssue CoverageIssue { get; set; }
    }
}
