using ApiTools.Base;

namespace UcpmApi.Shared
{
    public class AdvisorDashboard : BaseViewModel, IBaseViewModel
    {
        public string AgentName { get; set; }
        public string InsuredName { get; set; }
        public string Phone { get; set; }
        public DateTimeOffset QuoteDueDateZoned { get; set; }
        public DateTimeOffset PresentationDateZoned { get; set; }
        public bool InitialCallCompleted { get; set; }
        public bool CompetingWithRetailer { get; set; }
        public string InternalNotes { get; set; }
        public string DaysSinceLastMeaningfulTouch { get; set; }
        public int TermDays { get; set; }
    }
}