using Sprache;
using System;
using System.Collections.Generic;
using System.Text;
using UcpmApi.NonDbLogging;
using UcpmApi.Shared.ApiClientBase;
using UcpmApi.Shared.Policy;
using UcpmApi.Shared.SurplusLines;

namespace UcpmApi.Shared.Api.Policy
{
    public class RetroApi : UcpmApiClient
    {
        #region - Constants -
        private const string Retro = "Retro";
        #endregion

        #region - Constructors -
        public RetroApi(IHttpWrapper wrapper, IErrorLogger errorLogger)
            : base(wrapper, errorLogger)
        {

        }
        #endregion

        public async Task<IEnumerable<PolicyPartRetroLimit>> GetAllPolicyPartRetroLimits(Guid carrierSubmissionOptionGuid, IErrorComponent errorComponent, string authBearerToken) 
        {
            QueryStringBuilder query = new($"{Retro}/GetAllPolicyPartRetroLimits");
            query.Add(nameof(carrierSubmissionOptionGuid), carrierSubmissionOptionGuid);

            ApiResponse<PolicyPartRetroLimit> result = await GetAsync<ApiResponse<PolicyPartRetroLimit>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<PolicyPartRetroLimit>();
        }

        public async Task<IEnumerable<PolicyPartRetro>> GetAllPolicyPartRetro(Guid carrierSubmissionOptionGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"{Retro}/GetAllPolicyPartRetro");
            query.Add(nameof(carrierSubmissionOptionGuid), carrierSubmissionOptionGuid);

            ApiResponse<PolicyPartRetro> result = await GetAsync<ApiResponse<PolicyPartRetro>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<PolicyPartRetro>();
        }

        public async Task<bool> AddPolicyPartRetro(List<PolicyPartRetro> retrosToAdd, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"{Retro}/UpdatePolicyPartRetro");
           
            return await PostAsync<bool, List<PolicyPartRetro>>(query.ToString(), retrosToAdd, errorComponent, authBearerToken);
        }

        public async Task<bool> UpdatePolicyPartRetro(Guid carrierSubmissionOptionGuid, List<PolicyPartRetro> retrosToSave, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"{Retro}/UpdatePolicyPartRetro");
            query.Add(nameof(carrierSubmissionOptionGuid), carrierSubmissionOptionGuid);
            
            ApiResponse<PolicyPartRetro> result = await PutAsync<ApiResponse<PolicyPartRetro>, List<PolicyPartRetro>>(query.ToString(), retrosToSave, authBearerToken, errorComponent);
            return result?.StatusCode == System.Net.HttpStatusCode.OK;
        }

        public async Task<bool> AddPolicyPartRetroLimit(List<PolicyPartRetroLimit> limitsToAdd, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"{Retro}/AddPolicyPartRetroLimit");

            return await PostAsync<bool, List<PolicyPartRetroLimit>>(query.ToString(), limitsToAdd, errorComponent, authBearerToken);
        }

        public async Task<bool> UpdatePolicyPartRetroLimit(Guid carrierSubmissionOptionGuid, List<PolicyPartRetroLimit> limitsToSave, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"{Retro}/UpdatePolicyPartRetroLimit");
            query.Add(nameof(carrierSubmissionOptionGuid), carrierSubmissionOptionGuid);

            ApiResponse<PolicyPartRetroLimit> result = await PutAsync<ApiResponse<PolicyPartRetroLimit>, List<PolicyPartRetroLimit>>(query.ToString(), limitsToSave, authBearerToken, errorComponent);
            return result?.StatusCode == System.Net.HttpStatusCode.OK;
        }
    }
}
