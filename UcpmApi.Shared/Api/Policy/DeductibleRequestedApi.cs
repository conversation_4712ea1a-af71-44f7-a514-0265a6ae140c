using UcpmApi.NonDbLogging;

namespace UcpmApi.Shared.Api.Policy
{
    public class DeductibleRequestedApi : UcpmApiClient
    {
        #region - Constants -
        private const string DeductibleRequested = "DeductibleRequested";
        #endregion

        #region - Constructors -
        public DeductibleRequestedApi(IHttpWrapper wrapper, IErrorLogger errorLogger)
            : base(wrapper, errorLogger)
        {
        }
        #endregion

        #region - Public Methods -
        public async Task<IEnumerable<DeductibleRequested>> GetDeductiblesAsync(IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"{DeductibleRequested}");

            ApiResponse<DeductibleRequested> result = await GetAsync<ApiResponse<DeductibleRequested>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<DeductibleRequested>();
        }

        #endregion
    }
}
