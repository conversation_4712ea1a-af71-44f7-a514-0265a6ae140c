using System.Net;

using UcpmApi.NonDbLogging;
using UcpmApi.Shared.Api;
namespace UcpmApi.Shared.Apis
{
    public class PolicyStrategyApi : UcpmApiClient
    {
        #region - Constants -
        private const string PolicyStrategy = "PolicyStrategy";
        #endregion

        #region - Constructors -
        public PolicyStrategyApi(IHttpWrapper wrapper, IErrorLogger errorLogger)
            : base(wrapper, errorLogger)
        {
        }
        #endregion

        #region - Public Methods -
        public async Task<IEnumerable<PolicyStrategy>> GetPolicyStrategyAsync(Guid policyProspectGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"{PolicyStrategy}");
            query.Add(nameof(policyProspectGuid), policyProspectGuid);

            ApiResponse<PolicyStrategy> result = await GetAsync<ApiResponse<PolicyStrategy>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<PolicyStrategy>();
        }

        public async Task<IEnumerable<PolicyStrategy>> GetAllStrategies(IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"{PolicyStrategy}/GetAllStrategies");

            ApiResponse<PolicyStrategy> result = await GetAsync<ApiResponse<PolicyStrategy>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<PolicyStrategy>();
        }

        public async Task<bool> UpdatePolicyStrategy(PolicyStrategy policyStrategy, IErrorComponent errorComponent, string authBearerToken)
        {
            ApiResponse<PolicyStrategy> result = await PostAsync<ApiResponse<PolicyStrategy>, PolicyStrategy>(PolicyStrategy, policyStrategy, errorComponent, authBearerToken);
            return result?.StatusCode == HttpStatusCode.OK;
        }

        #endregion
    }
}