using System;
using System.Collections.Generic;
using System.Text;
using UcpmApi.NonDbLogging;
using UcpmApi.Shared.Policy;

namespace UcpmApi.Shared.Api.Policy
{
    public class RenewalApi : UcpmApiClient
    {
        #region - Constructors -
        public RenewalApi(IHttpWrapper wrapper, IErrorLogger errorLogger)
            : base(wrapper, errorLogger)
        {

        }
        #endregion

        #region - Public Methods -
        public async Task<Renewal> GetRenewal(Guid oldPolicyProspectGuid, Guid newPolicyProspectGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"Renewal/GetRenewal");
            query.Add(nameof(oldPolicyProspectGuid), oldPolicyProspectGuid);
            query.Add(nameof(newPolicyProspectGuid), newPolicyProspectGuid);

            ApiResponse<Renewal> result = await GetAsync<ApiResponse<Renewal>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data?.SingleOrDefault() ?? new Renewal();
        }

        public async Task<bool> AddNewRenewal(Renewal renewal, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"Renewal/AddNewRenewal");

            ApiResponse<Renewal> result = await PostAsync<ApiResponse<Renewal>, Renewal>(query.ToString(), renewal, errorComponent, authBearerToken);
            return result?.StatusCode == System.Net.HttpStatusCode.OK;
        }
        #endregion
    }
}
