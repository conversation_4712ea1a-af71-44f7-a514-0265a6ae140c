using System.Net;

using UcpmApi.NonDbLogging;
using UcpmApi.Shared.Api;
namespace UcpmApi.Shared.Apis
{
    public class PolicyInfoApi : UcpmApiClient
    {
        #region - Constants -
        private const string PolicyInfo = "PolicyInfo";
        #endregion

        #region - Constructors -
        public PolicyInfoApi(IHttpWrapper wrapper, IErrorLogger errorLogger)
            : base(wrapper, errorLogger)
        {
        }
        #endregion

        #region - Public Methods -
        public async Task<PolicyInfo> GetPolicyInfoAsync(Guid PolicyInfoGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"{PolicyInfo}");
            query.Add(nameof(PolicyInfoGuid), PolicyInfoGuid);

            ApiResponse<PolicyInfo> result = await GetAsync<ApiResponse<PolicyInfo>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data?.SingleOrDefault() ?? new PolicyInfo();
        }

        public async Task<bool> UpdatePolicyInfo(PolicyInfo policyInfo, IErrorComponent errorComponent, string authBearerToken)
        {
            ApiResponse<PolicyInfo> result = await PostAsync<ApiResponse<PolicyInfo>, PolicyInfo>(PolicyInfo, policyInfo, errorComponent, authBearerToken);
            return result?.StatusCode == HttpStatusCode.OK;
        }

        public async Task<PolicyInfo> CreateNewPolicyInfo(Guid employeeGuid, Guid packageGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"{PolicyInfo}/CreateNewPolicyInfo");
            query.Add(nameof(employeeGuid), employeeGuid);
            query.Add(nameof(packageGuid), packageGuid);

            ApiResponse<PolicyInfo> result = await GetAsync<ApiResponse<PolicyInfo>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data?.SingleOrDefault() ?? new PolicyInfo();
        }

        #endregion
    }
}