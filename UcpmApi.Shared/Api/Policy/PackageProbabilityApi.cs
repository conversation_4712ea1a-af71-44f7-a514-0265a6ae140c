using System.Net;

using UcpmApi.NonDbLogging;
using UcpmApi.Shared.Api;
namespace UcpmApi.Shared.Apis
{
    public class PackageProbabilityApi : UcpmApiClient
    {
        #region - Constants -
        private const string PackageProbability = "PackageProbability";
        #endregion

        #region - Constructors -
        public PackageProbabilityApi(IHttpWrapper wrapper, IErrorLogger errorLogger)
            : base(wrapper, errorLogger)
        {
        }
        #endregion

        #region - Public Methods -
        public async Task<PackageProbability> GetPackageProbabilityAsync(Guid PackageGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"{PackageProbability}");
            query.Add(nameof(PackageGuid), PackageGuid);

            ApiResponse<PackageProbability> result = await GetAsync<ApiResponse<PackageProbability>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data?.SingleOrDefault() ?? new PackageProbability();
        }

        public async Task<bool> UpdatePackageProbabilities(PackageProbability packageProbability, IErrorComponent errorComponent, string authBearerToken)
        {
            ApiResponse<PackageProbability> result = await PostAsync<ApiResponse<PackageProbability>, PackageProbability>(PackageProbability, packageProbability, errorComponent, authBearerToken);
            return result?.StatusCode == HttpStatusCode.OK;
        }

        #endregion
    }
}