using UcpmApi.NonDbLogging;
using UcpmApi.Shared.Api;
using UcpmApi.Shared.Pulse;

namespace UcpmApi.Shared.Apis
{
    public class PulsePendingEmailLogApi : UcpmApiClient
    {
        private readonly string _pulsePendingEmailLog = "PulsePendingEmailLog";

        #region - Constructors -
        public PulsePendingEmailLogApi(IHttpWrapper wrapper, IErrorLogger errorLogger)
            : base(wrapper, errorLogger)
        {
        }
        #endregion

        #region - Public Methods -
        public async Task<IEnumerable<PulsePendingEmailLog>> GetPulsePendingEmailLogs(IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(_pulsePendingEmailLog);

            ApiResponse<PulsePendingEmailLog> result = await GetAsync<ApiResponse<PulsePendingEmailLog>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<PulsePendingEmailLog>();
        }

        public async Task<bool> AddPulsePendingEmailLog(PulsePendingEmailLog pulsePendingEmailLog, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(_pulsePendingEmailLog);

            ApiResponse<ApiBoolResponse> result = await PostAsync<ApiResponse<ApiBoolResponse>, PulsePendingEmailLog>(query.ToString(), pulsePendingEmailLog, errorComponent, authBearerToken);
            ApiBoolResponse? resultData = result?.Data?.FirstOrDefault();

            return resultData != null && resultData.Result;
        }

        #endregion
    }
}
