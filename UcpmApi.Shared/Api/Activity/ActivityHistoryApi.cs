using UcpmApi.NonDbLogging;
using UcpmApi.Shared.Api;

namespace UcpmApi.Shared.Apis
{
    public class ActivityHistoryApi : UcpmApiClient
    {
        private string ActivityHistory = "History";

        #region - Constructors -
        public ActivityHistoryApi(IHttpWrapper wrapper, IErrorLogger errorLogger)
            : base(wrapper, errorLogger)
        {
        }

        public ActivityHistoryApi() { }
        #endregion

        #region - Public Methods -

        public async Task<bool> CreateActivityHistory(ActivityHistory activityHistory, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"{ActivityHistory}");
            ApiResponse<ApiBoolResponse> result = await PostAsync<ApiResponse<ApiBoolResponse>, ActivityHistory>(query.ToString(), activityHistory, errorComponent, authBearerToken);

            return result?.Data?.FirstOrDefault()?.Result ?? false;
        } 

        #endregion
    }
}
