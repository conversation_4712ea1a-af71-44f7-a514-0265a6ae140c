using UcpmApi.NonDbLogging;
using UcpmApi.Shared.Api;
using UcpmApi.Shared.SurplusLines;

namespace UcpmApi.Shared.Apis
{
    public class SurplusLinesPolicyApi : UcpmApiClient
    {
        #region - Constructors -
        public SurplusLinesPolicyApi(IHttpWrapper wrapper, IErrorLogger errorLogger)
            : base(wrapper, errorLogger)
        {
        }
        #endregion

        #region - Public Methods -
        public async Task<SurplusLinesPolicy> GetSurplusLinesPolicyByGuid(Guid surplusLinesPolicyGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());
            query.Add(nameof(surplusLinesPolicyGuid), surplusLinesPolicyGuid);

            ApiResponse<SurplusLinesPolicy> result = await GetAsync<ApiResponse<SurplusLinesPolicy>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data?.SingleOrDefault() ?? new SurplusLinesPolicy();
        }

        public async Task<IEnumerable<SurplusLinesPolicy>> GetSurplusLinesPoliciesByInsuredGuid(Guid surplusLinesInsuredGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());
            query.Add(nameof(surplusLinesInsuredGuid), surplusLinesInsuredGuid);

            ApiResponse<SurplusLinesPolicy> result = await GetAsync<ApiResponse<SurplusLinesPolicy>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data?.ToList() ?? new List<SurplusLinesPolicy>();
        }

        public async Task<IEnumerable<SurplusLinesPolicy>> GetSurplusLinesPoliciesByEmployee(Guid employeeGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());
            query.Add(nameof(employeeGuid), employeeGuid);

            ApiResponse<SurplusLinesPolicy> result = await GetAsync<ApiResponse<SurplusLinesPolicy>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data?.ToList() ?? new List<SurplusLinesPolicy>();
        }

        public async Task<bool> AddSurplusLinesPolicy(SurplusLinesPolicy surplusLinesPolicy, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());

            ApiResponse<SurplusLinesPolicy> result = await PostAsync<ApiResponse<SurplusLinesPolicy>, SurplusLinesPolicy>(query.ToString(), surplusLinesPolicy, errorComponent, authBearerToken);
            return result?.StatusCode == System.Net.HttpStatusCode.OK;
        }

        public async Task<bool> UpdateSurpusLinesPolicy(SurplusLinesPolicy surplusLinesPolicy, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());

            ApiResponse<SurplusLinesPolicy> result = await PutAsync<ApiResponse<SurplusLinesPolicy>, SurplusLinesPolicy>(query.ToString(), surplusLinesPolicy, authBearerToken, errorComponent);
            return result?.StatusCode == System.Net.HttpStatusCode.OK;
        }

        public async Task<bool> RequestBind(Guid invoiceGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());

            ApiResponse<SurplusLinesPolicy> result = await PutAsync<ApiResponse<SurplusLinesPolicy>, Guid>(query.ToString(), invoiceGuid, authBearerToken, errorComponent);
            return result?.StatusCode == System.Net.HttpStatusCode.OK;
        }

        public async Task<bool> SetRequestStatus(Guid invoiceGuid, int requestStatusId, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());
            query.Add(nameof(invoiceGuid), invoiceGuid);
            query.Add(nameof(requestStatusId), requestStatusId);

            ApiResponse<SurplusLinesPolicy> result = await PutAsync<ApiResponse<SurplusLinesPolicy>, Guid>(query.ToString(), invoiceGuid, authBearerToken, errorComponent);
            return result?.StatusCode == System.Net.HttpStatusCode.OK;
        }

        public async Task<bool> SetRequestStatusToQuoted(Guid invoiceGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());

            ApiResponse<SurplusLinesPolicy> result = await PutAsync<ApiResponse<SurplusLinesPolicy>, Guid>(query.ToString(), invoiceGuid, authBearerToken, errorComponent);
            return result?.StatusCode == System.Net.HttpStatusCode.OK;
        }

        public async Task<bool> SetRequestStatusToOpen(Guid invoiceGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());

            ApiResponse<SurplusLinesPolicy> result = await PutAsync<ApiResponse<SurplusLinesPolicy>, Guid>(query.ToString(), invoiceGuid, authBearerToken, errorComponent);
            return result?.StatusCode == System.Net.HttpStatusCode.OK;
        }

        public async Task<bool> ClosePolicy(SurplusLinesPolicy surplusLinesPolicy, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());

            ApiResponse<SurplusLinesPolicy> result = await PutAsync<ApiResponse<SurplusLinesPolicy>, SurplusLinesPolicy>(query.ToString(), surplusLinesPolicy, authBearerToken, errorComponent);
            return result?.StatusCode == System.Net.HttpStatusCode.OK;
        }
        #endregion
    }
}
