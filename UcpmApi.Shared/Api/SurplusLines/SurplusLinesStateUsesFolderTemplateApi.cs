using UcpmApi.NonDbLogging;
using UcpmApi.Shared.Api;
using UcpmApi.Shared.SurplusLines;

namespace UcpmApi.Shared.Apis
{
    public class SurplusLinesStateUsesFolderTemplateApi : UcpmApiClient
    {
        #region - Constructors -
        public SurplusLinesStateUsesFolderTemplateApi(IHttpWrapper wrapper, IErrorLogger errorLogger)
            : base(wrapper, errorLogger)
        {
        }
        #endregion

        #region - Public Methods -
        public async Task<SurplusLinesStateUsesFolderTemplate> GetSurplusLinesStateUsesFolderTemplateById(int surplusLinesFolderTemplateId, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());
            query.Add(nameof(surplusLinesFolderTemplateId), surplusLinesFolderTemplateId);

            ApiResponse<SurplusLinesStateUsesFolderTemplate> result = await GetAsync<ApiResponse<SurplusLinesStateUsesFolderTemplate>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data?.SingleOrDefault() ?? new SurplusLinesStateUsesFolderTemplate();
        }
        #endregion
    }
}
