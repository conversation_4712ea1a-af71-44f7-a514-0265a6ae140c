using UcpmApi.NonDbLogging;
using UcpmApi.Shared.EPay;

namespace UcpmApi.Shared.Api.EPay
{
    public class EPayApi : UcpmApiClient
    {
        private const string EPay = "EPay";
        public EPayApi(IHttpWrapper wrapper, IErrorLogger errorLogger) : base(wrapper, errorLogger)
        {
        }

        public async Task<EPayInvoicesModel> GetInvoices(string accountIdentifier, IErrorComponent errorComponent)
        {
            QueryStringBuilder query = new($"{EPay}/GetInvoices");
            query.Add(nameof(accountIdentifier), accountIdentifier);

            ApiResponse<EPayInvoicesModel> result = await GetAsync<ApiResponse<EPayInvoicesModel>>(query.ToString(), errorComponent);
            return result?.Data?.SingleOrDefault() ?? new EPayInvoicesModel();
        }

        public async Task<EPayInvoicesModel> GetInvoicesInsured(string accountIdentifier, IErrorComponent errorComponent)
        {
            QueryStringBuilder query = new($"{EPay}/GetInvoicesInsured");
            query.Add(nameof(accountIdentifier), accountIdentifier);

            ApiResponse<EPayInvoicesModel> result = await GetAsync<ApiResponse<EPayInvoicesModel>>(query.ToString(), errorComponent);
            return result?.Data?.SingleOrDefault() ?? new EPayInvoicesModel();
        }
    }
}