using System.Net;
using UcpmApi.NonDbLogging;
using UcpmApi.Shared.Api;
using UcpmApi.Shared.Flex;
using UcpmApi.Shared.ValidationAbstraction;

namespace UcpmApi.Shared.Apis
{
    public class FlexResponseApi : UcpmApiClient
    {
        #region - Constructors -
        public FlexResponseApi(IHttpWrapper wrapper, IErrorLogger errorLogger)
            : base(wrapper, errorLogger)
        {
        }

        public FlexResponseApi() { }
        #endregion

        #region - Public Methods -
        public async Task<FlexResponse> GetFlexResponseByGuid(Guid flexResponseGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());
            query.Add(nameof(flexResponseGuid), flexResponseGuid);

            ApiResponse<FlexResponse> result = await GetAsync<ApiResponse<FlexResponse>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data?.SingleOrDefault() ?? new FlexResponse();
        }
        public async Task<FlexResponseWithAdjustment> GetFlexResponseWithScheduledRatingAdjustment(Guid flexResponseGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());
            query.Add(nameof(flexResponseGuid), flexResponseGuid);

            ApiResponse<FlexResponseWithAdjustment> result = await GetAsync<ApiResponse<FlexResponseWithAdjustment>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data?.SingleOrDefault() ?? new FlexResponseWithAdjustment();
        }
        public async Task<FlexPublishedVersion> GetPublishedVersionUsingFlexResponseGuid(Guid flexResponseGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());
            query.Add(nameof(flexResponseGuid), flexResponseGuid);

            ApiResponse<FlexPublishedVersion> result = await GetAsync<ApiResponse<FlexPublishedVersion>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data?.SingleOrDefault() ?? new FlexPublishedVersion();
        }

        public async Task<FlexResponse> GetByLinkingGuid(Guid linkingGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());
            query.Add("linkingGuid", linkingGuid);

            ApiResponse<FlexResponse> result = await GetAsync<ApiResponse<FlexResponse>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data?.SingleOrDefault() ?? new FlexResponse();
        }

        public async Task<ValidationResult<FlexResponse>> AddNewResponse(FlexResponse flexResponse, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());

            ApiResponse<ValidationResult<FlexResponse>> result = await PostAsync<ApiResponse<ValidationResult<FlexResponse>>, FlexResponse>(query.ToString(), flexResponse, errorComponent, authBearerToken);
            return result?.Data?.SingleOrDefault() ?? new ValidationResult<FlexResponse>();
        }

        public virtual async Task<bool> CreateTankApp(PolicyProspect policyProspect, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());

            ApiResponse<PolicyProspect> result = await PostAsync<ApiResponse<PolicyProspect>, PolicyProspect>(query.ToString(), policyProspect, errorComponent, authBearerToken);
            return result?.StatusCode == HttpStatusCode.OK;
        }

        public virtual async Task<FlexSendPulseTankQuoteModel> CreateTankAppForPulse(PolicyProspect policyProspect, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());

            ApiResponse<FlexSendPulseTankQuoteModel> result = await PostAsync<ApiResponse<FlexSendPulseTankQuoteModel>, PolicyProspect>(query.ToString(), policyProspect, errorComponent, authBearerToken);
            return result?.Data?.SingleOrDefault() ?? new FlexSendPulseTankQuoteModel();
        }

        public virtual async Task<bool> AutoCreateConstructionApp(PolicyProspect policyProspect, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());

            ApiResponse<PolicyProspect> result = await PostAsync<ApiResponse<PolicyProspect>, PolicyProspect>(query.ToString(), policyProspect, errorComponent, authBearerToken);
            return result?.StatusCode == HttpStatusCode.OK;
        }

        public virtual async Task<Guid> CreateConstructionAppForPulse(PolicyProspect policyProspect, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"FlexResponse/AutoCreateConstructionApp");

            ApiResponse<PolicyProspect> result = await PostAsync<ApiResponse<PolicyProspect>, PolicyProspect>(query.ToString(), policyProspect, errorComponent, authBearerToken);
            return result?.Data != null ? result.Data.FirstOrDefault().FlexResponseGuid : Guid.Empty;
        }

        public virtual async Task<bool> HasFlexResponseLastYear(Guid policyProspectGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"FlexResponse/HasFlexResponseLastYear");
            query.Add(nameof(policyProspectGuid), policyProspectGuid);

            ApiResponse<FlexResponse> result = await GetAsync<ApiResponse<FlexResponse>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data != null ? result.Data.Any() : false;
        }

        public virtual async Task<Guid> CreateRenewalApp(Guid policyProspectGuid, int flexDefinitionId, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"FlexResponse/CreateRenewalApp");
            query.Add(nameof(policyProspectGuid), policyProspectGuid);
            query.Add(nameof(flexDefinitionId), flexDefinitionId);

            ApiResponse<FlexResponse> result = await GetAsync<ApiResponse<FlexResponse>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data != null ? result.Data.SingleOrDefault().FlexResponseGuid : Guid.Empty;
        }

        public virtual async Task<Guid> UpdateExistingApp(PolicyProspect policyProspect, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"FlexResponse/UpdateExistingApp");

            ApiResponse<PolicyProspect> result = await PostAsync<ApiResponse<PolicyProspect>, PolicyProspect>(query.ToString(), policyProspect, errorComponent, authBearerToken);
            return result?.Data != null ? result.Data.FirstOrDefault().FlexResponseGuid : Guid.Empty;
        }
        
        public async Task<FlexVersionCarrierOffer> GetFlexVersionCarrierOffer(int flexDefinitionId, int policyTypeId, int variationNumber, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());
            query.Add(nameof(flexDefinitionId), flexDefinitionId);
            query.Add(nameof(policyTypeId), policyTypeId);
            query.Add(nameof(variationNumber), variationNumber);
            
            ApiResponse<FlexVersionCarrierOffer> result = await GetAsync<ApiResponse<FlexVersionCarrierOffer>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data?.SingleOrDefault() ?? new FlexVersionCarrierOffer();
        }
        #endregion
    }
}
