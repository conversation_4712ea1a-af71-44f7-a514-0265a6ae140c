using UcpmApi.NonDbLogging;
using UcpmApi.Shared.Api;
using UcpmApi.Shared.Coverage;

namespace UcpmApi.Shared.Apis
{
    public class DcatTemplateProgramSnippetApi : UcpmApiClient
    {
        #region - Constructors -
        public DcatTemplateProgramSnippetApi(IHttpWrapper wrapper, IErrorLogger errorLogger)
            : base(wrapper, errorLogger)
        {
        }

        public DcatTemplateProgramSnippetApi() { }
        #endregion

        #region - Public Methods -
        public async Task<DcatTemplateProgramSnippet> GetDcatTemplateProgramSnippet(Guid programGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());
            query.Add(nameof(programGuid), programGuid);

            ApiResponse<DcatTemplateProgramSnippet> result = await GetAsync<ApiResponse<DcatTemplateProgramSnippet>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data?.SingleOrDefault() ?? new DcatTemplateProgramSnippet();
        }

        public async Task<bool> AddDcatTemplateProgramSnippet(DcatTemplateProgramSnippet dcatTemplateProgramSnippet, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());

            ApiResponse<DcatTemplateProgramSnippet> result = await PostAsync<ApiResponse<DcatTemplateProgramSnippet>, DcatTemplateProgramSnippet>(query.ToString(), dcatTemplateProgramSnippet, errorComponent, authBearerToken);
            return result?.StatusCode == System.Net.HttpStatusCode.OK;
        }

        public async Task<bool> UpdateDcatTemplateProgramSnippet(DcatTemplateProgramSnippet dcatTemplateProgramSnippet, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());

            ApiResponse<DcatTemplateProgramSnippet> result = await PutAsync<ApiResponse<DcatTemplateProgramSnippet>, DcatTemplateProgramSnippet>(query.ToString(), dcatTemplateProgramSnippet, authBearerToken, errorComponent);
            return result?.StatusCode == System.Net.HttpStatusCode.OK;
        }

        public async Task<bool> AddUpdateDcatTemplateProgramSnippet(DcatTemplateProgramSnippet dcatTemplateProgramSnippet, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new(this.GetMethodNameForApi());

            ApiResponse<DcatTemplateProgramSnippet> result = await PostAsync<ApiResponse<DcatTemplateProgramSnippet>, DcatTemplateProgramSnippet>(query.ToString(), dcatTemplateProgramSnippet, errorComponent, authBearerToken);
            return result?.StatusCode == System.Net.HttpStatusCode.OK;
        }
        #endregion
    }
}
