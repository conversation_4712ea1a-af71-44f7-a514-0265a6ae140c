using UcpmApi.NonDbLogging;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.News;

namespace UcpmApi.Shared.Api.News
{
    public class OnlineArticleApi : UcpmApiClient
    {
        public OnlineArticleApi(IHttpWrapper wrapper, IErrorLogger errorLogger)
            : base(wrapper, errorLogger)
        {

        }

        public OnlineArticleApi() { }

        public async Task<IEnumerable<OnlineArticle>> GetForRoz(IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"OnlineArticle/GetForRoz");

            ApiResponse<OnlineArticle> result = await GetAsync<ApiResponse<OnlineArticle>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<OnlineArticle>();
        }

        public async Task<IEnumerable<OnlineArticle>> OnlineArticleSearchForRoz(SearchOnlineRequest request, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new("OnlineArticle/OnlineArticleSearchForRoz");
            ApiResponse<OnlineArticle> result = await PostAsync<ApiResponse<OnlineArticle>, SearchOnlineRequest>(query.ToString(), request, errorComponent, authBearerToken);

            return result?.Data ?? new List<OnlineArticle>();
        }

        public async Task<IEnumerable<OnlineArticle>> GetNonViewedForRoz(Guid agentGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"OnlineArticle/GetNonViewedForRoz");
            query.Add(nameof(agentGuid), agentGuid);

            ApiResponse<OnlineArticle> result = await GetAsync<ApiResponse<OnlineArticle>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<OnlineArticle>();
        }

        public async Task<IEnumerable<OnlineArticle>> GetSearchedForRoz(Guid agentGuid, String searchQuery, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"OnlineArticle/GetSearchedForRoz");
            query.Add(nameof(agentGuid), agentGuid);
            query.Add(nameof(searchQuery), searchQuery);

            ApiResponse<OnlineArticle> result = await GetAsync<ApiResponse<OnlineArticle>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<OnlineArticle>();
        }


        public async Task<IEnumerable<OnlineArticle>> GetForRozAnonymous(IErrorComponent errorComponent, string authBearerToken, int maxArticles = 3)
        {
            QueryStringBuilder query = new($"OnlineArticle/GetForRozAnonymous");
            query.Add(nameof(maxArticles), maxArticles);

            ApiResponse<OnlineArticle> result = await GetAsync<ApiResponse<OnlineArticle>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<OnlineArticle>();
        }

        public async Task<OnlineArticle> GetArticle(Guid articleGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"OnlineArticle/GetArticle");
            query.Add(nameof(articleGuid), articleGuid);
            ApiResponse<OnlineArticle> result = await GetAsync<ApiResponse<OnlineArticle>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data.SingleOrDefault() ?? new OnlineArticle();
        }
        public async Task<IEnumerable<OnlineArticle>> GetForPathfinder(Guid agentGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"OnlineArticle/GetForPathfinder");
            query.Add(nameof(agentGuid), agentGuid);

            ApiResponse<OnlineArticle> result = await GetAsync<ApiResponse<OnlineArticle>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<OnlineArticle>();
        }

        public async Task<IEnumerable<OnlineArticle>> GetOnlineArticlesByPublishType(int publishType, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new("OnlineArticle/GetOnlineArticlesByPublishType");
            query.Add(nameof(publishType), publishType);

            ApiResponse<OnlineArticle> result = await GetAsync<ApiResponse<OnlineArticle>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<OnlineArticle>();
        }

        public async Task<IEnumerable<OnlineArticle>> GetUnreadOnlineNewsArticles(Guid accountGuid, PublishLocationEnum publishLocation, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new("OnlineArticle/GetUnreadOnlineNewsArticles");
            query.Add(nameof(accountGuid), accountGuid);
            query.Add(nameof(publishLocation), publishLocation);

            ApiResponse<OnlineArticle> result = await GetAsync<ApiResponse<OnlineArticle>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<OnlineArticle>();
        }

        public async Task<OnlineArticle> CreateUnpublishedOnlineArticle(CreateOnlineArticleRequest request, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder queryStringBuilder = new("OnlineArticle/CreateUnpublishedOnlineArticle");
            ApiResponse<OnlineArticle> result = await PostAsync<ApiResponse<OnlineArticle>, CreateOnlineArticleRequest>(queryStringBuilder.ToString(), request, errorComponent, authBearerToken);

            return result?.Data.FirstOrDefault() ?? new OnlineArticle();
        }

        public virtual async Task<IEnumerable<OnlineArticle>> GetRozArticlesForNotifications(IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new("OnlineArticle/GetRozArticlesForNotifications");

            ApiResponse<OnlineArticle> result = await GetAsync<ApiResponse<OnlineArticle>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<OnlineArticle>();
        }
        
        public async Task<IEnumerable<OnlineArticle>> GetBookmarkedArticles(Guid agentGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new("OnlineArticle/GetBookmarkedArticles");
            query.Add(nameof(agentGuid), agentGuid);

            ApiResponse<OnlineArticle> result = await GetAsync<ApiResponse<OnlineArticle>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<OnlineArticle>();
        }
    }
}
