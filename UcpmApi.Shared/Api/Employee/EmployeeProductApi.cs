using UcpmApi.NonDbLogging;
using UcpmApi.Shared.Api;

namespace UcpmApi.Shared.Apis
{
    public class ProductEmployeeApi : UcpmApiClient
    {
        #region - Constants -
        private const string ProductEmployee = "ProductEmployee";
        #endregion

        #region - Constructors -
        public ProductEmployeeApi(IHttpWrapper wrapper, IErrorLogger errorLogger)
            : base(wrapper, errorLogger)
        {
        }
        #endregion

        #region - Public Methods -
        public async Task<IEnumerable<ProductEmployee>> GetProductEmployeeAsync(Guid EmployeeGuid, IErrorComponent errorComponent, string authBearerToken)
        {
            QueryStringBuilder query = new($"{ProductEmployee}");
            query.Add(nameof(EmployeeGuid), EmployeeGuid);

            ApiResponse<ProductEmployee> result = await GetAsync<ApiResponse<ProductEmployee>>(query.ToString(), errorComponent, authBearerToken);
            return result?.Data ?? new List<ProductEmployee>();
        }


        #endregion
    }
}
