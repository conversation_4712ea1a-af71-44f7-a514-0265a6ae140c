using UcpmApi.NonDbLogging;
using UcpmApi.Shared.Api;

namespace UcpmApi.Shared.Apis
{
    public class InvoiceQuoteApi : UcpmApiClient
    {
        #region - Constants -
        private const string _InvoiceQuote = "InvoiceQuote";
        #endregion

        #region - Constructors -
        public InvoiceQuoteApi(IHttpWrapper wrapper, IErrorLogger errorLogger)
            : base(wrapper, errorLogger)
        {
        }
        #endregion

        #region - Public Methods -
        public async Task<InvoiceQuote> GetInvoiceQuote(InvoiceQuote InvoiceQuote, IErrorComponent errorComponent, string authBearerToken)
        {
            ApiResponse<InvoiceQuote> result = await PostAsync<ApiResponse<InvoiceQuote>, InvoiceQuote>(_InvoiceQuote, InvoiceQuote, errorComponent, authBearerToken);
            return result?.Data?.SingleOrDefault() ?? new InvoiceQuote();
        }


        #endregion
    }
}