
namespace UcpmApi.Shared.Coverage
{
    public class NewFormRequest : BaseViewModel
    {
        public Guid NewFormRequestGuid { get; set; }
        public Guid SubmitterEmployeeGuid { get; set; }
        public DateTimeOffset SubmittedAtZoned { get; set; }
        public int FormReviewStatusId { get; set; }
        public DateTimeOffset LastStatusAtZoned { get; set; }
        public bool IsBaseFormRequest { get; set; }
        public Guid CarrierGuid { get; set; }
        public Guid ProgramGuid { get; set; }
        public string FormNumber { get; set; }
        public string FormDescription { get; set; }
        public bool DisplayOnline { get; set; }
        public string DisplayOnlineName { get; set; }
        public string FormNumberAnnotation { get; set; }
        public bool IsEndorsement { get; set; }
        public bool UseForSpecificProject { get; set; }
        public bool UseForClaimsMade { get; set; }
        public bool UseOnline { get; set; }
        public string SubmitterNotes { get; set; }
        public Guid ProcessInstanceGuid { get; set; }
        public bool RejectForm { get; set; }
        public List<NewFormRequestReviewer> NewFormRequestReviewer { get; set; } = new List<NewFormRequestReviewer>();

        public NewFormRequest()
        {

        }

        public NewFormRequestReviewer GetCurrentReviewer()
        {
            List<NewFormRequestReviewer> orderedReviewers = NewFormRequestReviewer.OrderByDescending(r => r.AssignedOnZoned).ToList();

            return orderedReviewers.Count > 0 ? orderedReviewers[0] : new NewFormRequestReviewer();
        }
    }
}
