using ApiTools.Base;
using System.ComponentModel.DataAnnotations;

namespace UcpmApi.Shared.Coverage
{
    public class CoverageIssueStatus : BaseViewModel, IBaseViewModel
    {
        [Required]
        public string ShortText { get; set; }

        [Required]
        public int CoverageIssueStatusValue { get; set; }
        public Guid CoverageIssueStatusGroupGuid { get; set; }
        public Guid CoverageIssueStatusGuid { get; set; }
        [Required]
        public string AbbreviatedStatus { get; set; }
        public Guid CopiedFromIssueStatusGuid { get; set; }
        public Guid IssueRequirementGuid { get; set; }
        public bool Selected { get; set; }
        public Guid CarrierSubmissionGuid { get; set; }
        public bool Unavailable { get; set; }
        public bool IsArchived { get; set; }

        public CoverageIssueStatus()
        {

        }
    }
}
