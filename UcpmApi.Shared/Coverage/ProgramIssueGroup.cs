using System;
using System.Collections.Generic;
using System.Text;

namespace UcpmApi.Shared.Coverage
{
    public class ProgramIssueGroup : BaseViewModel, IBaseViewModel
    {
        public Guid ProgramIssueGroupGuid { get; set; }
        public Guid ProgramGuid { get; set; }
        public string GroupName { get; set; }
        public string GroupDescription { get; set; }
        public int GroupSortPriority { get; set; }
        public Guid CopiedFromProgramIssueGroupGuid { get; set; }
        public Guid ClassOfBusinessGuid { get; set; }
        public List<CoverageIssue> CoverageIssues { get; set; } = new();
        public CoverageIssue CoverageIssue { get; set; }
        public bool IsNewCoverageIssueGroup { get; set; }
    }
}
