using ApiTools.Base;
using UcpmApi.Shared.Coverage;

namespace UcpmApi.Shared.Cache;

public class ProjectOwnerCoverageIssue : BaseViewModel, IBaseViewModel
{
    public Guid ProjectOwnerGuid { get; set; }
    public Guid CoverageIssueGuid { get; set; }
    public Guid MinimumAcceptedCoverageStatusGuid { get; set; }

    public ProjectOwner ProjectOwner { get; set; }
    public CoverageIssueStatus CoverageIssueStatus { get; set; }
}