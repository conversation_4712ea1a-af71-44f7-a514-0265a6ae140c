using ApiTools.Base;

namespace UcpmApi.Shared.SurplusLines
{
    public class SurplusLinesEndorsement : BaseViewModel, IBaseViewModel
    {
        public Guid SurplusLinesEndorsementGuid { get; set; }
        public Guid SurplusLinesInvoiceGuid { get; set; }
        public Guid SurplusLinesAgentGuid { get; set; }
        public Guid SurplusLinesInsuredGuid { get; set; }
        public int SurplusLinesRequestStatusId { get; set; }
        public int SurplusLinesDueDiligenceId { get; set; }
        public int SurplusLinesEndorsementTypeId { get; set; }
        public string SurplusLinesEndorsementTypeName { get; set; }
        public string SurplusLinesRequestStatusName { get; set; }
        public string SurplusLinesDueDiligenceName { get; set; }
        public string EndorsementNumber { get; set; }
        public string EndorsementName { get; set; }
        public string PartnerAccountNumber { get; set; }
        public decimal EndorsementAmount { get; set; }
        public DateTime EffectiveDate { get; set; }
        public DateTime ExpirationDate { get; set; }
        public Guid SourceSuplusLinesPolicyGuid { get; set; }
        public Guid AssignedEmployeeGuid { get; set; }
        public string AssignedEmployeeName { get; set; }

        public SurplusLinesEndorsement()
        {

        }
    }
}
