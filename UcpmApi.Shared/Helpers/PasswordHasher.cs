using System.Security.Cryptography;
using System.Text;

namespace UcpmApi.Shared.Helpers
{
    public static class PasswordHasher
    {
        public static byte[] Sha512Salter()
        {
            RNGCryptoServiceProvider provider = new();
            //Salted
            byte[] saltBytes = new byte[16];
            provider.GetNonZeroBytes(saltBytes);

            //Static salt for testing
            //byte[] saltBytes = new byte[16] { 5, 100, 200, 55, 71, 15, 201, 191, 51, 77, 5, 100, 200, 55, 77, 11 };

            //Expected static salt: Zj760M0gfFmngJfjsh0yFB03keQV66ZSsbldiB/vMKVuNDjgczwWjMiNQ8foSCwuAMcZizJKWJmYE04yR36z4w==
            return saltBytes;
        }

        public static string HexPasswordConversion(string password)
        {
            byte[] passwordCharacters = Encoding.ASCII.GetBytes(password);
            int hash = 0;
            if (passwordCharacters.Length > 0)
            {
                int charIndex = passwordCharacters.Length;

                while (charIndex-- > 0)
                {
                    hash = hash >> 14 & 0x01 | hash << 1 & 0x7fff;
                    hash ^= passwordCharacters[charIndex];
                }
                // Main difference from spec, also hash with char count
                hash = hash >> 14 & 0x01 | hash << 1 & 0x7fff;
                hash ^= passwordCharacters.Length;
                hash ^= 0x8000 | 'N' << 8 | 'K';
            }

            return Convert.ToString(hash, 16).ToUpperInvariant();
        }
    }
}
