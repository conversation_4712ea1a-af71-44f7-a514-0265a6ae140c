using ApiTools.Base;

namespace UcpmApi.Shared.Helpers
{
    internal class ButFirstCoffee : BaseViewModel
    {
        /*[SwaggerSchema("The number of seconds to wait before trying the request again.")]*/
        public int RetryAfterSeconds { get; set; }

        /*[SwaggerSchema("A pithy message to make you smile. Maybe even chuckle.")]*/
        public string Message { get; set; }



        public ButFirstCoffee(int retryAfterSeconds)
        {
            RetryAfterSeconds = retryAfterSeconds;
            Message = "But first, coffee.";
        }
    }
}
