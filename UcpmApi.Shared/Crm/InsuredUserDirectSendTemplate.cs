using ApiTools.Base;
using System.ComponentModel;
using UcpmApi.Shared.Survey;

namespace UcpmApi.Shared
{
    public class InsuredUserDirectSendTemplate : BaseViewModel, IBaseViewModel
    {
        [Browsable(false)]
        public Guid InsuredUserGuid { get; set; }
        public int DirectSendTemplateId { get; set; }
        public int RecipientTypeId { get; set; }
        public InsuredUser InsuredUser { get; set; }
    }
}