using System.Text.Json.Serialization;

namespace UcpmApi.Shared.Zoho
{
    public partial class ZohoDealInfo
    {
        [JsonPropertyName("data")]
        public ZohoDealData[] Data { get; set; }
    }

    public partial class ZohoDealData
    {
        [JsonPropertyName("Broker_Guid")]
        public string BrokerGuid { get; set; }

        [JsonPropertyName("Owner")]
        public CreatedBy Owner { get; set; }

        [JsonPropertyName("Additional_Brokers_Non_Zoho")]
        public object[] AdditionalBrokersNonZoho { get; set; }

        [JsonPropertyName("$field_states")]
        public object FieldStates { get; set; }

        [JsonPropertyName("Package_Year")]
        public long PackageYear { get; set; }

        [JsonPropertyName("Expiration_Date")]
        public DateTimeOffset ExpirationDate { get; set; }

        [JsonPropertyName("Product")]
        public string Product { get; set; }

        [JsonPropertyName("Expected_Outcome")]
        public object ExpectedOutcome { get; set; }

        [JsonPropertyName("$sharing_permission")]
        public string SharingPermission { get; set; }

        [JsonPropertyName("gdriveextension__Drive_Folder_ID")]
        public object GdriveextensionDriveFolderId { get; set; }

        [JsonPropertyName("Quote_Due_Date")]
        public DateTimeOffset QuoteDueDate { get; set; }

        [JsonPropertyName("Effective_Date")]
        public DateTimeOffset EffectiveDate { get; set; }

        [JsonPropertyName("First_Advisor")]
        public object FirstAdvisor { get; set; }

        [JsonPropertyName("$process_flow")]
        public bool ProcessFlow { get; set; }

        [JsonPropertyName("Day1")]
        public string Day1 { get; set; }

        [JsonPropertyName("Day2")]
        public string Day2 { get; set; }

        [JsonPropertyName("Do_you_know_why_you_Lost")]
        public string DoYouKnowWhyYouLost { get; set; }

        [JsonPropertyName("Day3")]
        public string Day3 { get; set; }

        [JsonPropertyName("Stage")]
        public string Stage { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("Notes_for_PT")]
        public object NotesForPt { get; set; }

        [JsonPropertyName("$approval")]
        public Approval Approval { get; set; }

        [JsonPropertyName("Panic_Progress")]
        public string PanicProgress { get; set; }

        [JsonPropertyName("Audience")]
        public object Audience { get; set; }

        [JsonPropertyName("Created_Time")]
        public DateTimeOffset CreatedTime { get; set; }

        [JsonPropertyName("Changes_Needed")]
        public object ChangesNeeded { get; set; }

        [JsonPropertyName("Preferred_COB")]
        public string[] PreferredCob { get; set; }

        [JsonPropertyName("Change_Log_Time__s")]
        public DateTimeOffset ChangeLogTimeS { get; set; }

        [JsonPropertyName("Agency_Income_last_12_months")]
        public object AgencyIncomeLast12_Months { get; set; }

        [JsonPropertyName("Insured")]
        public AccountName Insured { get; set; }

        [JsonPropertyName("Closing_Advisor")]
        public object ClosingAdvisor { get; set; }

        [JsonPropertyName("Level")]
        public object Level { get; set; }

        [JsonPropertyName("Created_By")]
        public CreatedBy CreatedBy { get; set; }

        [JsonPropertyName("Audited_By")]
        public object AuditedBy { get; set; }

        [JsonPropertyName("Pulse_Progress")]
        public string PulseProgress { get; set; }

        [JsonPropertyName("Description")]
        public object Description { get; set; }

        [JsonPropertyName("Campaign_Source")]
        public object CampaignSource { get; set; }

        [JsonPropertyName("$review_process")]
        public Approval ReviewProcess { get; set; }

        [JsonPropertyName("Presentation_Date")]
        public DateTimeOffset PresentationDate { get; set; }

        [JsonPropertyName("Activity_Types")]
        public object[] ActivityTypes { get; set; }

        [JsonPropertyName("$canvas_id")]
        public object CanvasId { get; set; }

        [JsonPropertyName("Lead_Conversion_Time")]
        public object LeadConversionTime { get; set; }

        [JsonPropertyName("Agency_Premium_last_12_months")]
        public object AgencyPremiumLast12_Months { get; set; }

        [JsonPropertyName("zohoworkdriveforcrm__Workdrive_Folder_URL")]
        public object ZohoworkdriveforcrmWorkdriveFolderUrl { get; set; }

        [JsonPropertyName("Multi_Campaign_Lookup")]
        public object[] MultiCampaignLookup { get; set; }

        [JsonPropertyName("Overall_Sales_Duration")]
        public object OverallSalesDuration { get; set; }

        [JsonPropertyName("Account_Name")]
        public AccountName AccountName { get; set; }

        [JsonPropertyName("Queue")]
        public object Queue { get; set; }

        [JsonPropertyName("User_Exited_Plan")]
        public bool UserExitedPlan { get; set; }

        [JsonPropertyName("Renewal_Date")]
        public object RenewalDate { get; set; }

        [JsonPropertyName("$orchestration")]
        public bool Orchestration { get; set; }

        [JsonPropertyName("Pipeline")]
        public string Pipeline { get; set; }

        [JsonPropertyName("Contact_Name")]
        public AccountName ContactName { get; set; }

        [JsonPropertyName("Information_to_Share")]
        public object[] InformationToShare { get; set; }

        [JsonPropertyName("Expiring_Opportunity")]
        public object ExpiringOpportunity { get; set; }

        [JsonPropertyName("Layout")]
        public AccountName Layout { get; set; }

        [JsonPropertyName("First_Advisor_Steps")]
        public string FirstAdvisorSteps { get; set; }

        [JsonPropertyName("Locked__s")]
        public bool LockedS { get; set; }

        [JsonPropertyName("Tag")]
        public object[] Tag { get; set; }

        [JsonPropertyName("Desk_Ticket_ID")]
        public object DeskTicketId { get; set; }

        [JsonPropertyName("$pathfinder")]
        public bool Pathfinder { get; set; }

        [JsonPropertyName("Day")]
        public string Day { get; set; }

        [JsonPropertyName("Submission_Worklfow")]
        public object SubmissionWorklfow { get; set; }

        [JsonPropertyName("Scheduled_By")]
        public object ScheduledBy { get; set; }

        [JsonPropertyName("$currency_symbol")]
        public string CurrencySymbol { get; set; }

        [JsonPropertyName("Last_Activity_Time")]
        public object LastActivityTime { get; set; }

        [JsonPropertyName("Opportunity_Progress")]
        public string OpportunityProgress { get; set; }

        [JsonPropertyName("Step_3")]
        public string Step3 { get; set; }

        [JsonPropertyName("Deal_Name")]
        public string DealName { get; set; }

        [JsonPropertyName("Step_4")]
        public string Step4 { get; set; }

        [JsonPropertyName("Next_Years_Renewal")]
        public string NextYearsRenewal { get; set; }

        [JsonPropertyName("$locked_for_me")]
        public bool LockedForMe { get; set; }

        [JsonPropertyName("Trip_Information")]
        public object TripInformation { get; set; }

        [JsonPropertyName("BluePrint_Progress")]
        public object BluePrintProgress { get; set; }

        [JsonPropertyName("Confirm_Submission")]
        public object ConfirmSubmission { get; set; }

        [JsonPropertyName("Program")]
        public string Program { get; set; }

        [JsonPropertyName("PT_PCY_Stage")]
        public object PtPcyStage { get; set; }

        [JsonPropertyName("Renewal_Month")]
        public string RenewalMonth { get; set; }

        [JsonPropertyName("Final_Level_of_Difficulty")]
        public object FinalLevelOfDifficulty { get; set; }

        [JsonPropertyName("$wizard_connection_path")]
        public object WizardConnectionPath { get; set; }

        [JsonPropertyName("$editable")]
        public bool Editable { get; set; }

        [JsonPropertyName("Step_1")]
        public string Step1 { get; set; }

        [JsonPropertyName("Step_2")]
        public string Step2 { get; set; }

        [JsonPropertyName("zohoworkdriveforcrm__Workdrive_Folder_ID")]
        public object ZohoworkdriveforcrmWorkdriveFolderId { get; set; }

        [JsonPropertyName("AGA_s")]
        public object[] AgaS { get; set; }

        [JsonPropertyName("$zia_owner_assignment")]
        public string ZiaOwnerAssignment { get; set; }

        [JsonPropertyName("Broker_Workflow")]
        public string BrokerWorkflow { get; set; }

        [JsonPropertyName("Blue_Print")]
        public string BluePrint { get; set; }

        [JsonPropertyName("Reason_For_Loss__s")]
        public object ReasonForLossS { get; set; }

        [JsonPropertyName("Pulse_Sub_Progress")]
        public object PulseSubProgress { get; set; }

        [JsonPropertyName("Modified_By")]
        public CreatedBy ModifiedBy { get; set; }

        [JsonPropertyName("$review")]
        public object Review { get; set; }

        [JsonPropertyName("Culture_or_Placement")]
        public object CultureOrPlacement { get; set; }

        [JsonPropertyName("Lead_Info")]
        public object LeadInfo { get; set; }

        [JsonPropertyName("Closing_Advisor_Steps")]
        public string ClosingAdvisorSteps { get; set; }

        [JsonPropertyName("$zia_visions")]
        public object ZiaVisions { get; set; }

        [JsonPropertyName("Sub_Stage")]
        public object SubStage { get; set; }

        [JsonPropertyName("gdriveextension__Drive_URL")]
        public object GdriveextensionDriveUrl { get; set; }

        [JsonPropertyName("Modified_Time")]
        public DateTimeOffset ModifiedTime { get; set; }

        [JsonPropertyName("Due_Date")]
        public object DueDate { get; set; }

        [JsonPropertyName("Sales_Cycle_Duration")]
        public object SalesCycleDuration { get; set; }

        [JsonPropertyName("Package_Guid")]
        public string PackageGuid { get; set; }

        [JsonPropertyName("$in_merge")]
        public bool InMerge { get; set; }

        [JsonPropertyName("High_Priority")]
        public bool HighPriority { get; set; }

        [JsonPropertyName("$approval_state")]
        public string ApprovalState { get; set; }

        [JsonPropertyName("$has_more")]
        public HasMore HasMore { get; set; }
    }

    public partial class AccountName
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }
    }

    public partial class Approval
    {
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("delegate")]
        public bool? Delegate { get; set; }

        [JsonPropertyName("approve")]
        public bool Approve { get; set; }

        [JsonPropertyName("reject")]
        public bool Reject { get; set; }

        [JsonPropertyName("resubmit")]
        public bool Resubmit { get; set; }
    }

    public partial class CreatedBy
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("email")]
        public string Email { get; set; }
    }

    public partial class HasMore
    {
        [JsonPropertyName("Audience")]
        public bool Audience { get; set; }

        [JsonPropertyName("Multi_Campaign_Lookup")]
        public bool MultiCampaignLookup { get; set; }

        [JsonPropertyName("AGA_s")]
        public bool AgaS { get; set; }
    }
}
