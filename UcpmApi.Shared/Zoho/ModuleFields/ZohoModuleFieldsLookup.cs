using System.Text.Json.Serialization;


namespace UcpmApi.Shared.Zoho.ModuleFields
{
    public partial class ZohoModuleFieldsLookup
    {
        [JsonPropertyName("display_label")]
        public string DisplayLabel { get; set; }

        [JsonPropertyName("api_name")]
        public string ApiName { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("module")]
        public ZohoModuleFieldsModule Module { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }
    }
}
