using System.Text.Json.Serialization;

namespace UcpmApi.Shared.Zoho
{
    public partial class ZohoWorkDriveUploadResponse
    {
        [JsonPropertyName("data")]
        public ZohoWorkDriveUploadData[] Data { get; set; }
    }

    public partial class ZohoWorkDriveUploadData
    {
        [JsonPropertyName("attributes")]
        public WorkDriveUploadAttributes Attributes { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }
    }

    public partial class WorkDriveUploadAttributes
    {
        [JsonPropertyName("Permalink")]
        public Uri Permalink { get; set; }

        [JsonPropertyName("File INFO")]
        public string FileInfo { get; set; }

        [JsonPropertyName("parent_id")]
        public string ParentId { get; set; }

        [JsonPropertyName("FileName")]
        public string FileName { get; set; }

        [JsonPropertyName("resource_id")]
        public string ResourceId { get; set; }
    }
}