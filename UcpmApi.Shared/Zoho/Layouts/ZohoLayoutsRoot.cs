using System;
using System.Collections.Generic;

using System.Text.Json;
using System.Text.Json.Serialization;
using System.Globalization;

namespace UcpmApi.Shared.Zoho.Layouts
{
    public partial class ZohoLayoutsRoot
    {
        [JsonPropertyName("layouts")]
        public List<ZohoLayoutsLayout> Layouts { get; set; }

    }

    public partial class ZohoLayoutsLayout
    {
        [JsonPropertyName("has_more_profiles")]
        public bool HasMoreProfiles { get; set; }

        [JsonPropertyName("created_time")]
        public object CreatedTime { get; set; }

        [JsonPropertyName("convert_mapping")]
        public ZohoLayoutsLayoutConvertMapping ConvertMapping { get; set; }

        [JsonPropertyName("visible")]
        public bool Visible { get; set; }

        [JsonPropertyName("created_for")]
        public object CreatedFor { get; set; }

        [JsonPropertyName("profiles")]
        public List<ZohoLayoutsLayoutProfile> Profiles { get; set; }

        [JsonPropertyName("source")]
        public string Source { get; set; }

        [JsonPropertyName("created_by")]
        public object CreatedBy { get; set; }

        [JsonPropertyName("sections")]
        public List<ZohoLayoutsSection> Sections { get; set; }

        [JsonPropertyName("display_label")]
        public string DisplayLabel { get; set; }

        [JsonPropertyName("show_business_card")]
        public bool ShowBusinessCard { get; set; }

        [JsonPropertyName("actions_allowed")]
        public ZohoLayoutsActionsAllowed ActionsAllowed { get; set; }

        [JsonPropertyName("modified_time")]
        public object ModifiedTime { get; set; }

        [JsonPropertyName("api_name")]
        public string ApiName { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("modified_by")]
        public object ModifiedBy { get; set; }

        [JsonPropertyName("generated_type")]
        public string GeneratedType { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("status")]
        public string Status { get; set; }
    }

    public partial class ZohoLayoutsActionsAllowed
    {
        [JsonPropertyName("edit")]
        public bool Edit { get; set; }

        [JsonPropertyName("rename")]
        public bool Rename { get; set; }

        [JsonPropertyName("clone")]
        public bool Clone { get; set; }

        [JsonPropertyName("downgrade")]
        public bool Downgrade { get; set; }

        [JsonPropertyName("delete")]
        public bool Delete { get; set; }

        [JsonPropertyName("deactivate")]
        public bool Deactivate { get; set; }

        [JsonPropertyName("set_layout_permissions")]
        public bool SetLayoutPermissions { get; set; }
    }

    public partial class ZohoLayoutsLayoutConvertMapping
    {
        [JsonPropertyName("Contacts")]
        public ZohoLayoutsAccounts Contacts { get; set; }

        [JsonPropertyName("Deals")]
        public ZohoLayoutsDeals Deals { get; set; }

        [JsonPropertyName("Accounts")]
        public ZohoLayoutsAccounts Accounts { get; set; }
    }

    public partial class ZohoLayoutsAccounts
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }
    }

    public partial class ZohoLayoutsDeals
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("fields")]
        public List<ZohoLayoutsDealsField> Fields { get; set; }
    }

    public partial class ZohoLayoutsDealsField
    {
        [JsonPropertyName("api_name")]
        public string ApiName { get; set; }

        [JsonPropertyName("field_label")]
        public string FieldLabel { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("required")]
        public bool FieldRequired { get; set; }
    }

    public partial class ZohoLayoutsLayoutProfile
    {
        [JsonPropertyName("_default_assignment_view")]
        public ZohoLayoutsDefaultTypeView DefaultAssignmentView { get; set; }

        [JsonPropertyName("default")]
        public bool Default { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("_default_view")]
        public ZohoLayoutsDefaultTypeView DefaultView { get; set; }
    }

    public partial class ZohoLayoutsDefaultTypeView
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }
    }

    public partial class ZohoLayoutsSection
    {
        [JsonPropertyName("display_label")]
        public string DisplayLabel { get; set; }

        [JsonPropertyName("sequence_number")]
        public long SequenceNumber { get; set; }

        [JsonPropertyName("isSubformSection")]
        public bool IsSubformSection { get; set; }

        [JsonPropertyName("tab_traversal")]
        public int TabTraversal { get; set; }

        [JsonPropertyName("api_name")]
        public string ApiName { get; set; }

        [JsonPropertyName("column_count")]
        public long ColumnCount { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("generated_type")]
        public string GeneratedType { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("fields")]
        public List<ZohoLayoutsSectionField> Fields { get; set; }

        [JsonPropertyName("properties")]
        public object Properties { get; set; }
    }

    public partial class ZohoLayoutsSectionField
    {
        [JsonPropertyName("associated_module")]
        public object AssociatedModule { get; set; }

        [JsonPropertyName("webhook")]
        public bool Webhook { get; set; }

        [JsonPropertyName("operation_type")]
        public ZohoLayoutsOperationType OperationType { get; set; }

        [JsonPropertyName("colour_code_enabled_by_system")]
        public bool ColourCodeEnabledBySystem { get; set; }

        [JsonPropertyName("field_label")]
        public string FieldLabel { get; set; }

        [JsonPropertyName("tooltip")]
        public object Tooltip { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("field_read_only")]
        public bool FieldReadOnly { get; set; }

        [JsonPropertyName("required")]
        public bool FieldRequired { get; set; }

        [JsonPropertyName("display_label")]
        public string DisplayLabel { get; set; }

        [JsonPropertyName("read_only")]
        public bool ReadOnly { get; set; }

        [JsonPropertyName("association_details")]
        public object AssociationDetails { get; set; }

        [JsonPropertyName("businesscard_supported")]
        public bool BusinesscardSupported { get; set; }

        [JsonPropertyName("multi_module_lookup")]
        public ZohoLayoutsAutoNumber MultiModuleLookup { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("created_time")]
        public object CreatedTime { get; set; }

        [JsonPropertyName("filterable")]
        public bool Filterable { get; set; }

        [JsonPropertyName("visible")]
        public bool Visible { get; set; }

        [JsonPropertyName("profiles")]
        public List<ZohoLayoutsFieldProfile> Profiles { get; set; }

        [JsonPropertyName("view_type")]
        public ZohoLayoutsViewType ViewType { get; set; }

        [JsonPropertyName("separator")]
        public bool Separator { get; set; }

        [JsonPropertyName("searchable")]
        public bool Searchable { get; set; }

        [JsonPropertyName("external")]
        public object External { get; set; }

        [JsonPropertyName("api_name")]
        public string ApiName { get; set; }

        [JsonPropertyName("unique")]
        public ZohoLayoutsAutoNumber Unique { get; set; }

        [JsonPropertyName("enable_colour_code")]
        public bool EnableColourCode { get; set; }

        [JsonPropertyName("pick_list_values")]
        public List<ZohoLayoutsPickListValue> PickListValues { get; set; }

        [JsonPropertyName("system_mandatory")]
        public bool SystemMandatory { get; set; }

        [JsonPropertyName("virtual_field")]
        public bool VirtualField { get; set; }

        [JsonPropertyName("json_type")]
        public string JsonType { get; set; }

        [JsonPropertyName("crypt")]
        public object Crypt { get; set; }

        [JsonPropertyName("created_source")]
        public string CreatedSource { get; set; }

        [JsonPropertyName("display_type")]
        public long DisplayType { get; set; }

        [JsonPropertyName("ui_type")]
        public long UiType { get; set; }

        [JsonPropertyName("validation_rule")]
        public object ValidationRule { get; set; }

        [JsonPropertyName("modified_time")]
        public DateTimeOffset? ModifiedTime { get; set; }

        [JsonPropertyName("section_id")]
        public long SectionId { get; set; }

        [JsonPropertyName("email_parser")]
        public ZohoLayoutsEmailParser EmailParser { get; set; }

        [JsonPropertyName("currency")]
        public ZohoLayoutsCurrency Currency { get; set; }

        [JsonPropertyName("custom_field")]
        public bool CustomField { get; set; }

        [JsonPropertyName("lookup")]
        public ZohoLayoutsLookup Lookup { get; set; }

        [JsonPropertyName("convert_mapping")]
        public ZohoLayoutsFieldConvertMapping ConvertMapping { get; set; }

        [JsonPropertyName("rollup_summary")]
        public ZohoLayoutsAutoNumber RollupSummary { get; set; }

        [JsonPropertyName("length")]
        public long Length { get; set; }

        [JsonPropertyName("display_field")]
        public bool DisplayField { get; set; }

        [JsonPropertyName("pick_list_values_sorted_lexically")]
        public bool PickListValuesSortedLexically { get; set; }

        [JsonPropertyName("default_value")]
        public object DefaultValue { get; set; }

        [JsonPropertyName("sortable")]
        public bool Sortable { get; set; }

        [JsonPropertyName("sequence_number")]
        public long SequenceNumber { get; set; }

        [JsonPropertyName("global_picklist")]
        public object GlobalPicklist { get; set; }

        [JsonPropertyName("history_tracking")]
        public object HistoryTracking { get; set; }

        [JsonPropertyName("data_type")]
        public string DataType { get; set; }

        [JsonPropertyName("formula")]
        public ZohoLayoutsAutoNumber Formula { get; set; }

        [JsonPropertyName("decimal_place")]
        public long? DecimalPlace { get; set; }

        [JsonPropertyName("mass_update")]
        public bool MassUpdate { get; set; }

        [JsonPropertyName("multiselectlookup")]
        public ZohoLayoutsAutoNumber Multiselectlookup { get; set; }

        [JsonPropertyName("auto_number")]
        public ZohoLayoutsAutoNumber AutoNumber { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("quick_sequence_number")]
        public string QuickSequenceNumber { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("blueprint_supported")]
        public bool? BlueprintSupported { get; set; }
    }

    public partial class ZohoLayoutsAutoNumber
    {
    }

    public partial class ZohoLayoutsFieldConvertMapping
    {
        [JsonPropertyName("Contacts")]
        public string Contacts { get; set; }

        [JsonPropertyName("Deals")]
        public string Deals { get; set; }

        [JsonPropertyName("Accounts")]
        public string Accounts { get; set; }
    }

    public partial class ZohoLayoutsCurrency
    {
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("rounding_option")]
        public string RoundingOption { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("precision")]
        public long? Precision { get; set; }
    }

    public partial class ZohoLayoutsEmailParser
    {
        [JsonPropertyName("fields_update_supported")]
        public bool FieldsUpdateSupported { get; set; }

        [JsonPropertyName("record_operations_supported")]
        public bool RecordOperationsSupported { get; set; }
    }

    public partial class ZohoLayoutsLookup
    {
        [JsonPropertyName("display_label")]
        public object DisplayLabel { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("revalidate_filter_during_edit")]
        public bool? RevalidateFilterDuringEdit { get; set; }

        [JsonPropertyName("api_name")]
        public object ApiName { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("module")]
        public ZohoLayoutsModule Module { get; set; }

        [JsonPropertyName("id")]
        public object Id { get; set; }

        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("query_details")]
        public ZohoLayoutsAutoNumber QueryDetails { get; set; }
    }

    public partial class ZohoLayoutsModule
    {
        [JsonPropertyName("api_name")]
        public string ApiName { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }
    }

    public partial class ZohoLayoutsOperationType
    {
        [JsonPropertyName("web_update")]
        public bool WebUpdate { get; set; }

        [JsonPropertyName("api_create")]
        public bool ApiCreate { get; set; }

        [JsonPropertyName("web_create")]
        public bool WebCreate { get; set; }

        [JsonPropertyName("api_update")]
        public bool ApiUpdate { get; set; }
    }

    public partial class ZohoLayoutsPickListValue
    {
        [JsonPropertyName("display_value")]
        public string DisplayValue { get; set; }

        [JsonPropertyName("sequence_number")]
        public long SequenceNumber { get; set; }

        [JsonPropertyName("maps")]
        public List<object> Maps { get; set; }

        [JsonPropertyName("colour_code")]
        public string ColourCode { get; set; }

        [JsonPropertyName("actual_value")]
        public string ActualValue { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }
    }

    public partial class ZohoLayoutsFieldProfile
    {
        [JsonPropertyName("permission_type")]
        public string PermissionType { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("id")]
        public string Id { get; set; }
    }

    public partial class ZohoLayoutsViewType
    {
        [JsonPropertyName("view")]
        public bool View { get; set; }

        [JsonPropertyName("edit")]
        public bool Edit { get; set; }

        [JsonPropertyName("quick_create")]
        public bool QuickCreate { get; set; }

        [JsonPropertyName("create")]
        public bool Create { get; set; }
    }

}
