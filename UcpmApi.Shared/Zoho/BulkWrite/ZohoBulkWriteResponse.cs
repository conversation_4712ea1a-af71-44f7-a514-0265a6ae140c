namespace UcpmApi.Shared.Zoho.BulkWrite
{
    using System.Text.Json.Serialization;

    public partial class ZohoBulkWriteResponse
    {
        [JsonPropertyName("status")]
        public string Status { get; set; }

        [JsonPropertyName("code")]
        public string Code { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }

        [JsonPropertyName("details")]
        public ZohoBulkWriteResponseDetails Details { get; set; }
    }
}
