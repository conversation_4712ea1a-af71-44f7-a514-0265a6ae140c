using System;
using System.Text.Json.Serialization;

namespace UcpmApi.Shared.Zoho.BulkWrite
{
    public partial class ZohoBulkWriteResultFieldMapping
    {
        [JsonPropertyName("api_name")]
        public string ApiName { get; set; }

        [JsonPropertyName("index")]
        public long? Index { get; set; }

        [JsonPropertyName("format")]
        public object Format { get; set; }

        [JsonPropertyName("find_by")]
        public string FindBy { get; set; }

        [JsonPropertyName("module")]
        public object Module { get; set; }

        [JsonPropertyName("parent_column_index")]
        public object ParentColumnIndex { get; set; }

        [JsonPropertyName("default_value")]
        public object DefaultValue { get; set; }
    }
}
