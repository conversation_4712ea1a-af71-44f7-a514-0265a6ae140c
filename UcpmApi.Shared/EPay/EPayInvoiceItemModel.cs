using ApiTools.Base;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace UcpmApi.Shared.EPay
{
    public class EPayInvoiceItemModel : BaseViewModel, IBaseViewModel
    {
        /// <summary>
        /// The Id of the invoice item.
        /// </summary>
        [Required]
        public string Id { get; set; }
        /// <summary>
        /// A collection of custom attribute values.
        /// </summary>
        [Required]
        public List<AttributeValueModel> AttributeValues { get; set; }
    }
}
