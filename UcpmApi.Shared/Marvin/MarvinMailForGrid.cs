using ApiTools.Base;

namespace UcpmApi.Shared
{
    public class MarvinMailForGrid : BaseViewModel, IBaseViewModel
    {
        public Guid MailGuid { get; set; }
        public string Subject { get; set; }
        public DateTimeOffset DateToSend { get; set; }
        public DateTimeOffset SendSuccessDate { get; set; }
        public Guid SourceMarvinMailTemplateGuid { get; set; }
        public Guid PolicyGuid { get; set; }
        public string TemplateName { get; set; }
        public int TemplateCadenceDays { get; set; }
        public Guid AgentGuid { get; set; }
        public Guid PackageGuid { get; set; }
        public string Status { get; set; }
        public bool IsDeleted { get; set; }
        public bool ApprovedForSend { get; set; }
        public DateTime PinDate { get; set; }
    }
}