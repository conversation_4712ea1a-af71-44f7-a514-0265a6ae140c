namespace UcpmApi.Shared.Kato
{
    public class KatoCplRiskManagementModel
    {
        public DocInfo PppDoc { get; set; } = null;
        public IEnumerable<DocInfo> EroDocs { get; set; }
        public ClaimTemplateModelCollection ClaimsExamples { get; set; }
        public InsuredStoryViewModelCollection InsuredStories { get; set; }
        public Guid PolicyProspectGuid { get; set; }
        public KatoNavBarModel NavModel { get; set; }
        public bool IsSelectionMode { get; set; }
        public KatoAgentSelectionNavModelCollection AgentSelectionNavigation { get; set; }
        public int ShareableContentId { get; set; }
    }
}
