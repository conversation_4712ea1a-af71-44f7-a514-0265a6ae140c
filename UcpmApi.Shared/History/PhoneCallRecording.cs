using ApiTools.Base;
using Newtonsoft.Json;

namespace UcpmApi.Shared.History
{
    public class PhoneCallRecording : BaseViewModel, IBaseViewModel
    {
        [JsonProperty("next_page_token")]
        public string NextPageToken { get; set; }

        [JsonProperty("page_size")]
        public long PageSize { get; set; }

        [JsonProperty("total_records")]
        public long TotalRecords { get; set; }

        [JsonProperty("from")]
        public DateTimeOffset From { get; set; }

        [JsonProperty("to")]
        public DateTimeOffset To { get; set; }

        [JsonProperty("recordings")]
        public List<Recording> Recordings { get; set; }

        public Guid PhoneCallRecordingGuid { get; set; }
        public string ZoomRecordingId { get; set; }
        public Guid PhoneCallGuid { get; set; }
        public string DownloadUrl { get; set; }

        public static PhoneCallRecording FromJson(string json) => JsonConvert.DeserializeObject<PhoneCallRecording>(json, PhoneCallJsonConverter.Settings);
    }
}
