using FlexLibrary;
using UcpmApi.Shared.Flex;

namespace UcpmApi.Shared
{
    public class FlexRating : BaseViewModel, IBaseViewModel
    {
        public Dictionary<int, decimal> BasePremium { get; set; } = new Dictionary<int, decimal>();
        public decimal SolutionsPageBasePremium { get; set; }
        public decimal QuickRaterPremium { get; set; }
        public decimal MinimumPremium { get; set; }
        public decimal AdditionalPremium { get; set; }
        public List<FlexOption> Options { get; set; } = new List<FlexOption>();
        public List<FlexOption> OptionsToDelete { get; set; } = new List<FlexOption>();
        public string Diagnostics { get; set; }
        public FlexReferralDeclinationTypeEnum DiagnosticType { get; set; }
        public Guid FlexResponseGuid { get; set; }
        public Guid PackageGuid { get; set; }
        public bool UseLastYearsPolicy { get; set; }
        public string BasePremiumBreakdown { get; set; }
        public List<string> BasePremiumBreakdownList { get; set; } = new List<string>();

        public Guid CarrierSubmissionGuid { get; set; }
        public Guid PolicyProspectGuid { get; set; }
        public decimal IkeaFactor { get; set; }
        public decimal ExposureBasis { get; set; }
        public Dictionary<Guid, bool> ReferralDictionary { get; set; } = new Dictionary<Guid, bool>();
        public List<DocUrlInfo> DocUrlInfos { get; set; }
    }
    public class DocUrlInfo
    {
        public string DocName { get; set; }
        public string DocUrl { get; set; }
    }
}
