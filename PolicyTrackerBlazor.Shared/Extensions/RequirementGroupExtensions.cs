using PolicyTrackerBlazor.Shared.Models;
using UcpmApi.Shared.Vetting;

namespace PolicyTrackerBlazor.Shared.Extensions
{
    public static class RequirementGroupExtensions
    {
        public static IEnumerable<RequirementGroupModel> ToModels(this IEnumerable<RequirementGroup> groups)
        {
            List<RequirementGroupModel> models = new();

            foreach (var group in groups)
            {
                var model = new RequirementGroupModel
                {
                    RequirementGroupGuid = group.RequirementGroupGuid,
                    GroupName = group.GroupName,
                    CoverageIssueGuids = group.CoverageIssueGuids
                };
                models.Add(model);
            }
            return models;
        }

        public static RequirementGroup ToRequirementGroup(this RequirementGroupModel model)
        {
            if (model != null)
            {
                return new RequirementGroup
                {
                    RequirementGroupGuid = model.RequirementGroupGuid,
                    GroupName = model.GroupName,
                    CoverageIssueGuids = model.CoverageIssueGuids
                };
            }

            return new RequirementGroup();
        }
    }
    
}
