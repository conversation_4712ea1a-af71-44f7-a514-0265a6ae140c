namespace PolicyTrackerBlazor.Shared.Models
{
    public class AccessTokenModel
    {
        public Guid AccountGuid { get; set; }
        public string AccessToken { get; set; }
        public string Type { get; set; }
        public DateTime ExpirationDate { get; set; }
        public string RefreshToken { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public List<string> Roles { get; set; }

        public AccessTokenModel()
        {

        }
    }
}
