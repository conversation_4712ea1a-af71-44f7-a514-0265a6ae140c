using ORMStandard.HelperClasses;
using SD.LLBLGen.Pro.ORMSupportClasses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UcpmApi.Query.Carrier;
using UcpmApi.Shared.Carrier;

namespace UcpmApi.Command.Carrier
{
    public class CarrierSubmissionIssueOverrideCommand : BaseCommand
    {
        private static IErrorLogger _errorLogger;
        private static IQuickAdapterAsync _quickAdapterAsync;
        private static CarrierSubmissionIssueOverrideQuery _carrierSubmissionIssueOverrideQuery;

        public CarrierSubmissionIssueOverrideCommand(IErrorLogger errorLogger, IQuickAdapterAsync quickAdapterAsync, CarrierSubmissionIssueOverrideQuery carrierSubmissionIssueOverrideQuery)
        {
            _errorLogger = errorLogger;
            _quickAdapterAsync = quickAdapterAsync;
            _carrierSubmissionIssueOverrideQuery = carrierSubmissionIssueOverrideQuery;
        }

        public async Task<bool> AddCarrierSubmissionIssueOverrides(List<CarrierSubmissionIssueOverride> overridesToAdd)
        {
            UnitOfWork2 work = new();

            foreach (CarrierSubmissionIssueOverride issueOverride in overridesToAdd)
            {
                CarrierSubmissionIssueOverrideEntity entity = new()
                {
                    CarrierSubmissionGuid = issueOverride.CarrierSubmissionGuid,
                    CoverageIssueGuid = issueOverride.CoverageIssueGuid,
                    CreatedByEmployeeGuid = issueOverride.CreatedByEmployeeGuid,
                    RecordCreatedZoned = issueOverride.RecordCreatedZoned,
                    NewCoverageIssueStatusGuid = issueOverride.NewCoverageIssueStatusGuid,
                    ReasonOverridden = issueOverride.ReasonOverridden,
                    CustomIssueStatus = issueOverride.CustomIssueStatus
                };

                work.AddForSave(entity);
            }

            int result = await _quickAdapterAsync.CommitWorkAsync(work);
            return result > 0;
        }

        public async Task<bool> UpdateCarrierSubmissionIssueOverrides(Guid carrierSubmissionGuid, List<CarrierSubmissionIssueOverride> overridesToSave)
        {
            EntityCollection<CarrierSubmissionIssueOverrideEntity> entitiesToDelete = await _carrierSubmissionIssueOverrideQuery.GetByCarrierSubmission(carrierSubmissionGuid);

            UnitOfWork2 work = new();

            if (entitiesToDelete.Any())
            {
                foreach (CarrierSubmissionIssueOverrideEntity entity in entitiesToDelete)
                {
                    work.AddForDelete(entity);
                }

                await _quickAdapterAsync.CommitWorkAsync(work);
            }

            bool result = await AddCarrierSubmissionIssueOverrides(overridesToSave);
            return (result || !overridesToSave.Any()) ;
        }
    }
}
