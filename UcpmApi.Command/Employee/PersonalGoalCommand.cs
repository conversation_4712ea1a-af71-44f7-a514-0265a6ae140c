using LLBLGenHelper;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using UcpmApi.Logging;
using UcpmApi.Shared;

namespace UcpmApi.Command.Employee
{
    public class PersonalGoalCommand : BaseCommand
    {

        private static IErrorLogger _errorLogger;

        public PersonalGoalCommand(IErrorLogger errorLogger)
        {

            _errorLogger = errorLogger;
        }
        public async Task<bool> AddGoal(PersonalGoal goal)
        {
            PersonalGoalEntity goalEntity = new()
            {
                EmployeeGuid = goal.EmployeeGuid,
                StartDateZoned = goal.StartDateZoned,
                EndDateZoned = goal.EndDateZoned,
                GoalTypeId = goal.GoalTypeId,
                GoalTarget = goal.GoalTarget
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            return await quickAdapter.SaveEntityAsync(goalEntity, false);

        }
    }
}
