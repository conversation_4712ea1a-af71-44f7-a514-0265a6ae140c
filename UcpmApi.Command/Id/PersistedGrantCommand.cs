using UcpmApi.Shared.Id;
using UcpmTools;

namespace UcpmApi.Command.Id
{
    public class PersistedGrantCommand : BaseCommand
    {
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        private readonly IQuickAdapterAsync _quickAdapterAsync;

        public PersistedGrantCommand(IQuickAdapterAsync quickAdapterAsync)
        {
            _quickAdapterAsync = quickAdapterAsync;
        }

        public async Task<bool> AddPersistedGrant(PersistedGrant grant)
        {
            IdPersistedGrantEntity existingGrant = _linqMetaData.IdPersistedGrant
                .Where(pg => pg.Key == grant.Key && pg.SubjectId == grant.SubjectId && pg.ClientId == grant.ClientId)
                .SingleOrDefault();
            if (existingGrant == null || existingGrant.IsNew)
            {
                existingGrant = new IdPersistedGrantEntity();
            }

            QuickReflection.CopyProps(grant, existingGrant);

            return await _quickAdapterAsync.SaveEntityAsync(existingGrant);
        }

        public async Task<bool> UpdatePersistedGrant(PersistedGrant grant)
        {
            IdPersistedGrantEntity existingGrant = _linqMetaData.IdPersistedGrant
                .Where(pg => pg.Key == grant.Key && pg.SubjectId == grant.SubjectId && pg.ClientId == grant.ClientId)
                .SingleOrDefault();
            if (existingGrant == null || existingGrant.IsNew)
            {
                return false;
            }

            QuickReflection.CopyProps(grant, existingGrant);

            return await _quickAdapterAsync.SaveEntityAsync(existingGrant);
        }
    }
}
