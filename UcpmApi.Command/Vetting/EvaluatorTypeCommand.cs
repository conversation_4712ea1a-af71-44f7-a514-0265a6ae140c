using LLBLGenHelper;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using ORMStandard.Linq;
using SD.LLBLGen.Pro.LinqSupportClasses;
using UcpmApi.Logging;
using UcpmApi.Shared;
using UcpmTools;

namespace UcpmApi.Command.Vetting
{
    public class EvaluatorTypeCommand : BaseCommand
    {

        private static IErrorLogger _errorLogger;
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());

        public EvaluatorTypeCommand(IErrorLogger errorLogger)
        {
            _errorLogger = errorLogger;
        }

        public async Task<bool> AddEvaluatorType(EvaluatorType evaluatorType)
        {
            EvaluatorTypeEntity evaluatorTypeEntity = new();
            QuickReflection.CopyProps(evaluatorType, evaluatorTypeEntity);

            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            return await quickAdapter.SaveEntityAsync(evaluatorTypeEntity);
        }

        public async Task<bool> UpdateEvaluatorType(EvaluatorType evaluatorType)
        {
            EvaluatorTypeEntity evaluatorTypeEntity = await _linqMetaData.EvaluatorType
                .SingleOrDefaultAsync(e => e.EvaluatorTypeId == evaluatorType.EvaluatorTypeId);
            if (evaluatorTypeEntity.IsNew)
            {
                return false;
            }

            QuickReflection.CopyProps(evaluatorType, evaluatorTypeEntity);

            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            return await quickAdapter.SaveEntityAsync(evaluatorTypeEntity);
        }
    }
}
