using UcpmApi.Shared.Coverage;

namespace UcpmApi.Command.Coverage
{
    public class DcatTemplateCommand : BaseCommand
    {
        private static IErrorLogger _errorLogger;
        private readonly IQuickAdapterAsync _quickAdapterAsync;
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());

        public DcatTemplateCommand(IErrorLogger errorLogger, IQuickAdapterAsync quickAdapterAsync) 
        {
            _errorLogger = errorLogger;
            _quickAdapterAsync = quickAdapterAsync;
        }

        public DcatTemplateCommand() 
        {
        }

        public virtual async Task<bool> AddDcatTemplate(DcatTemplate dcatTemplate)
        {
            DcatTemplateEntity entity = new ()
            {
                HtmlTemplate = dcatTemplate?.HtmlTemplate ?? string.Empty,
                FutureHtmlTemplate = dcatTemplate?.FutureHtmlTemplate  ?? string.Empty
            };

            return await _quickAdapterAsync.SaveEntityAsync(entity, false);
        }

        public virtual async Task<bool> UpdateDcatTemplate(DcatTemplate dcatTemplate)
        {
            if (dcatTemplate.DcatTemplateId == 0)
            {
                return false;
            }

            DcatTemplateEntity entity = _linqMetaData.DcatTemplate.SingleOrDefault(s => s.DcatTemplateId == dcatTemplate.DcatTemplateId);

            if (entity == null)
            {
                return false;
            }

            entity.HtmlTemplate = dcatTemplate?.HtmlTemplate ?? string.Empty;
            entity.FutureHtmlTemplate = dcatTemplate?.FutureHtmlTemplate ?? string.Empty;

            return await _quickAdapterAsync.SaveEntityAsync(entity, false);
        }

    }
}
