using UcpmApi.Shared.Sync;
using UcpmTools;

namespace UcpmApi.Command.Sync
{
    public class SyncJobCommand : BaseCommand
    {
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        IErrorLogger _errorLogger;

        public SyncJobCommand(IErrorLogger errorLogger)
        {
            _errorLogger = errorLogger;
        }

        public async Task<bool> CreateSyncJob(SyncJob syncJob)
        {
            SyncJobEntity syncJobEntity = new SyncJobEntity();
            QuickReflection.CopyProps(syncJob, syncJobEntity);
            return await _linqMetaData.AdapterToUse.SaveEntityAsync(syncJobEntity);
        }

    }
}
