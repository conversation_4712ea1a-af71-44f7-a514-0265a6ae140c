using CsvHelper.Configuration;
using ORMStandard.DatabaseSpecific;
using ORMStandard.Linq;
using System;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;

namespace UcpmApi.Command.Sync
{
    public class SyncAgentCommand : BaseCommand
    {
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        IErrorLogger _errorLogger;

        public SyncAgentCommand(IErrorLogger errorLogger)
        {
            _errorLogger = errorLogger;
        }

        public async Task<bool> BulkUpdate(string sourceCsv)
        {
            StreamReader reader = new(sourceCsv);
            CsvConfiguration configuration = new(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
                Delimiter = ",",
                TrimOptions = TrimOptions.Trim,
                IgnoreBlankLines = true,
            };

            using CsvHelper.CsvReader csv = new(reader, configuration);
            await csv.ReadAsync();
            csv.ReadHeader();

            DataTable dt = new()
            {
                Columns = {
                    new DataColumn("AgentGuid", typeof(Guid)),
                    new DataColumn("IdentifierOnTarget", typeof(string)),
                    new DataColumn("LastLocalChange", typeof(DateTimeOffset)),
                    new DataColumn("LastSentToExternal", typeof(DateTimeOffset)),
                    new DataColumn("LastRemoteChange", typeof(DateTimeOffset)),
                    new DataColumn("LastReceivedFromExternal", typeof(DateTimeOffset)),
                }
            };
            DateTimeOffset now = DateTimeOffset.Now;

            IEnumerable<AgentData> records = csv.GetRecords<AgentData>();
            foreach (AgentData record in records)
            {
                Guid companyGuid;
                if (Guid.TryParse(record.Agent_Guid, out companyGuid))
                {
                    dt.Rows.Add(
                        companyGuid,
                        record.Id,
                        now,
                        now,
                        now,
                        now
                    );
                }
            }

            const string truncateSql = @"truncate table Sync.SyncAgent";
            using SqlConnection connection = new(_linqMetaData.AdapterToUse.ConnectionString);
            connection.Open();

            using SqlCommand command = new(truncateSql, connection);
            command.ExecuteNonQuery();


            using SqlBulkCopy bulkCopy = new(connection)
            {
                DestinationTableName = "Sync.SyncAgent"
            };
            {
                await bulkCopy.WriteToServerAsync(dt);
            }

            connection.Close();


            return true;

        }

    }

    public record AgentData(string Agent_Guid, string Id);
}
