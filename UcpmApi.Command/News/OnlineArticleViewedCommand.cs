using ORMStandard.HelperClasses;
using UcpmApi.Shared.News;

namespace UcpmApi.Command.News
{
    public class OnlineArticleViewedCommand : BaseCommand
    {
        private static IErrorLogger _errorLogger;
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        private readonly IQuickAdapterAsync _quickAdapterAsync;

        public OnlineArticleViewedCommand(IErrorLogger errorLogger, IQuickAdapterAsync quickAdapterAsync)
        {
            _errorLogger = errorLogger;
            _quickAdapterAsync = quickAdapterAsync;
        }

        public async Task<bool> CreateOnlineArticleViewEntry(Guid accountGuid, Guid onlineArticleGuid)
        {
            OnlineArticleViewedEntity viewedArticle = _linqMetaData.OnlineArticleViewed
                .SingleOrDefault(a => a.AccountGuid == accountGuid && a.OnlineArticleGuid == onlineArticleGuid);

            if (viewedArticle == null || viewedArticle.OnlineArticleGuid == Guid.Empty)
            {
                viewedArticle = new OnlineArticleViewedEntity()
                {
                    OnlineArticleGuid = onlineArticleGuid,
                    AccountGuid = accountGuid,
                    FirstViewedZoned = DateTimeOffset.Now
                };

                return await _quickAdapterAsync.SaveEntityAsync(viewedArticle);
            }

            return false;
        }

        public async Task CreateCollectionOfOnlineArticleViewed(Guid accountGuid,
            EntityCollection<OnlineArticleEntity> unreadArticles)
        {
            EntityCollection<OnlineArticleViewedEntity> onlineArticlesViewed = [];

            foreach (OnlineArticleEntity article in unreadArticles)
            {
                onlineArticlesViewed.Add(
                    new OnlineArticleViewedEntity()
                    {
                        OnlineArticleGuid = article.OnlineArticleGuid,
                        AccountGuid = accountGuid,
                        FirstViewedZoned = DateTimeOffset.Now,
                    });
            }

            await _quickAdapterAsync.SaveEntityCollectionAsync(onlineArticlesViewed, false, false);
        }

        public async Task<bool> AddViewedArticlesFromObjects(IEnumerable<OnlineArticleViewed> articles)
        {
            EntityCollection<OnlineArticleViewedEntity> onlineArticlesViewed = [];

            foreach (OnlineArticleViewed article in articles)
            {
                onlineArticlesViewed.Add(
                    new OnlineArticleViewedEntity()
                    {
                        OnlineArticleGuid = article.OnlineArticleGuid,
                        AccountGuid = article.AccountGuid,
                        FirstViewedZoned = article.FirstViewedZoned,
                    });
            }

            return await _quickAdapterAsync.SaveEntityCollectionAsync(onlineArticlesViewed, false, false) > 0;
        }

        public async Task<bool> BookmarkArticle(Guid accountGuid, Guid onlineArticleGuid)
        {
            OnlineArticleAgentSavedEntity bookmarkedArticle = new OnlineArticleAgentSavedEntity()
            {
                OnlineArticleGuid = onlineArticleGuid,
                AgentGuid = accountGuid,
            };

            return await _quickAdapterAsync.SaveEntityAsync(bookmarkedArticle);
        }
        public async Task<bool> RemoveBookmark(Guid accountGuid, Guid onlineArticleGuid)
        {
            OnlineArticleAgentSavedEntity bookmarkedArticle = _linqMetaData.OnlineArticleAgentSaved
                .SingleOrDefault(a => a.AgentGuid == accountGuid && a.OnlineArticleGuid == onlineArticleGuid);

            if (bookmarkedArticle != null && bookmarkedArticle.OnlineArticleGuid != Guid.Empty)
            {
                return await _quickAdapterAsync.DeleteEntityAsync(bookmarkedArticle);
            }

            return false;
        }
    }
}

