using UcpmApi.Shared.SurplusLines;

namespace UcpmApi.Command.SurplusLines
{
    public class SurplusLinesStateRequirementYearCommand : BaseCommand
    {
        private static IErrorLogger _errorLogger;
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        private readonly IQuickAdapterWriter _quickAdapterWriter;

        public SurplusLinesStateRequirementYearCommand(IErrorLogger errorLogger, IQuickAdapterWriter quickAdapterWriter)
        {
            _errorLogger = errorLogger;
            _quickAdapterWriter = quickAdapterWriter;
        }

        public async Task<bool> SaveSurplusLinesStateRequirementYear(
            SurplusLinesStateRequirementYear surplusLinesStateRequirementYear)
        {
            try
            {
                SurplusLinesStateRequirementYearEntity surplusLinesStateRequirementYearEntity =
                    new(surplusLinesStateRequirementYear.SurplusLinesStateRequirementYearGuid)
                    {
                        Note = surplusLinesStateRequirementYear.Note,
                        YearNumber = surplusLinesStateRequirementYear.YearNumber,
                        SurplusLinesStateRequirementGuid =
                            surplusLinesStateRequirementYear.SurplusLinesStateRequirementGuid,
                        Description = surplusLinesStateRequirementYear.Description
                    };
                return await _quickAdapterWriter.SaveEntityAsync(surplusLinesStateRequirementYearEntity);
            }
            catch (Exception ex)
            {
                _errorLogger.LogError(ex,
                    $"Failed to save SurplusLinesStateRequirementYear with SurplusLinesStateRequirementYearGuid: {surplusLinesStateRequirementYear.SurplusLinesStateRequirementYearGuid}"
                    , "SurplusLinesStateRequirementYearCommand", "SaveSurplusLinesStateRequirementYear");
                return false;
            }
        }
        
        public async Task<bool> UpdateSurplusLinesStateRequirementYear(
            SurplusLinesStateRequirementYear surplusLinesStateRequirementYear)
        {
            try
            {
                SurplusLinesStateRequirementYearEntity surplusLinesStateRequirementYearEntity = _linqMetaData
                    .SurplusLinesStateRequirementYear
                    .SingleOrDefault(i =>
                        i.SurplusLinesStateRequirementYearGuid ==
                        surplusLinesStateRequirementYear.SurplusLinesStateRequirementYearGuid);
                if (surplusLinesStateRequirementYearEntity == null) return false;
                surplusLinesStateRequirementYearEntity.Note = surplusLinesStateRequirementYear.Note;
                surplusLinesStateRequirementYearEntity.YearNumber =
                    surplusLinesStateRequirementYear.YearNumber;
                return await _quickAdapterWriter.SaveEntityAsync(surplusLinesStateRequirementYearEntity);
            }
            catch (Exception ex)
            {
                _errorLogger.LogError(ex,
                    $"Failed to update SurplusLinesStateRequirementYear with SurplusLinesStateRequirementYearGuid: {surplusLinesStateRequirementYear.SurplusLinesStateRequirementYearGuid}"
                    , "SurplusLinesStateRequirementYearCommand", "UpdateSurplusLinesStateRequirementYear");
                return false;
            }
        }
    }
}