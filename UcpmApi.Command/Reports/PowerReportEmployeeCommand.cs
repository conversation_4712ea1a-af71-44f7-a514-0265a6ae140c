using LLBLGenHelper;
using ORMStandard.HelperClasses;
using SD.LLBLGen.Pro.ORMSupportClasses;
using UcpmApi.Logging;
using UcpmApi.Query.Reports;

namespace UcpmApi.Command.Reports;

public class PowerReportEmployeeCommand : BaseCommand
{
    private readonly IQuickAdapterAsync _quickAdapterAsync;
    private readonly PowerReportEmployeeQuery _powerReportEmployeeQuery;

    public PowerReportEmployeeCommand(IQuickAdapterAsync quickAdapterAsync, PowerReportEmployeeQuery powerReportEmployeeQuery)
    {
        _quickAdapterAsync = quickAdapterAsync;
        _powerReportEmployeeQuery = powerReportEmployeeQuery;
    }

    public async Task<bool> AddNewPowerReportSelectionsForAccount(IEnumerable<Guid> reportsToAdd, Guid accountGuid)
    {
        UnitOfWork2 work = new();
        foreach (var reportGuid in reportsToAdd)
        {
            PowerReportEmployeeEntity connectionEntity = new()
            {
                PowerReportGuid = reportGuid,
                EmployeeGuid = accountGuid
            };
            work.AddForSave(connectionEntity);
        }

        int affectedRows = await _quickAdapterAsync.CommitWorkAsync(work);
        return affectedRows != 0;
    }
    
    public async Task<bool> RemoveOldPowerReportSelectionsForAccount(List<Guid> removeGuids, Guid accountGuid)
    {
        EntityCollection<PowerReportEmployeeEntity> reportsToRemove = await _powerReportEmployeeQuery.GetPowerReportEmployeesForAccount(accountGuid, removeGuids);

        int deletedCount = await _quickAdapterAsync.DeleteEntityCollectionAsync(reportsToRemove);
        return deletedCount != 0;
    }
}