namespace UcpmApi.Command.Lookups
{
    public class FeatureGroupCommand : BaseCommand
    {
        private static IErrorLogger _errorLogger;
        private readonly IQuickAdapterAsync _quickAdapterAsync;

        public FeatureGroupCommand(IErrorLogger errorLogger, IQuickAdapterAsync quickAdapterAsync)
        {
            _errorLogger = errorLogger;
            _quickAdapterAsync = quickAdapterAsync;
        }
    }
}