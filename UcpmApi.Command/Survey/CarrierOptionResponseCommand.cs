using LLBLGenHelper.Interfaces;
using ORMStandard.DatabaseSpecific;
using ORMStandard.Linq;
using System;
using LLBLGenHelper.Interfaces;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace UcpmApi.Command.Survey
{
    public class CarrierOptionResponseCommand : BaseCommand
    {
        private static IErrorLogger _errorLogger;
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());

        public CarrierOptionResponseCommand( IErrorLogger errorLogger)
        {            
            _errorLogger = errorLogger;
        }
    }
}
