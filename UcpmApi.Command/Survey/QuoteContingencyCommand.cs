using ORMStandard.EntityClasses;
using ORMStandard.HelperClasses;

namespace UcpmApi.Command.Survey;

public class QuoteContingencyCommand : BaseCommand
{
    private readonly IQuickAdapterAsync _quickAdapterAsync;

    public QuoteContingencyCommand(IQuickAdapterAsync quickAdapterAsync)
    {
        _quickAdapterAsync = quickAdapterAsync;
    }

    /// <summary>
    /// Adds quote contingencies to a new survey version.
    /// </summary>
    /// <param name="oldVersionWithLinkerQuestions">The old survey version.</param>
    /// <param name="questions">The questions for the survey.</param>
    /// <param name="newSurveyDefinitionVersionGuid">The ID of the new survey version.</param>
    public async Task AddQuoteContingencies(SurveyDefinitionVersionEntity oldVersionWithLinkerQuestions,
        EntityCollection<QuestionEntity> questions, Guid newSurveyDefinitionVersionGuid)
    {
        foreach (QuoteContingencyEntity quoteContingency in oldVersionWithLinkerQuestions.QuoteContingency)
        {
            QuoteContingencyEntity newQuoteContingency = new();
            _quickAdapterAsync.CopyEntityFieldValues(quoteContingency, newQuoteContingency);
            newQuoteContingency.SurveyDefinitionVersionGuid = newSurveyDefinitionVersionGuid;
            newQuoteContingency.QuoteContingencyGuid = Guid.NewGuid();
            await _quickAdapterAsync.SaveEntityAsync(newQuoteContingency, true, false);
            foreach (QuoteContingencyQuestionEntity quoteContingencyQuestion in quoteContingency
                         .QuoteContingencyQuestion)
            {
                QuoteContingencyQuestionEntity newQuoteContingencyQuestion = new();
                _quickAdapterAsync.CopyEntityFieldValues(quoteContingencyQuestion, newQuoteContingencyQuestion);
                newQuoteContingencyQuestion.QuoteContingencyGuid = newQuoteContingency.QuoteContingencyGuid;
                QuestionEntity newQuestion =
                    questions.SingleOrDefault(q => q.DataName == quoteContingencyQuestion.Question.DataName);
                newQuoteContingencyQuestion.QuestionGuid = newQuestion.QuestionGuid;
                await _quickAdapterAsync.SaveEntityAsync(newQuoteContingencyQuestion);
            }
        }
    }
}