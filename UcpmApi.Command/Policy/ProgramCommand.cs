using ORMStandard.DatabaseSpecific;
using ORMStandard.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UcpmApi.Query.Policy;
using UcpmApi.Shared.Policy;
using UcpmTools;

namespace UcpmApi.Command.Policy
{
    public class ProgramCommand : BaseCommand
    {
        private static IErrorLogger _errorLogger;
        private static IQuickAdapterAsync _quickAdapterAsync;
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());
        private static ProgramQuery _programQuery;

        public ProgramCommand(IErrorLogger errorLogger, IQuickAdapterAsync quickAdapterAsync, ProgramQuery programQuery)
        {
            _errorLogger = errorLogger;
            _quickAdapterAsync = quickAdapterAsync;
            _programQuery = programQuery;
        }

        public async Task<bool> UpdateProgram(ProgramModel program) 
        { 
            ProgramEntity existingProgram =  _programQuery.GetProgram(program.ProgramGuid);
            if (existingProgram == null) { return false; }

            QuickReflection.CopyProps(program, existingProgram);
            await _quickAdapterAsync.SaveEntityAsync(existingProgram);
            return true;
        }

    }
}
