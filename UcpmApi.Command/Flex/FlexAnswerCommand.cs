using FlexLibrary;
using UcpmApi.Shared;

namespace UcpmApi.Command.Flex
{
    public class FlexAnswerCommand : BaseCommand
    {

        private static IErrorLogger _errorLogger;
        private readonly LinqMetaData _linqMetaData = new(new DataAccessAdapter());

        public FlexAnswerCommand(IErrorLogger errorLogger)
        {

            _errorLogger = errorLogger;
        }

        public FlexAnswer CreateFlexAnswerFromQuestion(FlexQuestion question, FlexAnswer potentialReference = null, int rowNumber = 0, string answer = "")
        {
            FlexAnswer toReturn = new()
            {
                QuestionDataName = question.DataName,
                RowNumber = rowNumber,
                FlexResponseGuid = Guid.Empty,
                SavedBySecurityAccountGuid = Guid.Empty,
                ChildAnswers = new(),
                Answer = answer,
            };

            if (potentialReference != null)
            {
                toReturn.FlexResponseGuid = potentialReference.FlexResponseGuid;
                toReturn.SavedBySecurityAccountGuid = potentialReference.SavedBySecurityAccountGuid;
            }
            return toReturn;
        }

        public FlexAnswer CreateFlexAnswerFromQuestion(FlexQuestion question, Guid flexResponseGuid, Guid SaveBySecurityAccountGuid, int rowNumber = 0, string answer = "")
        {
            FlexAnswer toReturn = CreateFlexAnswerFromQuestion(question, null, rowNumber, answer);
            toReturn.FlexResponseGuid = flexResponseGuid;
            toReturn.SavedBySecurityAccountGuid = SaveBySecurityAccountGuid;

            return toReturn;
        }

        public string ProcessFlexQuestionDefaultOrCalc(FlexQuestion question, VariableHolder variableHolder)
        {
            if (string.IsNullOrEmpty(question.DefaultOrCalc))
            {
                Console.WriteLine("THE QUESTION PASSED DOES NOT HAVE A DEFAULToRcALC FIELD - Note from programmer.");
                return "";
            }

            string toReturn = "";

            switch (question.DataType)
            {
                case FlexDataTypeEnum.String:
                    break;
                case FlexDataTypeEnum.Number:
                    break;
                case FlexDataTypeEnum.Money:
                    break;
                case FlexDataTypeEnum.Date:
                    toReturn = FlexHelp.ProcessDateTime(question.DefaultOrCalc, variableHolder).ToString("yyyy-MM-dd");
                    break;
                case FlexDataTypeEnum.Body:
                    break;
                case FlexDataTypeEnum.Memo:
                    break;
                case FlexDataTypeEnum.Boolean:
                    break;
                case FlexDataTypeEnum.Percentage:
                    break;
                case FlexDataTypeEnum.Combobox:
                    break;
                case FlexDataTypeEnum.DataGrid:
                    break;
                case FlexDataTypeEnum.ZipCode:
                    break;
                case FlexDataTypeEnum.Video:
                    break;
                case FlexDataTypeEnum.MatchMyCoverage:
                    break;
            }

            return toReturn;
        }

    }
}
