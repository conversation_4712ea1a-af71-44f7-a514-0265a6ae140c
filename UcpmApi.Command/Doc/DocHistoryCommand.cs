using LLBLGenHelper;
using ORMStandard.DatabaseSpecific;
using ORMStandard.EntityClasses;
using UcpmApi.Logging;

namespace UcpmApi.Command.Doc
{
    public class DocHistoryCommand : BaseCommand
    {

        private static IErrorLogger _errorLogger;
        public DocHistoryCommand(IErrorLogger errorLogger)
        {

            _errorLogger = errorLogger;
        }
        public async Task AddHistory(DocEntity doc, Guid savedByEmployeeGuid, long sizeInBytes)
        {
            DocHistoryEntity history = new()
            {
                DocHistoryGuid = Guid.NewGuid(),
                DocGuid = doc.DocGuid,
                SizeInBytes = sizeInBytes,
                SavedAtZoned = DateTimeOffset.Now,
                SavedByEmployeeGuid = savedByEmployeeGuid
            };
            QuickAdapterAsync quickAdapter = new(new DataAccessAdapter(), new DataLogger());
            await quickAdapter.SaveEntityAsync(history);
        }
    }
}
