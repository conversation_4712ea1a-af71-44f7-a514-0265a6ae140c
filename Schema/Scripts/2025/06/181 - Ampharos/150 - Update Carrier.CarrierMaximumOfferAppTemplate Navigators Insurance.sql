/*
* Update [Carrier].[CarrierMaximumOfferAppTemplate] Navigators Insurance
*/

IF EXISTS (
	SELECT 1 
	FROM [Carrier].[CarrierMaximumOfferAppTemplate]
	WHERE CarrierMaximumOfferGuid = '56EB42EF-8917-4350-89A3-E9A69D6BE883'
)
BEGIN
	UPDATE [Carrier].[CarrierMaximumOfferAppTemplate]
	SET AppTemplate = '<!DOCTYPE html>
<html>

<head>
    <title>Navigators App</title>
    <style>
        body {
            font-family: Verdana, sans-serif;
            font-size: 8pt;
        }

        .marginal-boundary {
            margin: 5%;
        }

        table.grid {
            width: 100%;
            border-collapse: collapse;
            padding-top: 10px;
        }

        table.grid th {
            border: 1px solid black;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
            font-weight: normal;
            background-color: #DEEAF6;
        }

        table.grid td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
            vertical-align: middle;
        }

        .single-line {
            width: 100%;
        }

        .single-line td:first-child {
            white-space: nowrap;
        }

        .single-line td:last-child {
            width: 100%;
            border-bottom: 1px solid black;
            position: relative;
        }

        .single-line td:last-child input {
            font-size: 8pt;
            width: 100%;
            border: none;
            outline: none;
            box-sizing: border-box;
        }

        .single-line .underline {
            display: inline-block;
            width: 100%;
            border-bottom: 1px solid black;
        }

        .mult-label-input {
            width: 100%;
        }

        .mult-label-input td.label {
            white-space: nowrap;
            padding-left: 10px;
        }

        .mult-label-input td.label:first-child {
            padding-left: 0;
        }


        .mult-label-input td.input-cell {
            border-bottom: 1px solid black;
            position: relative;
        }

        .mult-label-input td.input-cell input {
            font-size: 8pt;
            width: 100%;
            border: none;
            outline: none;
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        }

        div.spacer {
            width: 100%;
            height: 15px;
        }

        div.large-blue-text {
            text-align: left;
            font-size: 10pt;
            color: #2E74B5;
        }

        div.backBlue-text {
            text-align: center;
            background-color: #DEEAF6;
            width: 100%;
            padding: 5px 0px;
        }

        .full-width-input {
            font-size: 8pt;
            width: 100%;
            border: none;
            outline: none;
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        }

        .same-line-container {
            display: flex;
            align-items: center;
        }

        .same-line-container span {
            padding: 0 5px;
        }


        .light-gray-background {
            background-color: lightgray;
        }

        .warnings-warranty {
            font-size: 7.5pt;
            text-align: justify;
        }

        @media print {
            .break-after {
                page-break-after: always;
            }

            table {
                page-break-inside: avoid;
            }

            .section {
                page-break-inside: avoid;
            }

            .no-repeat {
                display: table-row-group;
            }
        }
    </style>
</head>

<body>
    <div class="marginal-boundary">
        <table style="width: 100%;">
            <tbody>
                <tr>
                    <td style="width: 40%;">
                        <img style="width: 100%; height: auto;"
                            src="https://ucpmpublic.blob.core.windows.net/ucpmpublic/Production/fd/be/11ff/a7564199b082d3ecf045c7c8/Logo.png" />
                    </td>
                    <td style="font-size: 11pt; text-align: right;">
                        CONTRACTOR''S POLLUTION LIABILITY
                        <div style="height: 10px;"></div>
                        APPLICATION
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="spacer"></div>
        <div class="large-blue-text">SECTION 1 - APPLICATION INFORMATION</div>
        <div class="spacer"></div>
        <table class="single-line">
            <tbody>
                <tr>
                    <td>Applicant (Full Legal Name): </td>
                    <td>
                        <input type="text" id="{Flex.InsuredName}" value="{Flex.InsuredName}" />
                    </td>
                </tr>
                <tr>
                    <td>Physical Address of Applicant: </td>
                    <td>
                        <input type="text" id="{Flex.InsuredFullAddress}" value="{Flex.InsuredFullAddress}" />
                    </td>
                </tr>
                <tr>
                    <td>Mailing Address of Applicant: </td>
                    <td>
                        <input class="mailingAddress" type="text" id="{Flex.InsuredMailedAddress}"
                            value="{Flex.InsuredMailedAddress}" />
                    </td>
                </tr>
            </tbody>
        </table>
        <table class="mult-label-input">
            <tbody>
                <tr>
                    <td class="label">City:</td>
                    <td class="input-cell">
                        <input type="text" id="{Flex.InsuredCity}" value="{Flex.InsuredCity}" />
                    </td>
                    <td class="label">State:</td>
                    <td class="input-cell">
                        <input type="text" id="{Flex.InsuredState}" value="{Flex.InsuredState}" />
                    </td>
                    <td class="label">Zip Code:</td>
                    <td class="input-cell">
                        <input type="text" id="{Flex.InsuredZip}" value="{Flex.InsuredZip}" />
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="spacer"></div>
        <table class="grid">
            <thead>
                <tr>
                    <th>Additional Named Insured''s (if any)</th>
                    <th>Relationship</th>
                </tr>
            </thead>
            <tbody>
                <tr class="additionalInsuredRow">
                    <td style="height: 15px">
                        <input type="text" class="full-width-input" />
                    </td>
                    <td style="height: 15px">
                        <input type="text" class="full-width-input" />
                    </td>
                </tr>
                <tr class="additionalInsuredRow">
                    <td style="height: 15px">
                        <input type="text" class="full-width-input" />
                    </td>
                    <td style="height: 15px">
                        <input type="text" class="full-width-input" />
                    </td>
                </tr>
                <tr class="additionalInsuredRow">
                    <td style="height: 15px">
                        <input type="text" class="full-width-input" />
                    </td>
                    <td style="height: 15px">
                        <input type="text" class="full-width-input" />
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="spacer"></div>
        <div>***If there are more than three Additional Named Insured''s, please attach the schedule.</div>
        <div class="spacer"></div>
        <div class="spacer"></div>
        <div class="backBlue-text">Current Pollution Retroactive Dates (if applicable)</div>
        <div class="spacer"></div>
        <table class="grid">
            <tbody>
                <tr>
                    <td>Contractors Pollution Liability:</td>
                    <td>
                        <input type="text" class="full-width-input light-gray-background"
                            value="{Flex.CalculateCplRetroDate}" id="{Flex.CalculateCplRetroDate}" />
                    </td>
                    <td>Non-Owned Disposal Site Liability:</td>
                    <td>
                        <input type="text" class="full-width-input light-gray-background" value="{Flex.NodsRetroDate}"
                            id="{Flex.NodsRetroDate}" />
                    </td>
                </tr>
                <tr>
                    <td>Mold Liability:</td>
                    <td>
                        <input type="text" class="full-width-input light-gray-background"
                            value="{Flex.CalculateMoldRetroDate}" id="{Flex.CalculateMoldRetroDate}" />
                    </td>
                    <td>Transportation Pollution Liability:</td>
                    <td>
                        <input type="text" class="full-width-input light-gray-background"
                            value="{Flex.CalculateTplRetroactiveDate}" id="{Flex.CalculateTplRetroactiveDate}" />
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="spacer"></div>
        <div class="large-blue-text">SECTION 2 - OPERATIONS</div>
        <div class="spacer"></div>
        <div class="backBlue-text">Prior Revenue</div>
        <div class="spacer"></div>
        <table class="single-line">
            <tbody>
                <tr>
                    <td>1. Prior Year Actual Gross Revenue:</td>
                    <td>
                        <input type="text" id="{Flex.PriorYearRevenues}" value="{Flex.PriorYearRevenues}" />
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="spacer"></div>
        <div class="backBlue-text">Revenue Classification (by type)</div>
        <div class="spacer"></div>
        <table class="grid">
            <tbody>
                <tr>
                    <td>Commercial/Retail:</td>
                    <td>
                        <input type="text" class="full-width-input light-gray-background" data-type="percent"
                            value="{Flex.Com_Percent}" id="{Flex.Com_Percent}" />
                    </td>
                    <td>%</td>
                    <td>Industrial:</td>
                    <td>
                        <input type="text" class="full-width-input light-gray-background" data-type="percent"
                            value="{Flex.Ind_Percent}" id="{Flex.Ind_Percent}" />
                    </td>
                    <td>%</td>
                    <td>Single-Family Residential:</td>
                    <td>
                        <input type="text" class="full-width-input light-gray-background" data-type="percent"
                            value="{Flex.Sin_Percent}" id="{Flex.Sin_Percent}" />
                    </td>
                    <td>%</td>
                </tr>
                <tr>
                    <td>Goverment:</td>
                    <td>
                        <input type="text" class="full-width-input light-gray-background" data-type="percent"
                            value="{Flex.Gov_Percent}" id="{Flex.Gov_Percent}" />
                    </td>
                    <td>%</td>
                    <td>Manufacturing:</td>
                    <td>
                        <input type="text" class="full-width-input light-gray-background" data-type="percent"
                            value="{Flex.Man_Percent}" id="{Flex.Man_Percent}" />
                    </td>
                    <td>%</td>
                    <td>Multi-Family Residential:</td>
                    <td>
                        <input type="text" class="full-width-input light-gray-background" data-type="percent"
                            value="{Flex.Mul_Percent}" id="{Flex.Mul_Percent}" />
                    </td>
                    <td>%</td>
                </tr>
                <tr>
                    <td>Hospitals:</td>
                    <td>
                        <input type="text" class="full-width-input light-gray-background" data-type="percent"
                            value="{Flex.Hos_Percent}" id="{Flex.Hos_Percent}" />
                    </td>
                    <td>%</td>
                    <td>Schools:</td>
                    <td>
                        <input type="text" class="full-width-input light-gray-background" data-type="percent"
                            value="{Flex.Sch_Percent}" id="{Flex.Sch_Percent}" />
                    </td>
                    <td>%</td>
                    <td>Other:</td>
                    <td>
                        <input type="text" class="full-width-input light-gray-background" data-type="percent"
                            value="{Flex.Oth_Percent}" id="{Flex.Oth_Percent}" />
                    </td>
                    <td>%</td>
                </tr>
            </tbody>
        </table>
        <div class="break-after"></div>
        <table class="grid">
            <thead class="no-repeat">
                <tr>
                    <th colspan="3" style="border: none;">Breakout of Projected Revenue (for the Next 12 Months by
                        Operations)</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Contracting Services</td>
                    <td>Projected Gross Receipts</td>
                    <td>% Subcontracted</td>
                </tr>
                <tr>
                    <td>Appliance Installation</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.AI_Revenue}" id="{Flex.AI_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Asbestos / Lead Abatement</td>
                    <td>
                        <div class="same-line-container">                            
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.AL_Revenue}" id="{Flex.AL_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Barrier / Liner Construction</td>
                    <td>
                        <div class="same-line-container">                            
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.BC_Revenue}" id="{Flex.BC_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Carpentry / Framing</td>
                    <td>
                        <div class="same-line-container">                            
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.CF_Revenue}" id="{Flex.CF_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Carpet / Upholstery Cleaning</td>
                    <td>
                        <div class="same-line-container">                            
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.CFCI_Revenue}" id="{Flex.CFCI_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Concrete / Masonry</td>
                    <td>
                        <div class="same-line-container">                           
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.CO_Revenue}" id="{Flex.CO_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Construction Management</td>
                    <td>
                        <div class="same-line-container">                           
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.CM_Revenue}" id="{Flex.CM_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Crime Scene Cleanup</td>
                    <td>
                        <div class="same-line-container">                           
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.CSI_Revenue}" id="{Flex.CSI_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Demolition - Non-Structural</td>
                    <td>
                        <div class="same-line-container">                           
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.DNS_Revenue}" id="{Flex.DNS_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Demolition - Structural (over 3 stories)</td>
                    <td>
                        <div class="same-line-container">                           
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.DS3S_Revenue}" id="{Flex.DS3S_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Demolition - Structural (under 3 stories)</td>
                    <td>
                        <div class="same-line-container">                        
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.DS3U_Revenue}" id="{Flex.DS3U_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Drilling - Non-Environmental</td>
                    <td>
                        <div class="same-line-container">                         
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.DI_Revenue}" id="{Flex.DI_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Drilling - Petroleum Based</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.DRO_Revenue}" id="{Flex.DRO_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Drywall</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.DW_Revenue}" id="{Flex.DW_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Electrical</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.EL_Revenue}" id="{Flex.EL_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Excavation / Grading</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.EG_Revenue}" id="{Flex.EG_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Exterior Insulation (EIFS)</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.EIFS_Revenue}" id="{Flex.EIFS_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Fire Suppression / Sprinklers</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.FSS_Revenue}" id="{Flex.FSS_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Flooring</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.FL_Revenue}" id="{Flex.FL_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>General Contracting</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.GC_Revenue}" id="{Flex.GC_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Glazier / Glass / Window</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.GL_Revenue}" id="{Flex.GL_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>HazMat Clean-Up</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.HZ_Revenue}" id="{Flex.HZ_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Home Building</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.HB_Revenue}" id="{Flex.HB_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Home Heating Oil/Gas Services</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.Home_oil_Revenue}" id="{Flex.Home_oil_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>HVAC / Mechanical / Refrigeration</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.HV_Revenue}" id="{Flex.HV_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Industrial Cleaning</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.IC_Revenue}" id="{Flex.IC_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Insulation</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.IN_Revenue}" id="{Flex.IN_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Landscaping</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.LA_Revenue}" id="{Flex.LA_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Maintenance / Janitorial</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.MA_Revenue}" id="{Flex.MA_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Mold Abatement</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.MD_Revenue}" id="{Flex.MD_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Painting</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.PA_Revenue}" id="{Flex.PA_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Paving / Street / Road</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.PV_Revenue}" id="{Flex.PV_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>PCB Removal / Remediation</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.PCB_Revenue}" id="{Flex.PCB_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Pesticide, Herbicide and Fertilizer (no aerial)</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.PE_Revenue}" id="{Flex.PE_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Pipeline Construction / Repair - Non Oil/Gas</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.PI_Revenue}" id="{Flex.PI_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Pipeline Construction / Repair - Oil/Gas</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.Pipe_oil_Revenue}" id="{Flex.Pipe_oil_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Plastering / Stucco</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.PL_Revenue}" id="{Flex.PL_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Plumbing</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.PU_Revenue}" id="{Flex.PU_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Restoration - Build Back</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.RBB_Revenue}" id="{Flex.RBB_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Restoration - Fire / Water</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.RS_Revenue}" id="{Flex.RS_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Roofing</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.RO_Revenue}" id="{Flex.RO_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Sandblasting</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.SB_Revenue}" id="{Flex.SB_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Sewer / Water Main</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.SE_Revenue}" id="{Flex.SE_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Soil Remediation</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.SO_Revenue}" id="{Flex.SO_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Steel / Metal Erection</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.SERS_Revenue}" id="{Flex.SERS_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Storage Tank Installation / Removal</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.STIR_Revenue}" id="{Flex.STIR_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Swimming Pool Services</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.SWPS_Revenue}" id="{Flex.SWPS_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Tank Cleaning</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.TC_Revenue}" id="{Flex.TC_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Trucking - Hazardous</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.trk_haz_Revenue}" id="{Flex.trk_haz_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Trucking - Non-Hazardous</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.NHT_Revenue}" id="{Flex.NHT_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Trucking - Petroleum Based</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.TPB_Revenue}" id="{Flex.TPB_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Utilities</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.UT_Revenue}" id="{Flex.UT_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Waste Hauling</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.WH_Revenue}" id="{Flex.WH_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Waterproofing</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.WA_Revenue}" id="{Flex.WA_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Weatherization</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.WE_Revenue}" id="{Flex.WE_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Other Services (Please Describe Below)</td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.Other_Revenue}" id="{Flex.Other_Revenue}" />
                        </div>
                    </td>
                    <td>
                        <div class="same-line-container">
                            <input type="text"
                                class="full-width-input light-gray-background" data-type="percent" />
                            <span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <input type="text" id="{Flex.ContractingOperations}"
                            class="full-width-input light-gray-background" value="{Flex.ContractingOperations}" />
                    </td>
                </tr>
                <tr>
                    <td>Total Projected Gross Receipts</td>
                    <td colspan="2">
                        <input type="text" id="{Flex.ProjectedRevenueTableTotal}"
                            class="full-width-input light-gray-background" value="{Flex.ProjectedRevenueTableTotal}" />
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="break-after"></div>
        <div class="large-blue-text">SECTION 3 - PROFESSIONAL LIABILITY</div>
        <div class="spacer"></div>
        <table style="width: 100%;">
            <tbody>
                <tr>
                    <td style="width: 90%;">2. If the applicant is not interested in Professional Liability coverage,
                        please mark N/A</td>
                    <td style="width: 10%;"><input type="checkbox" id="interestedInProfLiabilityCheckbox" /> N/A</td>
                </tr>
                <tr>
                    <td colspan="3">3. What professional services does the applicant provide?<div class="spacer"></div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3"><textarea class="light-gray-background proServicesTextArea" style="width: 100%;"
                            rows="3"></textarea></td>
                </tr>
            </tbody>
        </table>
        <div class="spacer"></div>
        <div class="backBlue-text">Current Professional Retroactive Dates (if applicable)</div>
        <div class="spacer"></div>
        <table class="grid">
            <tbody>
                <tr>
                    <td>Professional Liability: </td>
                    <td><input type="text" class="full-width-input light-gray-background"
                            value="{Flex.PlRetroactiveDateApplicable_1}" id="{Flex.PlRetroactiveDateApplicable_1}" />
                    </td>
                    <td>Rectification/Mitigation: </td>
                    <td><input type="text" class="full-width-input light-gray-background"
                            value="{Flex.RectificationMitigationRetro}" id="{Flex.RectificationMitigationRetro}" /></td>
                </tr>
                <tr>
                    <td>Protective Liability: </td>
                    <td><input type="text" class="full-width-input light-gray-background" value="{Flex.ProtectiveRetro}"
                            id="{Flex.ProtectiveRetro}" /></td>
                </tr>
            </tbody>
        </table>
        <div class="spacer"></div>
        <div class="backBlue-text">Professional Services Breakout (must equal 100%)</div>
        <div class="spacer"></div>
        <table class="grid">
            <tbody>
                <tr>
                    <td>General Contracting Only: </td>
                    <td><input type="text" class="full-width-input light-gray-background" data-type="percent"
                            value="{Flex.GC_Percent}" id="{Flex.GC_Percent}" /></td>
                    <td>%</td>
                    <td>Design for Third Parties Only: </td>
                    <td><input type="text" class="full-width-input light-gray-background" data-type="percent"
                            value="{Flex.D3_Percent}" id="{Flex.D3_Percent}" /></td>
                    <td>%</td>
                </tr>
                <tr>
                    <td>Construction Management - Agency: </td>
                    <td><input type="text" class="full-width-input light-gray-background" data-type="percent"
                            value="{Flex.CMA_Percent}" id="{Flex.CMA_Percent}" /></td>
                    <td>%</td>
                    <td>Design/Build with In-House Design: </td>
                    <td><input type="text" class="full-width-input light-gray-background" data-type="percent"
                            value="{Flex.DB_Percent}" id="{Flex.DB_Percent}" /></td>
                    <td>%</td>
                </tr>
                <tr>
                    <td>Construction Management - At Risk: </td>
                    <td><input type="text" class="full-width-input light-gray-background" data-type="percent"
                            value="{Flex.CMR_Percent}" id="{Flex.CMR_Percent}" /></td>
                    <td>%</td>
                    <td>Design/Build with Subcontracted Design: </td>
                    <td><input type="text" class="full-width-input light-gray-background" data-type="percent"
                            value="{Flex.DS_Percent}" id="{Flex.DS_Percent}" /></td>
                    <td>%</td>
                </tr>
            </tbody>
        </table>
        <div class="spacer"></div>
        <div class="large-blue-text">SECTION 4 - WARRANTY STATEMENTS AND SIGNATURE</div>
        <div class="spacer"></div>
        <table style="width: 100%;">
            <tbody>
                <tr>
                    <td style="width: 85%;">4. Does the applicant perform any geotechnical services?</td>
                    <td style="width: 15%;">
                        {Flex.geotechnicalServices}
                    </td>
                </tr>
                <tr>
                    <td style="width: 85%;">5. Does the applicant transload, load/unload, clean or maintain railcars?
                    </td>
                    <td style="width: 15%;">
                        {Flex.RailCarReferral}
                    </td>
                </tr>
                <tr>
                    <td style="width: 85%;">6. Does the insured perform any services in the Oilfield/Oil and Gas
                        Industry, fracking, or fracking-related transportation services?
                    </td>
                    <td style="width: 15%;">
                        {Flex.PrimarilyOilGasFracking}
                    </td>
                </tr>
                <tr>
                    <td style="width: 85%;">7. Within the past 5 years, has the applicant had a prior pollution or
                        professional claim, suit or notice?
                    </td>
                    <td style="width: 15%;">
                        {Flex.ApplicantPriorPollutionProfessionalClaimSuitNoti}
                    </td>
                </tr>
                <tr>
                    <td style="width: 85%;"></td>
                    <td style="width: 15%;">If yes, please provide details.</td>
                </tr>
                <tr>
                    <td colspan="2">
                        <textarea class="light-gray-background" style="width: 100%;" rows="3"></textarea>
                    </td>
                </tr>
                <tr>
                    <td style="width: 85%;">8. Has the applicant ever had any pollution or professional coverage
                        declined, cancelled or non-renewed?
                    </td>
                    <td style="width: 15%;">
                        {Flex.InsuranceDeclinedCancRenew}
                    </td>
                </tr>
                <tr>
                    <td style="width: 85%;"></td>
                    <td style="width: 15%;">If yes, please provide details.</td>
                </tr>
                <tr>
                    <td colspan="2">
                        <textarea class="light-gray-background" style="width: 100%;" rows="3"></textarea>
                    </td>
                </tr>
                <tr>
                    <td style="width: 85%;">9. Is the applicant aware of or know of any fact, circumstance or situation
                        of an incident/occurrence which may reasonably result in a claim against the applicant/any
                        entity for which coverage is being sought?
                    </td>
                    <td style="width: 15%;">
                        {Flex.Anyknowledge}
                    </td>
                </tr>
                <tr>
                    <td style="width: 85%;"></td>
                    <td style="width: 15%; ">If yes, please provide details.</td>
                </tr>
                <tr>
                    <td colspan="2">
                        <textarea class="light-gray-background" style="width: 100%;" rows="3"></textarea>
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="break-after"></div>
        <div class="warnings-warranty">
            FRAUD WARNINGS:<br /><br />
            Notice to Arkansas and West Virginia Applicants: Any person who knowingly presents a false or fraudulent
            claim for payment of a loss or benefit or knowingly presents false information in an application for
            insurance is guilty of a crime and may be subject to fines and confinement in prison.<br /><br />
            Notice to Colorado Applicants: It is unlawful to knowingly provide false, incomplete, or misleading facts or
            information to an insurance company for the purpose of defrauding or attempting to defraud the company.
            Penalties may include imprisonment, fines, denial of insurance and civil damages. Any insurance company or
            agent of an insurance company who knowingly provides false, incomplete, or misleading facts or information
            to a policyholder or claimant for the purpose of defrauding or attempting to defraud the policyholder or
            claiming with regard to a settlement or award payable for insurance proceeds shall be reported to the
            Colorado Division of Insurance within the Department of Regulatory Agencies.<br /><br />
            Notice to District of Columbia Applicants: WARNING: It is a crime to provide false or misleading information
            to an insurer for the purpose of defrauding the insurer or any other person. Penalties include imprisonment
            and/or fines. In addition, an insurer may deny insurance benefits if false information materially related to
            a claim was provided by the applicant.<br /><br />
            Notice to Florida Applicants: Any person who knowingly and with intent to injure, defraud, or deceive any
            insurance company files a statement of claim containing any false, incomplete, or misleading information is
            guilty of a felony of the third degree.<br /><br />
            Notice to Hawaii Applicants: For your protection, Hawaii law requires you to be informed that presenting a
            fraudulent claim for payment of a loss or benefit is a crime punishable by fines or imprisonment, or
            both.<br /><br />
            Notice to Kentucky Applicants: Any person who knowingly and with intent to defraud any insurance company or
            other person files an application for insurance containing any materially false information or conceals, for
            the purpose of misleading, information concerning any fact material thereto commits a fraudulent insurance
            act, which is a crime.<br /><br />
            Notice to Maine Applicants: It is a crime to knowingly provide false, incomplete or misleading information
            to an insurance company for the purpose of defrauding the company. Penalties may include imprisonment,
            fines, or denial of insurance benefits.<br /><br />
            Notice to Maryland Applicants: Any person who knowingly and willfully presents a false or fraudulent claim
            for payment of a loss or benefit or who knowingly and willfully presents false information in an application
            for insurance is guilty of a crime and may be subject to fines and confinement in prison.<br /><br />
            Notice to New Jersey Applicants: Any person who includes any false or misleading information on an
            application for an insurance policy is subject to criminal and civil penalties.<br /><br />
            Notice to New Mexico Applicants: Any person who knowingly presents a false or fraudulent claim for payment
            of a loss or benefit or knowingly presents false information in an application for insurance is guilty of a
            crime and may be subject to civil fines and criminal penalties. <br /><br />
            Notice to New York Applicants: Any person who knowingly and with intent to defraud any insurance company or
            other person files an application for insurance or statement of claim containing any materially false
            information, or conceals for the purpose of misleading, information concerning any fact material thereto,
            commits a fraudulent insurance act, which is a crime and shall also be subject to a civil penalty not to
            exceed five thousand dollars and the stated value of the claim for each such violation.<br /><br />
            Notice to Ohio Applicants: Any person who, with intent to defraud or knowing that he is facilitating a fraud
            against an insurer, submits an application or files a claim containing a false or deceptive statement is
            guilty of insurance fraud.<br /><br />
            Notice to Oklahoma Applicants: WARNING: Any person who knowingly, and with intent to injure, defraud or
            deceive any insurer, makes any claim for the proceeds of an insurance policy containing any false,
            incomplete or misleading information is guilty of a felony.<br /><br />
            Notice to Oregon Applicants: Any person who, with intent to defraud or knowing that he or she is
            facilitating a fraud against an insurer, submits an application or files a claim containing a false or
            deceptive statement may be guilty of insurance fraud.<br /><br />
            Notice to Pennsylvania Applicants: Any person who knowingly and with intent to defraud any insurance company
            or other person files an application for insurance or statement of claim containing any materially false
            information or conceals for the purpose of misleading, information concerning any fact material thereto
            commits a fraudulent insurance act, which is a crime and subjects such person to criminal and civil
            penalties.<br /><br />
            Notice to Tennessee, Virginia and Washington Applicants: It is a crime to knowingly provide false,
            incomplete or misleading information to an insurance company for the purpose of defrauding the company.
            Penalties include imprisonment, fines and denial of insurance benefits.<br /><br />
            Notice to Vermont Applicants: Any person who knowingly presents a false statement in an application for
            insurance may be guilty of a criminal offense and subject to penalties under state law.<br /><br />
            Notice to Applicants of all other states: Any person who knowingly and with intent to defraud any insurance
            company or other person files an application for insurance or statement of claim containing any materially
            false information or conceals for the purpose of misleading, information concerning any fact material
            thereto, commits a fraudulent insurance act, which is a crime and subjects the person to criminal and civil
            penalties.<br /><br />
            WARRANTY STATEMENT<br /><br />
            The undersigned authorized officer of the Applicant declares that the statements set forth herein are true.
            The undersigned authorized officer agrees that if the information supplied on the application changes
            between the date of the application and the effective date of the insurance, he/she (undersigned) will
            immediately notify the insurer of such changes, and the insurer may withdraw or modify any outstanding
            quotations and/or authorization or agreement to bind the insurance. Signing of this application does not
            bind the Applicant to the insurer to complete the insurance.<br /><br />
            I warrant that the information contained in this application is true and that it will form the basis of and
            be incorporated into the final policy, if issued.<br /><br />
        </div>
        <div class="spacer"></div>
        <div class="backBlue-text">Application Signature</div>
        <div class="spacer"></div>
        <table class="mult-label-input">
            <tbody>
                <tr>
                    <td class="label">Applicant Signature:</td>
                    <td class="input-cell"><input class="signature" type="text" /></td>
                    <td class="label">Date: </td>
                    <td class="input-cell"><input class="date" type="text" /></td>
                </tr>
                <tr>
                    <td class="label">Applicant Print Name:</td>
                    <td class="input-cell"><input class="printName" type="text" /></td>
                    <td class="label">Title:</td>
                    <td class="input-cell"><input class="title" type="text" /></td>
                </tr>
            </tbody>
        </table>
        <div class="break-after"></div>
        <div class="same-line-container">
            <div class="large-blue-text">PROJECT SUPPLEMENTAL</div> <span>(Only complete for project specific
                policies)</span>
        </div>
        <div class="spacer"></div>
        <div class="backBlue-text">Project Information</div>
        <div class="spacer"></div>
        <table class="single-line">
            <tbody>
                <tr>
                    <td>Project / Contract Number: </td>
                    <td><input type="text" id="{Flex.ContractNumber}" value="{Flex.ContractNumber}" /></td>
                </tr>
                <tr>
                    <td>Project Owner: </td>
                    <td><input type="text" id="{Flex.ProjectOwner}" value="{Flex.ProjectOwner}" /></td>
                </tr>
                <tr>
                    <td>Project Name: </td>
                    <td><input type="text" id="{Flex.ProjectName}" value="{Flex.ProjectName}" /></td>
                </tr>
                <tr>
                    <td>Project Address: </td>
                    <td><input type="text" id="{Flex.ProjectLocations}" value="{Flex.ProjectLocations}" /></td>
                </tr>
            </tbody>
        </table>
        <div class="spacer"></div>
        <div class="backBlue-text">Project Requirements</div>
        <div class="spacer"></div>
        <table class="single-line">
            <tbody>
                <tr>
                    <td>Estimated Project Start Date: </td>
                    <td><input class="estimatedStartDate" type="text" /></td>
                </tr>
                <tr>
                    <td>Length of Project: </td>
                    <td><input type="text" id="{Flex.ProjectTermLength}" value="{Flex.ProjectTermLength}" /></td>
                </tr>
                <tr>
                    <td>Completed Operations Period Required: </td>
                    <td><input type="text" id="{Flex.CompletedOperationsPeriod}" value="{Flex.CompletedOperationsPeriod}" /></td>
                </tr>
            </tbody>
        </table>
        <div class="spacer"></div>
        <div class="backBlue-text">Project Specifications</div>
        <div class="spacer"></div>
        <table class="single-line">
            <tbody>
                <tr>
                    <td>Insured''s Contract Gross Revenue / Cost: </td>
                    <td><input type="text" id="{Flex.ProjectedRevenueTableTotal}"
                            value="{Flex.ProjectedRevenueTableTotal}" /></td>
                </tr>
            </tbody>
        </table>
        <div class="spacer"></div>
        <table style="width: 100%;">
            <tbody>
                <tr>
                    <td>Project Description: </td>
                </tr>
                <tr>
                    <td><textarea class="light-gray-background" id="{Flex.ProjectDescription}"
                            value="{Flex.ProjectDescription}" style="width: 100%;" rows="3"></textarea></td>
                </tr>
            </tbody>
        </table>
        <div class="spacer"></div>
        <table class="grid">
            <tbody>
                <tr>
                    <td>Breakout of Contracting Services</td>
                    <td>Projected Gross Recepts</td>
                    <td>% Subcontacted</td>
                </tr>
                <tr>
                    <td><input type="text" class="full-width-input" /></td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="currency" value="" /></div>
                    </td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="percent" /><span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><input type="text" class="full-width-input" /></td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="currency" value="" /></div>
                    </td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="percent" /><span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><input type="text" class="full-width-input" /></td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="currency" value="" /></div>
                    </td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="percent" /><span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><input type="text" class="full-width-input" /></td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="currency" value="" /></div>
                    </td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="percent" /><span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><input type="text" class="full-width-input" /></td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="currency" value="" /></div>
                    </td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="percent" /><span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><input type="text" class="full-width-input" /></td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="currency" value="" /></div>
                    </td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="percent" /><span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><input type="text" class="full-width-input" /></td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="currency" value="" /></div>
                    </td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="percent" /><span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><input type="text" class="full-width-input" /></td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="currency" value="" /></div>
                    </td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="percent" /><span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td><input type="text" class="full-width-input" /></td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="currency" value="" /></div>
                    </td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="percent" /><span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>Other Services (Please Describe Below)</td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="currency" value="" /></div>
                    </td>
                    <td>
                        <div class="same-line-container"><input type="text"
                                class="full-width-input light-gray-background" data-type="percent" /><span>%</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3"><input type="text" class="full-width-input light-gray-background" /></td>
                </tr>
                <tr>
                    <td>Total Projected Gross Receipts</td>
                    <td colspan="2">
                        <div class="same-line-container">
                            <input type="text" class="full-width-input light-gray-background" data-type="currency"
                                value="{Flex.ProjectedRevenueTableTotal}" />
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</body>

</html>'
	WHERE CarrierMaximumOfferGuid = '56EB42EF-8917-4350-89A3-E9A69D6BE883'	
	PRINT 'Successfully updated App Template CarrierMaximumOfferGuid = 56EB42EF-8917-4350-89A3-E9A69D6BE883'
    
END ELSE BEGIN
    PRINT 'Failed to update App Template CarrierMaximumOfferGuid = 56EB42EF-8917-4350-89A3-E9A69D6BE883'
END



-- 150 - Update Carrier.CarrierMaximumOfferAppTemplate Navigators Insurance.sql