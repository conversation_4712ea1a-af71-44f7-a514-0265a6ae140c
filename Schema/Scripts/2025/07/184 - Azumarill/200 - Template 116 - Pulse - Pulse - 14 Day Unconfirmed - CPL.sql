-- This script updates the HTML body template and subject line for a specific DirectSendTemplateVersion in the Mail schema.
DECLARE @VersionGuid UNIQUEIDENTIFIER = CAST('7e309edf-f029-425d-b867-521e0d37cd65' AS UNIQUEIDENTIFIER)

IF EXISTS (SELECT 1 FROM [Mail].[DirectSendTemplateVersion] WHERE [DirectSendTemplateVersionGuid] = @VersionGuid)
    BEGIN
        UPDATE [Mail].[DirectSendTemplateVersion]
        SET
            [HtmlBodyTemplate] = N'<div>
                <p>Hi {ExtraData.AgentName},</p>
                <br>
                <p>
                    Just a friendly reminder that coverage is expiring soon. Once again, click this <a href="{ExtraData.PulseLink}">link</a> to generate an e-sign document for your client to sign and finalize coverage, or to request an incumbent quote. You can also update the revenues if they have changed, and a new set of quotes will be automatically generated and sent to your inbox.
                </p>
                <br>
                <p>
                    Please let me know if you have any questions!
                </p>
                <br>
                <p>Thanks!</p>
                <br>
                <p>{ExtraData.BrokerName}</p>
            </div>',
            [SubjectLine] ='{ExtraData.EffectiveDate} CPL Renewal - {ExtraData.InsuredName} #{ExtraData.PreviousPolicyNumber}',
            [RecordCreatedZoned] = GETDATE(),
            [IsReleased] = 1
        WHERE [DirectSendTemplateVersionGuid] = @VersionGuid

        PRINT 'Template version updated successfully.'
    END
ELSE
    BEGIN
        PRINT  'Template version does not exist. No update performed.'
    END