/*
*  Create News.OnlineArticleClassOfBusinessGroup
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'OnlineArticleClassOfBusinessGroup'
    and TABLES.TABLE_SCHEMA = 'News'
)
begin

    create table News.OnlineArticleClassOfBusinessGroup (
        OnlineArticleClassOfBusinessGroupGuid uniqueidentifier not null
            constraint PK_OnlineArticleClassOfBusinessGroup
            primary key,
        GroupName nvarchar(100) not null
    );

    print 'Created News.OnlineArticleClassOfBusinessGroup'
end else begin

    print 'News.OnlineArticleClassOfBusinessGroup Already Created'

end

-- 110 - Create News.OnlineArticleClassOfBusinessGroup.sql
