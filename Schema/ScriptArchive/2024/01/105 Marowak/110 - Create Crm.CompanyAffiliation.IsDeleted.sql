
/*
*  Create Crm.CompanyAffiliation.IsDeleted
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'CompanyAffiliation'
    and COLUMNS.TABLE_SCHEMA = 'Crm'
    and COLUMNS.COLUMN_NAME = 'IsDeleted'
)
begin

    alter table Crm.CompanyAffiliation 
    add IsDeleted bit not null
        constraint DF_CompanyAffiliation_IsDeleted 
        default 0;
    
    print 'Created Crm.CompanyAffiliation.IsDeleted'
    
end else begin
  
    print 'Crm.CompanyAffiliation.IsDeleted Already Created'

end

exec util_refreshviews;

-- 110 - Create Crm.CompanyAffiliation.IsDeleted.sql
