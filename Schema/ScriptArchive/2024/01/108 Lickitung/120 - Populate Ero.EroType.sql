
/*
*  Populate row Ero.EroType
*/

if not exists (
    select 1
    from Ero.EroType
)
begin

    insert into Ero.EroType
    values 
        (1, 'CPL'),
        (2, 'CPL/PL'),
        (3, 'Insurance Products'),
        (4, 'Property Type'),
        (5, 'Transportation');
    
    print 'Populated Ero.EroType'
end else begin
  
    print 'Ero.EroType. Already Populated'

end

-- 120 - Populate Ero.EroType.sql
