/*
*  Create Sync.SyncInsuredLog
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'SyncInsuredLog'
    and TABLES.TABLE_SCHEMA = 'Sync'
)
begin

    create table Sync.SyncInsuredLog (
        InsuredGuid uniqueidentifier not null
            constraint FK_SyncInsuredLog_Insured
                foreign key references Crm.Insured(InsuredGuid),
        EventZoned datetimeoffset not null,
        EventMessage nvarchar(100) not null,
        EventDetails nvarchar(max) not null
            constraint PK_SyncInsuredLog
            primary key (InsuredGuid, EventZoned)
    );

    print 'Created Sync.SyncInsuredLog'
end else begin

    print 'Sync.SyncInsuredLog Already Created'

end

-- 190 - Create Sync.SyncInsuredLog.sql
