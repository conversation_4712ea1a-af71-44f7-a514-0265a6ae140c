
/*
*  Create Pathfinder.PathfinderSituation.PathfinderSituationTypeId
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'PathfinderSituation'
    and COLUMNS.TABLE_SCHEMA = 'Pathfinder'
    and COLUMNS.COLUMN_NAME = 'PathfinderSituationTypeId'
)
begin

    alter table Pathfinder.PathfinderSituation 
        add PathfinderSituationTypeId int not null
        constraint FK_Pathfinder_PathfinderSituationType
            foreign key references Pathfinder.PathfinderSituationType(PathfinderSituationTypeId)
        constraint DF_PathfinderSituation 
            default 0;

    
    print 'Created Pathfinder.PathfinderSituation.PathfinderSituationTypeId'
    
end else begin
  
    print 'Pathfinder.PathfinderSituation.PathfinderSituationTypeId Already Created'

end

exec util_refreshviews;

-- 130 - Create Pathfinder.PathfinderSituation.PathfinderSituationTypeId.sql
