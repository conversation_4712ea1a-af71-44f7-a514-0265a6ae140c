
/*
*  Populate Carrier.CarrierProgramQuotaShare
*/

if not exists (
	select 1
	from Carrier.CarrierProgramQuotaShare
	where CarrierProgramGuid = '7241A46F-924A-4367-8FC4-A00EEA228F30'
)
begin

      insert into Carrier.CarrierProgramQuotaShare
      values ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1005', '5CB6D6B6-FF50-4950-BD06-C077CCEB1005', 1.0000),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1000', '5CB6D6B6-FF50-4950-BD06-C077CCEB1000', 1.0000),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1000', '5CB6D6B6-FF50-4950-BD06-C077CCEB1111', 1.0000),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1111', '5CB6D6B6-FF50-4950-BD06-C077CCEB1111', 1.0000),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1111', '5CB6D6B6-FF50-4950-BD06-C077CCEB1222', 1.0000),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1111', '5CB6D6B6-FF50-4950-BD06-C077CCEB1333', 1.0000),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1111', '5CB6D6B6-FF50-4950-BD06-C077CCEB1555', 1.0000),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1222', '5CB6D6B6-FF50-4950-BD06-C077CCEB1222', 1.0000),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1222', '5CB6D6B6-FF50-4950-BD06-C077CCEB1333', 1.0000),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1222', '5CB6D6B6-FF50-4950-BD06-C077CCEB1444', 1.0000),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1222', '5CB6D6B6-FF50-4950-BD06-C077CCEB1555', 1.0000),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1222', '5CB6D6B6-FF50-4950-BD06-C077CCEB1666', 1.0000),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1333', '5CB6D6B6-FF50-4950-BD06-C077CCEB1333', 0.8621),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1333', '5CB6D6B6-FF50-4950-BD06-C077CCEB1444', 0.8710),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1333', '5CB6D6B6-FF50-4950-BD06-C077CCEB1555', 0.8788),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1333', '5CB6D6B6-FF50-4950-BD06-C077CCEB1666', 0.8571),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1333', '5CB6D6B6-FF50-4950-BD06-C077CCEB1888', 0.8378),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1444', '5CB6D6B6-FF50-4950-BD06-C077CCEB1444', 0.7576),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1444', '5CB6D6B6-FF50-4950-BD06-C077CCEB1555', 0.7714),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1444', '5CB6D6B6-FF50-4950-BD06-C077CCEB1888', 0.7763),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1555', '5CB6D6B6-FF50-4950-BD06-C077CCEB1555', 0.6944),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1555', '5CB6D6B6-FF50-4950-BD06-C077CCEB1666', 0.7105),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1555', '5CB6D6B6-FF50-4950-BD06-C077CCEB2100', 0.6667),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1666', '5CB6D6B6-FF50-4950-BD06-C077CCEB1666', 0.6250),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1777', '5CB6D6B6-FF50-4950-BD06-C077CCEB1777', 0.5682),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1888', '5CB6D6B6-FF50-4950-BD06-C077CCEB1888', 0.5208),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB1999', '5CB6D6B6-FF50-4950-BD06-C077CCEB1999', 0.4808),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB2100', '5CB6D6B6-FF50-4950-BD06-C077CCEB2100', 0.4464),
             ('7241A46F-924A-4367-8FC4-A00EEA228F30', '5CB6D6B6-FF50-4950-BD06-C077CCEB2100', '5CB6D6B6-FF50-4950-BD06-C077CCEB1122', 0.4219)
    
    print 'Populated Carrier.CarrierProgramQuotaShare'
    
end else begin
  
    print 'Carrier.CarrierProgramQuotaShare Already Populated'

end

-- 140 - Populate Carrier.CarrierProgramQuotaShare.sql
