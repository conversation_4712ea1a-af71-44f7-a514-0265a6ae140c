/*
*  Create Sales.SalesEmployee
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'SalesEmployee'
    and TABLES.TABLE_SCHEMA = 'Sales'
)
begin

create table Sales.SalesEmployee (
    EmployeeGuid uniqueidentifier not null
        constraint PK_SalesEmployee
            primary key
        constraint FK_SalesEmployee_Employee
            foreign key references Crm.Employee(EmployeeGuid),
    IdentifierOnTarget nvarchar(100) not null,
    LastLocalChange datetimeoffset(7) not null,
    LastSentToExternal datetimeoffset(7) not null,
    LastRemoteChange datetimeoffset(7) not null,
    LastReceivedFromExternal datetimeoffset(7) not null,
    );

    print 'Created Sales.SalesEmployee'
end else begin

    print 'Sales.SalesEmployee Already Created'

end

-- 150 - Create Sales.SalesEmployee.sql
