/*
*  Create Crm.CompanySocialMedia
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'CompanySocialMedia'
    and TABLES.TABLE_SCHEMA = 'Crm'
)
begin

    create table Crm.CompanySocialMedia (
        CompanyGuid uniqueidentifier not null
            constraint FK_CompanySocialMedia_Company
			foreign key references Crm.Company(CompanyGuid),
        SocialMediaId int not null
            constraint FK_CompanySocialMedia_SocialMedia
            foreign key references Crm.SocialMedia(SocialMediaId),
        constraint PK_CompanySocialMedia
        primary key (CompanyGuid, SocialMediaId)
    );

    print 'Created Crm.CompanySocialMedia'
end else begin

    print 'Crm.CompanySocialMedia Already Created'

end

-- 300 - Create Crm.CompanySocialMedia.sql
