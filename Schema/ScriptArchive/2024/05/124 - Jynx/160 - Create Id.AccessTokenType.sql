/*
*  Create Id.AccessTokenType
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'AccessTokenType'
    and TABLES.TABLE_SCHEMA = 'Id'
)
begin

    create table Id.AccessTokenType (
        AccessTokenTypeId int not null
        constraint PK_AccessTokenType
            primary key,
        TokenType nvarchar(100) not null
    );

    print 'Created Id.AccessTokenType'
end else begin

    print 'Id.AccessTokenType Already Created'

end

-- 160 - Create Id.AccessTokenType.sql
