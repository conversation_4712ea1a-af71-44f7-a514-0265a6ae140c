/*
*  Create Ika.IkeaCoveragePart.InsuredExposureOpCode
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'IkeaCoveragePart'
    and COLUMNS.TABLE_SCHEMA = 'Ikea'
    and COLUMNS.COLUMN_NAME = 'InsuredExposureOpCode'
)
begin

    alter table Ikea.IkeaCoveragePart 
    add InsuredExposureOpCode nvarchar(10) null
        constraint FK_IkeaCoveragePart_OpCode
            foreign key references Coverage.SetOperator(OpCode); -- Any | All
    
    print 'Created Ika.IkeaCoveragePart.InsuredExposureOpCode'
    
end else begin
  
    print 'Ikea.IkeaCoveragePart.InsuredExposureOpCode Already Created'

end

exec util_refreshviews;

-- 130 - Create Ika.IkeaCoveragePart.InsuredExposureOpCode.sql
