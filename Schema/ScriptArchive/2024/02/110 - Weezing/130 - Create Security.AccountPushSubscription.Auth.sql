
/*
*  Create Security.AccountPushSubscription.Auth
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'AccountPushSubscription'
    and COLUMNS.TABLE_SCHEMA = 'Security'
    and COLUMNS.COLUMN_NAME = 'Auth'
)
begin

    alter table Security.AccountPushSubscription 
    add Auth nvarchar(100) not null
        constraint DF_AccountPushSubscription_Auth 
        default 0;
    
    print 'Created Security.AccountPushSubscription.Auth'
    
end else begin
  
    print 'Security.AccountPushSubscription.Auth Already Created'

end

exec util_refreshviews;

-- 130 - Create Security.AccountPushSubscription.Auth.sql
