ALTER view [dbo].[PackageProductMapping]
as
with PolicyTypeMap (PackageGuid, MappedInt)
as (
	select DISTINCT PackageGuid, power(cast(2 as bigint), cast(PolicyTypeId - 23 as bigint))
	from Policy.PolicyProspect
	where PolicyProspect.IsDeleted = 0
	group by PackageGuid, PolicyTypeId
), PolicyGrouping (PackageGuid, ProgramGuid, PolicyTypeFlags, Income, ExposureBasis, Cob, RenewableStatusId, ResponseGuid, MaxNumberOfTanks, MaxNumberOfFacilities, OldestTankInstallDate, LocationCount)
as (
select PolicyTypeMap.PackageGuid, Package.ProgramGuid, 
	Sum(MappedInt) as PolicyTypeFlags,  -- Bitflag for checking the mask against
	Min(IsNull(FinancialSummaryPackage.TotalEstimatedIncome, 0)) as Income,  --Lowest income found
	 (select Max(ExposureBasis)
                   from   Carrier.CarrierSubmissionOption
				   inner join Carrier.CarrierSubmission
				   on CarrierSubmission.CarrierSubmissionGuid = CarrierSubmissionOption.CarrierSubmissionGuid
				   inner join Policy.PolicyProspect
				   on PolicyProspect.PolicyProspectGuid = CarrierSubmission.PolicyProspectGuid
                   where  PackageGuid = PolicyTypeMap.PackageGuid) as ExposureBasis,--Highest exposure found
 (select Min(IsNull(PreferredClassOfBusinessGuid, 'F14BBFCA-961E-47A6-99F3-9BAF6B8FC109')) -- f14b-... is 'NA'
                   from Policy.PolicyProspect
                   where  PackageGuid = PolicyTypeMap.PackageGuid and PolicyProspect.IsDeleted = 0) as Cob,--COB
 (select Min(UcpmRenewableStatusId)
                   from Policy.PolicyProspect
                   where  PackageGuid = PolicyTypeMap.PackageGuid
				   and PolicyProspect.IsDeleted = 0)                                     as RenewableStatusId,--RenewableStatusId
 (select Min(ResponseGuid)
                   from Survey.Response
				   inner join Policy.PolicyProspect
				   on PolicyProspect.PolicyProspectGuid = Response.ResponseLinkingGuid
                   where  PackageGuid = PolicyTypeMap.PackageGuid and PolicyProspect.IsDeleted = 0)  as ResponseGuid,
(select Max(UstCount + AstCount)
from Policy.PolicyProspectTankSummary
inner join Policy.PolicyProspect
on PolicyProspect.PolicyProspectGuid = PolicyProspectTankSummary.PolicyProspectGuid
where PackageGuid = PolicyTypeMap.PackageGuid and PolicyProspect.IsDeleted = 0)														as MaxNumberOfTanks,
(select Max(NumberOfFacilities)
from Policy.PolicyProspectTankSummary
inner join Policy.PolicyProspect
on PolicyProspect.PolicyProspectGuid = PolicyProspectTankSummary.PolicyProspectGuid
where PackageGuid = PolicyTypeMap.PackageGuid and PolicyProspect.IsDeleted = 0)														as MaxNumberOfFacilities,
(select Min(OldestTankInstallDate)
from Policy.PolicyProspectTankSummary
inner join Policy.PolicyProspect
on PolicyProspect.PolicyProspectGuid = PolicyProspectTankSummary.PolicyProspectGuid
where PackageGuid = PolicyTypeMap.PackageGuid and PolicyProspect.IsDeleted = 0)														as OldestTankInstallDate,
(select count(*)
from Crm.InsuredLocation
inner join Crm.Insured
on Insured.InsuredGuid = InsuredLocation.InsuredGuid
inner join Crm.InsuredAgentHistory
on InsuredAgentHistory.InsuredGuid = Insured.InsuredGuid
inner join Policy.Package
on Package.InsuredAgentHistoryGuid = InsuredAgentHistory.InsuredAgentHistoryGuid
where Package.PackageGuid = PolicyTypeMap.PackageGuid and InsuredLocation.IsDeleted = 0) as		LocationCount
from PolicyTypeMap
inner join Policy.Package
  on Package.PackageGuid = PolicyTypeMap.PackageGuid
inner join Crm.InsuredAgentHistory
 on InsuredAgentHistory.InsuredAgentHistoryGuid = Package.InsuredAgentHistoryGuid
left join Cache.FinancialSummaryPackage
  on FinancialSummaryPackage.PackageGuid = PolicyTypeMap.PackageGuid
where Package.IsDeleted = 0 
group by PolicyTypeMap.PackageGuid, Package.ProgramGuid
)
select 
	PackageGuid, ProgramGuid, PolicyTypeFlags, Income, ExposureBasis, Cob, RenewableStatusId, ResponseGuid,
	cast(case 
	-- Environmental Facilities should always be GL/Site even if other elements are present
	when ProgramGuid = 'B1E075EF-4A2C-4CB6-A817-2453E42330D2' and ExposureBasis < 2500000  then '13FAD141-C8D4-4064-AE29-9D000247E240' --GL/Site
	when ProgramGuid = 'B1E075EF-4A2C-4CB6-A817-2453E42330D2' and ExposureBasis >= 2500000  then '11691620-78F8-4B03-AF4F-CF57D5E44970'
	when ProgramGuid = '31DF7320-6587-4EE3-8268-A1A598D6A9F3' and (PolicyTypeFlags & 64 = 0 or PolicyTypeFlags != 64) then 'F83BED05-15E0-4FAA-AC06-5D9E2D47015F' --Transp Pkg
	when Cob = 'AF6A8C49-01F3-4333-A564-1261DDEC6860' then '33A534B0-D693-4890-84DC-AE613C083AA8' --Lender Single Site
	when Cob = 'D7B7FC3D-5253-4442-8DCA-43D0666BF34A' then '01BF0DEF-7095-40B7-B4E6-256DE22384F2' --Lender Portfolio
	when ProgramGuid = '9B068B16-BE96-4448-9473-276E12F46EED' and RenewableStatusId = 2  then '30F52F05-5E63-42DB-8BEE-3FE7DF3BD8A1' --Trans Site
	when ProgramGuid = '9B068B16-BE96-4448-9473-276E12F46EED' and RenewableStatusId = 1 and ExposureBasis > 0 and ExposureBasis < 10 then '3D4D2157-07F0-4DD0-B477-2AC7A79C77BC' --Small Ren Site
	when ProgramGuid = '9B068B16-BE96-4448-9473-276E12F46EED' and RenewableStatusId = 1 and ExposureBasis > 0 and ExposureBasis >= 10 then '138CCF17-467F-48E2-884C-B7534D5C0530' --Large Ren Site
	when ProgramGuid = '9B068B16-BE96-4448-9473-276E12F46EED' and RenewableStatusId = 1 and ExposureBasis = 0 and LocationCount < 10 then '3D4D2157-07F0-4DD0-B477-2AC7A79C77BC' --Small Ren Site
	when ProgramGuid = '9B068B16-BE96-4448-9473-276E12F46EED' and RenewableStatusId = 1 and ExposureBasis = 0 and LocationCount >= 10 then '138CCF17-467F-48E2-884C-B7534D5C0530' --Large Ren Site
	
	-- (PolicyTypeFlags & (4160 | 8 | 16)) = PolicyTypeFlags -- We and the policy type flags with the three permitted policy types. If that leaves them unchanged, no other policy types were found.
	-- Added PolicyTypeFlags 134217728 and 4294967296 for XS - Layer 2 and 3, as well as 512 and 128 for Pollution/Professional Combined and Professional Liability Insurance (E&O).
	-- Had to cast as bigint or sql would get very mad about numeric values and the | symbol
	when (ProgramGuid = '33909A95-31E5-4367-B757-A96E313DC3BD' or ProgramGuid = '09D65605-95DC-4C19-BA57-596BA9641EFD') 
		and (PolicyTypeFlags & (cast(4160 as bigint) | cast(8 as bigint) | cast(16 as bigint) | cast(134217728 as bigint) | cast(4294967296 as bigint) 
		| cast(512 as bigint) | cast(128 as bigint) | cast(4503599627370496 as bigint) | cast(9007199254740992 as bigint) | cast(2251799813685248 as bigint)
		| cast(18014398509481984 as bigint))) = PolicyTypeFlags and ExposureBasis >= 5000000 then 'B7D04F06-5326-4B1A-8401-A4E3E59D55C3' --Large ECC/Rest
	when (ProgramGuid = '33909A95-31E5-4367-B757-A96E313DC3BD' or ProgramGuid = '09D65605-95DC-4C19-BA57-596BA9641EFD') 
		and (PolicyTypeFlags & (cast(4160 as bigint) | cast(8 as bigint) | cast(16 as bigint) | cast(134217728 as bigint) | cast(4294967296 as bigint) | cast(512 as bigint) 
		| cast(128 as bigint) | cast(128 as bigint) | cast(4503599627370496 as bigint) | cast(9007199254740992 as bigint) | cast(2251799813685248 as bigint)
		| cast(18014398509481984 as bigint))) = PolicyTypeFlags and ExposureBasis < 5000000  
		then '8053EA62-3C0C-4618-9C84-6CEBB796F2E4' --Small ECC/Rest
	-- Any other instances of these programs
	when (ProgramGuid = '33909A95-31E5-4367-B757-A96E313DC3BD' or ProgramGuid = '09D65605-95DC-4C19-BA57-596BA9641EFD') and ExposureBasis >= 50000000 then '3A5981C4-B6ED-4A33-8AC7-C235208E6D80' --Large ECC/Rest Pkg
	when (ProgramGuid = '33909A95-31E5-4367-B757-A96E313DC3BD' or ProgramGuid = '09D65605-95DC-4C19-BA57-596BA9641EFD') and ExposureBasis < 50000000 then 'C3C01421-5A12-4013-971B-9B83BBF932C2' --Small ECC/Rest Pkg
	when ProgramGuid = '10C86BC2-4768-4B0B-BFFD-3C15F34894B3' and (PolicyTypeFlags = 64) and ExposureBasis >= 100000000  then 'B268DB3B-A734-4D9B-8254-128478DAE128' -- Large CPL
	when ProgramGuid = '10C86BC2-4768-4B0B-BFFD-3C15F34894B3' and (PolicyTypeFlags = 64) then 'F80F3D2A-A4FD-4002-A21F-D56139561D41' --CPL
	when ProgramGuid = '31DF7320-6587-4EE3-8268-A1A598D6A9F3' and PolicyTypeFlags = 64 then '7E05CEDC-790C-4810-95B6-5C55ED0492B1' -- TPL
	when (ProgramGuid = '10C86BC2-4768-4B0B-BFFD-3C15F34894B3' or ProgramGuid = 'DF942BAC-EE45-4384-B1AD-2514DCB3A6F9') and ExposureBasis >= 50000000 then '541EAEBC-FEBA-411D-AE39-957D814F3DBC' -- Large Const non-CPL
	when (ProgramGuid = '10C86BC2-4768-4B0B-BFFD-3C15F34894B3' or ProgramGuid = 'DF942BAC-EE45-4384-B1AD-2514DCB3A6F9') then '23F42F9E-492F-420D-BC41-D1ACFCA13C8A' -- Const non-CPL
	when (PolicyTypeFlags = 8192 or PolicyTypeFlags = 1 or ((PolicyTypeFlags & 256) = 256) or (ProgramGuid = '4248DFD7-53E8-4FB6-8DA7-2AB4D4D2F6D2') or  (ProgramGuid = '8AA35787-F72A-4BEA-B53B-BA0ACB272DC3') or (ProgramGuid = '19351858-B75A-4DA8-A172-5CCB0A85C098') or (ProgramGuid = 'DBB2B8BB-8F48-435D-93DB-189CCFEDF910')) then '8BAAE68D-20B0-4EA3-AEBE-A652B7C01974' --Other
	when ProgramGuid = 'DD1E85E0-6483-40EA-AD57-95EB6D6169CF' then '7F823B80-A2C0-4CF8-B3D2-85C7D83609AF' --D&R
	when ProgramGuid = 'A04493FA-D2B4-47FE-8960-4C34EBE80C5B' then '152205BD-0070-4067-BC96-D5986E660B0D' --Small Tank
	when ProgramGuid = 'DBB2B8BB-8F48-435D-93DB-189CCFEDF910' then 'CF3C497F-402C-48B0-BC6E-D3BBEF5529B6' -- Federated
	else 'C52FACA6-72B9-49C1-84ED-675E6A9852A4' -- None
	end as uniqueidentifier) as ProductGuid
from PolicyGrouping -- 120,582
GO