/*
*  Create Acc.ExternalPaymentLog.Fee
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'ExternalPaymentLog'
    and COLUMNS.TABLE_SCHEMA = 'Acc'
    and COLUMNS.COLUMN_NAME = 'Fee'
)
begin

    alter table Acc.ExternalPaymentLog 
    add FeeAmount money not null
            constraint DF_ExternalPaymentLog_FeeAmount 
                default 0,
        Description nvarchar(max) not null,
        ExternalPaymentTypeId int not null
            constraint FK_ExternalPaymentLog_ExternalPaymentTypeId
                foreign key references Acc.ExternalPaymentType(ExternalPaymentTypeId)
            constraint DF_ExternalPaymentLog_ExternalPaymentTypeId
                default 0,
        ExternalIdentifier nvarchar(50) not null
            constraint DF_ExternalPaymentLog_ExternalIdentifier
                default '',
        ExternalEventDateZoned datetimeoffset not null,
        PayerName nvarchar(100) not null
            constraint DF_ExternalPaymentLog_PayerName
                default '',
        EmailAddressOfPayer nvarchar(100) not null
            constraint DF_ExternalPaymentLog_EmailAddressOfPayer
                default '',
        PublicIdentifier nvarchar(50) not null
            constraint DF_ExternalPaymentLog_PublicIdentifier
                default '';

    
    print 'Created Acc.ExternalPaymentLog.Fee'
    
end else begin
  
    print 'Acc.ExternalPaymentLog.Fee Already Created'

end

exec util_refreshviews;

-- 130 - Create Acc.ExternalPaymentLog.Fee.sql
