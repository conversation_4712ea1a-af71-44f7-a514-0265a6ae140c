IF NOT EXISTS (SELECT 1 FROM Flex.FlexAdditionalService WHERE FlexAdditionalServiceName = 'Document Review')
BEGIN
    INSERT INTO Flex.FlexAdditionalService (FlexAdditionalServiceName, Cost) VALUES
        ('Document Review', 350.0)
END;

IF NOT EXISTS (SELECT 1 FROM Flex.FlexAdditionalService WHERE FlexAdditionalServiceName = 'Account Review')
BEGIN
    INSERT INTO Flex.FlexAdditionalService (FlexAdditionalServiceName, Cost) VALUES
        ('Account Review', 150.0)
END;

IF NOT EXISTS (SELECT 1 FROM Flex.FlexAdditionalService WHERE FlexAdditionalServiceName = 'O&M Plans (various)')
BEGIN
    INSERT INTO Flex.FlexAdditionalService (FlexAdditionalServiceName, Cost) VALUES
        ('O&M Plans (various)', 400.0)
END;

IF NOT EXISTS (SELECT 1 FROM Flex.FlexAdditionalService WHERE FlexAdditionalServiceName = 'PPPs')
BEGIN
    INSERT INTO Flex.FlexAdditionalService (FlexAdditionalServiceName, Cost) VALUES
        ('PPPs', 50.0)
END;

IF NOT EXISTS (SELECT 1 FROM Flex.FlexAdditionalService WHERE FlexAdditionalServiceName = 'BMPs')
BEGIN
    INSERT INTO Flex.FlexAdditionalService (FlexAdditionalServiceName, Cost) VALUES
        ('BMPs', 50.0)
END;

IF NOT EXISTS (SELECT 1 FROM Flex.FlexAdditionalService WHERE FlexAdditionalServiceName = 'CERC Certification')
BEGIN
    INSERT INTO Flex.FlexAdditionalService (FlexAdditionalServiceName, Cost) VALUES
        ('CERC Certification', 350.0)
END;
