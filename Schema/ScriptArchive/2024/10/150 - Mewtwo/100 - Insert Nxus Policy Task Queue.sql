/*
*  Insert NXUS Policy Task.Queue
*/

IF not exists (
    SELECT 1
    FROM Task.Queue
    WHERE QueueName = 'NXUS Policies'
)
BEGIN
	DECLARE @policy UNIQUEIDENTIFIER
	SET @policy = NEWID();

	INSERT INTO Task.Queue (QueueGuid, QueueName, IsSharedQueue, AutoReturnHours, AllowDirectTaskCreation, Priority, IsUrgent, DisplayPriority, CompletionPoints, CompletionMinutes, ExcludeFromCompletionTotals)
	VALUES
	(@policy, 'NXUS Policies', 0, 1, 0, 125, 0, 275, 0, 0, 0);

	INSERT INTO Task.TaskTemplate (TaskTemplateGuid, SubjectTemplate, BodyTemplate, TaskDeadlineHours, QueueGuid)
	VALUES
	(NEWID(), 'NXUS Policy For {Insured.Name}', NULL, 1, @policy);

    PRINT 'Insert NXUS Policies Task.Queue Success'

END ELSE BEGIN
    PRINT 'NXUS Policies Task.Queue Already Created'

END

-- 100 - Insert Nxus Policy Task Queue
