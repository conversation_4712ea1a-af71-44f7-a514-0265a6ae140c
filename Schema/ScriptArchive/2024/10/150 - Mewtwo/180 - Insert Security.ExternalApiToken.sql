/*
*  Insert Adobe Refresh Token into Security.ExternalApiToken
*/
if not exists (
	select 1
	from Security.ExternalApiToken
	where ProgramName = 'AdobeEsign' 
	and ExternalApiName = 'AdobeESign'
)
begin
	insert into Security.ExternalApiToken
	values (
				'-',
				'AdobeEsign', 
				'AdobeESign', 
				'3AAABLblqZhASEapOJBIuXTxUkrtGN2Zq503Fy4MWgRgCl0F2WcEZPTpI2fMUdCYYWAEiRC4VOWYjPYXB2bhhBJ8m9O6HZpL8', 
				'3AAABLblqZhBfdsfOc1pYxbOfnx_j4XqaLnSxBj6aF1clbuSZbO-oVP0e9US3jQLQpil6De2M5uc*'
			)
	print 'Adobe Refresh token has been added'
end else begin
	print 'Unable to add Adobe refresh token'
end
-- 180 - Insert Security.ExternalApiToken.sql
