/*
*  Create News.OnlineArticle.VideoUrl
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'OnlineArticle'
    and COLUMNS.TABLE_SCHEMA = 'News'
    and COLUMNS.COLUMN_NAME = 'VideoUrl'
)
begin

    alter table News.OnlineArticle 
    add VideoUrl nvarchar(500) null;
    
    print 'Created News.OnlineArticle.VideoUrl'
    
end else begin
  
    print 'News.OnlineArticle.VideoUrl Already Created'

end

exec util_refreshviews;

-- 100 - Create News.OnlineArticle.VideoUrl.sql
