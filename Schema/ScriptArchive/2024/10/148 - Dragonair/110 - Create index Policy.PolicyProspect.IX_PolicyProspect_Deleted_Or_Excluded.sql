
/*
*  Create index Policy.PolicyProspect.IX_PolicyProspect_Deleted_Or_Excluded
*/
 
if not exists (
select 1
from sys.indexes
where indexes.name = 'IX_PolicyProspect_Deleted_Or_Excluded'
)
begin

create index IX_PolicyProspect_Deleted_Or_Excluded
on Policy.PolicyProspect (EffectiveDateZoned, IsDeleted, ExcludedFromMarketplace)
include ([PolicyProspectGuid], [PackageGuid], [PreferredClassOfBusinessGuid]);

print 'Created index IX_PolicyProspect_Deleted_Or_Excluded'
end else begin

print 'IX_PolicyProspect_Deleted_Or_Excluded already Created'

end

-- 110 - Create index Policy.PolicyProspect.IX_PolicyProspect_Deleted_Or_Excluded.sql




