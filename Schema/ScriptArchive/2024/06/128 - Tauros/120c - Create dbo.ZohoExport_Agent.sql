create or alter view dbo.ZohoExport_Agent

as

select 
isnull(SyncAgent.IdentifierOnTarget, '') as 'AgentIdentifier',
Agent.AgentGuid as "Agent Guid",
isnull(Agent.AgentFirstName, '') as "First Name",
isnull(Agent.AgentLastName, '') as "Last Name",
SyncCompany.IdentifierOnTarget as "Agency Name.id",
SyncLocation.IdentifierOnTarget as "Office Name.id",
Agent.AgentEmail as Email,
isnull(Agent.PositionTitle, '') as Title,
isnull(Agent.Phone, '') as Phone,
isnull(Agent.Mobile, '') as Mobile,
concat(AgentAddress.Address1, ' ', AgentAddress.Address2) as "Mailing Street",
isnull(AgentAddress.City, '') as "Mailing City",
isnull(AgentAddress.StateCode, '') as "Mailing State",
isnull(AgentAddress.Zip, '') as "Mailing Zip",
case when exists (
    select 1
    from Policy.PolicyProspect 
    inner join InvoiceToCompany
        on PolicyProspect.PolicyProspectGuid = InvoiceToCompany.PolicyProspectGuid
    where PolicyProspect.EffectiveDateZoned > dateadd(YEAR, -1, getdate())
    and InvoiceToCompany.AgentGuid = Agent.AgentGuid
)   then '0' 
    else '1' end as "Cold Agent", -- drill to submissions, check 12 months
case when exists ( -- TODO, check why this didn't pull through
    select 1
    from Tag.AgentTag 
    where AgentTag.TagId = 52 -- TagId 52 = "Has Left Agency"
    and AgentTag.AgentGuid = Agent.AgentGuid
)   then 1
    else 0 end as 'Has left agency',
isnull(Agent.AgentMiddleName, '') as "Middle Name",
isnull(Agent.AgentNameSuffix, '') as Suffix,
isnull(Agent.PhoneExt,'') as "Phone Ext",
isnull(Agent.AliasEmail,'') as "Alias Email",
BlastStatus.BlastStatusName as "Email Blast",
SyncAgent.LastSentToExternal
from Crm.Agent -- 42,256
inner join Crm.Location
  on Agent.LocationGuid = Location.LocationGuid -- 42,256
inner join Sync.SyncLocation
  on Location.LocationGuid = SyncLocation.LocationGuid -- 42,629 (increases due to duplication in the import)
inner join Crm.Company
  on Location.CompanyGuid = Company.CompanyGuid 
inner join Sync.SyncCompany
  on Company.CompanyGuid = SyncCompany.CompanyGuid -- 40,842 -- Walking it back to  locaton+company match underreports
left join Mail.BlastStatus
  on Agent.BlastStatusId  = BlastStatus.BlastStatusID
left join Crm.AgentAddress
  on Agent.AgentGuid = AgentAddress.AgentGuid
left join Sync.SyncAgent
  on Agent.AgentGuid = SyncAgent.AgentGuid
where Agent.IsDeleted = 0 -- 39,794
and not exists ( 
    select 1
    from Tag.AgentTag 
    where AgentTag.TagId = 52 -- TagId 52 = "Has Left Agency"
    and AgentTag.AgentGuid = Agent.AgentGuid
    and AgentTag.RecordCreatedZoned < '2024-02-01' -- We need records *after* this as this indicates we already imported the prior state
)
and Agent.IsDeleted = 0 
and SyncAgent.IdentifierOnTarget is null

-- 120 - Create dbo.ZohoExport_Agent.sql