/*
*  Add Offset Lines for DirectBill to Acc.TransactionTypeRule
*/

IF NOT EXISTS (
    SELECT 1
    FROM Acc.TransactionTypeRule
    WHERE TransactionTypeRuleContextId = 4
)
BEGIN

    INSERT INTO Acc.TransactionTypeRule (TransactionTypeRuleContextId, IsDebit, TransactionTypeID, ChartId, PortionId, IsDirectBill)
    SELECT 
        4,
        CASE 
            WHEN IsDebit = 0 THEN 1 
            WHEN IsDebit = 1 THEN 0 
            ELSE IsDebit 
        END AS IsDebit,
        TransactionTypeID,
		ChartId,
		PortionId,
		IsDirectBill
    FROM Acc.TransactionTypeRule
	WHERE TransactionTypeRuleContextId = 1


    PRINT 'Lines were inserted into Acc.TransactionTypeRule'

END ELSE BEGIN

    PRINT 'Lines were already inserted into Acc.TransactionTypeRule'

END

exec util_refreshviews;

-- 190 - Add Offset Lines for DirectBill to Acc.TransactionTypeRule.sql
