/*
*  Create Ikea.IkeaDefinedTermOptionFlexInsuredExposure
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'IkeaDefinedTermOptionFlexInsuredExposure'
    and TABLES.TABLE_SCHEMA = 'Ikea'
)
begin

    create table Ikea.IkeaDefinedTermOptionFlexInsuredExposureAvailableOption (
        IkeaDefinedTermGuid uniqueidentifier not null
			constraint FK_IkeaDefinedTermOptionFlexInsuredExposure_IkeaDefinedTerm
				foreign key references Ikea.IkeaDefinedTerm(IkeaDefinedTermGuid),
		FlexInsuredExposureGuid uniqueidentifier not null
			constraint FK_IkeaDefinedTermOptionFlexInsuredExposure_FlexInsuredExposure
				foreign key references Flex.FlexInsuredExposure(FlexInsuredExposureGuid),
		AvailableIkeaDefinedTermOptionGuid uniqueidentifier not null
			constraint FK_IkeaDefinedTermOptionFlexInsuredExposure_IkeaDefinedTermOption
				foreign key references Ikea.IkeaDefinedTermOption(IkeaDefinedTermOptionGuid),
        constraint PK_IkeaDefinedTermOptionFlexInsuredExposure
        primary key (IkeaDefinedTermGuid, FlexInsuredExposureGuid, AvailableIkeaDefinedTermOptionGuid)
    );

    print 'Created Ikea.IkeaDefinedTermOptionFlexInsuredExposure'
end else begin

    print 'Ikea.IkeaDefinedTermOptionFlexInsuredExposure Already Created'

end

-- 140 - Create Ikea.IkeaDefinedTermOptionFlexInsuredExposure.sql
