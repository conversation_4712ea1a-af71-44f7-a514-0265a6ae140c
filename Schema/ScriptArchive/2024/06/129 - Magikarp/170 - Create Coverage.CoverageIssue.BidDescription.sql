
/*
*  Create Coverage.CoverageIssue.BidDescription
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'CoverageIssue'
    and COLUMNS.TABLE_SCHEMA = 'Coverage'
    and COLUMNS.COLUMN_NAME = 'BidDescription'
)
begin

    alter table Coverage.CoverageIssue 
    add BidDescription nvarchar(max) null;
    
    print 'Created Coverage.CoverageIssue.BidDescription'
    
end else begin
  
    print 'Coverage.CoverageIssue.BidDescription Already Created'

end

exec util_refreshviews;

-- 170- Create Coverage.CoverageIssue.BidDescription.sql
