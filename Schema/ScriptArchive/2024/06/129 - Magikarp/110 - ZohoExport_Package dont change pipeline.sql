ALTER view [dbo].[ZohoExport_Package]
as

with PolicySort (PackageGuid, PolicyProspectGuid, RowNumber)  as (
    select 
        PackageGuid, 
        PolicyProspectGuid, 
        ROW_NUMBER() over (partition by PackageGuid order by EffectiveDateZoned, PolicyProspectGuid)
    from Policy.PolicyProspect
    where PolicyProspect.IsDeleted = 0
)
, EarliestPolicy (PackageGuid, PolicyProspectGuid) as (
    select PackageGuid, PolicyProspectGuid
    from PolicySort
    where PolicySort.RowNumber = 1
)
select 
-- *** Policy/Pipeline information ***
isnull(SyncPackage.IdentifierOnTarget, '') as PackageId,
case when SyncEmployee.OwnerId is null then 'zcrm_6049878000000537001'
     when SyncEmployee.OwnerId = '' then 'zcrm_6049878000000537001'
     when SyncEmployee.OwnerId = 'zcrm_6049878000000444001' then 'zcrm_6049878000000537001' -- Map <PERSON>'s accounts to admin
     else SyncEmployee.OwnerId end as "PolicyPipelineOwnerId", -- Policies/Piplines Owner (needs to be user)
Broker.EmployeeGuid as BrokerGuid,
case 
    when PolicyStatus.Status = 'Bound' then 'Current (Bound)'
    when PolicyStatus.Status = 'Expired' then 'Expired'
    when PolicyStatus.IsOpen = 1 then 'Open' 
    else 'Closed' 
end as "Stage", -- Stage Open (all open statuses), Closed (all other statuses)
concat(Insured.Name, ' ', datepart(year, PolicyProspect.EffectiveDateZoned), ' Opportunity') as "Policies/Pipelines Name",
case when SyncPackage.IdentifierOnTarget is null then  'PT Package Opportunity' 
	else '' end as "Pipeline", -- Layout (PT Package Opportunity)
-- *** Module Links***
SyncInsured.IdentifierOnTarget as "InsuredId", -- Insured (lookup, set InsuredId)
Package.PackageGuid, -- PackageGuid
SyncAgent.IdentifierOnTarget as "AgentId",  -- Agent/Contacts Name (lookup Agent.Id)
SyncCompany.IdentifierOnTarget as "AgencyId", -- Agency Name (lookup Agency.Id)
'Traditional' as "Broker Workflow", -- Broker Workflow (Traditional, Pulse +, Pulse) ***TODO: JWL what defines these buckets*** Traditional = 1 person, Pulse = autopilot, Pulse+ = not yet
-- *** Auto Email Trigger Dates (system)
-- *** Policy Info (earliest?) ***
isnull(cast(PolicyProspect.EffectiveDateZoned as date), '')as "Effective Date", -- Effective Date
isnull(cast(CarrierSubmissionOption.ExpirationDateZoned as date), '') as "Expiration Date",-- Expiration Date
isnull(cast(PolicyProspect.QuoteDueDateZoned as date), '') as "Quote Due Date",-- Quote(s) Due Date
isnull(cast(PolicyProspect.PresentationDateZoned as date), '') as "Presentation Date",-- Presentation Date
isnull(ClassOfBusiness.ClassOfBusinessName, '') as "Preferred COB",-- Preferred COB
-- *** Package Line ***
datepart(year, Package.PackageDate) as "Package Year",-- Package Year
Program.ProgramName as Program,-- Program
Product.ProductName, --Product
-- Description Information
datename(month, PolicyProspect.EffectiveDateZoned) as MonthName,
SyncPackage.LastSentToExternal
from Policy.Package
inner join Crm.InsuredAgentHistory
  on Package.InsuredAgentHistoryGuid = InsuredAgentHistory.InsuredAgentHistoryGuid
inner join Crm.Agent
  on InsuredAgentHistory.AgentGuid = Agent.AgentGuid
left join Sync.SyncAgent
  on Agent.AgentGuid = SyncAgent.AgentGuid
inner join Crm.Insured
  on Crm.InsuredAgentHistory.InsuredGuid = Insured.InsuredGuid
left join Sync.SyncInsured
  on Insured.InsuredGuid = SyncInsured.InsuredGuid 
inner join EarliestPolicy -- Drops 5 rows that don't have policies, but we can't load effective date and similar without a policy
  on Package.PackageGuid = EarliestPolicy.PackageGuid
inner join Policy.PolicyProspect
  on EarliestPolicy.PolicyProspectGuid = PolicyProspect.PolicyProspectGuid
inner join Policy.PolicyType
  on PolicyProspect.PolicyTypeID = PolicyType.PolicyTypeID
left join Crm.Employee as Broker
  on Package.PrimaryRepLoginGUID = Broker.EmployeeGuid
left join Sync.SyncEmployee 
  on Broker.EmployeeGuid = SyncEmployee.EmployeeGuid
inner join Cache.FinancialSummaryPolicy
  on PolicyProspect.PolicyProspectGuid = FinancialSummaryPolicy.PolicyGuid
inner join Carrier.CarrierSubmission
  on FinancialSummaryPolicy.SelectedSubmissionGuid = CarrierSubmission.CarrierSubmissionGuid
inner join Cache.FinancialSummarySubmission
  on FinancialSummarySubmission.SubmissionGuid = CarrierSubmission.CarrierSubmissionGuid
inner join Carrier.CarrierSubmissionOption
  on CarrierSubmissionOption.CarrierSubmissionOptionGuid = FinancialSummarySubmission.SelectedOptionGuid
inner join Policy.PolicyStatus
  on CarrierSubmission.PolicyStatusID = PolicyStatus.PolicyStatusID
inner join Crm.Location
  on Agent.LocationGuid = Location.LocationGuid
inner join Crm.Company
  on Location.CompanyGuid = Company.CompanyGuid
left join Sync.SyncCompany
  on Company.CompanyGuid = SyncCompany.CompanyGuid
left join Cob.ClassOfBusiness
  on PolicyProspect.PreferredClassOfBusinessGuid = ClassOfBusiness.ClassOfBusinessGuid
inner join Policy.Program
  on Package.ProgramGuid = Program.ProgramGuid
inner join PackageProductMapping
  on Package.PackageGuid = PackageProductMapping.PackageGuid
inner join Policy.Product
  on PackageProductMapping.ProductGuid = Product.ProductGuid
left join Sync.SyncPackage
  on Package.PackageGuid = SyncPackage.PackageGuid
where ( -- Base critiera for being included are the active policies
    Package.IsDeleted = 0 -- 5 
    and Agent.IsDeleted = 0
    and Insured.IsDeleted = 0 -- 161,256 rows, note 161,285 in select * from Policy.Package where Package.IsDeleted = 0 
    and SyncEmployee.IdentifierOnTarget is not null
    and SyncEmployee.OwnerId != 'zcrm_6049878000000444001' -- Has been set up as a Zoho user, not the default value -- 977 rows
    and PolicyProspect.EffectiveDateZoned > dateadd(year, -2, getdate()) -- Currently no effect as the selected users are all newer than this
    and PolicyProspect.EffectiveDateZoned < dateadd(month, 6, getdate())
    ) 
or Package.PackageGuid in (  -- This finds the renewed policies and includes them
    select OldPackage.PackageGuid
    from Policy.Package as CurrentPackage
    left join Crm.Employee as CurrentBroker
      on CurrentPackage.PrimaryRepLoginGUID = CurrentBroker.EmployeeGuid
    left join Sync.SyncEmployee as CurrentSyncEmployee
      on CurrentBroker.EmployeeGuid = CurrentSyncEmployee.EmployeeGuid
    inner join Policy.PolicyProspect as CurrentPolicyProspect
      on CurrentPackage.PackageGuid = CurrentPolicyProspect.PackageGuid -- To here we have reproduced the main query's return set
    inner join Policy.PolicyProspect as OldPolicyProspect -- Now we get the renewed policies
      on CurrentPolicyProspect.RenewalOfPolicyGuid = OldPolicyProspect.PolicyProspectGuid
    inner join Policy.Package as OldPackage -- Resulting in the old packages to transfer
      on OldPolicyProspect.PackageGuid = OldPackage.PackageGuid
    where ( -- Base critiera for being included are the active policies again
        CurrentPackage.IsDeleted = 0 -- 5 
        and CurrentSyncEmployee.IdentifierOnTarget is not null
        and CurrentSyncEmployee.OwnerId != 'zcrm_6049878000000444001' -- Has been set up as a Zoho user, not the default value -- 977 rows
        and CurrentPolicyProspect.EffectiveDateZoned > dateadd(year, -2, getdate()) -- Currently no effect as the selected users are all newer than this
        ) 
)
or Package.PackageGuid in (
    select SupportPackage.PackageGuid
    from Policy.Package as SupportPackage
    inner join Policy.PolicyProspect as SupportPolicyProspect
      on SupportPackage.PackageGuid = SupportPolicyProspect.PackageGuid
    inner join Sync.SyncPolicyProspect as SupportSyncPolicy
      on SupportPolicyProspect.PolicyProspectGuid = SupportSyncPolicy.PolicyProspectGuid
    where SupportPackage.IsDeleted = 0
    and SupportPolicyProspect.IsDeleted = 0
)
