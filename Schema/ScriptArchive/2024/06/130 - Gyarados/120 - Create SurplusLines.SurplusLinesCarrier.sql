/*
*  Create SurplusLines.SurplusLinesCarrier
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'SurplusLinesCarrier'
    and TABLES.TABLE_SCHEMA = 'SurplusLines'
)
begin

CREATE TABLE SurplusLines.SurplusLinesCarrier(
    SurplusLinesCarrierGuid uniqueidentifier NOT NULL
        constraint PK_SurplusLinesCarrier primary key,
    CarrierName nvarchar(100) NOT NULL,
    Address1 nvarchar(75) NOT NULL
        constraint DF_SurplusLinesCarrier_Address1  default (''),
    Address2 nvarchar(75) NOT NULL
        constraint DF_SurplusLinesCarrier_Address2  default ('') ,
    City nvarchar(30) NOT NULL
        constraint DF_SurplusLinesCarrier_City  default ('') ,
    State char(2) NOT NULL
        constraint FK_SurplusLinesCarrier_State foreign key (State)
            references Lookups.State (Code),
    Zip nvarchar(7) NOT NULL
        constraint DF_SurplusLinesCarrier_Zip  default (''),
    RecordCreatedZoned datetimeoffset(7) NOT NULL
        constraint DF_SurplusLinesCarrier_RecordCreatedZoned  default (sysdatetimeoffset()) 
);

    print 'Created SurplusLines.SurplusLinesCarrier'
end else begin

    print 'SurplusLines.SurplusLinesCarrier Already Created'

end

-- 120 - Create SurplusLines.SurplusLinesCarrier.sql


