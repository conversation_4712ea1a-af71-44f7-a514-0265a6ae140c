/*
*  Create Employee.EmployeeDesignationAttempt
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'EmployeeDesignationAttempt'
    and TABLES.TABLE_SCHEMA = 'Employee'
)
begin

    create table Employee.EmployeeDesignationAttempt (
        EmployeeGuid uniqueidentifier not null,
        DesignationId int not null
            constraint FK_EmployeeDesignationAttempt_Designation
                foreign key references Employee.Designation(DesignationId),
        AttemptDate date not null,
        WasSuccess bit not null,
        EmployeeDesignationAttemptId int not null,
        constraint FK_EmployeeDesignationAttempt_Employee
            foreign key (EmployeeGuid, DesignationId) 
            references Employee.EmployeeDesignation(EmployeeGuid, DesignationId),
        constraint PK_EmployeeDesignationAttempt
            primary key (EmployeeGuid, DesignationId, AttemptDate)
    );

    print 'Created Employee.EmployeeDesignationAttempt'
end else begin

    print 'Employee.EmployeeDesignationAttempt Already Created'

end

-- 260 - Create Employee.EmployeeDesignationAttempt.sql
