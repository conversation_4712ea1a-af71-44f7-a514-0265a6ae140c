
/*
*  Create Carrier.CarrierSubmission.PausedUnderwriterSubStatusId
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'CarrierSubmission'
    and COLUMNS.TABLE_SCHEMA = 'Carrier'
    and COLUMNS.COLUMN_NAME = 'PausedUnderwriterSubStatusId'
)
begin

    alter table Carrier.CarrierSubmission 
    add PausedUnderwriterSubStatusId int null
        constraint FK_CarrierSubmission_PausedUnderwriterSubStatus
            foreign key references Carrier.UnderwriterSubStatus(UnderwriterSubStatusId);

    
    print 'Created Carrier.CarrierSubmission.PausedUnderwriterSubStatusId'
    
end else begin
  
    print 'Carrier.CarrierSubmission.PausedUnderwriterSubStatusId Already Created'

end

exec util_refreshviews;

-- 200 - Create Carrier.CarrierSubmission.PausedUnderwriterSubStatusId.sql
