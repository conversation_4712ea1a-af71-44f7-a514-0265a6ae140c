CREATE OR ALTER procedure [dbo].[util_UpdateStep4_Policy]
as
	update Cache.FinancialSummaryPolicy
	set
		FinancialSummaryPolicy.TotalPremium = source.TotalPremium, 
		FinancialSummaryPolicy.TotalFeeUcpm = source.TotalFeeUcpm, 
		FinancialSummaryPolicy.TotalSLFee = source.TotalSLFee, 
		FinancialSummaryPolicy.TotalCarrierFee = source.TotalCarrierFee, 
		FinancialSummaryPolicy.TotalSLFeeUcpm = source.TotalSLFeeUcpm, 
		FinancialSummaryPolicy.TotalSLTax = source.TotalSLTax,  
		FinancialSummaryPolicy.TotalEstimatedIncome = source.TotalEstimatedIncome, 
		FinancialSummaryPolicy.IsInvoiced = source.IsInvoiced,
		FinancialSummaryPolicy.SelectedSubmissionGuid = source.SelectedSubmissionGuid,
		FinancialSummaryPolicy.SubmissionCount = source.SubmissionCount,
		FinancialSummaryPolicy.PackageGuid = source.PackageGuid,
		FinancialSummaryPolicy.GaCommission = source.GaCommission,
		FinancialSummaryPolicy.AgentCommission = source.AgentCommission,
		FinancialSummaryPolicy.TotalAgencyContingency = source.TotalAgencyContingency
	from FinancialSummaryPolicyCalc as source with (nolock)
	where FinancialSummaryPolicy.PolicyGuid = source.PolicyProspectGuid
	and (
		FinancialSummaryPolicy.TotalPremium <> source.TotalPremium or
		FinancialSummaryPolicy.TotalFeeUcpm <> source.TotalFeeUcpm or
		FinancialSummaryPolicy.TotalSLFee <> source.TotalSLFee or
		FinancialSummaryPolicy.TotalCarrierFee <> source.TotalCarrierFee or
		FinancialSummaryPolicy.TotalSLFeeUcpm <> source.TotalSLFeeUcpm or
		FinancialSummaryPolicy.TotalSLTax <> source.TotalSLTax or
		FinancialSummaryPolicy.TotalEstimatedIncome <> source.TotalEstimatedIncome or
		FinancialSummaryPolicy.IsInvoiced <> source.IsInvoiced or
		FinancialSummaryPolicy.SelectedSubmissionGuid <> source.SelectedSubmissionGuid or
		FinancialSummaryPolicy.SubmissionCount <> source.SubmissionCount or
		FinancialSummaryPolicy.PackageGuid <> source.PackageGuid or
		FinancialSummaryPolicy.GaCommission <> source.GaCommission or
		FinancialSummaryPolicy.AgentCommission <> source.AgentCommission or
		FinancialSummaryPolicy.TotalAgencyContingency <> source.TotalAgencyContingency
	) -- 2 seconds
GO

-- 335 - util_UpdateStep4_Policy.sql


