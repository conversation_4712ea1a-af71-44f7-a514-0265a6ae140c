/*
*  Drop Policy.PolicyProspect.VariationNumber
*/

if exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'PolicyProspect'
    and COLUMNS.TABLE_SCHEMA = 'Policy'
    and COLUMNS.COLUMN_NAME = 'VariationNumber'
)
begin

    alter table Policy.PolicyProspect 
    drop column VariationNumber 
    
    print 'Dropped Policy.PolicyProspect.VariationNumber'
    
end else begin
  
    print 'Policy.PolicyProspect.VariationNumber Already Dropped'

end

exec util_refreshviews;

-- 161 - 130 - Drop Policy.PolicyProspect.VariationNumber.sql
