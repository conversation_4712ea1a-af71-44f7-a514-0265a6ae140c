
CREATE OR ALTER procedure [dbo].[util_RebuildStep5_Package]
as
	insert into Cache.FinancialSummaryPackage(
		PackageGuid,		 --2
		IsInvoiced,          --3
		TotalPremium,        --4
		TotalFeeUcpm,        --5
		TotalCarrierFee,     --6
		TotalSLFeeUcpm,      --7
		TotalSLFee,          --8
		TotalSLTax,          --9
		TotalEstimatedIncome,--10
		GaCommission,
		AgentCommission,
		TotalAgencyContingency
	)
	select 
		source.PackageGuid,         --2
		source.IsInvoiced,          --3
		source.TotalPremium,        --4
		source.TotalFeeUcpm,        --5
		source.TotalCarrierFee,     --6
		source.TotalSLFeeUcpm,      --7
		source.TotalSLFee,          --8
		source.TotalSlTax,          --9
		source.TotalEstimatedIncome,--10
		source.GaCommission,
		source.AgentCommission,
		source.TotalAgencyContingency
	from FinancialSummaryPackageCalc as source with (nolock)
	where not exists (
		select * 
		from Cache.FinancialSummaryPackage as fsp with (nolock)
		where fsp.PackageGuid = source.PackageGuid
	) -- 39553 != 39362
GO

-- 340 - util_RebuildStep5_Package.sql