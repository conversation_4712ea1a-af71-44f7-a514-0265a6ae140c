create or alter procedure util_CleanupCachedFinancials
as
begin

delete from Cache.FinancialSummaryInvoice
where not exists (
	select *
	from Policy.Invoice
	where Invoice.IsDeleted = 0
	and FinancialSummaryInvoice.InvoiceGuid = Invoice.InvoiceGuid
);

delete from Cache.FinancialSummaryOption
where not exists (
	select *
	from Carrier.CarrierSubmissionOption
	where CarrierSubmissionOption.IsDeleted = 0
	and FinancialSummaryOption.CarrierSubmissionOptionGuid = CarrierSubmissionOption.CarrierSubmissionOptionGuid
);

delete from Cache.FinancialSummarySubmission
where not exists (
	select * 
	from Carrier.CarrierSubmission
	where CarrierSubmission.IsDeleted = 0
	and FinancialSummarySubmission.SubmissionGuid = CarrierSubmission.CarrierSubmissionGuid
);

delete from Cache.FinancialSummaryPolicy
where not exists (
	select *
	from Policy.PolicyProspect
	where PolicyProspect.IsDeleted = 0
	and FinancialSummaryPolicy.PolicyGuid = PolicyProspect.PolicyProspectGuid
)

delete from Cache.FinancialSummaryPackage
where not exists (
	select *
	from Policy.Package
	where Package.IsDeleted = 0
	and FinancialSummaryPackage.PackageGuid = Package.PackageGuid
)

delete from Cache.FinancialSummaryInsuredAgentHistory
where not exists (
	select *
	from Crm.InsuredAgentHistory
	where InsuredAgentHistory.IsDeleted = 0
	and FinancialSummaryInsuredAgentHistory.InsuredAgentHistoryGuid = InsuredAgentHistory.InsuredAgentHistoryGuid
)

delete from Cache.FinancialSummaryAgent
where not exists (
	select *
	from Crm.Agent
	where Agent.IsDeleted = 0
	and FinancialSummaryAgent.AgentGuid = Agent.AgentGuid
)

delete from Cache.FinancialSummaryLocation
where not exists (
	select * 
	from Crm.Location
	where Location.IsDeleted = 0
	and  FinancialSummaryLocation.LocationGuid = Location.LocationGuid
) 

delete from Cache.FinancialSummaryCompany
where not exists (
	select *
	from Crm.Company
	where Company.IsDeleted = 0
	and FinancialSummaryCompany.CompanyGuid = Company.CompanyGuid
)

end

-- 500 -util_CleanupCachedFinancials.sql