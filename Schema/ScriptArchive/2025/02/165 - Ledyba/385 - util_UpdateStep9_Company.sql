
CREATE OR ALTER procedure [dbo].[util_UpdateStep9_Company]
as
	update Cache.FinancialSummaryCompany
	set
		FinancialSummaryCompany.TotalPremium = source.TotalPremium, 
		FinancialSummaryCompany.TotalFeeUcpm = source.TotalFeeUcpm, 
		FinancialSummaryCompany.TotalSLFee = source.TotalSLFee, 
		FinancialSummaryCompany.TotalCarrierFee = source.TotalCarrierFee, 
		FinancialSummaryCompany.TotalSLFeeUcpm = source.TotalSLFeeUcpm, 
		FinancialSummaryCompany.TotalSLTax = source.TotalSLTax,  
		FinancialSummaryCompany.TotalEstimatedIncome = source.TotalEstimatedIncome, 
		FinancialSummaryCompany.IsInvoiced = source.IsInvoiced,
		FinancialSummaryCompany.GaCommission = source.GaCommission,
		FinancialSummaryCompany.AgentCommission = source.AgentCommission,
		FinancialSummaryCompany.TotalAgencyContingency = source.TotalAgencyContingency
	from FinancialSummaryCompany as source with (nolock)
	where FinancialSummaryCompany.CompanyGuid = source.CompanyGuid
	and (
		FinancialSummaryCompany.TotalPremium <> source.TotalPremium or
		FinancialSummaryCompany.TotalFeeUcpm <> source.TotalFeeUcpm or
		FinancialSummaryCompany.TotalSLFee <> source.TotalSLFee or
		FinancialSummaryCompany.TotalCarrierFee <> source.TotalCarrierFee or
		FinancialSummaryCompany.TotalSLFeeUcpm <> source.TotalSLFeeUcpm or
		FinancialSummaryCompany.TotalSLTax <> source.TotalSLTax or
		FinancialSummaryCompany.TotalEstimatedIncome <> source.TotalEstimatedIncome or
		FinancialSummaryCompany.IsInvoiced <> source.IsInvoiced or
		FinancialSummaryCompany.GaCommission <> source.GaCommission or
		FinancialSummaryCompany.AgentCommission <> source.AgentCommission or
		FinancialSummaryCompany.TotalAgencyContingency <> source.TotalAgencyContingency
	)-- 0
GO

-- 385 - util_UpdateStep9_Company.sql