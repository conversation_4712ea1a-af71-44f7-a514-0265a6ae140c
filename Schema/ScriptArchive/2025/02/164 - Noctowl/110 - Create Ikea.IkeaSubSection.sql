/*
*  Create Ikea.IkeaSubSection
*  For the coverages, there are two or three used:
*  A. INSURING AGREEMENT
*  B. DEFINITIONS
*  C. EXCLUSIONS (Triggered by included policy parts having exclusions)
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'IkeaSubSection'
    and TABLES.TABLE_SCHEMA = 'Ikea'
)
begin

    create table Ikea.IkeaSubSection (
        IkeaSubSectionGuid uniqueidentifier not null
            constraint PK_IkeaSubSection
            primary key,
        IkeaSectionId int not null
            constraint FK_IkeaSubSection_IkeaSection
            foreign key references Ikea.IkeaSection(IkeaSectionId),
        IkeaSubSectionTitle nvarchar(200) not null,
        Content nvarchar(max) not null
    );

    print 'Created Ikea.IkeaSubSection'
end else begin

    print 'Ikea.IkeaSubSection Already Created'

end

-- 110 - Create Ikea.IkeaSubSection.sql
