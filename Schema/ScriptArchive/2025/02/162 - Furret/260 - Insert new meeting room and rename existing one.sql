/*
*  Insert new meeting room "Meeting Room 3" and rename existing one "Meeting Room" to "Meeting Room 2" in Calendar.Room table
*/

-- Insert "Meeting Room 3" if it does not already exist
if not exists (
    select 1 
    from Calendar.Room 
    where RoomName = 'Meeting Room 3'
)
begin
    insert into Calendar.Room (RoomGuid, RoomName)
    values (NEWID(), 'Meeting Room 3');

    print 'Inserted Meeting Room 3 into Calendar.Room';
end
else 
begin
    print 'Unable to insert, Meeting Room 3 already exists in Calendar.Room';
end

-- Update "Meeting Room" to "Meeting Room 2" if it exists
if exists (
    select 1 
    from Calendar.Room 
    where RoomName = 'Meeting Room'
)
begin
    update Calendar.Room
    set RoomName = 'Meeting Room 2'
    where RoomName = 'Meeting Room';

    print 'Updated Meeting Room to Meeting Room 2 in Calendar.Room';
end
else 
begin
    print 'Unable to update, Meeting Room not found in Calendar.Room';
end

-- 260 - Insert new meeting room and rename existing one.sql