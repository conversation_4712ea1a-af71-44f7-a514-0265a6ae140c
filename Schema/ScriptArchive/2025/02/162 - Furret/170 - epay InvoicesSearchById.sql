create or alter procedure [EPay].[InvoicesSearchById]
    @paymentAttributes nvarchar(max)
as
begin
    declare @invoiceId nvarchar(50)
    select @invoiceId = JSON_VALUE(@paymentAttributes, '$.invoiceId')

    SELECT 
        Invoice.InvoiceNumber AS Id,
        Insured.Name,
        Insured.Email,
        Invoice.DueDate,
        SUM(InvoiceItemTable.GrossAmount) AS Amount,

        PolicyProspect.ApplicationNumber AS PolicyNumber,
        PolicyProspect.EffectiveDateZoned AS EffectiveDate,
        CarrierSubmissionOption.ExpirationDateZoned AS ExpirationDate,
        SUM(CASE WHEN InvoiceItemTable.TransactionGroupID = 2 THEN InvoiceItemTable.GrossAmount ELSE 0 END) AS TotalFees,
        SUM(CASE WHEN InvoiceItemTable.TransactionGroupID = 6 THEN InvoiceItemTable.GrossAmount ELSE 0 END) AS TotalTaxes,
        SUM(CASE WHEN InvoiceItemTable.TransactionGroupID = 1 THEN InvoiceItemTable.GrossAmount ELSE 0 END) AS MinimumPremium
    Into #TempInvoiceGetTable
    FROM Policy.PolicyProspect
    join Carrier.CarrierSubmission on PolicyProspect.PolicyProspectGuid = CarrierSubmission.PolicyProspectGuid
    join Carrier.CarrierSubmissionOption  on CarrierSubmission.CarrierSubmissionGuid = CarrierSubmissionOption.CarrierSubmissionGuid
    join Policy.Invoice on CarrierSubmissionOption.CarrierSubmissionOptionGuid = Invoice.CarrierSubmissionOptionGuid
    join ( 
        select InvoiceGuid, GrossAmount, TransactionGroup.TransactionGroupID from Policy.InvoiceItem 
        join Acc.TransactionType
            on InvoiceItem.TransactionTypeID = TransactionType.TransactionTypeID
        join Acc.TransactionGroup
            on TransactionType.TransactionGroupID = TransactionGroup.TransactionGroupID
        ) As InvoiceItemTable on Invoice.InvoiceGuid = InvoiceItemTable.InvoiceGuid
    join policy.Package on PolicyProspect.PackageGuid = Package.PackageGuid
    join Crm.InsuredAgentHistory on Package.InsuredAgentHistoryGuid = InsuredAgentHistory.InsuredAgentHistoryGuid
    join Crm.Insured on InsuredAgentHistory.InsuredGuid = Insured.InsuredGuid
    where InvoiceNumber = @invoiceId
    group by Invoice.InvoiceNumber, 
        insured.Name, 
        insured.Email, 
        invoice.DueDate, 
        PolicyProspect.ApplicationNumber, 
        PolicyProspect.EffectiveDateZoned,
        CarrierSubmissionOption.ExpirationDateZoned

    select 
        Name
    from #TempInvoiceGetTable

    select '' as EmailAddress;

    declare @MaximumAmount decimal set @MaximumAmount = 0
    declare @AllowPartialPayment bit set @AllowPartialPayment = 0
    declare @DivisionId nvarchar(max) set @DivisionId = ''

    select 
        Id,
        Name,
        DueDate,
        Amount,
        @MaximumAmount as MaximumAmount,
        @AllowPartialPayment as AllowPartialPayment,
        @DivisionId as DivisionId,
        PolicyNumber,
        EffectiveDate,
        ExpirationDate,	
        TotalFees,
        TotalTaxes,
        MinimumPremium
    from #TempInvoiceGetTable

    drop table #TempInvoiceGetTable
end