/*
*  170 - Update Coverage.CoverageIssue
*/

if exists (
    SELECT 1 FROM Coverage.CoverageIssue 
    WHERE CoverageIssueGuid = 'bf6bf1b5-0b3e-4adc-be6b-ef90e2ddffb7'
)
begin
    UPDATE [Coverage].[CoverageIssue]
SET CoverageIssueDescription = '{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;
        }
    }
{\*\generator Riched20 10.0.19041
    }\viewkind4\uc1 
\pard\f0\fs22 Carriers handle coverage for bacteria differently, with some offering affirmative pollution coverage (Covered), others remaining Silent (coverage uncertain), and some excluding it entirely (Excluded). Bacteria includes living microorganisms that can multiply in various environments, often associated with waterborne or foodborne illnesses. Coverage considerations for Legionella are addressed separately. \i\f0\fs17 Note: some carriers may impose a communicable disease exclusion that limits this coverage. \i0 \par
}'
    WHERE CoverageIssueGuid = 'bf6bf1b5-0b3e-4adc-be6b-ef90e2ddffb7'
    
    print 'Successfully Updated Coverage.CoverageIssue'
    
end else begin
  
    print 'Failed to update Coverage.CoverageIssue with Id bf6bf1b5-0b3e-4adc-be6b-ef90e2ddffb7'
end

-- 170 - Update Coverage.CoverageIssue