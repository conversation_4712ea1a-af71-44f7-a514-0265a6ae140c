-- This script creates a table named DirectSendTemplate in the Mail schema of the
DECLARE @DirectSendTemplateId INT = 0;

-- Check if the Description already exists
IF NOT EXISTS (SELECT 1 FROM [Mail].[DirectSendTemplate] WHERE [DirectSendDescription] = 'Pulse 0 Day Unconfirmed')
    BEGIN
        -- Insert into Mail.DirectSendTemplate
        INSERT INTO [Mail].[DirectSendTemplate] ([DirectSendDescription], [DirectSendTemplateGroupId], [IsDeleted], [StorageLocationId])
        VALUES ('Pulse 0 Day Unconfirmed', 6, 0, 0)

        -- get the last inserted id
        SET @DirectSendTemplateId = SCOPE_IDENTITY();
        -- Insert into Mail.DirectSendTemplateVersion
        DECLARE @NewID UNIQUEIDENTIFIER = CAST('36f5905d-9a68-4fc9-a459-a2d3b4118708' AS UNIQUEIDENTIFIER)
        DECLARE @CPLGUID UNIQUEIDENTIFIER = CAST('10c86bc2-4768-4b0b-bffd-3c15f34894b3' AS UNIQUEIDENTIFIER) -- CPL Program GUID
        DECLARE @VariationSortOrder INT = 1


        -- This script inserts a new record into the Mail.DirectSendTemplateVersion table with the specified values.
        --check if the template already exists
        IF NOT EXISTS (SELECT 1 FROM [Mail].[DirectSendTemplateVersion] WHERE [DirectSendTemplateVersionGuid] = @NewID)
            BEGIN
                INSERT INTO [Mail].[DirectSendTemplateVersion] (
                    [DirectSendTemplateId],
                    [VersionId],
                    [HtmlBodyTemplate],
                    [SubjectLine],
                    [SendFromName],
                    [SendFromEmail],
                    [RecordCreatedZoned],
                    [CreatedBy],
                    [VariationSortOrder],
                    [BooleanOpCode],
                    [DirectSendTemplateVersionGuid],
                    [IsReleased]
                )
                VALUES
                    (
                        @DirectSendTemplateId,
                        1,
                        N'<div>
                             <p>{ExtraData.AgentName},</p>
                             <br>
                             <p>
                                 Current coverage for the above referenced expires tonight at midnight. We can still process the renewal for you in time to avoid any lapse in coverage. The above referenced policy is scheduled to renew soon.
                             </p>
                             <br>
                             <p>
                                 In order to bind up coverage please click this 
                                 <a href="{ExtraData.PortalLink}">link</a> 
                                 to review the quotes again, generate an e-sign document for your client to sign, and finalize coverage. Looking forward to wrapping up this renewal with you. Please call me or send me a note if you have any questions.
                             </p>
                             <br>
                             <p>Thanks!</p>
                             <br>
                             <p>{ExtraData.BrokerName}</p>
                         </div>
                         ',
                        'Re: Renewal of Policy #{ExtraData.PolicyNumber} for {ExtraData.InsuredName} to a NXUS CPL Policy',
                        '{ExtraData.PrimaryRepName}',
                        '{ExtraData.PrimaryRepLogin}',
                        GETDATE(),
                        '00000000-0000-0000-0000-000000000000',
                        @VariationSortOrder,
                        'AND',
                        @NewID,
                        1
                    )

                -- Insert into Mail.DirectSendTemplateVersionProgram
                INSERT INTO [Mail].[DirectSendTemplateVersionProgram] (
                    [DirectSendTemplateVersionGuid],
                    [ProgramGuid])
                VALUES
                    (
                        @NewID,
                        @CPLGUID
                    )
            END
        ELSE
            BEGIN
                PRINT 'Template version already exists.'
            END
    END
ELSE
    BEGIN
        PRINT 'Template already exists.'
    END
