/*
*  180 - Update Coverage.CoverageIssue
*/

if exists (
    SELECT 1 FROM Coverage.CoverageIssue 
    WHERE CoverageIssueGuid = '46a53d58-5d97-4a6b-90c5-f497f6fdb100'
)
begin
    UPDATE [Coverage].[CoverageIssue]
SET CoverageIssueDescription = '{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;
        }
    }
{\*\generator Riched20 10.0.19041
    }\viewkind4\uc1 
\pard\f0\fs22 Carriers handle coverage for viruses differently, with some offering affirmative pollution coverage (Covered), others remaining Silent (coverage uncertain), and some excluding it entirely (Excluded). Viruses are defined as non-living infectious agents that require a host to replicate, commonly linked to airborne or surface transmission. \i\f0\fs17 Note: some carriers may impose a communicable disease exclusion that limits this coverage. \i0 \par
}'
    WHERE CoverageIssueGuid = '46a53d58-5d97-4a6b-90c5-f497f6fdb100'
    
    print 'Successfully Updated Coverage.CoverageIssue'
    
end else begin
  
    print 'Failed to update Coverage.CoverageIssue with Id 46a53d58-5d97-4a6b-90c5-f497f6fdb100'
end

-- 180 - Update Coverage.CoverageIssue