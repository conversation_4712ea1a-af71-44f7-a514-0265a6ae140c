/*
*  Update Acc.FeeTax
*/

if exists (
	select 1
	from Acc.FeeTax
	where FeeTaxID = 825
)
begin
	Update Acc.FeeTax
	set FeeTaxDescriptionOverride = 1, FeeTaxDescription = 'Fire Property Tax (SD) 0.175%'
	where FeeTaxID = 825

	print 'Updated Fee Tax 825. DescriptionOverride set to True, and Desctiption changed to Fire Property Tax (SD) 0.175% per the users request'
end
else
begin
	print 'Fee Tax 825 was not updated'
end

-- 150 - Update Acc.FeeTax.sql

