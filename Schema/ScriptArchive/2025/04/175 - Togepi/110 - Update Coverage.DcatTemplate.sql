/*
*  Update Coverage.DcatTemplate
*/

if exists (
    SELECT 1 FROM [Coverage].[DcatTemplate]
    WHERE DcatTemplateId = 1
)
begin
UPDATE [Coverage].[DcatTemplate]
SET HtmlTemplate = N'
    <!DOCTYPE html>
<html>

<head>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Poppins:ital,wght@0,400;0,500;0,700;1,200&display=swap"
        rel="stylesheet">
    <style>
        body {
            font-family: "Calibri", sans-serif;
            font-size: 13px;
            margin: 0px;
        }

        .front-page {
            position: relative;
        }

        .front-page-container {
            height: 100%;
            width: 100%;
            display: flex;
            justify-content: space-between;
            flex-direction: column;
        }

        .front-page-title-container {
            display: flex !important;
            flex-direction: column;
            align-items: flex-end;
            padding-right: 30px;
        }

        .front-page-title {
            font-size: 20pt;
            margin-bottom: 5px;
            color: #146492;
        }

        .front-page-line-top {
            width: 60%;
            height: 1px;
            background-color: #dadbdc;
            margin-bottom: 10px;
        }

        .front-page-line-bottom {
            width: 50%;
            height: 1px;
            background-color: #dadbdc;
            margin-top: 10px;
        }

        .front-page-details-container {
            display: flex !important;
            flex-direction: column;
            padding-right: 30px;
            padding-left: 70px;
        }

        .front-page-details-container-row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            margin-bottom: 50px;
        }

        .page-size {
            height: 842px;
            width: 100%;
        }

        .section {
            margin-bottom: 24px;
            font-size: 13px;
        }

        .single-section {
            page-break-inside: avoid;
        }

        .dcat-image {
            width: 400px;
            margin-bottom: 30px;
            margin-top: 30px;
        }

        .title {
            font-size: 21px;
            color: #146492;
            font-weight: bold;
            margin-bottom: 16px;
        }

        .subtitle {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 16px;
        }

        .text {
            font-size: 13px;
        }

        .table-header {
            font-size: 13px;
            font-weight: bold;
        }

        .table-data {
            font-size: 13px;
            font-weight: normal;
        }

        .coverage-event-container {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 8px;
        }

        .coverage-events {
            width: 70%;
            height: 35%;
        }

        .page-wrapper {
            display: block;
            margin: 0.7in;
        }

        .page-wrapper-top {
            display: block;
        }

        .full-table {
            width: 100%;
            border-top: 4px solid #7fb7df;
            border-bottom: 4px solid #7fb7df;
            border-collapse: collapse;
            margin: 1rem 0 2rem 0;
        }

        th,
        td {
            border: 2px solid #dfdfdf;
        }

        .footer {
            position: relative;
            height: 100px;
            overflow: hidden;
        }

        .footer::before {
            content: '''';
            position: absolute;
            bottom: 0;
            left: 0;
            height: 60%;
            width: 100%;
            background-color: #b8cee0;
            z-index: 0;
        }

        .footer::after {
            content: '''';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 200px;
            height: 100%;
            background-color: #0d6699;
            clip-path: polygon(100% 0, 100% 100%, 0 100%);
            z-index: 1;
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                margin: 0;
                padding: 0;
            }

            .first-page-footer {
                position: relative;
                bottom: 0;
                width: 100%;
                z-index: 100;
            }

            .first-page-footer~* {
                page-break-before: always;
            }

            .first-page-footer~* .footer {
                display: none !important;
                height: 0px !important;
            }
        }
    </style>
</head>

<body>
    <div class="front-page page-size">
        <div class="page-wrapper-top" style="height: 100%;">
            <div class="front-page-container">
                <div class="front-page-title-container">
                    <image class="dcat-image"
                        src="https://ucpmpublic.blob.core.windows.net/ucpmpublic/Production/6f/7d/bcb6/22164b6eaec6d8e05a606b99/DCAT_Logo_Teal.Blue.png">
                        <div class="front-page-line-top"></div>
                        <div class="front-page-title">ENVIRONMENTAL INSURANCE</div>
                        <div class="front-page-title">COVERAGE REVIEW</div>
                        <div class="front-page-line-bottom"></div>
                </div>
                <div class="front-page-details-container">
                    <div class="front-page-details-container-row">
                        <div style="display: flex; flex-direction: column; align-items: start;">
                            <div style="color: #808080; font-size: 13px;">Prepared for</div>
                            <div style="color: #808080; font-size: 16px;">{Flex.InsuredName}</div>
                        </div>
                        <div style="display: flex; flex-direction: column; align-items: end;">
                            <div style="color: #808080; font-size: 13px;">On behalf of</div>
                            <div style="color: #808080; font-size: 16px;">{Flex.Company}</div>
                            <div style="color: #808080; font-size: 16px;">{Flex.CompanyDba}</div>
                        </div>
                    </div>
                    <div class="front-page-details-container-row">
                        <div style="display: flex; flex-direction: column; align-items: start;">
                            <div style="color: #ffffff; font-size: 13px;">{Flex.FormalizedToday}</div>
                        </div>
                        <div style="display: flex; flex-direction: column; align-items: end;">
                            <div style="color: #808080; font-size: 13px;">{Flex.CopyRight}</div>
                            <div style="color: #808080; font-size: 13px;">Patent #10,475,126</div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="first-page-footer">
            <div class="footer"></div>
        </div>
    </div>

    <div class="content">
        <div class="page-wrapper">
            <div class="section single-section">
                <div class="title">
                    Contractors Pollution Liability
                </div>
                <div class="text">
                    Contractors Pollution Liability provides third-party coverage for bodily injury, property damage,
                    defense expense, and clean-up costs for pollution conditions arising from covered contracting
                    operations
                    performed by or on behalf of the insured. Coverage is typically triggered by the following chain of
                    events:
                </div>
                <div class="coverage-event-container">
                    <img class="coverage-events"
                        src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABBMAAAFgCAYAAADpSDGVAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAMGaSURBVHhe7d0HnDVJWe/xe71GVIxXxYCAAcQIBkCCqIiAWUBBSUpSVBQVTKByRUBRQDIKEpUkYWGXsOScF1jysoRdWFlYliDIIqLn+u2d5916e6tnzpk5Z+acmf/v86nP+053n+7qqu6nnvrXU9X/axZCCCGEEEIIIYSwABETQgghhBBCCCGEsBARE0IIIYQQQgghhLAQERNCCCGEEEIIIYSwEBETQgghhBBCCCGEsBARE0IIIYQQQgghhLAQERNCCCGEEEIIIYSwEBETQgghhBBCCCGEsBARE0IIIYQQQgghhLAQERNCCCGEEEIIIYSwEBETQgghhBBCCCGEsBARE0IIIYQQQgghhLAQKxcT/vu///tC6b/+67+SkpI2PI3f63A4Gddz71lISkrarDR+r6Vw+BjXce9ZSEpK2sw0fr8PipWJCXVjbvYzn/nMkP7zP//z2L9JSUmHI9X73Rq2sNlM2e+kpKTDkdp3Ovb78FH2u2x41XObqv6TkpI2J/XeY/8epA1fuphQN1I3WDd5EDcXQlgt3uuxsxKndLPZrk5DCIcL73be98NDz36nLkM43Hjnve/tO7+f7/1SxYQyYm7mP/7jP4Z/QwhHh3r345BuHmW/1d2nP/3poR79HUI4GnjfvfsH5ZCGvaGu1Jv6U4+puxCOFuW/sQH76YMvTUwoI1ZOaAjhaMIWlJhYDmlYb9SRhkedfepTnxrqLYRwNPH+RxTeHNSPVPXGjocQjiZsATugP75fPvjSxIQyYuedd97WlhDCUeaTn/zkvhqzsDvUTQkJ7Lf/hxCONkRFqUa4wvrSCsH88BBCKHuwHz740sQEGf73f//3rb9CCEcdhiyCwnozFhLiiIYQCj5dCQqx3+uJetG+VnsbQghgF9jw/RAUliImyOAnPvGJodMQQggFu8DByejWesJ214iWukodhRAKduHjH//4vo1uhcUoIUH9sN9seQghFPzvGtTj363Khi9VTAghhBYG7N/+7d8GpzTO6HrROqI6DJmiFkIYwzYY3Vq1MxoWQz2oD/WifrSzIYTQwsf72Mc+Nvh3q4wwW5qYEEMWQhjDNpx77rlxRtcQdaFxoVp/5CMfyRSHEMKFICZwRiMIrxfqQX2oF/WjjQ0hhDHsw6ojzJYiJnBKY8hCCD0+9KEPxRldM8oR1bgQgs8555ytPSGEcAF8Oza8FYTDwdNGJagf7WsIIYzh4xkwWmV0wtLEhIxqhRB6iEyQau2EiAkHjzpooxI4oyGEMEaH9f3vf3+mq60RJQZXVIL6Yc9DCGFMDRjV2oarEISXIiYwaiGE0OPDH/7w7IMf/OCxMKuMbB08NaqlcdHIEBRCCKHH+973vpWPbIX5acVg7et73/ve+OEhhC4lOBIVVjXVIWJCCGGl6Kz+67/+a6Y6rAk1qlVTHM4+++xEJoQQJnnPe95zbKpDxISDp8SEmuLw7ne/O354CKHLRz/60eME4YgJIYSNQ1TCqg1ZmJ8SEwg7GpmzzjorayaEECZ517veNdjxVYbJhvlpI8vUy+mnn55pDiGELqKXzjjjjJVON16KmBAjFkKYwsj3mWeeORi0rJtw8JSYQNgh8AiR/cAHPrC1N4QQjkdnlY3IVLX1QPmrB/WhXt7xjnfEDw8hdOF7iy4zaLSq6LKICSGElUJMWLUqGuZH2asDdaFO1E3EhBDCFKeddtpxc251ZmPDDwblXmKC+lAvb3/72+OHhxC68PNMhYqYEELYWDg7DFnm3K4HJSbUfFuKtToKIYQeOqurXsArzEfEhBDCIpSYYEoUv28VU9UiJoQQVorIhIgJ68NYTFA36iiEEHrorFpEN2LCwRMxIYSwCOXnrXLdm4gJIYSVwgmNmLA+9MSERCaEEKYoMcEXeSImHCw9MeFtb3tb/PAQQhd+nkV0TWeNmBBC2Eg4OxET1oeICSGERYiYsD5ETAghLELEhBDCxpPIhPWiJyaooxBC6BExYX2ImBBCWISICSGEjSeRCetFT0xIZEIIYYqICetDxIQQwiJETAghbDyJTFgvemJCIhNCCFNETFgfIiaEEBYhYkIIYeNJZMJ60RMTEpkQQpgiYsL6EDEhhLAIERNCCBtPIhPWi56YkMiEEMIUERPWh4gJIYRFiJgQQth4EpmwXvTEhEQmhBCmiJiwPkRMCCEsQsSEEMLGk8iE9aInJiQyIYQwRcSE9SFiQghhESImhBA2nkQmrBc9MSGRCSGEKSImrA8RE0IIixAxIYSw8SQyYb3oiQmJTAghTBExYX2ImBBCWISICSGEjSeRCetFT0xIZEIIYYqICetDxIQQwiJETAghbDyJTFgvemJCIhNCCFNETFgfIiaEEBYhYkIIYeNJZMJ60RMTEpkQQpgiYsL6EDEhhLAIERNCCBtPIhPWi56YkMiEEMIUERPWh4gJIYRFiJgQQth4EpmwXvTEhIOKTHjpS186O+WUU7b+CuHwcPrpp8+e//znHwpbFzFhfVg3MeGtb33r7AUveMHSOyfrwPve977h3j7ykY9sbZkNnbEXvvCFQ+cs70DYBCImdHj9618/e+xjHzu7293uNvv7v//72bOf/ezjXvQwzT/8wz/MfvmXf3n28Y9/fGtLOCje8IY3zK573esOnanDTiIT1gtlPxYTlhGZoOM0byoudrGLza585SvnedhnTj755Nn1r3/92Wmnnba1JSyb293udrPP+qzPGt6vvfDc5z53dr3rXW/oMB7UexIxYX1Q7qsSE/glPXvdS2efffaQl5ve9KbDc/7Rj3506yyHh/vc5z6zz/7szx7ewep8velNb5p97ud+7uxP/uRPVtr3eeADHzi72c1uNtRxWH9e9KIXHfd+vOIVr5idccYZ29pJ7S+xii+2SiImNDzqUY+affmXf/nsf/2v/9VN//zP/7x1ZJjiile84lBWr33ta7e2hIPib//2b4e6+IM/+IOtLYeXRCasF8p+LCYsIzKBQ9na5O2S6+JrvuZrIiYcALe+9a1n//t//+/ZQx/60JT9iigxgRO3F37jN35jOI/Bk4Ma/Y2YsD4o91WJCde61rXmtuN8cvm48Y1vfOjFBIOWYzHhj/7oj1ba97n85S8/2Og3vvGNedfWHP7MF3zBF3TfE+mHfuiHZv/yL/8y2M2WX//1Xx+er7e85S0rreOICVuIQlAhX/3VXz37y7/8y9mLX/ziwRF+5zvfOTvppJNmt7jFLYZwpP2GuPHyl79866/14MEPfvDsR3/0R7f+Oh5q2T3vec+tv8Kq+eAHPzg8t+ecc87WlgvwUv/5n//5oFwedhKZsF4o+7GYsIrIhD/7sz8bnKHf/d3fnT3vec87bl8RMWF1vOxlL5v93//7f7tOA+flL/7iLw5lB2BdmBITjDYa1ZzXmdORV1fnnnvu1pb9J2LC+qDcPTv7EZnwtKc9bfY5n/M5s6tc5Sqz5zznOcftS2TC3iMT7nWve81+7ud+bqjLMQSM+93vfhfqgIb1o8SEq13takPUn/fj6U9/+jBo6P344i/+4qEv8JSnPOW4+oyYMGKVYsKTnvSkoRIue9nLDp2zdUFIi3ytm5hw7Wtfe/YjP/IjW3+Fg+Qf//EfJ8WEo0QiE9YLZT8WE5YRmTDm8Y9//CAmCNecqu+v+qqvipiwIkQ9fcVXfMXSnYYwHyUmGPQoOJIGIf7wD/9wo+olYsL6oNxXJSaMMYVYx1mHVwekRyITdg9f/Sd/8ie7YkLYHEpMMB3tU5/61NbWC3jMYx4zPDO3utWtjqvriAkjVikmfOM3fuNQSe95z3u2tswH4UEI5+/8zu8MHWxhnU9+8pO39h7P6173umEUDQrcKMBP//RPD3NK73KXu1zISD7zmc+cfe/3fu/QUbz5zW8+/FaiRkHn0d9GnY0m/L//9/+G8wl1aR0L13Wtm9zkJrMf//EfH871kIc8ZPbJT35y64jjae/J8SIydFjPOuus2Yc//OHZne985yFPl7zkJY/lSSqe+MQnHvc32nvXqai8uvftRq7Ux9/93d8NDYn7+tVf/dXjrsn47oSG6uEPf/gwcqmO3L/1MHpwZpxX4ymfd7rTnQYj/PM///ODUfeSTKGhveMd7zi7znWuM5SZ6A0v15j73ve+Q93iYQ972FAv7s3/C9fRwPzar/3akOdf/MVfHMpp/HzqSF3qUpca6uP2t7/9sXKh/KPuR/mPOfPMM2f3v//9B0PjGr/92789jAr08MxVtIlGjlr+Ez/xE7Nf+qVf2jYK5alPfeqx5+iGN7zhkBcRP6sgkQkXdOD9W+mgqLwsOzJhjLA+YoLw7Kn7tWbCVa961WG/qWrez5/6qZ8abNl264mwkWzljW50o9nP/uzPDnbLHMVFEM0mb7/1W781hPfe9ra3HUYTenk1yi9Czj7vrI6639zgBjcYouU00FPI1+///u8Px7vWox/96K6NF6n06le/evg/G+Pc7I93v/KkfbrHPe4x2ErvLvtr9GMstLNxX/mVXzm0nX/6p396zP5UBJ/7cT22Zsyb3/zmYcSMTWd/7nCHOwxT43rloo4f8YhHDPte8pKXDPfpN0ZjpqZQuHdloE12D+zsXe9618lrtIhwYdc4QsqKDfuxH/uxYbR/7ByxwZ4n7cQf//Efz571rGd1z2+hRHWovNlOdeSe2kgAgwfbRZKx19qPNg/jNRPUnTK1zUhv1Yu62s6x8/y4tvNU/nUgvSPvfe97h3dYW8yOu1f11R67DI66mOBe1VHd90Hee+VlXcSEikzQibIw4e/93u8N7xG/7glPeMK2z7Z3kn/DN/M7Pup2x7ewIY973OOG4w06lk/GFvDZ2hHgFuWl7XBdNvk3f/M3ZyeeeGL3uotEJvAD2Zzeddk276t6Un/6CN5p5/7Wb/3WwT9lC5yjzulc2rVevfL7tEfeef6646beeXap8l/lpLzZX+U/b3kXfGT34pnQT3AfbFR7nte85jVD34ltV8bssL7KuJ3U/si756aXD/XiWuN18ZSj8/O/PWvq8pGPfORgs8dloL61mfLLX9B+sPm9snJv2jDl49zqRZuzUxmxwduJCfoN9iuLdn/EhBGrMGLgnOiMaYAXwctuxMtvOZo6cz/8wz88/P193/d9F3KgGB77OJYqnDPLWfSS2s7hLWfN4oUcvOooXu5ylxv+bjudVre1z8P97d/+7ccdx/jDA277d3zHdwyLIjKkdaxQmTFWPBeyar9k/QMOif8zVBwS5/f3l37plx7Lk1S4jv3nnXfe1pYL7t3ijL17F4Y87qh7we372q/92tktb3nLoTHxf9sIGf6uTvkUDN83fMM3DL9xTXXEsfT3ZS5zmcF5aRFuZ58yVbfmk3HWGAjbjcAxYGMYA/uVv+N1XPzt2uOpMZe+9KWHvHNQHUPIUn7ut1DuX/ZlX3Ysz4yOYz//8z//2PkYOr/z3Nj3gz/4g8fqQggU6n5aoQIWYxESJTFmDNv3f//3D8de4xrXuJBR1QDZp3H8oi/6osG4cyYda7uyHHdcGDT7lKEGn/DwLd/yLUP9MzrLxrOuvp37qIkJ7lNiuNWDd4+z4e/at9+4pjpYdWTCvGKC0ZkrXelKg33z/HLuLn7xiw/PKEdj/Fu20PvLzulMcx69k45/wAMeMFeZiijzmy/8wi8cHADvWa0pw/6Obd7f/M3fDPdCkPZusr1sNufVdu1Br2Muf/Y7p3fNO+ca2qGxY8Uh5yBxjvzm277t2wabUQIk2D5lI8/sD3vm2K//+q8f6hOE3Ktf/eqzL/mSLzl2P2V/OC2o+yHYtOVlHrR8sPvyzhayIc7zK7/yKxdyQDiznGGCrryxJWWznJ+taX+jM6rNs09EijJ0L/L/Td/0TZMdgIKIrM7co3xe9KIXHeyra9Z1dPi/53u+Z7iGRW5vc5vbDOd3D67X5ueEE04YzuNZkA9CgmmCfvtXf/VXx44lNjiOT9J7vkROaj/a/I/FBGLVD/zADwznVodVL9rQcbm2EAqcR9tQ17YQNSf07ne/+zD903ncm/pQPu51Kq+74SiLCe5T/bCZ3ln/tjZ8v6n8rJuY4B3kg7Dnnv0rXOEKw7POVo5/67z8Pb/jQ3nv+CCO57NPXauFbfE+sQlsHbvpugb6nIewNj4PX9n7wtcxEMUmy598sP98uLZO5xUT9Av4gGxIL+/aQNNF2BvnMX1BObmuSKWyBYRR9Qpt4f/5P//nOH8dbLI8aR/YDqIAe+2eCcvj54B91om+5jWvOdgo/Snl/c3f/M3Db3TW5312fuZnfmbIE/uiXZBvbTGBou5bR11ZVN9GnbCPriUPdX/Q/nze533epGhSne1WKCFQKX82W3vDvuvfKUsieHuef/qnfxqO1S9xnGdE/rXhBs+qTvELv/ALw77v/u7vHsQJ7Z97cF4d8e3e9Z3EBKKN++Sbt89HxIQR8z6Ii8Lh8dBStBaBc+F3z3jGM7a2nI9RJw2tjmVLdaglI7YtHgLbOQItlC7be9McSkzwknn5SkAY0xt5c7zfvvKVr9zacj5eRnkfLzQ5dnoZ1ak1E7YTEyTzeVo4bLZbAKrlEpe4xOAMjY2cF1Y+56HucxyJYASIceVYlnOM6nxLnKQWdWa7xqDFehq2M7gtVacczRaGVp1pbCx606NGDlt05J3P6FcLI297b5rDlJigU6JBHgsjVU9EjJYSExgyo40tlS/PalHXNZI2ZlXrNxzVyAT3yGBzPBlvRpwtUAb+PiiH1PXUQSsmHGRkgufRSHt7DOFFZ9M+c3NbODHES05LwZlj1x3fRn9NwU55Z8a2RDiiPOuMt41tdb7ZprHQaaTDPp3t9h44NrZXREOhc2g7e9Fu57Swq5wZI849vOPjsiQqOJ8or3YfB2tqmkNPTNCW6JwTUce2gMPpeI5hez6dV9vZzHe84x1bW8+n8sWBrmu4rvvUtozvYyzu9tBx8Ht2eko44pBf5CIXGdrmdj+BXH6MgtV2bRbhoRUBICKvFXuWISZAp4/DSnzp1UuP7cQEz7sIh/ZcnEaONj9ofF+75aiKCe7RvbKXOgnsN1vj/7YdRDm4nvpeNzHhO7/zOy8UIeU597wbBGqfUT6l7eNIBJ1c2/kuOz27xATXJQaMB1nk1z7Xbc9TYh770163RGKidtvZXZWYUPjN1DSHnphglNy12fY2atj/XdvxojXaeyYmuGf2QARzSwk6bWd9CnbIPWjntE/t8eOIAO1Ue5/QkXetNgJtN2KC/gexetxp9wy0A2f8AOVLpGmjzNgy9pEfUWWrXOWDODvOB7u3U9lsJyYQCghcxBsRzO25IiaMWIURA2dEYzkV4t1DiKbfMHA9OAX2MyZFddQooj10bBmZlnnEBI5n2yGeBwKI3xohKhgy2zzoO7FbMWHcSS04SAxw4aV2vHONEcpk305TUioqg3HpIcrCfkaxqE4w9bBHRSi036znVBotG4se0FlwPOeocK+2EZ0WhSGRh5ZFxYQSOTjMPai/9redmRITNMI9NCIapUL4ruP38+snRzEywf1JGnWOgkaOw+F5GzukjPp+lodruW4rJhxkZILnsdd55lD6fTv1gHBoGydxfE6OqX1GlLcrT4Ilp8aoVO84252H01NU55sD2fuNEQz30doTnXK2s9doGyknHLT75Gn8fs8Dp8lvzcNv87aomMBxdh6jOb17NJLGznl2ixIThNKOf+O54tyKDqs8KD/HE2V719iJEhN60xrAKXNNx433i/Cwz7zV2sepJFrt1HFZVzGBqNLzv4zY+c2yfLOjKCa4P3WkDNlq9rLst3/5FW1Z7Fd5VL5ce53EBHZ+/B7xBf3eYFzlT5513PiQ4zzr7NrH1kxdrygxoe3oF8RQ52nFATZHXkS+9cqKnfJOtSPW6yYmmDZgmzyMnzfPp/aUaNB2aEtMMEA5Lid+oHzxR8f7xrBvoiHGneV5McVCubVix27EBG2Q52OnfIgC8Fu2a1xW2vi2rvkO8kag7uVjJ0pM0H73kvO27WYRMWHEbgp/HjhHKkIna16MuPqNh6PHqaeeOux37qI61BYK6yGUlPPXMo+YIExmEai6nGS/NapVmHZgmwduJ3YrJnjYewhjYqAKL7XjOZFjKtpgHFY/hjFxHNGgB6fF/jaioDrfbbm0mKZhvxCowt/yb37ZOJlCYH+7ZgFjKSpgEdyrKAbzkzmlLYuKCRxM23rRD6hIi/Y5LTFhSgAR6dBGbGisOaB+w+Emvq0a9em5OQgxwXUOKmkwOQmeEc6n0YM2lUPKsO+nc+466qAVE9TRsplXTBDe2mvYPNN+T1it3xvRss25x++0KCf7hHFuV5YcF8f5Te842zmpbHztr843MaP3GyG+9teUBM69c7C54y9ZSEbTHK8OCsebBrFII+8Z0sH1W/fd/nZRMUGklm2c/R5sr+u0UQUlJoyjEgqOr85G5UHbqI0SZaeTPE80QkuJCaae9erh3ve+97Dfv71ylx8jVeXMsp+O54B73qbarnUVE0T0tNcsTHnwm0XLd4qDEhNc46CS+mEndVp0BHSsxzacY648HOd4v1s1lTfXXScxQR5696+zyBeu/LGhOrD8Wp308TvKZyG0ur/tKDFhqlNpKpnw/9pvCoB3xoBd793jy7vXNux/3cQEAjVbM3XP7LHftBEIxASRY+15CuXtXviUPTtSnHbaaUNe2bSp52A7dGL56MpAH63KbTdigufGNr49m92rS2hL+br6j+NnTDsiL6Le3bf3uKbfERrY16nz9igxgdhS61+IGNOHMaipTvQjx+9IxIQRvYdgGXBedHraUfqdEDrqN+NpAoWOnf1tR7861F70HpyPr/u6r9v663zmERPGYe8twm6cwxwi4S9jVavtNJcI0IbqTLFbMWFqcUr3zhC16JzKb1tepkg4D0FhJ4QFO1bneAr7zaUrep3vloro0GDAaJ2/d0oaiaKU1+3QYTHqI6RP5El7rr2KCVRn26Y6dqIuGDuNWFFiQhuR0eLZMnexRdh4RXJIGl2jtativyITnLMSQ9kmDcZ+JvfIQSghwTPg/R07o5Jj9lNQcA35a8WEg4xM0JHr7bfuit+3UQhsgm317PaSCLPtytF76RxTHWD2m5NqbmqdpzrfU51JTqf93mv72SPn6OWvTW1EhuPlfbtGXrSGcH0hmsJy23PtVUxg143Uew57sH3y2E6nKDGBg9KDE8WutXngUJWYIt/qtDfS1qPEBKJN73hzXeu8U4nT2N4jR9p926f95NiOy2xdxYReBAZEdfrN1HSZRdkvMcE5JffUJtfb78RGlpCgc8YRH9tunW3PvuMcL6+rKJeWKh/1sE5iwjiEvtBZNGe/8ieKybM7fi/bZFBnqsNcEBO8r1PH8UXlq/brJHr3DP706sh7Kq/ah7rXdRMTHG9qQu94sDnya5Co7pGYYCpXr5xM+XIvBhM981PogCub3jSAMQZFtS/W7er1bfYqJsCaE+re+aydZK2h8e/H/nkvyWf9jn/mOVXm9pluyAfZrlyKEhN60xxgrTT3SQxqBeuICSN2erh2iwdYpSrwedG4+o2HtIdFsuw376eoDjVHsMduxYS209eiQa6wWPfGQam5ZkalbW/FBMKHbeP5aD12KyZsd+9jMUEZVv6NKnpB/N+8oHlGODlHjp8Sb3Ry7G+nXlTneyqaoRaFZGTA+PjbVIpWkRyntgHkDFoEcwqG2jl1foRseTkLz8dexQTPpG2+ztHDc2I/xbMoMUHETY+emFAIBbQSrw6Hc7TROsvEM6E+ViUmOJfEODp/JUbzoBLngqPH4dRIeXfdv+eNgzb+dz8FBedXB8qoxIR53ttFmVdM8N709vfEBI6Xbfb13mdpar2TwkJMzsFO9yDMcUJFG9R1q/NttLuXVyKm/ZwG++XPOdiA3gh5pfZcjrco2FQjX/fOSXadGnX2rPntXsUE9UCgmHKejOK4ThspUmLC1G96YkLB4RVyrOPA/rDtvbJtKTFhSnyoDrwvSfTKW3rVq151od96/9hko13uhwPerr2xk5jAzh6EmGAUtXeeTRMTnMt9sIHrYL8l75X7ZZ+14UT4nv2W2HqdCHbVfSyzbMZUWa2bmKCt6zElJuhA9iITJIMbO70fO0Um1Bz92u+d8O6JRu3Vjw6de9U+VF6XJSbwS5clJvDvp8QE7Y3fiCSoe2TLjNLvRUxwnHrsddpbvAs1wi+KkL307shLnWMRMcHUafczFhPARohy0W6p5+/6ru867jhtn8HBXmRCJX2Z8Xm1q/p3otHdhynEU89+sZOYAP2Zev7qGYiYMGK7h2uvcG7Me/eyzkNNE5iKZuCE2W9RrGK/xQRTMOxn3MYI27GvFRNqmsNUtEXLfogJ6sILTOTgANanZ+ald48t5irbL1SzqM43J6wHxdT+EigYGn9TR+dlOzFBmTmfY3ow8nsVE2o9D05jj1pQsX229yImtJiD7DyLTCmal1VGJjiPpCFkIFsHb5OSfO+XoODc6kBdlJhwkJEJi4gJnAvb5pnyNQW74xwio3rXrUUY23xX53u8yGFBCLS/BAoj5xwcDlXv+B6OnxITdObsNyo13q/+7NurmKBTYNuUsKQdsl87VL/Zi5hQcG7YaefhBG/HTmKC9TLsn5qOMg/KRL5FTNR91XnbDn0LPyViwu5wHvfAJnkWevZxE9J+CQpVXpsqJmgXdKxFqE7ZjXkoMaHXIYTFVQ10VefuQQ960PDOTH2uUnSufGkfKl/zign8Qz7g1JcoRCk7z17FBBFQpoFMdVjZfFPI2t8sQ0zwfKlH06qmngP4XKfzCfUfP4+1LkErJvikpPMa1Oo9v8qmbOh27xPb6DzKv8qSL2+aSxsJsCgi3dSbqLztymceMaHqs/2MZsSEEb2HYFlUZ0lnZx40eI5vQ+RbOKP2t/PSdyMmmJPpN17GMTuJCUax7Pcd2TGcDPvajrZFQmyjmO6Ez6sZOe+xLDGBAzM+zyJwSIxGjRe1LGqtjPZrHNX5Fq41Rj40HPa3DZq5SrZxvOZhOzGBMXMuzvMYX16wbywmUDRt781b7YkJFEvbjNb1MCVmfL5liQkEHM68Tsay8U4qv1WJCYy8BsS5RQFsauKQavjci3taVePivOqgFROmOpB7YRViQi3AyBbutnw03BwUTnDvHDr0rtFOg6jON4dg/Bsjlr6CoI1oG2hOn7VUpiKNxsjTlJhABLef0zW+vgUT7RuLCYQHdrbnBPXEhJrGoPPaKxf20f2051uGmADr6Li26XK9axc7iQnq1jXZ/qk8zYMFes23rXNUVAZneXxdnRPX5LS31+yJCRw6zqOQ63mducMsJjiH/LNH2nHn79nGTUhsqfyvWlCoMlMPmygmOKdtOsbKa7eUmEAkGL/rOu2EAban8i3SyHVvcIMbdO/F4Jj32LtV9TavmAC2UaddvYxhG9jJsZhgehVb1ftNT0yohQMNYrbnAVHF/flccXu+ZYgJ4Bsb2N0uQtqAmHJnx8fnq7y3YkJ9RlK/bFwnbJd9/NudxASYOqeNqvKqBRi1jzvd2xS1VgRxZOr5x05igmeef+AZ4C/UvURMGLEKI9bC2fRAmafOMW3DUxlTTqsCKmok/6//+q+3tpxPrWQ//iTgbsSEii5oR8+LncSEmt/P2WvzbcTZy2rfeNRenm3nRHMACx3Z1lkx/50D2W4rliUmCLOvvOjMSpwaTn/vuj3UjXOMQ+t1FmynsLZU59u9teWq/mv+//iLBoy+T4Qx2Eb128+dMaLtdAFsJyZAh0FqR8+ERAvrMjI1FhOMZslXb2HPnpgAja7tRkdbhJfZTiltWVRM0JgZXW3hBFQURS/SZq+sKjLBORhEhtF5GWzXqFQO3ib9LbEJnIFlOexjnFMdtGLCpkQmoD7xx561X1wgxhBjOdY7YWEk5/D+teeu6QpW/G+3V+eb/dGO1D7lRrz17ghlbX9TUx2MjimLVrDRAeVUtcc7dkpM4Cjaz0a1dWUxxJpmNhYTjCLJM0e4vQ56YgJ8covNHIfzO7fjx3leVEywCNd48UTOVa2FsZPwspOYAE6rY0TouQ9tBLR72h22v35rKstY7PX1KPVsbZzKt/J3L8Jm2+kPBiW0j/NGJojq4zzyZeaNtjzsYgJbxAFXT2N7uGl/S6sWFJzPedXDJooJYEc8vz7nKxqS7S5Euxpo6z3XLcQEo/Cir9qpCyKnyo/mn7Xl76sS3mMiQfuu8tHkRz7b+1xETPDb8bnLtzKvX37GYoLQfAOA408GoicmsFVG2/l17W9cRzukHtj09hrLEhP0WxzrXIRndeb6nhF21jXriw3a6PZ50M66z2pD2nLjP6tHgnnl230SdPnbyo0NdS0+izbe/ra8PEOeM4POJaR4F9lk1+Qntx127wtfuo71e1Pj2nyxz/p36t/+tkzHeP97YgL7207F0N9pn68pMUEb7Jlxv8tA/iImbPEXf/EXx+Z299I4lJwjZbuXVYeYk+Rv4ZRtBx67ERNgu99ZuMoLXsLCTmICGELHSNYa8CByCs0j9RCNxQQPVX3OcJzaz1zWFAIvrnz5f5XNssQEc422q4vt7ruF0+d4DprrVH5FLDBQLdX5NqXCiBFVmxLppbNd2fRgLDl6jhknL37LTmICg1u/5QzqCPk/B5kRG4sJXmD35hhzcdVrCQtTYoK6oSzbRwRQLrU+Bad8zKJiAsHK8VR053Yf/pZW9bnIVUUmOIcGkPHmwDGUVHPP+yYn5bRKQcH51EErJmxKZAKUjSgd++rZreRdn/cLJfX1Am2Dd8GCS85h+7ihrc63EWoLO/mN96qua5pD7x68b5yIOq5NHIv2N47bbs0E7UvdMyfclDbOFkeHLdTmtb/VDrHdbKQy9q9jXXNKTPD+OLd9zqlcfBrZNYmv43tcVEwQWeE+tXXOLXrDubVXU1MIWuYRE1DiR5V1JaNMta4Fqh0jwiujais4w+M2iP2vuuRXsKH+b8oj4cGimG059MQEmJJnu3Jlf/27nQ91WMUEv5f3EoM5/67Rs4mblNhUHalVCQpVbuphU8UEGCDxDHuHxkmbMWVTCj40+0EgdB6r5Xsv673XbvTOUZ1+Ua5skH/9xjs/vsdFxATPrvw4F9vg3fZ/toWI4DdjMcEIvpF8/QfHKceyOz0xAQR07Sbb757dA/vJtvXC8ZclJkCHW37H9SXaw/MorwZJ3Yf8aEu0O8rF1D9tlmu15SbsvxbAvcQlLjGsw6bctC0iwF2vxASRm4Qi+7XB+gpls7XbbEiL6EL21fFtfiX2usq2pmDIhzLlC7gHx43z28M7rw83vkYl5cD3H5+nJyYQwJSddlOZLgP5i5gwgjOkcjklOlJGkqY6Ui972cuGsBvHWeCj7XS3MMY6yAq8B9XTuXroDDq/Rr3yoWF0PpW3HeZo+a3UftnAaMeUE0D5q99I/h6j4Weo7eeIFB5Y+WqZ597bSIjqjDKyRmsKLzFDWY5nO3qzHQyj9SvkVZ1OfeGhOt9CluBaVQa1bQoOsnKo4+W9VzcUbYtzbQcnjrDlPBT0MvxU4t4XFTT2BBDHqxPHQXkr93aUsYXD65mt301FDDCyzjMWyAqGuh29BYNSz63EGW6jfZaNe1yVmOBcGhjPn3fGvfUcvE1MqxIUqtxaMWHqOdwL3jvP5nZCBdvSe2+gTv3eyHUPYkM9w97JqcURt4OTWO+Zc7Sj1i3V+WYfPG/sYF17au2FQvlyeOt4kUGe0zE6ixzV7bAwmbbGeYTcq0N4f3sRGcpO3h0vD54rsN3KdqoD0LYzOr9T+SLcOM/U/bunsW3RNpRNlNjmcXTAFOz2dvauxWhZlZV/p+rJM1DtpTaIY7nd/ZT954dUyK97ateSgBDeqbKRt/JhjMJOXQ/smuu24oZRM9vKyR4jP/Zvd95FWJWY4Dw6OdpJ74R7HdvBTU3eLZ0VtnaZgoLz7JeYADZx6gsI8H571qY6J34/FcbNrpSPLhGgxyLeFMQEYqDnR0RT+Xje9amvvRTy1PYNWrGghV0av3tsj9+zReNrsLci1Lzbzlsj9vAb7e34N2zoXe961yEvbVSDsvGbXr48W4TtsiFlQ3v3zC6JYJ46j2sov+3Kq8WzVj67ZO0LtqH9PRtatrf9fKO+Te9aniF+qOO1N/W8KK9eGYxttr7UlBiijdReV1m5Tq88XEubWseJLNan65VbD20ze9+mdrHFHqKcPV/VjkO7QtgQCTJ1T4vi3iImhLWC8d5uUcOKdBDas0xKTGAUwmahoVmFmMAYMoqMI+eNsXQtBvMwJB2VVQgKzjMWE7br8IfzpywRE1phNYSjwirFBB1unRqdDLaoZws3MbHfQsGXLSg4h3Ptl5iwrhATRHv2RtxD2FREGO514cgxERPC2qFD79NkU1D0HLPszkmtpSB0LWwWq4pMKIfKKB0jKaTtMIkJEodUmS1TUHCOsZiwisiEw0TEhHCUWZWYwA5xmnW6RSWI5ujZwU1OyxYU/D5iQsSEcDgx/cK6EOMpN3shYkJYOwgJOvXmrgqhL4SVCWuyz8qnyyZiwubCCV2lmMChEl4oZMzolv8fpqQBUG4agGU68a2YsGzx77ARMSEcZVYtJpjSZIFkgnDPBm56Iijo9C6j7fP7iAnniwnm4EdMCGF7IiaEtUM4ogV3rL6qc98mCwWax7UKIiZsLquOTCiHisNbYoK/D9O/GgFOt7LbayNQTnwiE+YnYkI4yuyXmEAQnscebuK/5t0vo+z8NmJCxIQQ5iViQlhrdEIsTKkh06itEi+DBU00zGGz2I/IBA4Vh1eorOsdtqQRMJ2D47QMZ3QsJrhGmKYWLNSZCuGosR9igsWJ6zqHMYlOcK97deSVW8SE86NhX/ziFy+9UxTCYSNiQjg0WJnWg3wU6X1F4yjB2TkoMUGkwqb/bWV2/xrZquiEvZRfOfGJTNg9VoNup3mhVu9XR2H9EFVn5ez2K0RhPg5KTNiNvVy3vz1vbKz1bwjCey0/v4uYEA4rvt7gCxd7tS/hAiImhI1HeLBv4NZUiJ/6qZ/a2nN08A1y917ftD1qcKr2S0zguHHgfCZp0/+VjL4Q4oQAV/ktY2RrLCaoozA/vnHtM7htPfz+7//+MBVi3s/i7haLMy3zs39HBZ9p8912n5Nu682nHn0taN7y9M785m/+5uCcHRX2U0yY1z5uwr9sgc/DWQtCG6X89hpd5nfjtm+3YoIyZ0uIoJKRflNNthNEvUd1vOSTeO5vr89DWAyfnvT5wL34AvsNX+Of//mfh88/PuQhDxk+8dhG+/GRv+ALvmB2gxvcYHi+NxGDDNJ29UJUJJoYgNiP9yZiQthofF9WJ/qHfuiHZo997GNnz3nOcwbjcdT45V/+5SMtJnB29lNM4MhtehJhoUEyjcg3vjl9ym8ZDYGyH4sJyi/Mz/d93/fNLnWpSx1XDyUmcBBWxSmnnDL7rM/6rNkP/uAPLt0ZOOzoKI3FBPbCtm/+5m8eOnjz8MAHPnCog9vd7nZHpg72U0zo2cNNTDrYOtqimHTQlZ/oGH7AuogJOqSe/xrsadONbnSjoR0a5/PLv/zLu8dLBk5e+9rX7vnZCNujjfm8z/u8YZ2yTVgzwqDIj/3Yjw12s/fcnHDCCcM7cRjEBG2JgYbtBDnvyOd//ufPfvd3f3fw51ZNxISw0fzKr/zKYCiE9x1lbnjDGw7lkMiE1YsJHFIOkE7Cpv4rCoFqTXh75StfOUyTsY/DXeW3l4ZA2TtHIhN2z6rFhO/6ru+aPfGJT+y+J/e85z2H5yMO+2L0xATc//73H0b42vI0peizP/uzh/duzDnnnDN0whKZsLfnr+xQKyboFE/ZxU36961vfevs2c9+9vDMiUxgE6z3pK1ap8iEEhPYlOc973nDANC97nWv2bWvfe2h4/e1X/u1Q723eSUmfPu3f/twfyITvDt+70tf1Vn84z/+413lJ8wPu3XyySfvyRfYD+5yl7vMPudzPmf2jd/4jbO73e1us5e//OVDh5bA9pSnPGV2zWtec/B5PGMRE1ZDxISw0fz4j//47Iu+6Iu2/jq6HPVpDpyd/RQTOHSbmjSqRIRnPetZQzjp61//+sERdX81qrXX8vNb50hkwu65/OUvP7vEJS5xXINcYgInaS+oD0754x//+KW8J+F8jBL3xIQej370o4c68E6G/RUTxjZx05Jpac985jOHzvmrXvWqQQy2na3lyCs/z99uy8/vxm3fXsUEeR2/E3/91389CGo3v/nNjzv3l33Zl82udKUrdTtB7vNHf/RHh3fHdKzd5CkcHh70oAcNQsIVr3jF4R3fiRITfvEXf3GjxQQDDRETdsFRNxicv3vc4x6zW9ziFkMHWngY1bDH3/3d3w1qIswZuvGNbzxMA/DpwxYqNmP+sz/7s0NndJFPLlLG/+zP/mxoZKQ73/nOs+te97qzn/7pn5793u/93vBATeFeHP+TP/mTs5vd7GazJzzhCVt7judhD3vY7F/+5V+G/3N6f/VXf3V29atfffbQhz50GNURtkSd04mWF+kRj3jEcHzhZXv4wx8+RDBc5zrXmf3hH/7h0Hnq8bjHPe7YZyGp57e85S1nP/IjPzIo4jBa5Bo6YyIhrNVgfYZb3/rWwzXMUYLyuN/97jcYq+td73pDGW8H1f23fuu3BqX+Dne4wzDiMAUnwlzan/iJnxiiESj8jONRn+awn5EJruMZ2MTEmTYViCOq46PBcU9CZisqYa+OKMqJX9fIBHMqf/u3f3sY6fIOc0rNP54HnZ0///M/H5xr4gs7YJSDffmd3/mdbdczYLce8IAHDPab/fvTP/3TIVS5x7xrJrz61a8e8jMlMGgD5LHq0/Gccef5hV/4hWO2kz11DBvifPX3GJEsf/AHfzDcL7v6qEc9qnucOncez5V352/+5m+G62kj/uRP/mTh54H91UYJ/ffJNm2htkBbNOZJT3rS7Nd//deH49SJZ76XRx2c+9znPsO+U089dRjtZFu1h/e+9727vymUKRusHLR5bPNUZMJf/uVfzv7pn/7p2Pnk79KXvvRQB+6n6sAUE8cQ95Sd8PVeHqy/cNvb3nZoM/yrve8dRyxU7vLiefW8ee7k23bv57qwX2KC9rVnGzclmZLGPyEIG4H1dznxym5ZYvC47VuFmKBePvdzP3eIkmo7PCITTLOa6gR5bi93ucsN789eF9JzLr4mW+bd5yOyBb1zajdMp/Vs8qfLR+Tv2TYFu/XkJz95uH9+JvHkh3/4hwd/s/2d9oFfrz25/vWvP/y//Moepiiyb5VvURyu4X7YmPbcbJK2oJdPbYd6YtvbOrLWS91vQQTls9tm1J8dVgY69e1xnkXb2NKf//mfH+ys9rKHzyFrE7QN+hH8YDaul9cWZXOxi11s9lVf9VWD7ZjnOSgxYSoygQ1nz6s9Z4PHz22hX6ItdH/sqvZZ2zzOh/tjb70/7Bzbzu+QB/2D7eq4x14iE/SR9KuUrXr8tV/7taGd9PzX9t0QMWFD+Jqv+Zqhs2j+Eif4ale72vD3Va961a0jLsBDxljd5ja3GY4xunWVq1xl6PAWHqIv/MIvHPbpnHqJHSu0TIO1Exoxx3OML3nJSw4LIP7AD/zAELJm+8UvfvGuk3zf+9532P9t3/Ztw4tqjpO/r3zlKw+fNWohgBBO7nrXuw7HfN3Xfd1w38KYbn/72w/7v/RLv3TY5/+SF6eQR7+p/HzP93zP8H+JADCGEfve7/3e2YMf/ODhGPfkeow0OCG2cwy/8iu/cqiTK1zhCsfyoOyMElzkIhcZjJUwZVET9n33d3/3cI4WhpURtp8hUh6MhL9LwGjRGH/xF3/xsF8dO6f/C+EiGPn/URUTODv7JSYwmN4Rjukm/SvfrZCgkaxROuWmQVvGZyHh9+qgFROU3zqg88YJNfqlU0+I87d3S4dwJ3RejYqxA6IHnEfHX4ild/ArvuIrBjsw5sQTTzxmK7znl73sZYf/Sxy5cZnPKyZw2Gw76aSTuvVmhE+e6jycpMtc5jLDddmssp0lOHBo3R/xtr02x0Un1LXYNfm76EUvOpzHKIk5qy0WXXMegqf7ZhO1EbVY7rzlDQLwV3/1Vx8rL3a6bCsHuO5bubDz8sg+s8H+7zhtiXtrudOd7jTksdYo4JS6Lzbcb5TP2LniKBG1ndf9u4Z7kh/Ov/OMxQSjr8rdubyHOhLVNhlRqzqoOvSO6oBpi9rzePaUoWtXXuv+nJPg0qJO5ecf/uEfhvNpt+S32hHP4aKO7KrYDzGBHSwxYV67uU7/Eg48I+w4/0an1zPPtnq23Wc58XspO791jlWLCeAz8iHbjt1OYgJ00L1Xe5nuQGz7+q//+uEdYr+JGvU+8Z/H5+WneU/ZNNdmY/jgd7zjHbfNg2P42ARDv/Pu26YjWfZFnbIjOsdECoNRbArflf0b1ycfVrmWr1n3oXP7Dd/wDYNP2JafclamvQ60Z4qoo8Pf2judUYON7W8ImPx2eRcRIL/uhfBQZWCKXHXyDRgSFNhK24jZ7XOg7JSJNoQArN1h49gt5+k9MwXBRL756Ns9Ky1TYoLttilTfjmRlo8tH8SN8fkNiLp/tlTfQWf8S77kS4Znx3PV5puQ4FjtjPxqj9v2kx2eJ6qi2IuYYCBB0oeSp2/91m8d/Bj3KS+mjMxbli0REzYEo9fjzjkjqvKpYy0eMgaIwaLajtF58JDpwGsoCg+Cl8HDtRMlJkgVPVB4wW33wLYwIrZ78Vp0anrHM9qMNYNEge3hN/I8RuPHyWdgvVSFDjxj63rU5Bb54vS6nvIeU2KCRJFt+Y3f+I1hO+NA7GihuNpXUQ8Fw2k7xbqlzkUFb1GfGoQ2soIjSBV1vHRUxQRO6KrFBJE8FHzOm2ttUpJn8097QoIOiPsjJCi3ZTQA5cS3YsKiI9Grwn3r6LaYc6wx9S7t9NyUmOB902lsj+f8sDs6oW05cvr9Rke3FQJEOHG2OIHj1f5XJSagQux7ayZMiQlGMFxnLHwYIWWniSPt8SUmKCcOY/sbIzX2cc7b30zxnd/5nYMDKN/tebQL7d/KnXPXlgWHq8QCwnl7vRITiAfjSAwdBvv8227XIbCd89yeSztsez0X7b5WTChEozleVNCYKTFBp0Fex/VmtMnxOhDtNUpMkCcjjO1vnNtvOI/z1MGqWbWYYLBCp9z7P7aPm5C0+57rikgYCwls7TLLzTNRbd+qxAQRODpXRuHbc88jJhBX/NYI7246P+w4cU0ntiKC4DlhZ+X57//+7497n9gr/iF7qvM/73ujs81/8zu2b/w71ySWGiwUIVd50T4QWnTCtaOF6GM2hRDe3rtOrGPZaUJ5u2+ZYoJrED56dep50f/4lm/5luF9q3vRWRaN5XeeJ9vdk2toA8d1OE+kAV9eOYgSmbcupsSEWneBINw+i74KYTub3m73TvKp2vJiw/QhCNftopUlJrDD42gUopiyFzU273O8VzFBm2CgtI0U4QcSUewbR6jMQ8SEDYYx9HBSmFo8ZLYzdj2qc6txGFMd7bGzPabEBA5kD6qp/R7KgqPjJR5HIMCL7XgNREFMsK2NqBgzJSZ48f12LLQURuYIDS3EBL/x4vcoMYFBHKMxso+yPabqqY2aYHBto9iO4RjY58UuKlpC6O0YL7F9UiITli8mMIpEG0aS86OudRqFmhKCRPkIXZQ47PudHvOYxwzCkzBqDQiDrizqX3m2PkI7tWEsJHhulJn7XUa5OYfzrWNkwhQcPiM6OzWAJSZweHvHiqTizHkuqiyNctnW2sOCQ+Bcrt+eT8dxngUYdZRtE/nQqzsj31NiAiF4/JsSE4y01G88I7ZxRnv3LOJMHtrzCbn1m7GwUlzjGtcYHPPWsepR5xG51TtPwSF2nBGj3nGcYnn0PBZEDr9hm3u/0aEQjVH7PM+2ceZ6+b7JTW4yXGMRMcHzNKbEBEJRnYfjbpv2vpdX7atre68LjrBrGOns/YbwYqrPTnWwH6xaTPBcK2sDLL5ioyOgE0KY4ScclA1nv8uGa09KrG7tOLFzv4QEOIfnxTlXJSaIsqgoorYzj3nEBPjaADvZdgrnRWSZ99LAUO/d8I5L7bmJCd6ncad7J9h2vyP69X5nYEte1O84L3w++zwf9Vt+Jl/aAMcYtsfxqxQT3It2p3cvpp7pOGuPxvfCntlX0y0MaqhDNns3zxYRwrM1fn62Y0pMIJCI7up9ucI+Qs88/rUIFM9v29EvMYFA3yt/opapKr1r91iGmNCbRmKwTNmYnj1vXoqICRsKAy+UVgeSOtniIaOOTcH4ejl6cICdkyK7HSUmCAXqwUDbL7yy8MKIhujhZXM8J6ooMYFzMcWUmECt9luNbA9OpP0a6aLEBC9EjxITzBEbwwmyj+PZwz7qe8GJsW1qnQrTQCjZRUUrcC56GMW0/6iKCcpf2axKTGAcdbxdgxPk+dfwi2BhlNWnkU3P/X4mzqdUDrEGhJNZSeNgPjUxYUpIaCMSllFmcB7nbMUEdbSuKKeyNzs1gCUmcBp65aWM7TfSUPs5Ikaeps5NVBx3dNcpMsEzZBs73buG0bTxbyoyQZ57v6nOb88hbSF+O24chTCGEO44EXC944wIyQ+nvPZXZIL3uPcbDrjnovJoWsZ2HXr23PlWEZlgnq3jzent5VVE4Pg3FZnANvR+QxjnD+xUB/vBqsQE52HjjMI5v+t4R9lDz7X3puz3QdlwdUeI05YQClsbLq/tGgmrFhLgPJ4h512WmCBk3LskiTTyrhrBVv7jd2keMUGbvBcxweixd2Oq01TRCe2aDCUmaAMWKesSE3oDadCWmNLQ8988pzr63n/lb4TffZve2rtvPq39qxYTdBp7aMuUm2dzjGdZ3tjqypspE6Kt2EP2dZFyJZK61nbrGozpiQn6UvLlWe0948raPW/XeYf3Ul3KU1vXJSa0glALwZ2wNq//vgwxYWpahbxIO93rmIgJGwLljWigk1nzbCqNxQSjWb0R8oJj2f6+l8ah+mNKTJg6zgic/RxBeLja808lo6uFRkejsh1G73tigjm1ve0F9dT12ikaJSZMUWKCxdrGaGztM3Whh33t9A5qs23bJQavoFraNoVGzv6jKiYo/1WICc7hXAyrxpHjr+OpwfYOaOiNnHL0JNFA+5mMrhEKOJscUfkqJ7QVEnRANC4cQo7QKoUEVLm1YoI6Wgc0djr65mW28/Ar7dQAlphA4OuVmfu03yh17RfaacGwqXMTNznX7HyxTmKC6DDb2kUEx1iDx1oC9ZsSEywc3PuNyAf57jlXLTe96U2H46ac16LEiamFNIkRnLw2rL/EBO9NL486O62YYLTN8Zzu3vHeQ9dYhZigbXF8W/ctOpl+Y/5w/abEhCkBgtOordypDvaDVYoJzie6TNg4W6SDSHQSFu45NWp+EDac/Waj1bf3l61uxYQSEuRPNEUttsjGrEpIgHN5hpx72WKCzqn3brvQ9HnEBNM+dIB13rY7bgr50cGuDuUY4e7e23aEnZ/Fzi06aktMcK2p38kL2zVui9pk4E75+3KHji/73Ltv79CqxQSDlVO+pvaGzendQyUiQJs34qyyto8YMW+YvaneftcTpKboiQkGUd3/OJ/jxG8qiAUiv9yvsh3XX09MYF96+VT/UpWp4/VtSnyTtNVVN3sVE/g9U78VMShijW1ZhIgJGwBnzMPJGdHocGYL23tiggdzCo6lY3SCppK5TttRYoJ5oz04Z/ZTIOHB9LcXr3e9Sm3YlhdIXrdjSkyg8omEmEKYlfxQCot5xQQGbIzG1j7hzD3sa8UERts25dcrB0kofbGTmFDrJhxVMUEDuioxgQHnBHCqOKNG9XXUqegcGkabMr5fiRMsUdM1ujpJRA0NlUXwrK3C8eOoSjoSOqklJCijVQoJcD7nbsUEdXTQKB82RUPMBninC6F9HIKdGsASE251q1t1y43DYT+nqPazUcSBqXO3o+/FsqY56CxOiQnzTnMgvtomHLt3DXCyOav1GzbMb8zv7f1mXjGh1pTY6WsbFd3hHe2h4yg/OjKVx5rmMDUiRkwwt7TyuJOYoIO4qJjQtudFiQntNAfPm+OnxAS2yG8sMFq/qWkOOqK9/JqmaPGtnepgP1iVmKAsOLZskWfbNTi93n0CDNt4kDZc3ZSoIWlb2HDHiZjxHu2nkIAqN+dflpjQm18/xTxigikpOmgGtHbz/BLSxgs/tpSYwEZWvokJOlq7ERNMq91OTOCvEpZaH7BNngv1wvYTC9jPXvlU5MIiYoIImUXEBOsCTfmariHKgjDWuw/JszR+ZnW+tcmipdhxPu92UckwTUi+DQ7M+2xuJyaYSseG9/Is1XMg/6ZESsrMu1n7CCWem56YwBfrvQOLign6N+pzOzFBH0398S3a54CYYHBj6rfq1/vKvixCxIQ1RwXpJBrxGVMd2N40h+3EBFELohv2QokJUyPxtQgjR7JwTeFt8zKPmDA1zYEBdP2plaqN3tivkS7mFRMq2qKl6sJoZQ/7WjGhppP01kDoUdMcvKw9zIu2/6iKCcp/VWICg8gwMr4aN51FHRuCm/rQuHP+9jtxLjRqOkLEO8+y/9tWjZ/FnuzjqJeQ4J1YpZAA53T+VkxQRweNcEWOSk8sNV/SO7RTA1hiAseyV3Y6CPa34f3s8XbTHHwyUb5awWXeyART0myb+pQjp2NKTJg3MoGDZVt98WGM0XX7tUX1m4pM2KuYwFY7zrPcO08hYsxxOmm948xXlh9TEWp/RSZsJyZohyqPjuNoqYdeXdZUilVEJpjj7Xjl2suruvQbzm39piITpsSEwx6ZAOdwLjaPk8vJN2jhPWaXvEsHYcPZb/dspJmoQTwgDttH4PDOGVBgTzx3+yUkwDk9Q66xrmKC99KzrVx2UwY6TcLrpzr4RDn5JjTV+VclJtTUAXU7DzqJRpDbTn7hWeqJCa4vGq/3G3ZGZ3cZYgI/V7l5x3b7bMqH/ExNOyi8y/Joscd5fd+emEDMU2a9rzb08KUN96isx/doYM++vYgJO1HX51tNUQs7qtu2DGuaQxtl0WIBfl822U6o6CEvERPWGAaYg2sEZUx1SBeNTNAZ9jthZrulxATOVg8Pu/0awYJxs60N5d2OecSEqcgEYbmuZWSnh09icu5b9jMyQaNhG0duHijHjmdkxxiJs086qmICJ3QVYgKcRwNbgoLOuPpzLWUvMaD7mTTUnrkSNTig1HHvG+fTCLd/jcRzWnVY5Hc/hAQ4r2u0YkLbUT4ofHWA4zFu5DjMHCTv0E4NYIkJnMreKHh1+Nt5+Oa72ma0Zlzm6pMN85nZ9trzignmXMuPRbzG59YBsG8sJtSXB4gK49/0xAR25au+6quGEZFe+RgBly+jhXW+ZYkJwrydx6K+vWsXOmKOm1pMkRPleu1zuKiYAI6WEf1evrXFrjGPmOBb5q7dRpkUPTGhhIypxRQ5sa7dni9iwgUdY/aI7WOTXIOzXzZ8bF/3I7Hh7pc94YR7V3VO2HH2m4+lI2tKBvvpeOs+aIOWWT49qsxcZ93EBHVnEMs5jSTvJk/wbnkvH/awh13o+feM8BGltiO9KjHBArbywl+d511kS7zXnovxM1ALNo7FBNPstH+9PNQ6B8sQE3RidZx9BWe3dQMdfjatvXYPX+hxv2z5PNfriQlQPyJV5vGh2U1lOe5w+602gx1epZig/SZ+jL880eL98Yy0X9RAiQme+/FvCZrqXDTgTuU+hi2NmLDGcAw4uJw4Br0Q0szQ2bdoZAJjbM6NESsGvsULwMHciRITJOGXLY961KOG7cKGW4SHc2p9ScFoaYuGsl2/AHuJTADjKdxKp6rF3Fr5E1LVsp+RCeD8285J9vK1cCY4xy1EIosytp+G1NCbM1jfRZ/XGB02lP+qxAQ4l0bWeTXGGhHXUW866AeR1L2OX0VKMOQVuisZ8WI/9ltIgHO7TismtPbroNBB9560nXp2yYKttRbNTg1giQmOHUcnmFZin4Vm2/Owq5wMC98SfgrlU51cv23PNa+YoEPimhwhdV7ofBqJ4vyOxYTqeLdfKih6YgI4367NeWvzKQLG8cLl2+OXJSZANJ3y5my359LZ8mwVFrl1TkJ7e5zpZPIyXjhxN2KC76v7jU5Sey4htyVIzSMmKJcqn/ZY9MQEqBP3Ryxv83vPe95zOJdOVnt8xITzcR7lwiY59zrYcPabOMA2cMDb6XMlKigTX7iyv4QETvoyy6ZHlZeyWgcxQYfIvHi+lxBt75gw+LHYoFx1soSf79QZ4ivxk12rjT6Ajqb3hi/bvhurEhPkhZjMTovqbcvJPYk6an1EQrVRZ3axzbtpWPoFbMRYTPBpX/Uggqvq0Tl1SK2BoEyXISbAIJ9z6ku053M94rc2Bp557cT4uZAnnW95HdfxGO8FO+aerSsxjlTxfpmeV/mQ756YoIyVKT99HFXB5xYlVPm81rWuNTwf2pk6r3eEKKNs5GWVYgL0b5Qx8aZ9L9lQ6wxpcwxijMuvxATPGhtT92l6jHPqS7A/7f3PQ8SEDcAL5UWX6gHi+DIcjNSiYgI0UAQF56SwXeUqVznmaEs7UWICZdj1nIvTZU2EyienYAwRoRoDBt981HIUnadlr2ICg2AVdedWTs7H4fa3T9GM2W8xAV52+yR1Jo8aN3+PF7fktOuM2Fd17P+Mvfvx/0WM0WGCAV2lmADnYxw1Hs4vMZht0jjtVyqHmLOnIVAGnC4dTcn/PZf2cUj2S0iA87tWKybI30HDcSnRgNPlPfJ/HWQjOpyAnRrAEhNufvObD+8gO+a9ZWP8no3pze9nPxzreuye39T7bE2CcZ3MKybAKLftzmUqWS0saS4psdd83PF9VcebUC0vnE15mBITUBEWQiH9hmjib+XgWWtZppigvdLpdTwHl0jC7rvHVhjyvNVUFs65PJajycEa389uxATt2s/93M8N5/QsqSflq12TFw77PGICZ1O5OY9nR+JE+92UmICKQLCmhrxpP/1tqsy4LCMmXIBzScrTuQ/afkvaa8+sDpEICcIvW+n99q+/bd9PIQFVTvJ4UGKCd3ucrMviKww6+b1zGVnVIWTL5smr8uUjen/4wN4nkUf+JhqO34tViQkwKMBmeV/5vXzp+tu982dbtAXsin0ipeRL38A6YDquYzGBiM3Xd29sJ7vv3Hx3azUot2WJCWxbLcQoT/5ff8uvvHjGdNCJPzqwxCM2rY4xjUPHdJ5n3TuijOu3veT9wZSYAAOMysHx1m7QTmuzlRnfvsrTc6bttl29ag/936LLph06x6rFBAKHMpu65/aLGS36THyEW9ziFsNgq3Lz3Is8dA+iC8fP/TxETNgQzOnhyP3Zn/3Z0HGs+S7C3ylqLZw3i3LthJfLHE/nrMShbkfOpigxwYgIOFF1Do5xvbg9NIxeuPa6D3jAAwYnvUUYDmdoO8yD3enLE9TDuo7zcep7eIkcM4Uyt19UyBidNfuo5z3sc/4eHFkrjDtGUs9T52Gg3I+Gzm8qwkM4u98eVTg7qxYT4JxtYiwPIjH2EoOtAfL8eTYYcmXB8fC86hgy7PspJMA1XK8VE8adzYOCnbGobb1vOr0wKuid2ql8Skwwp9axHAQOs3PpDLJvUygPozV1bfZTR7kHGznuiHP62IfWUSncR2tHjFjVdvaid1/CJev4WpPA8+QaU2swGLVwPr9xnDaod5w6t3+qI+t39vf2TVG/qTz31nyAznjVibUGpvKgg+N8vekqEKqrHer9liMsUsI12s9Nup7IsvY3tvW+hOHd1PY5h7oTSeQY/8oXp7V3bfPpRVvU7zyDvePKbxi3rYXntSdYHASrFhMK56zU2tT9Tu6PjXSv7IL7ZivZbraSLScE68zvp5CAKptliAneEz7reMR4O0yb8mxXYgdFU+30e++SDrbnfpFy4puVXdE2uNfe773nd73rXRcuB6PHBEb1txNsWmvj5EfHsZcffh9/W/nybT0z2v3emgnwfLFZdf5WPGBPlLF6L2wTcdXmW/ulfuYpA/Za3upetBvapfYabFnZaok9m/rqwU4YcGuvJ+nTjOvTNdqogha2V1m251An48FR74RzV1lqc5zPII5ya8te2+oex1MOCv01aTfvl8W3RarpB8mHutGnmCo/YgKBhE3RLmjj3IM8b/e7nYiYEHZFiQke3BAOmv2ITFgX3FcljVcJCgy4Bk9nViIwaDBax3y/ysR11EErJqxDZMIyKDHBSMV+lWcIh539EhPWgbLFnG336X7Zah33st/K4SCEYFTeliEm7CeircbrHBw1thMTQjAFpbfew16JmBB2RcSEsE5wdo6KmNDiHjmj7pfTSVTQSPjX3wz6fgsJcC15asUEdXQYiJgQwvI5SmJC4f5aQYHNZr8l/7eNHd1PIQGVr00TE4SeixxQnkeViAlhOyImrLkRO2pETAjrxFGKTBjTOqTuu5K/99sJLVxTHhKZEEKYh6MoJmDKfpcNt3+/y6HytGliQjh//YCICWGKq1/96hETwvoQMSGsE0c1MqEoh5PxrlTbDgLXVQeJTAghzMNRFRNQtlpaF/stDxETNo+ICWE7IibEiK0VGnyLnfi0SwgHzVGOTBizDvctD2Mx4bBEJsCihvMsVBtCmI+jLCa0rIv9jpiwuVj00meCj+L7E7bHYp4WhFx2Rz9iQghh4znqkQnrhrIfiwmHJTIhhLB8IiasDxETQgiLEDEhhLDxJDJhveiJCYcpMiGEsFwiJqwPERNCCIsQMSGEsPEkMmG96IkJiUwIIUwRMWF9iJgQQliEiAkhhI0nkQnrRU9MSGRCCGGKiAnrQ8SEEMIiREwIIWw8iUxYL3piQiITQghTRExYHyImhBAWIWJCCGHjSWTCetETExKZEEKYImLC+hAxIYSwCBETQggbTyIT1ouemJDIhBDCFBET1oeICSGERYiYEELYeBKZsF70xIREJoQQpoiYsD5ETAghLELEhBDCxpPIhPWiJyYkMiGEMEXEhPUhYkIIYREiJoQQNp5EJqwXPTEhkQkhhCkiJqwPERNCCIsQMSGEsPEkMmG96IkJiUwIIUwRMWF9iJgQQliEiAkhhI0nkQnrRU9MSGRCCGGKiAnrQ8SEEMIiREwIIWw8iUxYL3piQiITQghTRExYHyImhBAWIWJCCGHjSWTCetETExKZEEKYImLC+hAxIYSwCBETQggbTyIT1ouemJDIhBDCFBET1oeICSGERYiYEELYeBKZsF70xIREJoQQpoiYsD5ETAghLELEhBDCxpPIhPWiJyYkMiGEMEXEhPUhYkIIYREiJoQQNp5EJqwXPTEhkQkhhCkiJqwPERNCCIsQMSGEsPEkMmG96IkJiUwIIUwRMWF9iJgQQliEjRITNC4hhDDmrLPOipiwRvTEhPe9731be0MI4QL4dhET1oeICSGERdgoMUEjE0IIY975zndGTFgjemKCzkIIIYzRYdVZJSb4f8SEg6UnJrDfn/zkJ7eOCCGECzj77LMHMeGDH/zg+osJCZMNIfR4y1veMnvPe94zdFw5PBETDpYSE9TFueeeO9TNG97whsE5DSGEFqNZOqt8vBITOKKx4QfDlJigfQ0hhDF8vBITakBvbcUEo1shhNBy3nnnzU499dTZGWecMXRcIyYcPK2Y8OEPf3h25plnzt74xjfOzjnnnK0jQgjhfNju0047bRjdiphw8IzFBPWifnQYUichhDEG9PTR+Xirig5empggs/4NIYTCeglvetObZu9973uHjitxIWLCwaLshSmri4985CND3bz5zW+enX766VtHhBDC+esl8O3e8Y53DBEKH//4x1cSIhsWo8QE9aFe1M/rX//6ob5CCKEQsWSwqKYar2pAbyliAgP2ute9bpgbHUIIoIC+8pWvHJxRosJHP/rR2ac+9anMtz1gSkxQF9a6MRfanGh1lVDZEEJhxJtvJ0TWqNaq5tuGxVD+6kF9CF3me59yyilDFEna1hBCYTBPEoFqQG/txQTKx0te8pI4oyGEgde+9rWzV7/61YND2s63jZhw8IxHtkQlqC82PIQQRC299KUvHXy7TFNbL5S/eiDYqxf1Yzrhi1/84qHeQghBNMKrXvWq2Vvf+tbhi12rHNBbmphQI1vPec5ztraGEI4q5nG+6EUvGkIvM6q1fqiDckYJwObbckaJCSJJQghHG/abT8cRrcgyU6MiBh88yl891FQ1HQX1pL4IQETiEMLRxfTVF77whcMgkWlQfHJ2YVVr3ixFTJAxHQYdB8roM5/5zEHNDiEcPcy/JyrWFAeODocno1rrQ+uM6iSY6mBFcJEkz3ve82YveMELhu0hhKOFke5nP/vZgy/HpxNCX+slJLJsPSj7PY4uMyWFCPT85z9/mP6Qegrh6MEWsOEve9nLhr64wSJ2fVWLL2JpYoIOg+iE17zmNYMj+vSnP334l5ELIRx+KKEM2LOe9axhhNvnBomMbECmOKwX6oDdLmdU5IgGR8Pz8pe/fBCDnva0p0UUDuGIQOz1vvPddEaFx/Lp+HbtXFt2Ixw86kF9qBf1o/0VnaDeCMInnXTSEG1mfwjh8CPK9BWveMUwoE9UtI5KTTO2PtYq1yxbipggY5xR87aMSjJmhAQ3dMIJJwwdDDdYC7EJt0hKStrc1M6zF0rFAdX5PPnkk4cRrTJi3vc2KiGO6PrAbquTik7Q4KhTIhBFm6DAIS1hmFAsekHd956JpKSkzUlEAgtzteKhd11HVFSZfQRGI9yJSlg/1IP60EEg1qsnc6QJQvztEhSe/OQnD1Mf2G71ztarT79JSkrazOQd9i6z4/rdBOCnPvWpQ7+bT85fIy4SGSsqYZXTjJcmJlA9OJmMGdHAjehUaKQ0UG6SUXviE584+5d/+ZfZE57whNnjH//4pKSkDUveXe+wd9k7zQl9xjOeMXvuc587OC1CLTkuZcSslRBHdP1QFxoWDYyGxuiW6Q7m1xEUOKQaKJEmJ5544uwpT3nK7ElPelLsd1LSBifvbtlw77MBH51OQjAn1GAQIUFUWXU+CY6rCo8Nu6eNTihB2LQUggJBiAjMfmujx/a7Uu8ZSUpKWs/U2u+xDa/pafrf+uEG+A30Ex5W/SW1pYgJKGNGUHADwuPMt2PQ3ByVVGOl0+GmOacSI5eUlLQZyTtbo9UUUMbLu21aAydUWKXOKLVUyNV+GLGwe9SJuiH2EH1KUOCQUrs1SqIUOKWEYXWu7suG956RpKSk9Uzld3l/+WI6mt5rIoIIBRFlnFCDQiUk8O1qRCs2fL1QH60gLAqQoEAIYr9FDrLfRGFtdWu/Wz88KSlpc1K9vz0bbjBP/1s/XLSSgf4Sg1cVlYCliQmMmUZH+AUlRGeCQRNmQVTglBrpMnJJXHDjEic1KSlp/VO9s+ZieYc5KcRCDgsRgQHjhOqMikgoIaGMWBzR9aQVFMoh1ZHQGJmqYpRSJ4NYpM4JR56B2PCkpM1K9c6y395jzqdFVzmgOp+EYBFlnNASEhJVtt707HcN6rHfohTYb2112W/139rwpKSkzUnlg5cN55uVDTdVVf9bP1x/vBUSVmnDlyYmyKgM6zzoRJRBc1M6GBopwgLHVMdDGC2RoZKCSEpKWr/UvqeSd5eDwnAREBgvc2utj1BOqFHuCAmbQ+uQ6kBQs0WWGOXilIpUMHVFnat7Nnz8XPSenaSkpINP7XvKfnt/+WKiEHQ4DfwQEYiIhGBOaEUkREhYf8b2W/219lsbra1u7bfnoE3tM5KUlLReqX1Xvb+tDeebtTZchKl++H4JCViamCCjJSiUQStRgULiBnU23CzjpvNBZEhKStqs5N31DhMKRSEQDTkuRATvfDuaFSFhcyiHVAeCEEQQIiroXBCJ1LU6P/PMM4f67z0bSUlJ653KfvPF+GQ6nN5vDqhOqNHtEoIjJGwOZb9rUI/91iaz39rosf32HCQlJW1mKhvune7ZcD74fg7mLU1MgAy3Bq1EBUbNDTJsxAXGTdIBSUpK2qzk3WW0vMs6mwQExosKWgYsTuhmos40Pq2oUMKwulbn6j72OylpM1P5X95lPhnfjI/mPfe+VzRChODNo2e/tc09+z1OvWclKSlpPVLvnW1teA3ktTacPdgvG75UMaGoG3AzOhZuTCfDTepwuGGJkUtKStqsVO+vd9k73QoI+23AwvKp+iuntITh2O+kpM1PPfvNRysbHhFhs5nXfo9T71lJSkpaj9R7Z9fJhq9ETCjGRq2SG05KStrsVO+z97uM134bsLA6qj7HNrz3LCQlJW1Wqve5bHjs9+Ei9jsp6XCndbLhKxUTWlrDlpSUdLhSOPz06j0pKWnzUzjc9Oo8KSnp8KSDZt/EhBBCCCGEEEIIIRwOIiaEEEIIIYQQQghhISImhBBCCCGEEEIIYSEiJoQQQgghhBBCCGEhIiaEEEIIIYQQQghhISImhBBCCCGEEEIIYSEiJoQQQgghhBBCCGEhIiaEEEIIIYQQQghhISImhBBCCCGEEEIIYSEiJoQQQgghhBBCCGEhIiaEEEIIIYQQQghhISImhBBCCCGEEEIIYSEiJoQQQgghhBBCCGEhIiaEEEIIIYQQQghhISImhBBCCCGEEEIIYSEiJoQQQgghhBBCCGEhIiaEEEIIIYQQQghhISImhBBCCCGEEEIIYSEiJoQQQgghhBBCCGEhIiaEEEIIIYQQQghhISImhBBCCCGEEEIIYSEiJoQQQgghhBDCGvLf//3fQ/qv//qvhVL9LoRVEjEhhBBCCCGEENaEj3/847OPfexjx9JHP/rRudN55503+/SnPz37z//8z9lnPvOZY8JCCKsgYkIIIYQQQgghrAkf+chHZh/60Id2lYgPn/jEJ2b//u//PvvUpz41CAtEhQgKYRVETAghhBBCCCGENeHDH/7w7IMf/OCu0tlnnz37wAc+MDv33HMHYYGo8B//8R8RFMJKiJgQQgghhBBCCGuADj8hgCAgEQjq//P8/c53vnN2+umnz84444xBWBDlQFAQoWDKQwjLJGJCCCGEEEIIIawBOvwVYfD+979/4X9PPfXU2Wtf+9rZm970ptm73vWuYbsIBVMeEp0Qlk3EhBBCCCGEEEJYA0pMIAzsJr3sZS+bPf/5z5+98pWvnL35zW+enXnmmUOkwyc/+clhUcaICWGZREwIIYQQQgghhANGR1/0gGiCs846a1fpec973uykk06aveAFL5idcsopw5SHc845Z1iUMWJCWDYRE0IIIYQQQgjhgGnFhPe9731DIhDU/+f5+1nPetbsSU960uzkk08eohPe/va3D+fzucmsmxCWTcSEEEIIIYQQQjhgSkz413/919l73/veYYrCov8+/elPnz3hCU+YPeMZzximPLztbW8bpj9ETAirIGJCCCGEEEIIIRwwJSaINiAM7CaZ4vD4xz9+EBWICW9961sHcSJiQlgFERNCCCGEEEII4YApMcF0hfe85z27SieeeOLscY973CAmvPSlL42YEFZKxIQQQgghhBBCOGBKTDBl4d3vfveu0tOe9rSICWHfiJgQQgghhBBCCAdMiQmmK7zrXe+avfOd71z434gJYT+JmBBCCCGEEEIIB0yJCWecccYgDOwmPfWpT42YEPaNiAkhhBBCCCGEcMCUmGDtg3e84x27ShETwn4SMSGEEEIIIYQQDpgSE6x9cNppp+0qRUwI+0nEhBBCCCGEEEI4YEpMMF3hbW9725De/va3d/+d2n/CCSdETAj7RsSEEEIIIYQQQjhgemKCRBCY9++ICWE/iZgQQgghhBBCCAdMiQnWPnjLW96yqxQxIewnERNCCCGEEEII4YAhJvznf/7nICa8+c1v3lV6ylOeMogJJ5100iAmEBiICf/2b/82+4//+I9BTHCdEJZBxIQQQgghhBBCOEB08HX0RQ9Y/+CNb3zjkN70pjd1/53a34oJL3nJSwaB4ayzzpp97GMfm33qU58axIoICmFZREwIIYQQQgghhH1Ch97nH9/1rncNyRoJlU4//fQLCQbjdOqpp3a3S6961atmr3jFK2avfvWrZ6973euGbaITfOnh7LPPPiYomE4RUSHslYgJIYQQQgghhLCPfOITnxg6+m94wxv2JYl2+PCHPzysnfDJT35ymPKQKIWwVyImhBBCCCGEEMI+ogP/kY98ZIgyeP3rX7/S5BqiHkx3OOecc4brEjPaaQ8h7IaICSGEEEIIIYSwj+jA68h/6EMfGjr8p5xyykqSaQ+vfOUrB0HBVIczzjhj9v73v38QFP793/99iFAw5SHRCWE3REwIIYQQQgghhH2EmGCxRR36973vfcP6Bq997WuXmizA6BORz3ve82Yve9nLhmu87W1vGwSFD37wg8MXHkQnREwIuyViQgghhBBCCCHsI62YcO655w6fg7Ro4rLSi170ouGrDk960pOGLzsQFCzMaJ0GUx4sxugLD+edd94QIRExIeyGiAkhhBBCCCGEsI/UNAeLIZpyYD0DX3EwLWGv6QUveMHsUY961OzRj3707PGPf/zsqU996uzZz3727OUvf/kgJrz73e9OZEJYChETQgghhBBCCGEf0XnXideZ16n/wAc+MHTyrXMggmC3iZDw8Ic/fPawhz1sEBOe8IQnzE488cTZ85///CFiwTQH0yp82UFUhOiILMAYdkvEhBBCCCGEEELYZ8bRCf/6r/96bLqDKIJK1juY529CAhHhH//xH2ePfOQjZ4997GNnT3nKU45FJYh8eM973jN80cEnIrP4YtgrERNCCCGEEEIIYZ/RiW/XThAtIGrg7W9/+xBl8NKXvnRIFlKs/0/9bU0EQsJDH/rQ2SMe8YjZYx7zmNmTn/zk2bOe9azhGF9zeNe73jVEQLRrJSQqIeyFiAkhhBBCCCGEcADUdAdRAp/4xCeGxRjf+973zt761rcO0QSEghe/+MXb/msKg6kNJST88z//87Dw4jOf+cxh/xve8IbuoosREsJeiZgQQgghhBBCCAfEWFD40Ic+dExQMIXhhS984WR67nOfOwgIU0LC61//+kFIeP/73z/76Ec/OkypKCEh0xvCXomYEEIIIYQQQggHSCsoWM+AoHDmmWfO3vKWtwyigPUQxomQYG2EVkh44hOfOHvGM54xRC0QEk4//fTjhIRacDFCQlgGERNCCCGEEEII4YAZCwoWSjzjjDNmb37zm2cvetGLhnURKhESiAfWSegJCa973euOCQkWd4yQEFZBxIQQQgghhBBCWANKUPDJyFZQ8CWGmtbwnOc8ZxAOCAj/9E//NCy2aGpDKyT4KoSvQ0RICKskYkIIIYQQQgghrAk6/VOCgukNxIQTTzxxEBCICieccMKwRkKEhLDfREwIIYQQQgghhDVC599CiWNBwZSHV73qVYOocPLJJw+ffiQuWFdhLCT43GSEhLBKIiaEEEIIIYQQwprRCgr/9m//dkxQ8JWHU045ZfaKV7xi+HwkceHUU08d1kiIkBD2k4gJIYQQQgghhLCG9AQFn40kHBAVRCq8/e1vn7373e8+tthihISwX0RMCCGEEEIIIYQ1pRUUTHn48Ic/PDv77LNnZ5111iAsiEYgMuTzj2G/iZgQQgghhBBCCGtMCQo+GynyQJSCKATpYx/72CAynHfeeRESwr4SMSGEEEIIIYQQ1hwCga88EAyICsQDScSCv4kNERLCfhIxIYQQQgghhBA2AEIBwYCoIBEQ/FsiQoSEsJ9ETAghhBBCCCGEDaKEgzaFsN9ETAghhBBCCCGEEMJCREwIIYQQQgghhBDCQkRMCCGEEEIIIYQQwkJETAghhBBCCCGEEMJCREwIIYQQQgghhBDCQkRMCCGEEEIIIYQQwkJETAghhBBCCCGEEMJCREwIIYQQQgghhBDCQkRMCCGEEEIIIYQQwkJETAghhBBCCCGEEMJCREwIIYQQQgghhBDCQkRMCCGEEEIIIYQQwkJETAghhBBCCCGEEMJCREwIIYQQQgghhBDCQkRMCCGEEEIIIYQQwkJETAghhBBCCCGEEMJCREwIIYQQQgghhBDCQkRMCCGEEEIIIYQQwkJETAghhBBCCCGEEMJCREwIIYQQQgghhBDCQkRMCCGEEEIIIYQQwkJETAghhBBCCCGEEMJCREwIIYQQQgghhBDCQkRMCCGEEEIIIYQQwkJETAghhBBCCCGEEMJCREwIIYQQQgghhBDCQkRMCCGEEEIIIYQQwkJETAghhBBCCCGEEMJCREwIIYQQQgghhBDCQkRMCCGEEEIIIYQQwkLsq5jw3//930lJSYcohaNF7xlISkra3BSOBr26T0pKOhzpoFmpmNDe6H/9138N6TOf+UxSUtIhSfVer4tBC8sl9jsp6fCmsf2ODT9ctPa7V///+Z//mZSUtGFp/B6P7fhBsBIxYTsDVjedlJS0+Wls4Gw7SIMWlkNrv9s6bus+KSlps1Prm1WyPfZ7sxnb73Hd1v6kpKTNTO17XO95veu1bz9ZqphQN1A39ulPf3q40RDC4cd7751vnZawWaizapzUJTseQjj8tL6bf8ufC5tDz36nDkM4GpT9PggbvjQxoTVi//Ef/zEYshDC0YId8O6XqLCfxizsDfVUjREbHiE4hKOH9/5Tn/rUYAf8P/Z7M4j9DiGgbMB+2vCliQkyLOMaoRDC0aZExTikm0E5ohGCQwjgy7WicFhfyn6rL/WW+grhaFP98f3ywZcmJjBi55133mDQQgjhk5/85DFjFudmfSlHlJDAhocQApvAhkdQWG9a+62+EpEQQkD5dPshKCxNTGDEZDyEEIp///d/HwSFOKPrSeuIqiuNTgghgCNagkIizNYP9aFe1A/7HR88hNDCLpSgsPZiggxqcEIIoYVz84lPfGL4N87oejF2RBOVEEIYs1/OaFgc9lu9qJ+Pf/zjW1tDCOF8+Hdsw6oH9ZYmJugwhBBCC+P1kY98ZBAb44yuF+qiHNGPfvSjQ2MTQggtfDvOKEE4EWbrg3pQH+z2v/3bvw2iTwghjOHfsQ8VYbYKliImyFzEhBBCjw996ENxRtcMdcBuV+SIOkq9hBDGcEI//OEPRxBeMyoqQb2on4jBIYQexMaPfexjK41OWJqYEEMWQuhx7rnnJjphzVAHNapFtSYmhBDCGILjBz/4wWOCMH8vNvxgKfutPnQUPvCBDwx/hxDCGDaCH04YXpUPvhQxIUYshDAFIeGcc84ZRsBXGWYV5kdjolHRuBASCAohhNDjrLPOOjYValUjW2F+SkwwRU37+r73vS9+eAihSwmOqxSEIyaEEFaKzur73//+waBlZGs9UAfqQuOikaFahxBCjzPPPHOwEYkuWw+Uf4nB6uU973lP/PAQQhdC8KoF4YgJIYSVIiohI1vrg7JXB+rCPLp//dd/zTSHEMIk7373uwc7vsow2TA/xGBRfqL9TEF55zvfGT88hNDFmioEYf+KZlpbMUHjEkIIPc4+++zZe9/73iEcc1WGLMxPiQltiKzohBBC6HH66acfC5PNVLWDp8SEiix7xzveET88hNCFiHDGGWcMg0arii6LmBBCWCmcHYYsYbLrQYkJtQo4xTpiQghhCp1VonA75zYcHO00NfVy2mmnxQ8PIXSpqVCrjC6LmBBCWCnWS2DIIiasB8peHagLdULoUUchhNBDZ3W87k04OEpMUB/q5e1vf3v88BBCF36eqWqmREVMCCFsJEZOGLJVhliF+WnFBHVC6FFHIYTQQ2c1YsL6EDEhhDAv/LyICSGEjYazU2JCFvA6eEpMUBfVyKijEELoobNqodaICevBWEx429veFj88hNAlYkIIYeOJmLBeREwIISxCxIT1ImJCCGFeIiaEEDYeTmjEhPWhJyaooxBC6BExYb2ImBBCmJeICSGEjSeRCetFT0xIZEIIYYqICetFxIQQwrxETNiBN7zhDbMXvehFW3+Fg+R1r3vd7CUvecnWX4cLn897/vOfn9HbXZLIhPWiJyYclmfbp9K8qxaVDCEsh4gJ60XEhBDCvBwpMeHBD37w7M/+7M/mSu985zuH3/z4j//47Au/8AuH/+8nz3jGM4Z85NvsF/BDP/RDs6/4iq/Y+mt/+aM/+qPZ7//+72/9tXye/exnz/7X//pfs3/4h3/Y2hIWIZEJ60VPTFjHyAR2/s///M8vZP97Sfvhvoian/VZnzX7f//v/x3oM+Z7zg984ANnv/IrvzK0Uze72c1mf/EXfzF7+ctffqD5eupTnzq7/vWvH7ElLETEhPUiYkIIYV6OlJhw+ctffuiwzZN07sBJu8hFLjL8fz/57d/+7SEfb3zjG7e2BGLCl3/5l2/9tb94Blb5HJx88slDff/93//91pawCIlMWC+U/VhMWMfIBHb+f//v/32c7Z9K2g8OdokJd77znQ/sGSNuVL4vetGLzq5whSvMvuqrvupYXl/1qldtHbn/3PKWtxzyduKJJ+YdDHMTMWG9iJgQQpiXIyUmjHnHO94xOF63vvWtt7ZcmIgJ68NBigmeFWlVREzYG4lMWC96YsI6Rib0+LVf+7WhM1zRaWMOMjLBdKjv/d7vHWyFTvupp566ted83ve+980e9ahHHeiz/9GPfnT20pe+NO9fWIiICetFxIQQwrxETJhDTPiCL/iCrb/2j4gJF4aY8GVf9mVbfx0unvWsZ0VM2AOJTFgvlP1YTFjHyIQet7rVrQYxYUo8LDHB9Ij9fsZ++qd/esjb3e9+9zzf4VARMWG9iJgQQpiXiAlziAlf/MVfPDv33HNnf/iHfzj77u/+7uE3X/u1Xzu7ylWuMizE1eOkk06aXe9615t9y7d8yxDZcKUrXWmYc/+xj31s64jtmRITbPu7v/u7waG91KUuNfz9+Z//+bPv+I7vmP3O7/xOd/TvDne4w+xHfuRHhjw7XvqGb/iG2f3vf/+tI2bDXH3bn/a0p21tOZ4rX/nKsy/5ki/Z+ut87na3uw1lA/OJv+d7vmc4x7d+67cO24oTTjhh9vM///Ozb/7mbx7Wn1AWf/AHfzAsZNbjTne60+zbvu3bBhHn4he/+OwGN7jB7E1vetPsR3/0R+daM+EjH/nIkI9/+qd/mr3lLW+ZXfva1x5ECNue97znbR01m5111lmz2972tkN+1JH83fCGN5y97GUv2zriAi596UvPvv3bv33rr/P5jd/4jWH7Jz/5ydnP/MzPDOeX5PknfuInhhdLPV3zmtccyts+z4ORTyOILbVmwqMf/ejZ4x//+NnP/dzPzf7v//2/QySG/IlcGPOLv/iLw2965XjeeecN+6573etubTkfZSivcO/f9E3fNBwnT/C7el4udrGLDfsk92R+9hjPwI/92I8NIdby674f8pCHbO29AOVwk5vcZPb93//9Q1nXea94xSsOdbtXEpmwXvTEhMMWmWB9guc+97lDB/+rv/qrB9t2mctcZnaLW9xi9olPfGLr6AvglP/Jn/zJ7OpXv/owNYFN8J4/+clPnutZfc1rXjNc1zu8SEdLG2Vdhe/6ru8ariv94A/+4Oye97znha6rjdKuOP897nGPY+0dG2Qb+/B//s//Gey99vOnfuqnjk2v0HY4H5FF+Vm8eIxzalO/5mu+ZvaVX/mVs2td61qDXRnnwxo1bM5nPvOZ2d/+7d8ObYtzsh/b3fuv//qvD22hCA7t9eUud7nhd1//9V8/lHVvHYd3vetdg83TnsuX9pStZx9bO+1cym6q3arpHa9+9auH+3nSk5402F9tme1l89xz7/kY43nRFl7jGtcY8qX99ey4L23cGM+g49/61rcOdV3X9Ex6pr2D60zEhPVC+UdMCCHMQ8SE/2lsdxITOD1CS3/4h3949ld/9VezpzzlKbPf+q3fGn7LgRw7yTqx9nHE7nOf+8z+5V/+ZehI2Wbe7TxsJyZc5zrXGfL0u7/7u7PHPvaxs8c85jFDJ84+HbsWDpHtf/zHfzx73OMeN+T9L//yLwfH6aEPfejWUbsXE/zmb/7mb4Z/b3SjGw3XePjDH751xGz2j//4j8M+jliVheNsM893jPzbZwGvJz7xicP5iCREBB3xRcQEZcjB4og+6EEPGhxZohD8ywn+oi/6osGRJHhwxEpwsYhZy5SY4FhlI2riXve61+BA3uY2txm2uxcdBmWtg20f59s+AkdLiQk6ChxfdStPHG3OsX2PeMQjto4+n92KCcqduKPz797V1wtf+MJhf+Vdp6d9XnQ+xtf/yZ/8yeHYX/qlXxqOvd/97jc4+7bd9a533TpqNogthAlijWfg6U9/+nBN9Up4Eha9VxKZsF4o+7GYsCmRCfOKCdoG9sjx7LAONjvn+fcejZ+/7/u+7xv2iXxg2/76r/966Gjaps3Y6XmtTjqxcZFnm10iDnonLY4on9oy5xqv+0BMIPZZk4GYe/Ob33xoX+qaJSa4P7aNbfLeawOqHZwSE9gr24nDpmKwycRE2/70T//0uHwQE2w3lcS/v/qrvzqUsbTdvRMT2HT3yxaxXdo051dnxIAzzzxz6+jz0UnS4SZwP/KRjxzqhk/guj/wAz8wCBoQNeYc2rRxHti4L/3SLx3q3/Gvfe1rh3IiBPidBZUf8IAHzG5605sOz02dcztKCPG8uG/PCFFIHuR3fA5tnfv+uq/7uqEcCNPaW22p87D781z3oIiYsF5ETAghzEvEhP9x5HYSExyjcR4jQsC+8YitTiohYYxIAMdz5nZiOzFB0nkbU4KCEfdCB85oxk7sRUzgdPYW/OJ4lhAz5t73vvfw21bQEM1hm47uGMfZt4iYIOnw99BZt/8Vr3jF1pbz4WhyCjmiLduJCb38Ghmy72pXu9rWlgu46lWvOuxrR5dKTNDB91K2GMX6xm/8xmF0itNa7FZMsN39lLDScslLXnJwgHeipmUYnR2jo2JfdS58ztPfnPBV4VoRE9YHZT8WE8ai67oyr5jgmeZgt3jvjOyL2Go7Q+yrcxLk2ufSu3vZy152iEDaqfNUUxzOOOOMrS17g3A6HuknJrgGG/jmN795a+sFlJjg3nXwe3nuiQk+r6zMiJTj99JIvePbTn6JCTr/43UhtkM77Xfa7XHetCHyIHJkp7KGjrtz1XPLDotw0xaOO+U67s5dURbaRn9PTZXZCQKN34tCGOf1CU94wlAHrtHuIyaoF6LF+DclQqiTdbWLERPWC+UfMSGEMA8RE/6n8Z1HTBA6OMY2+zg+hZFc24y69LCP87QT24kJRnl73Pe+9x32v+AFL9jacv5oGEeVA7wdexETeh1KGM2yvxf2DvuM1BdGbWybCns3YraImKBjPIXz9CIjYDTO79vOz3Zigo71mIoI8TyMuctd7jLsa+ukxIQp8UNEjP2OK/YiJlggrYfRMGXz+te/fmtLH6OLziOceEzV+8Me9rDhb1NNOOWmVywjCqFHIhPWC2U/FhMOW2SCzn3vGfOJRr9vxTrRSTrovQ6SUX3HmzKx3TNLmHTcVJj9ohANib1tnkpM0CHt5aXEBIL5VGevJyZUx7z3DIiWUJ7tNUtMINhvVyZjSkxwzt7vREJpx+YZoRfRIV+mvdW5tMvOz6a15zeVTftUArH21G+VxW46xaLptNs6cj2Iy5e4xCWOuw9iAhGr5ysRO9QbIWKR8txPIiasF8o/YkIIYR4iJvxPp2ceMaEHA2sfB6MQSmobh7L9Rnkljb0QxZ3YTkwwst1D6Kj9rSBgvmzNaRVSysnqsRcxwahTDyGq9hvB6pWFiIbv/M7v3Dr6/AUWOWRT6NAvIib88i//8taW46l6Ez3SyxfH3/62w72dmNDrdBiBs6/XKeek2vfiF794a8sFYgLnuYcpEvarp2IvYsIU8iE6wjGeF59366HshBP3ys+z7/dCi4uKBJGIRuYpLxN1GjFhfVD26qAVE1pxbp2ZV0zQ8e49Y9UhbyPEjGgLP/dOjN+Xn/3Znx2OJ7pu98ya8++48Xor83D22WcP77IO+x3veMdhbRhCApveExOmBN0SEwiDi4gJhFtrEfTuX6SA400zqHOWmMB+LvIel5gwZV9ufOMbD/s5PC0ivqynI3qK2EtQZu8dS5yvPOhQuf/f+73fO5ZXkSK2+U1tE8VAYPB7dW8KXRuJthOf/dmfPbSHU6LHL/zCLwznbtdgIibIc+83z3zmM4c8ml42VW8HTcSE9UL5R0wIIcxDxIT/6dzsJCZYF6FHT0zgENkmtJ8z0Es6gTuxnZhgRKxHT0woLLZlPqX9OoLjqRZ7ERMsONWjOpCiI3rlIJlvX5g/bB7uFPKwiJjAuetRESWc+16eKlnwrNhOTOh1kkpM6EW0bCcm9BY5BMHGfgJNsVsxYVyPPSx6ZnqMcxglG0eXGBUjBvXKrZKOSwsHX5kJ63Ze00N2ioCYl0QmrBfKfiwmHLbIhPF6A0VPTHC8TmXvPalkrv52z2x9ZcL6JvM+28qe3fc77yybYf0cnfgapW87bZX3qXsvMYHdnurs9cQE9tM7L7qid++S9VTqvkpM0MFchBITpoSr293udsP+dgpHbSOOmiqo3VI+1oSxvRUTIKKDQF+ddusyjCMYCnWlrExRU/+/+Zu/OfvABz6wtbePaC9igrqaEhP4LK7ZiibEBAMNERPCMoiYEEKYl4gJ/+Pg7CQmTI2W98SEWoxQ470Xli0mFBwZoxqOI3wUFomybSpyQQTBuBNaURhTYkKJDcJ356HEjil05oV+7gRnzHmmxAQPvf3KYV44wzrBLSUm9DpJHHb7thMT2ogOX2uwTQelh4W07G8jF7YTE7zQ9o3XcyAmmIc8L54XXyVxLgJJQSBynt0aC8+E9Tym1ttYFO9ixIT1QdmPxYSpDt66Ma+YQNjrPWM9MUFHzzoqe+kgGTV3Xh3teZ9t767faJfGvzHdbjz1YlViAhFBOzrVOR6zVzFhKv8W/7VfpAaUqbo03U6nqcW0QfvGYgJbbLt1i2wXaahud7o3QrE21ELC3ontICZYpHfqnFWv7Xk8Y8o5YkJYBsr/MIgJbIl36dOf/vTWlnBQaF9EtXmudkL/bFlT+lq0DaYNE44NmFlLZ952ad0wwCDabp7yXDXl50VMmGDRyASjzba1I8i7YTsxoT7tN2YeMaGw8JaOXPHP//zPw2+dY4zGxL5FIxOEjdrvuHmotQrG9wxhqOphGZEJ8ElEndl52a/IBKHMPW5/+9sP+9s1E6q83vve925tuQCrh9u328iEMSJtjNyVcah7793fvHAeOcQ6b3slkQnrhbIfiwmHLTJhETGBAKwz3Vv0dF7YNe+u93DesjSCzs71Oma2O1+7b1VigmkBts0bibRXMWFqbQDrDYlIK+eRDXW8NRDGWPfGvrGYAOcwjYEgrDwID/PYm/oixE5f7zAthFjbi2LwHGgHtUmtExwxISwT5b9uYoKvyOgA9iKbxsni2t4xEUamF3/qU5/aOsv+IDLTAJnneh7bcBRgM02j1Y5sB3v8eZ/3ecMX0ZbZUfZMOK9IMT6s/p1PM2+qmMAv1w7sVJ77QcSE/+kQLVNMgJEK80N7C/NBYe/EssSEGoEZ84M/+IODQ1RwkP3WfPYxPuFn36JiAqxsTrSYik4455xztv43G5w25+tFXggPtW9ZYkLlnbLXY/z5sP0SEyThsi0Mqw6ET0a2VBRMLXTY4hOk9i0qJkw9Lz6RZgpKGQd17vyu03aaivYZ7y3SiNNPP31w1q1YvlfUQcSE9UHZj8WE3nuyjqxCTPCZV9s4R733Yd51ECzs6zzska+k7PSMW6hP9IF6aDGNyXn2S0zwf2VmBN+zMKZtB7BXMcFUvrFwY90CeSASVN7ZcMezv21ZssHKjY3riQkiyJzLs6J9G19rSuxhq/3O+hXb1Z1Ov+OIy2NH13oX8uzTom0dREwIy0T5r7uYQHTznugcjqdQHbSYQED1zvlM7E52+qgwr5hggIyvrz+yzIgS6xOpE77nYaiTiAm7YJPEBEaXE2cfZbLmYOokczzaRfSmWJaY4IXU4WPYasErDq3jxp/qq0/6Cf+vY32j2mgOQ70bMcHcVJ1Qx5lr2pYFwWXcEa4ReKGgv/VbvzU4h+bsV2OxLDEBFmh0HMHDauPyxbk0t3j8pYf9EhPqixbq+A53uMMwlcHzR0wYL3Rp1KoWS+Qgyz+HWcefaGDfomICB1rUSu954Yi21Dobzuc5cc+mzvi97ZwQeB4990IN5c853ZuvbXg23vOe9wzH7QVOaMSE9UHZj8WEeUfTD5pViAnQAbVdtIAOoXfBe6aDPe7Ubwf74TzeMTaJzfDeeQc50RaQrXPVsWynxQ8thisqy3vHsdqvaQ545CMfOZQbx55dMhWMkGihV8e3QuZexYTrXOc6g930L7vk6zO2K+v2izLqkm1l8+VH+2RtHsfeYmthyJ6Y4FlWDtow5T8uC+0Y++u6teik9kK0gc7QPCNhjlde8lPn0EbLU289hYgJYZko/3UTE8ZYM+RzP/dzB/9tKm/EBFFhByUmWHsrvsj5EBNMVT6ozq8BOfZ0v5+FVUFMEGHYfjL+oDjSYoLRIM6XUZopOAVTn3I0GuH3QhzHMMDm5XCUGDKOn049x2ae74Q7p3OPV6UmTjhHD/Pu/aYd2bZwXq1FIHGmOFy9b3dbgdpnHr1sjuVocqx19swrMseoRaim62lktoPzdve7333o5OpUtmUxjgCAEH2OLsfLserAy8IRmlovosU8K/kycrMTRoh03H1z3bUYupvc5CYXWvPCvCRfKWi5973vPVynF7qs3O3r1bUQXPvaOtDgcBRhygkn2IJdjJ8OwGmnnTbsG6NuOJ3qVf51FNShurSdE9rCWR7XY4sw3PZ5IeR4XnpTT0As8oxYzM3xnGuiQftOED3UYS28qF7dq4XL2tXI9wJnZ53FBHmROGhHwbFwj+qgFRN6ots6YrFaHb4p8YMIbf8jHvGIbl2aG2+/+x7z8pe/fOh8EtK8Czq8bN3UuaZgV7zLRuAJETrE3i9CcM3lLyzsSMjVCXU9a93Y/5znPGdwtNtOW+W9F21UeHfZmKn8stPOYfRnjA4juyR6z/1LHMzxJyDZIefotQ/bUWKCNkD7JPrONb7jO77jmF0cQ/Bl3wnVjiXKeFYd616nviihLXItIu94PyGFmF1fUtK+KGvt6FS59fA1JqK332sLiDDE2d45RDG0URct1qVxL9q1Ra6/n6yrmKC8pKNiuwv3u+5ignaFmOBdm8qb94591IHkixIHvbOet53qU8fosY997Owe97jH4F8v8kxORSa4NvtqGztuEJCv6pPaBsKmMGhnAMexbDyfsD2vOnJv8ty7L3bMAFZ7D+yrr5bVNv7k/e9//+E843vlxz3mMY8ZImJ3KgvRd/xceWXz6lO6bL2vCM0jJli8lm0e3wsb4dz6V/xsx81TL/p7/FMdcPlnW4nm4/OfcsopQ/tDiFdXU+d2XX5xb/9UXfgNv8A2z58BXc+WyMXtBGZlr87ds/a1IivcC987kQkLsG5GLISwPmhg1lFMkAdJgyNcT+Pt/7X9sOLe1EErJkx1zkNYFvXFi6npWsvC800QEkE3T5RB2Jl1ExPKRsuHwQx5K9t9FHCv6y4m6PwSE0QRTeWNmGBg5pWvfOUQ4co+lJApGejqvcOivYgB7bEGawhz8zwDU2KCDq0oI1G5n/M5n3Pc+YmP484x4cEC6ON8S65R961j6nw6nb37ISYaIGw7njqyFnu1zWBZ3S/xtR29F0nquPbaRsR7URcEAB3ccX4Jxgay5pnmQPRRTvLUTnNwv+N8SDsJFAYyRXmNf6eO6/wGxnrHuRdTCts6cS0DowYCvCNj1K3n0mK9VRfEDL8hrohqHNe9CGkDjOPyJG5c/OIXv1B5ivS70pWuNAjzO5XnfhAxIYSw8XB21k1McH0NkLww9hwfSri/NTD2H3QeV4X7cp+tmKCOQlglNUVl1WLCk570pMHpFJ12WN/h/WZdxISyy2w0GyZChd3WGTgKtrs4TGKCd1UE1x3veMchOkwEgIX3qpM27hTr7Ou0Eid15jwD/tXZFMHkWdip/rcTE5zDtUWNeeaNPIuIc02CRduRN6VJ3k866aTBrtW5LHBeI/7Yi5jgazYim5SNetdm13lN/3KMqC/TwkR9uSdf9CLOeDba+/OJd9GnRvd1LN2bCGBTyVzfve/U+e2JCdagU9ci8tqIBc+AaK3ePY8hOpi2O57m4Hoiv9yPyGB14r5Et5niLKKvjWLYi5ggD8QaUWKeQ/n3XKo70Xht3pS150SZiWBRL/ImMkPUnXJOZMKCrJsRCyGsDwzsOokJrq0R0RDKjwZDeLzwNIZfA3SYnVL3pA5aMUEdhbBK9kNM4GRy8Diey5qmFdZDTGC3XJdtlgf1y2ZLosrYbja9bPdh5rCJCTrO1bErfApdJ87Ur9qng2fFf9OY2lFxiG5wPWu67FQW24kJRpZNRx4/47e73e2G37zpTW869hvRT6bF9jqtLXsREwgDvfNbGFd+CQ3j/UbrlYU1cqosTMOQB1+yGOfBFAvlOs+aCT0xQR05d1s2izIlJpgOrBxM/RvXievJi3U5qgzkf7digrrvTa/wGXd1z2cqTGtwz6bajMvzKU95ylCeno+dynM/iJgQQth4ODvrICa4psTwa2Q4YvKl8ZCsgWKbsFn7HVe/OYj8rgr3og5aMUEdhbBKVikmGHHzFSSjVEaEjJQdpnf2oDloMUFduia7pbOhU8lel+0mJpTtbgWFSoeNwyYmaIPG9WQKgd/7Yln9/tGPfvTQgTNC3XsGTUVgB3qdyJbtxASjyeMOLYw+uzaRo659y1vecjiP9Qd07qeetb2ICdZJ6P2mpmJMndOIeSt06HDL69TaD9Z+2W1kgjUIdJ6tyeVLHbuxD1NiAttumsFUvtS3dTdqocO9iAnWL+rVvTXElLW1LereCFqiUuq6Y6zHFzFhQdbNiIUQ1od1iExwPY2Aa2ssNIglJJRT6l8OgRGvEhQcr8GR/P4gkrxXWgbO475aMSGRCWHVCKc1UtZbaHGv6FyY6xoRYTUcpJigPl2PzeKYW1SZrW7tto4rW9YKCofRdhfOrR4Oi5jQ68CpTx1Unx6v3+vU6WC389PHydfaeudr2U5M0KHsdUItwqhDafqD8i982cy5XNtUC6P04/vdi5jQW3ARbOlOZdEKIxbv3m79AhEQ2+0vemIC2HdflyMY+yIR8cK7Oe+zPyUm6OT70tlUnZrioRzYAteS/92KCRZU7/3Gwpbqz0KbVRcWWDbNYqq8CCCZ5rAg62bEQgjrw0FHJriWBsB1NUg1F06exskIl/zKq0aTsMBhMz/uoJLOl1Tlttey83vnasWERCaEEKY4KDGhtd2ccmLv2GZLOi2+f28OOLGB7ZbXdbDdxA33sFe73eJ8h0VM8DnaXkexJyZUyLuOan2SdZx8Aa7XYW/ZTkywRkHbSS6mxAQYsb7LXe4yfHqbOGIxw/oqBHYSE8zHnxITxgsMFrVegvKdKgvrxlTZEQvM5Z/q3Oqw70VMAN/KVxx8Xln5+iTu05/+9G7+x2wnJviC2pSY4AtEyqHWapD/7cQE+ZsSE5yrV/c9MYFYYPHNqfLytbXtxIb9JGLCAcMgUPfWAQ2SOWFWFNVIrQNWPrV67kFBYfQya4zUk++6rwsWw5Ennzw76hxkZILrMP6uuZOQsO5J2Wn83M9eys9vlUcrJiwjMkHjbVRiHsdhN1iAiqMmpHRVz4+QSnZkfA86KD7taD5+jfr4DNQy86Ghv971rnfcNSwytZtrGOkSsrqqupjCp4PNF11V/WwaHFZOtfDsTS6TgxATlJfrsFUccu/H2CZuQiJQKzO2e1nPgHI5imLCgx/84KHjaJ76Xp7BZYsJLTqrjmujAmpKQm9+PXw+fFExwfQH52w7xdthbQX3PBWWb+HBvYoJLb7A4L74BFPRFS1TYoLFF7frtP/4j//44Bf45GVBGPBZ+Z6YwH8Yl9tuxARTSHz9Yqo8TRtJZMKCrJsRWxYHLSZ4aX/7t3/7OOeyklAuyuNBYaVa+fAt1YOgOuvUP6rv/e53v8FQrwsREy7goCITWmdUA6VD6NvRbeLwbMrfHEf3sdfy81vnaMWEZUQmHGYx4SY3uclwb0JafS3Ae62TsEysDO4abL5rCNs3urUbDkpM4Kyye/M4t0eBiAm7o2w3x56zzgke28NN+ZudYGuXKSgom6MoJlgo2TlvfOMbb9uR3YlVigkgCmurqiNJYPXb+9znPheyjcrJfZp/v4iYoLPud6I05ikLa084n68rjM/n3Xbvu12AcQrXUl++HrFTmzAlJtzgBjcY6qq3FgPfRV5EXbS/cx8+zdh7rrSN6mavYoJnUL56i3V6H+UrayYsyLoZsUW4293uNrvuda+79dfxHKSYwHEWkuT65gRRZH2upOaGWnTkXve619bRB4POg078QXCnO91pKBud9oPEYl+9PERMuICDiEwoZ9S1GHNCAqeudfQ2LSk/YbN7dUr9Trm0YsImRSZYRXlVzw8x4bKXveyF7sHCfsJAV3VvcG8WsJr3GmwgkaN3PIeJQ7XK/PbwaTBiyH5fd13xmbCICYtRtptTT0gwdaFnDzcpiYhjb92Te9vrs+AcR1FMwPWvf/3BVt7hDne40Hlf+tKXDlMOdirfZYkJRIJ2RBw+U3mZy1xmEKXrvkzBcS/WVFBvhXq72tWuNtzPomICtLl++wd/8AcXKgtfaBDJXPfn//Igb+1nK00dsk6E8+xWTNA2v+ENbzgun+qP+O4eXvjCF07eQzElJhAR5Nsov2eq8u2duuIVrzjkWwRfK1Zc61rXGra3HX1TVvWd1CHffK9iwote9KIhX5e73OWOWxtCXRMyXD9iwoJMGYpNwMPosx89DkpM0Ph82Zd92eDAepHChaHGqhuG7aB49rOfPSloaFAiJpzPfkcmODeD7zolJDDwvURg6G2vtE77OfOedw72XsrQ7/y+FRM2KTJhv8UEzpbrWrl7VddV/q7BcZ73GhaAWjcxIRxPxITFUEYc/BISfPmjtYfjtE72uZdqP0FEZ0ZHaS9CcKH8j6qY4J5NOWMvdWpFpwo5N02Az3Xf+973uI5lj2WJCULZtXk6lFe5ylUGv93fBgJ1Ntv3RCSxjrWwfQJC/da9GOUed+TnERP4AwZD3YvOsHwoC+dy7nFEAPFDaL59xA7tnf9bk+CXf/mXh327ERPueMc7Dnn1SV73pg11XvXxh3/4h93yHDMlJkCknnMTXK561asem2Lgiwn3vOc9L/R86fSrC9d3vHLxvMhXCQN7FRPgCx91HVMxCB7uWzSFrz0QbiImLMC6GbF5YNw95B4CL1QtWGK12IKY4KUBlc+Ii5Gj29/+9sNLuR323+pWtxrOYaEUK43Oy81vfvMhX9S2RWAYKW8czGte85qDEffCjPFwu1dhUhpsYcPmUxl5o1jCqthCsmCthtve9rbDfgpo4RwMaw9KpLAq5eX7vNTiMZWPN77xjcP/Kx9WnfX92B5GZi3ocvnLX34oI98Udg4NyBiL3agDKqU8qMMe5vlWhIV7paa6V+ee4qSTThoaMXm49a1vPeRBqmtUZIJFeaxxYVEgYczS3/3d3w1K5hQMm+eQMdLILPocrBv7GZngvIy9a3imXHPs4G1q4py5H++ABm/KwdgJZaR8WjFhWZEJGnf5IrT9yZ/8yfD+czp26kyxl94jTo3f9d6PEhM4WRwaDbxRKos9sVXKp4ftjiUGsAWcJu97Lz/jaQ46UX7LQbC4Vi10RSycB5Fkrqscfv/3f38YKRlf18JRbJ9rELfrGkaSeug8GJVzPEGhjm9FlnaaA1us7VK2bBrbtV1dsOnyrO3iJE6Vaw/fMGeL2/OLqHvgAx84bFNu8iAvN7rRjYZ5v1N50RG/zW1uM9SZY+985zsPI4/t8cRaDl3vHNoQK563o3NsgvNUm6NuDSZoc2xrz6PuRX8oB23pCSecMJlX76Q60DHQGXGPp59++kLTHHwH3rPde68JWvJ98sknH3cebUltMy/bauTmEMuv77LvdM152Q8xQV4XERI2JZWgQNTeqxBcKP91FxOMHPNH208pjmEfff2g1/nXPvk9e9X7vY4d/8p7J3k/2Jp5nk3+reM9021d8LvkqZcf9kR+2lF95c6GVB4kv59a14z9d791rDXH5JffyH63dcjWyaPnZ6fnRVmwhXVev3OPvbLQ9+Eb3/3udx+OrWkP8sb+7PQcsZuu9eQnP/lYOblfNo69rTzweZXPvM+6+3/kIx/ZLXvU4o51DbavXeRyjH3a+Treb90bUU/+23r0HtnmWetdX12o+971vNuPecxjjtXr8573vKE8n/vc5w7t2zq8lxETVogHnbqlw0cR83+J41JwIuyj7jlOpEB1ICUO0RgvKofQfh1jquolLnGJ4W/Owjy4jjURFoHBcQ2LqHC8KK9UONs46S1eSttNnSjhgqpm3lF1+v2eM3raaacNyjHVl/LKWQGD4ncc8xaNpU6FfVRGgoJz+5uo0lL58MLp9Pu//MvHa17zmq2jjofTrZ6sEut4efK3TnfBSFhF1X7qr3Ud/F/SQef4tXC0nY/h871aSqPzcsh6cApcs+rV+ev5qY5/iQmc0bq+jopP51ReLEozhsGzT3gUAUcnxN/KxLO1iXB29kNMcE5G3PlLSCDMVNIQbPLfOicc7HLoNXq7KUe/UUatmKCO9or33ne+iQc6uqYAte+eDvA4v+7nCle4wrDf71t76V1ojy8xgXNQdpiNK1tA+NVxHmMkyEiRERx50HGUP3lTBi2tmECw0CkXsuh4tqTec4LmdmXPjn7TN33TkC/XL/FT0uFkJ8GWuIY2wzUcW9eY6gxyeI3+ON691/FsWzmPzqkNsZp31UXbdimLOrbwjLEzjq+2q9brecQjHrHt/RZsPdvbOmSEXDaV063+jDARZowEObf6b/Pi3b3yla885MNxpk4QYS95yUsO7XHrqOuoE87H9wJr6Lheu4ilNsdvqs1xDfXtvjnkdRzH0m+JNdqwKm8ddc5oCwfNs2S/+5Rn/xo545w7zzxighBo9dnzp7RNRv4MZLT36hlTNtplbZf3QZ7lRdl6h3plsyirFhOUjWeGkOCdZBeWbT8P6m+dDeKo52SvQnDh9+suJoQQ1oOICftAOVY9iAn2c/CoTYUGwciNfUa1W4S32M4RbNHBt53ytR3mHTnO8YtCPRtT9+C8RXXidVg5bR6wMcQEx3AMrVo+ZkpMMEpm+1g40QGwvaId0OaDw75IZ/mmN73p8NuxAqxuOJ3CvCiDhRfIGhO9PBMTKh+U0XnhcPrddmsmSM7f3psIEo4fp7NFpIjjW0ELFPFyrDeR/YhMcD4OlnN7Nj3TnLnDlAh7ynKv4bJ+o5zURTUyzrtXapqDDmgrlMkvO2Rfa0ehY217+65CtIHtRprqHktM8I6wT+07pSPonWLPxo66EZcxRm6c30h6W4bjyARw1l3XKPk85c2u+c42YcWIU/2GraoIBB3Z9lw6HK6h4zzPNTzjjt9umoPr6GyqizYPtZjk85///OOuZfVr24XnttstJma7utspb1Nigt8LUTVa1p6jOu2tcKS9sc1oz/h6yqllt2JCtTk9Ec1Iv2MI7e15PaO2e2bb+1OXrkOcb49/0IMeNGz3vK5STFC2/I42T6JJSlQw0LBXVikmKJeekHBYkmfWwoHslfdvGeXn9xETQgjzEDFhH9DQ7yQmmCs1RkikfTrJhcqyzbybMRU5MLU+Q8HZctx2IfaLIITJ+dovHVQnXprqwJeY0LsX9MQE2ziMRm96iG7gbBZtPhbtzEyJCUQM26cWpyyxxyhvUWKCEahFmEdM4Fj38MzZ31JRIj0HV3SJfb3pIuuO+1mlmOBcnCvn9QwKCfY8HbbEYeScGjFftpjQe+YWpcQE9nKcLx0EHSsLPdU+o3W2icAZH68zZJ9pDLWvxAQ2pOeM1/XncdRFBDjXeI2CZYgJQivlQ0Rb73gRBPa3HeNViQlCjMfnsw6P3xI2ap+pFrb1okd0JO0T/bVT2W4nJhCax79nv3XQf+3Xfu3YPrbb9XaK/sBuxQS21KhxD1PcRBWM2xZo29xLCfDO4XyEmPaeizp+1WKCdZbGEIXkzTO1U73txKrEBGXiXCUk8EfGdm/Tk2eEEMwGRkwIIew3ERP2AU7FTmLCeLVWqBT7zK8vjHLZppNp1GechD5yVrfD6IdzcBp2C8fU3H/XFM7pfO2aAtWJ54RNUWLCeCSx6IkJojFs4xiO713yzVn7i8qHsONFmRITamFGL04PjqX95pcWJSZwvhZhHjFBB6SH58Z+L3kh/Jdz2Cu7qg+jdZsGh2rVYgJHXgebkMC5cs3D9i+HflVigvPvlerMV0drjKlD7ecKq9MtbL/3zBMfTXuq40tM0Dnt3TfBzfnYlR7y5b2s8zuXUeb2XMsQEyqqYkqoJTLYb95qnW9VYsJ4Shd0PP3W+jd1rVoXgsDA5rf1IJnqZipA71ot24kJ4/UOCvWsra1zexZrOph5qjpKU+xWTFA+vc6/8tJp97UkbfG4HEQMupda+0LkjPNZE6KXB/OS5WGVYoLpLFM+GHFf+9q710VYpZgg755nne3W3h2Wf4kJys/fyyo/v4+YEEKYh4gJ+wCHZScxoYdOi33tyL3FqmzbLnEWtqNC3TmJiyA/nDbrLdS1OIBWE/X/nphQ6x/0qM7r1EKBPTHBFAHbdkolAFQ+zLFelCkxwerAtk9RYg2nuSgxYdFw0HnEBM5wj7qmeiv8vVPitG4anJ1ViQnOw7GqkS3X4JQexsRh5Jiue2TClKMsKsj+EmetoeLv3nNeyVoHdb4SE3zOt3ffREznE3XQYqTbVLX2vELA/dsTE8w7b+9B2biuT/TOU961JsNUOYgWc752EUi2x7YpoWSMNRccPxUtoLOsI9nbV9dqoxCIGDvVhY7r1D0VxJ+xmCDiy7mnFiEmslsbpj23PBIJKk/WLGjFl2I7MYHw6j7bjnyJCdqP3m/k0f7xvY/TiSeeOJyTPXYNi5H16s3ikM43r5hgakzPnxI5QkywiGebb3XiWZ7ywQhj1iDZq4+2CjFBeXhOnE87ztHt2b1NT8otYkII4aCImLAPcAyWJSbooNo2nv+7KAQBzu+86FxwgjmPVjjldNfooBEUeeqJCcLqpygxwQPYoycmWJHdtnk7vPPkY4opMcHCYbZ7YXpwAu23MFlRHfs27Hge5hETHNOjJyaod3O+DxucqVWKCRxSnWtz8zlvBDDXbB26w5BWKSYor71SYsK4M18Q+uyvd5Yo4G8jvvNQYkIbnt/SExNqvr9pY+xyKxg61yoiEyq0farDoFPsfObY1/lWEZnQRoG01LVaMcEq1PJcI+67ZbvIhKmvQljfxmLGvbyaay6SQ6eZvRwft52Y8I//+I/DffYiEwjpvd9oN+0XgdDbP4ZI4BpTX5RYVEyYikzwFYypyATP7JQP5lNl1ofYq4+2KjFBvjzL6sX5iTk927fJyX1FTFgftKHWkfHMrSPeAflrfVhtmm2epb3Y5xZRWBYs5i8t65yF87ZfS9gJ9+c3y7y/cAERE/YBDsrUOgaLignVUTXitBdqdH3exQCFczq+Xdyw8Jma8T4jg7Zt14nn1DpmJzHhl37pl7a2zIZ5gbaZAz0PJSZsFyExBSfab8digtFO203z6OFTQvb7dF2x28gEUQd+1xMTOH/27SQmlOgDCyzqABw2ODurFBOcz0it54mjwLGyyKUpAULNlfFU2qT9mxKZMPUlFp3MtoNrhN7x40UQp1hUTKgR+LFggFqTYRViQnWepz5vWxEZvh9e5ztoMcFnGG176EMfOtf1p1i2mNCirXUen1usPOqo+454e72CQOKeFhEToENvSkfvnGPUofO5Vu98nlV5mEdMMLXCVzd6/pTvrLtOT0zwVYzeb7SNBAhrQMxzL9uxCjHBOSqizDvrfRWZqT31zo3tXy9twn73EjFhfeALey+Iy8t4jpeNT9bLnzotm2Hh9s/93M8dhNW9vsuF9unzPu/zhk8xt88O/90Cxbt9ntgdNokg6xmdB+vjuD8C+7LuL1xAxIR9wOfEOCM9FhUTYP6nBn4vWAHcuX0Gi7q3E+ZlOp7DMaYW+tsPMQFCLk2vaEfcp1iFmMAA294TiBwr4sPiWi27FRPMM/Y7q3+P2Y2Y4DNithlRO0xwQvdTTBASTEyidKvT1rnb5LRKMUEd7ZUSEzgn43ypD/t8brX2sSFC3HU+53HqFhUT2FHH64CNjxfB5dhViAnefcdbv2V8vLxZD0DoeXuNRcUEON6XX3plt6iYoFPiaxjs9zx1McUqxQRfTpJvi+xWvpWjdrLngPoUpusuKiZYlNN1RMzMUxc+GWpqTC8PPnspD/OICfX+iMYYH6u9k6eemOA3ymR8Pz5/7Tfa/r3UKVYtJrBD1vLQwREdQ6RxvZ4d3LQUMWG9KDHBGl/LeI6XjbVspsSEZXa2S0wgbtezw4/SJzK9UPu8GzyTuxUTfAEnz/HyiZiwDwgt1IGrBfnKEcVuxISKTvDZKQv61TepjZTqWO70acii1l/gEBr1aBfFMqpuZexaHLEWPvSta/t0mjXInNnLXe5yw779mOaAU089dXBKhVYqC8o8dPDuec97DiNfxTz5mGJqmgPqM5Qcr0KDy8G2/YQTTtjaej7VsedkL4LFt/zOfF73h7rf3UxzwNWvfvVhO4GIUwn3aDSuXexzk1D2qxQTNK6eR51sDqgIGe+bsEBCj06Mct70tEoxQR3tFZ0hU3UucpGLDPbGeYVSek8udalLDZ1G+W+p6ATTe4g/rb0URdSu6L+omOBfx+tsmj+v3HwNRUSRTpjtqxATwPbIC9tdv2FfLEJpe/vJS+xGTNBxZyuqo1q2B4uKCXjiE584bGfPCDGcQog0UWbalp3ytgwxQTQdsafCZDlA1iQocaANn7WOhW2+psDmqmOig+g+HXz7FhUTPIOeAwMDRgKrDfQOihYcf5Xir/7qr4ZysyaIPHivPGeEfNMM5GEeMUFHQd4uf/nLD76D8lcP7tFaFM7TExO0t9470wtLiDDl0Lm2W09hEVYhJsinvOnAGODwXGpHX/WqVw2+jZFj1xzbwU1L3suICetDxITz6YkJcF02byd7NYVnMmLCehExYR/wiUffBOeUVSp2IyZAhIARt/acEkdLJ39eOFCcmvF5KnkBCyuij/dbnEtHVNjmfokJ8M33cZlKl7zkJYdOcbEqMQE19aNN1FajhmN2KybAgljtNTQE2K2YoPPFMW7PWYlTuYlwCD1HqxQTNFqeBcZSp0VDqfPJYfCemBfo+pucVikmqKO9Qkz4xV/8xaEzKEy8fXZFA+kk9GDndNza4yUdIV8AKHazZgInbHxuCyQSnHT25Lc917LEBDz84Q8f8tNe20g6wXXMbsQEtoxNa89fjuZuxASou15dEKV9UnKnvC1DTODsy984Dxe/+MWPLXxY6IRe8YpXPK6cdayF6upY61AvKiZAB/CHfuiHLlR/yobINf4tAXucZ1F73lnXm0dMQH0toj2P59QzaHHGnpjgeda+K5/2d57lsXi3W1YlJnhO2LJa78Z88Te84Q3De08I9qls9zC2hZuUIiasFyUmEEeX8RwvG3Z5SkzQ2W5t614oMcF6Qst8drzLohv0PxYVEwiieY6XDzsUMWGfMBJg1N8LtizqnJIRht3Ciavz1Lmo+WM8KHWMDsdBQzhp873fcByNdrr2IiLOonCAXMO1vKjL4PTTTz+u7HYjdKwLnJ1ViQngEDind8Jz73rllKp3ToPGWJnqaG5q4pCu6tOQymzZEHeEK4sQmYfWXvrdsqiFppx3qkO7KtSb+3dtYsiyG3CirvbA+SuSaRm0deEdWna+d8L0pLq+JFJluzyYa+849TwlMO8GURBtPrSxU7A//AfHea92i3D/ema2ux6ICSISq4NRn4Redp2tSkxwnprqQBhi3wh9xHjPNTFBFI/2b2wPNyWpw4gJ60OJCb01E0RhEfQMhhkQJNSZLtTrwPMrTD8iPhMATV8T1UWElESlVZ2wC6b4ia6yULq1UayRQhwd52GeNRMI6gRj70rvPWe/rZXyiEc8YlJ8KDFhvGYCgd0gqfeyxWfmLXbrU7TuV7ShAS7Pd5sH9ldkwq1udatBFDRYSNgUQaWsLEo7zlOJCb3IC++OgQK2zhRq0X13utOdLpS/ME3EhBDCxrPKyAQ4V+uUEpFck3jQCgoabg1TOXmc19bpW/e/VykmLCMyIYSwv3CwdVBW7YOtQkwAW8SOOSfRj50TieB6Im9KUNDZGAsKy7avq/pb9KH7EXmhoxUx4WCZEhOsVfU5n/M5wxRl03H9rUMu4khntu28+iKPY4kGzudvYoEopG/+5m8eRtg9v9U+izYgHohqMs2WKOCz7TrlomjbDvQ8YgLB0N8ilXr1fsMb3nDIS3uOMVNiArFAXvkYhedYFBzxQIf/qU996jDtVj5FELbHlphAWBF1JgpMdBhhgzijPEVStdecEhNEJxFelBUxQ+ScqYnu7cd+7MeOu26YJmJCCGHj4eysUkyA85VT6hocubGgYMqDOfrm5RIcesnxohrWIZmaYSTdKK17MQrhX2GEmxCZEEJYLZsuJqBsN5umI6LjUoKCCAVRSjoVOhyiNnp2m5O8Lrab3S7bTQBxL6KGTHeImHDw9MQEdWMKk1Fv5doiUkHntRUHdKAtvDruzIpUcKw6b9vmXqSwZ9ZUNB339przfs3Be28K83ihROKVaQY+UTy+l5ZFxATUekYt1naTV/mrsiwxwVQr69e0z7roLevUEQjaMpoSEwgzbNx4OrCoD9f1FaL2+NAnYkIIYePhhK5aTIBzalg0oK7DySxBwTz1WkNBhIKw6LFDqsGyUJ9Rhsc//vFDQyVZ52O/k8XwjGBojIU8C7/kkBrdKofeve6mHP1GHbRignIKIWwWh0FMQNmksaBQUx4ICjVdTSd9bLvZRiO8B2272W1rZhm5NT1DZIX8Exc48qIvjHDvtfz8Xj1ETFicnpjgy1y2mR4wrht+g45u+7lEnXXTDMaddV/kch7RNPPUsWkDBIG24z6vmKDjLTpiPG3gXve617B9p472omJCD+XlHBbMrXOUmGB9oLHQAWskuRdlVb/piQneI9tEiIzvgx+kDqydNq6DcGEiJoQQNp79iEwonFfDo4EpQcH1jRiVoMCJ0DhbfNWIAcVd4sByBq2ELoKB6OA4Dd1+J44o51k4ozVT5FUZEjz26pCW496KCYlMCGHzsMjjLW95y207DctgP8SE1nbXlAfTuggFvo5h/nUJCuw5m1322986KQRYttsxB2G7ib9lu63fYdFZwrXoMvezrPZP+SuniAmL0xMTfEpc+L2OvFHvNlksW7SBcP2a6qCjrNM9FZmgvsd17Fn1jJgC4es4zi18X0RE2+meV0zwjliU1VSLtkNtEVxRE72OfMtuxARr15iyYM0G+bcugrz6u85RYoIFmdt8FURCQoDfVnkql7GYYAqHc9/61rcevqDT1om/a5rJPKLHUSdiQghh49mvyITCuXuCgtEhnXKdc58e04BxUktMMDXCturEcwaNLElGxvYzcZwJHyISiCAcao61exEqqAyXKSYkMiGEMMWqxYSitd06SzVdjf1rp6vpXNlWtlu0gk68fWy3EdODsNuSPGpjdLx02Agd2h+dSR28ZbR/ERN2T09M8EluYsJVr3rVYwsojhMBoMq4Ihmud73rHXvmfCLWNl/9addXgM8vWoBQ6L9IBNMpbnazmw2fjt2tmABfE3MsH8Gx/AbHWaBwp+dhETGBH3KFK1xhKCNCgAUVfe3O19yIJz0xwb6emAD7r33tax/b3xMTLNzo3L7W06sPidAwLutwYSImhOPQsFLlhFCtCmGCrrHO3O1udxs+uzYvQqLcE4Mf9h/Ozn6KCXB+jZKGpr7y4DkgKGhErXpu+oB3yVxITpljCAg68RxBQoNGmgCxX0neJKNZVpLnxMszIUH5aag18u5tt2Xod+qgFRPUUTgc+Bxt+znEg0aEj08n6hyGzWS/xASU7XYdNqpd/6aiy4jBbLdt8mS0k3jArrPdhIeDtN3W5TFFo4Ra9+D5d097sd1FxITd0xMTrn/96w8dZfU0Lz55q7NLIPCVge///u/vhuTXCPtd73rXC53/J3/yJ4evRuxWTCCoEQQshsjXEaUkTwSsnZ4xz+o8YoJ1P3zamfAh0qZ994lmztETEyyQWGJBi/eBIOGrEbW/Jyb4zLpyGF8zLE7EhHAcOhUM1+/93u9tbVk+vmntGusAQ/WVX/mVgwrc4tM6VoWdF06Fe1p3keSwovHYbzEBrqFhGgsK8sLhe/WrXz2MJBGZPCNGuCjwGtlyBC34JWRRg7rfiYPMUdQAcEI00jWypXHdbRn6nXO0YoI6CocDzuR1rnOd4xwwq3tf8pKXXMhZXhZWALdquXevMJ2Ig2p17lXZAiNnl770pSNiLIH9FBNQttu12CkRWa6vk6TjTjQgHrDd/CJ20toE7LoO1kHabnab+Fu2W949gzpny2r7lH/EhN3RExN0hm0jUs3zbHvGLA5o3v9YPBhjKoNpEb1wfPZpL5EJ+Omf/ulj5zft4ad+6qe6nfgx84oJj370oyevTbS2rycmfM3XfM1x91WYiuQ3pirUb3pigjVIpq4bFiNiQjiOwyom3POe9xxW0x3DYZAXI20tERM2C84OQ7bfYgLKKW0FBc+asFijRxxQooJEQLCtOtcMr3muOmAcwv1O8krg0Dgrt3JGOTt7KT+/dZ5WTFBHRx1hqspj0+mJCUbAOJrrIiZYHMxIIEd1VbZA6LK5w2Mx4WEPe9gQ9bNfNugwsN9iAsp2t4ICO8UPIhqIKGO3CcAiFOSRXXeMtWUO2nYrK7Zbu+MelmG7C+eJmLA7emIC4UmnXid6no64RQ6dg4+6U32yQd/wDd9wITFBxJbOMn92L2KCdTqqc+53OuvzvJ/zignWeLA+gX/H1zYthB3viQl8bgOB7bm9GyI4/IYoWPfXExOcR16I4Hy2Zbw3R5WICeE4NJj7JSaMjcaqoOC7npDyeWF8OYrz4twREw4OTuhBiQlwLc9zCQo66IyqUSQjDASEmlLAGdXp0ehVJ95vNPb7nTTmEudG3t3DMpxRv1cHrZigjo4yngEOjudg0xFCaj7qPA7lfuAb7cSEdRCsvEscXZER+2mDNp2DEBNQtts12asSg3X+iL/eV+8uOy4Kwb6y3ez2OthutnZZtrtwLueOmLA4PTEBFvDUaRYt8Ld/+7dDJ90nSfmNl7/85YepM1V/njsCAbvGt6z09V//9cM6CqJq69w19YDPSsh85CMfOawH4FhRBXuZ5lBc9rKXHezaVAREj3nFBO+Z4/jdph4QK+5+97vPvv3bv324D3ntiQm+MqNNNd3hoQ996Owud7nLcG5l5vftNXtiAog1Pp95sYtdbPaXf/mXw+Kq6s3vCRkWy+6VRzieiAlHHOIBo6RxxDyRCdR4L+A8oZ3Ue/ORGItiVZEJDDElcoyFYlxP9MC8MGrXuMY1tv664J7bka+WeSITOEpGObxoYblwdg5STIDradx1yjXcnDD5Ue+cUM4pcaGc0QpLdbz8arAOKsm35B6WUW7O4Z5aMWEdOnoHyR3ucIfByeHA7YRnhb1Zhq2o+d3znosD6Pjt6EUmzIP2xijvsqcF9CIT5sUItOezh+3Kb5F34iEPecjg4O4kJji3tveoi2zFQYkJUE/sYCsG811a221qAUeZ2OBdqk78OtnuZeKcERN2h08OimwV2TKuF+tw1EKDfEbJQn+i1pQ1PF83utGNho67r6nUlwasoXC1q11t+C0flQ2p89tXCzB+4Rd+4exXfuVXhnfKYs8/8zM/M9RloUMtf57t+j1b9KM/+qNDZ7r37tU0DVG88z4H8sePZhM9q4W1DH7jN35jeN8K00G/7/u+71i5fO/3fu/sQQ960PA7X7nwWdY6h3eUgKBv4VPbykh0gTaAcNIuZFm4P3lxf21eoJ58QrOtE3VkHR42YNnv1mEkYsIRxTzSL//yLz/24kgMmjmC/t8TE6h1XrD2N9/93d89LFY0xgvI2WyPtTYBxdYKrP5uoUbaZgSgh9VpGZcxjImVWNvrSD61QwAQVjbeJ1lgETr37d8FQ/1zP/dzg6Gyqmz7W+XEuWghYtjXExMoohTU9hzuhQELy8HzxpAdpJgA19QQuz6hgIJOOJAvzql/OaPt/NZyBA86LRPnc2/qohoZdXQU4YhP2SHTr6rsNcK/8Au/MLvoRS963DFGhDhS89SRVb85RBwnK3mPR7UIGr3zsKNs+/h4IzM9gbYnJphH6/djR42ttI+D2577Wte61mCjOcDCUnsOLNhOi4/p4E3RExPMh1UW7UKR3kV5f9SjHjV8t9y0jMqPkSkOvXxwIoUO1z6RGNqucdlx7LVNdc/f9E3fdOw3bfKJsrq/O97xjsNaDu1+I2NWY58qg6PAQYoJULfqscTgse32rHoGS0hwrDz63UGnVeDeIiYcDL/5m7852EvRML36NXIvwmE8yr5KbnrTmw62U2Tlqp65sLmUnxcx4QhBqeTAcHwIAVQ+DblQKQu+2DcWEziztnNQzQE3T5BDxgHjDBpxbdFZdrxOOkVfo2z+Fqf6W77lW4Z9LbsREzipQp2EjPnygr/lQ5iXzzbB4klUTee2Cq6/pRIDthMTKJ3CzBhsRp0zIQTsIhe5yOxSl7rU1pHnMyUmuKbtwnCN9nnh5I+wQkHdzUhauDDrEJlQuC5HTB44Y94v4gHntEJjx0LCYcM9ub9WTDiqkQk6JmyO0RMdXnaz7FBrN4V9shU6siLEiE5GUXRY/Y5QsNOzUmKCVbGNwgjRdB521ZoGzuNTX+152GeOqw67Djg75/pslw61DrdtLfOKCeqfHdV51nkX9aDtMHLnWvLBcfY7EQvj+xMV4H7Yz/ZaYxYVE3wn3SjY4x//+CE/8mUxRefwSTVto88A6kjJA8HaPp/ka/M4FhO0O0J6XffOd77zsNiaeta++h3xyD4roxN86vxCale5tsMmcNBiApR/2W42mq0uu82G+1veDrPtLtxfxISDgf0msCr/HqIbhOzzJfdDTNBGmIZgesVUnsLRJmLCEeQHfuAHBqe1nXpQcI7sa8UEDajOtRH5MUbXHf87v/M7W1vOd+JsMxozxii9fVLLbsSEinAYRwmMEZbluN4I23Zigu0+7TfGCJV9hIWiJyZwXI1ocbrHcDodbzQq7J11iUwoXFvS0MuLBphz6t+a1nCYnVH35R5bMeGoRiYU5qnqkOo0jdG51sn0/fDxM6EMiY+iwHbqYJWYcPGLX/xCxxI1Sixu68J3tOXrqU996oWuzf45n7y3++YVE3SSbTM3eOpZNzfYNW5xi1tcKM/Kw++tSTP1eywqJhChx0Kuzr19ymfcNpqeYZ/8tHkciwkgCrhub5qD0GKhy/vRAdg01kFMgDpzbXXEVo9tt+32b/c8Hgbco3uOmLD/iFBjQ9rQ/oItJIaKLCZG78dzSIiWHzbyoN7LsN5ETDhiEAY4S4xDD6Nm9rdigtEt2x7wgAdsbTkeo/eMW2GKgePNRe3xdV/3dcP+lt2ICUbThMruxG7FBA58DyNZfkPMKHpiAqfSNovh9BClIbQ47J11ikxoaR3TNtm+DvlbFe5tLCYc1ciEYjsxgfNo35QwWgsM7rTuS4kJRvt7z5d5r84jTLb2s3WmUkw5iTq/Y3FiXjHhcpe73LAI2E4OqPmvIr7Y1kJbJXrrSle60o6/X1RMMDd4fE5ii32iENp7KERuyGe7b1Ex4ba3ve2wj4g8juY76qyLmAD1JsmDuq3k79p32HGvERMOBuXNdrJpfEi+r8E8n0K0jW9qQGqV7wgb9cM//MODv+6aFidM/YcpIiYcMXTWGaff/d3f3dpyPJRH+1sx4d73vvewjWDAoI2TTr05n4XFXhw/hRVnx/sXFROMZjmeg74TuxUTOLFTuOcrX/nKW3/1xQQry9pmzm+v3OwjxIS9s26RCS3lfLbpsOMe1UErJiQy4XwxgVM+RgdWZ3rKObz//e8//LYXPdBiCpvOarsWQ4upZvabcmC/uvG38NWpa1sN3LXb/T0xoaZRtB3r3nE9CNbycd/73vdYvk1ds810i+3uGT0xoT4N2YoJxAp5Mv+3lyf7rJXT22e6B/Ggvb+emECQd92emGDKiYXR5JX9NwXvne9859beo806iQmF+huno4Lyj5hwsJgaS5y8613vOviWkvXOWnuzKrQ1pmqZdjf+KkUIYyImHDGsEM6J8YWDHsI77W/FBKNZtt34xjc+ZtDGqe2MUzMdP8XP/uzPXmj/TmICJbYVE0r0mLqPlt2KCeYwT0Eh9tmaoicmWIHXNp+2GZdXpXvc4x5bR4e9sK6RCUcVZT8WExKZMB2Z8B3f8R3bjuDrUPutBWG3e64rMqHtlLe89KUvHfZzEO23mre/2aipa1t527XbqIGeSDCOTDDH3HHs/TyOqE96KYc6VkddZMI8X35YNDJBJ76XJ/tEifT2zSsmbBeZUGjnrKFBlNZGiLDzCeOjzDqKCUcZ5R8xIYQwDxETjhgaBc4LZ6pHzeVvxYQK1+fQzsNNbnKT4XgrIPcw4m9/i1XGbZuaGmFfKybUJyzNs90Jn4lxbC9EuMQEym8LMWG8yGKL34iwKHqfhvRZG9ss8hVWyzpHJhxFlP1YTEhkwrSYYC699VWmOlCinPzWgn7bPdclJhhR6h1Xo/WmrNmvo+5vQsDUtUVgWWOg3T+PmABTA+aZpgCitby88IUvHBa89X/TAub57aaJCS2Pe9zjhuNNNWnPc9SImLBeKP+ICSGEeYiYcATRwTUHtse97nWvYX8rJmjgbeMszkONyHOqxnjIdNTtb9GZt6234KGVa+0br5lgJG+eaQI13cCq2WO2ExNs74Wg1hoSBJCiJyb4rW2+FxxWSyIT1gtlPxYTjnpkglX+dXh9OWCM+aj2nXzyyd3n1jQIHXqrym9HiQmiw3qdserws3tFRQD0Pr34gQ98YDjfta997ePON6+Y4He2zTPq7jlxXgKxiC3XnRKXx6yTmCAM2XWJBPPaoFpLQaTIUSViwnqh/CMmhBDmIWLCEeR2t7vd0Mn1eaoWDp9QU/taMQHmmNpuYS8LY7Vwjn2nvNCJNv9XR38cDcChdh6pxZws29pFDaEhu8xlLjPsG4sJRtds11lvnUh4sIuKELjf/e63teUCtpvmYLuFutrwXuGpPhtmFLEVGnrTHHDd61532H77299+a8sFWJXXZzbD3klkwnqh7MdiwlGPTLD2jA7vP/zDP1zo2RTFZd0Z9ndsE3zK0e/Gn3TsUWICmyMKoT2eHXQe07fajpov7PiNNQ9au8mmWtvFb9jn9lzzigmiDJxbdMN4rYj2WoXpFhZ8dLxrz9uhXCcxgfjtPNYlGp/npJNOulD7CVFuFuQ9yn5OxIT1QvlHTAghzEP5eRETjhicKQ6nlVqNitzwhjccohUs9GX7WEzgAOm02yfVooK+ye3v8dx/I2ycLPvMB3UNn6QUzmtlctvH/PzP//yw3VoEPjVpISwOlrUaXGssJsBnzfxGqgUiL3/5yw+ObqEh9J1zx9zgBjcYnMWHPOQhw77txATrOJjDa16r33GWHUsoEZ3QMiUmGEksQYEAYYVeeeQs2/bABz5w68iwFxKZsF4oe3XQiglHPTLBlxqs/eK9N1/eooePeMQjjj2nr3nNa2aXvvSlh/0WsfVVHMKlvwm/8zzPJSYQjHWufS2G7WV3/c2e9b4Y4XvlF73oRYf8+aSvqWpsIPvrazTja88rJoDoWwKH+2H/TH0gGow7jATWOrYVAXZincQEX4UggMuPyIyb3exmw7o9zqkNvNjFLjaI8iL4XMvvfeZtp8U1DzsRE9YL5R8xIYQwDxETjjAW49Jh5mhyHmvk8D73uc8wutJDpIHoAqNYnEKO6jOf+cytvcdz7rnnDtEARIJrXOMaQ/gnTjnllAt1ugurjRvFFxHA4a5z+w77gx/84OH/Y6z6fc1rXnPID1Hkjne844XyZI0FooTwXyNGRtogGkNeXvziFw9/F8QFIbYcQ6tzc1Y5y5zCXsiucGDnef7zn7+15Xh0FMyZ5oDLJ2d/qozD4iQyYb1Q9mMx4ahHJsBaAERZNpdga1rX+DkVUUA8sHCh6VdTi9L2KDHhoQ996NA5E83A3rgmwWC7d4IN0/HXwRWJ5v/qroepY+POPvvOPvauwR4TSORFe/BHf/RHXZECBAeixliU2A7thkUl28UaffHHNm1WXYc9t54EMbh3bfuscdPbRzAfl6F1hGwfd3xFmtzmNrcZIg4IB4QNvyPk+NLGT/zETwxtkXZFXWkrjzoRE9aLiAkhhHmJmBBC2HgSmbBeKPuxmHDUIxP2AxFhxAQRZpv4/JsyJ/9EkHQmjxYRE9aLiAkhhHmJmBBC2HgSmbBe9MSERCasnhIT6msNm4bpZKYHiOAIR4uICetFxIQQwrxETAghbDyJTFgvemJCIhNWz7Of/eyNExN0ViD8X96tqZOO5NEjYsJ6ETEhhDAvERNCCBtPIhPWi56YkMiE1bNpkQm1IKIFF6WrXvWqO37+MhxOIiasFxETQgjzEjEhhLDxJDJhveiJCYlMWD0+Y2sR2N4iseuKT2HKsy/r5J09ukRMWC8iJoQQ5iViQghh40lkwnrRExMSmRBCmCJiwnoRMSGEMC8RE0IIG08iE9aLnpiQyIQQwhQRE9aLiAkhhHmJmBBC2HgSmbBe9MSERCaEEKaImLBeREwIIcxLxIQQwsaTyIT1oicmJDIhhDBFxIT1ImJCCGFeIiaEEDaeRCasFz0xIZEJIYQpIiasFxETQgjzEjEhhLDxJDJhveiJCYlMCCFMETFhvYiYEEKYl4gJIYSNJ5EJ60VPTEhkQghhiogJ60XEhBDCvERMCCFsPIlMWC96YkIiE0IIU0RMWC8iJoQQ5mWjxASGLYQQxrz3ve+NmLBGlJjwyU9+8lgjc8YZZ2ztDSGEC/j0pz89iAk6rRET1oOICSGEedkoMeHDH/7w1l8hhHAB73jHO2bvec97BoOmAxsx4WBpxYRzzz13qJu3vOUtW3tDCOECPvrRjw6d1YgJ68NYTCD26CSEEMIYNuJd73rXZogJZ5111tZfIYRwAW9605uGkW8d14gJB89YTFA3p556apzREMKFML0hkQnrRSsmnH322UP9nHPOOWlXQwgXgpAgMoGNWHsx4Z3vfOfWXyGEcD6f+MQnho7qmWeeOUQv6cB+5jOfidNzgCh7dXDeeefNPvKRjwzTUN74xjcOTmkIIbRwRE877bTZBz7wgdnHP/7xYdpDxISDpcQE9aFeRP+dfvrpaVdDCMfBXvPBa6rxqgb0liYmvO51rxtU0hBCKDg4b37zm2fve9/7ho6rDmzEhIOlxIRPfepTQwizqDLTHDLVIYTQwj68/vWvH+y4EFnicMSEg0f5qwf1oV7Uz2tf+9r44CGE4zCQJzrYdNZVRgcvRUzgmGpwTjnllK0tIYSjjhHvV77ylcN8W6GyH/vYx4YObMSEg6XEhPGc21e96lVZiDGEcAyDRHy7LKC7Xih/9aA+anE19STCLHUTQoDIpVe84hXDgB5/nDi8Kh98aWKCzL7sZS8bwilCCOEFL3jBMFpi1KRCZDPfdj1oR7bMoxPKrOPwwhe+cNgWQjjaEBj5dEa1OKI1TS1iwsFTYoL6qKlq5YOrt9RPCEcb9uE1r3nN7NWvfvVgE9pP+66tmMAxNadOxp///OcPTmkI4WjCiL385S+fvfSlLx1GSmrxxYxqrQ+tM6qTUM6oenv2s589iD8hhKMJe8CX49Px7UQvJbJsfVD+6kF9VHRZ64Oz5amjEI4m7IKo4Be/+MVDxJLBIoNGq5ymtjQxQYeBgs0Zfc5znjN7+tOfPoxIhhCODuZlnXTSSUNUQjmiFNFVhleFxSlntKY6EA/Ya0Lwi170otkznvGMITyO2BBCOBoQDDihJ5988jDKTQyuz/q2jmhs+MFT0WU11YEPrr7UG0GY/c4n20M4WlgjQR/8ec973jB19a1vfetxa5atakBvKWKCjFkJ3Bcd3vCGNwwjkozZ0572tKFDoTFyIyGEwwWBwLtvNWnvPRGREeOQGh3h4MQRXU/UQxudoMGxvgWbbboDQeGZz3zmsRA5TmsI4XBB6GWnLcDKZyMkvOQlLxmERXbdqLdjsnjueqEeKjqBCKSeCMJGItWfenzqU586tMUiTRwXQjh8mELs3TeId+KJJ86e+9znDmJiLbwoKqGdZrwKG740MYFYwOHU+BAURCjoVHBIGbQnP/nJsyc96UmDyCAMS7I/KSlps1K9vyIQnvjEJ86e8pSnDAbsWc961tAJpYYyYhaFMuJt5DtRCetHOaMaGA2NBqc6FQQFEQrstXpWx+qaLY/9Tkra3FTvr3e7/LITTjhhEIKNaBGFCQlERAIjoZGQGDF4vVAPbXSCevJlHtGA6k89qs/Wfvu/erc9KSlpsxOfm0/GhvtXf5uNJySIUjK9wWAfsXGVUQlYmphQxoygQCFxIxZ+oJC6OTetsaJ8u2mNFwOXlJS0Ocl76/31HnNMGK8SCIVXWnBRZ5SQwIgZ0apFuzg+Yb1gu9UNsYfo4zNjBAURChxSojCBSB2LUlDnseFJSZubWhvOJ+ObGckyv9YotkW0DQoREkSV1YhWxOD1Q32UICz6z9pE6q0G9dQn+63j0bPfSUlJm5e8v60f7t0uG67fbTCPkCBayUA/H3zVYvBSxAQwZgQFGdeJEFpB2dYwGeXilLpRhq1Gtdz8WGlJSkpaz+R9lWp0y8i10Q8Oi8/CMmCcGCGVOqUlJGREa31RJ+qGoEC5JiiIUOCQEoVNVSEqiDZR1+pcKF3Z79jwpKTNSa399h7zyfhmHFCdT/NrOaEGhXRMCQmExlWOaIW9UYKCeipBQf2pR8J+z36XD97a8aSkpPVO9c6ObbiBvLLhBoL0v/XD9ccrqmzVYvDSxIRSR3UehFRQtBk0N6WDwaiJVjCfS8fDCCaRISkpabOSd5eDwnAREBgvHU8Lv1BCOTM6pTqn+2HEwt5oBQUOqQ6EKDNTVAhDnFLCMGFBnav72O+kpM1M3l0+GF/MYA/fTGi8aDIiYgnBnNASEhJVtt6UoNAO6rX2WxutrS77XT54UlLSZqbWhvPNyoab6mRASD+8BvP2wwdfmphQxkzGSyHVIBEVKCRuUGfDzTJuOh8SoSEpKWn9U72zFlr1DhMKOStEQw4oEYEBGzuhERLWn1ZQKFGYIERUULdEInVtCoS69wzEhiclbVaqd9Y7zBfzPhMQ+GgcUJ1QYqL3nx0oJzQ2fP0pH7zsd4nC7Lc2mv3WZpcP3trwpKSkzUnlg7c2nI9WNlz/e78H85YmJqAc0jJobkbHglNKWGDYiAtumIGTqKdJSUmbkeq99R4TDxguAkI5oESEMmAREjYLdVUOaSsqaJjUsbpW5+o+9jspafNSvbd8MO8xn4xvxkfjq/HZvPcRgjeTnv3WNo/td+uDJyUlbV4qG15+OBteIsJB2PCligkoh7REhTJqOhkMmwZLctMSQ5eUlLT+qd5ZyTvsfWa4vNtlvCIibD5j+13RZuq6bHj7LPSelaSkpPVLrf0uG17223teNrz8uLB5VN1tZ7/HNjwpKWmzUmvDS0AoG14++H7a8KWLCUXdiJtqHdNKbjopKWkzU73H3mvpIIxXWC2tDa96jv1OStr8VO9xbPjhZTv7LfWei6SkpM1I7bu8DjZ8ZWJCS91cm9x0UlLS5qXe+xwON7067z0bSUlJ659673M4vPTqW+o9G0lJSeudeu+ydJDsi5gQQgghhBBCCCGEw0PEhBBCCCGEEEIIISxExIQQQgghhBBCCCEsRMSEEEIIIYQQQgghLETEhBBCCCGEEEIIISxExIQQQgghhBBCCCEsRMSEEEIIIYQQQgghLETEhBBCCCGEEEIIISxExIQQQgghhBBCCCEsRMSEEEIIIYQQQgghLETEhBBCCCGEEEIIISxExIQQQgghhBBCCCEswGz2/wHwOoCKH65frwAAAABJRU5ErkJggg=="
                        width="241" height="94" alt="">
                </div>
            </div>
            <div class="section single-section">
                <div class="title">
                    Cost of Placement
                </div>
                <div class="text">
                    We know that cost is a primary consideration in determining value. The total cost of a policy always
                    includes the premium but may also include state surplus lines taxes, state stamping fees, carrier
                    inspection
                    fees, or a UCPM fee, where applicable. For the purpose of this analysis, we have used your total
                    “out-the-door” cost.
                </div>
                {Flex.CostOfPlacementTable}
            </div>
            <div class="section">
                <div class="title">
                    Coverage Overview
                </div>
                <div class="text">
                    <i>
                        The analysis is intended to be a resource to help identify the coverage being offered. It is
                        based
                        on
                        our evaluation of various issues, policy language, and the combined wording of both policy forms
                        and
                        relevant endorsements. It is not a guarantee of coverage. Whether or not there is coverage will
                        depend
                        on the details of the individual claim and the court''''s interpretation of coverage. Please refer
                        to
                        the
                        individual policy forms for specific coverage details.
                    </i>
                </div>
                {Flex.DcatHeaderNoteSection}
                {Flex.CoverageOverviewIssueTable}
            </div>
            <div class="section">
                <div class="title">
                    Detailed Analysis of Coverage
                </div>
                <div class="text">
                    The following pages contain various coverage issues. Each issue includes a brief write-up explaining
                    the
                    issue, why it matters, and how the policy, as quoted, should respond.
                </div>
            </div>
            <div class="section">
                {Flex.DetailedAnalysisSection}
            </div>
            <div class="section single-section">
                <div class="title">
                    Financial Considerations
                </div>
                <div class="subtitle">
                    Proposed Policy Term
                </div>
                {Flex.FinancialConsiderationTable}
            </div>
            <div class="section single-section">
                <div class="subtitle">
                    Rating Basis
                </div>
                <div class="text">
                    The primary rating factor for CPL coverage is your estimated revenues. Most carriers look at your
                    historical
                    sales as a fairly accurate representation of your future revenues and their potential exposure.
                </div>
                {Flex.RatingBasisTable}
            </div>
            <div class="section single-section">
                <div class="subtitle">
                    Surplus Lines/Admitted
                </div>
                <div class="text">
                    When a carrier chooses to write policies with state-guaranteed claims (up to determined limits), the
                    policy
                    offered is referred to as an admitted policy. Carriers also offer policies not backed by state
                    guarantees.
                    These are referred to as non-admitted or surplus lines policies. Admitted policy forms and rates
                    must be
                    registered with state insurance entities. This makes them less flexible. Surplus lines policies
                    allow
                    the
                    carrier to be more creative in how they craft coverage to meet specific needs. Often, coverages like
                    pollution are not available from the standard market.
                </div>
                {Flex.SurplusLinesOrAdmittedTable}
            </div>
            <div class="section single-section">
                <div class="subtitle">
                    Limits & Deductible
                </div>
                <div class="text">
                    Limits represent the second biggest factor in determining your premium after your contracting
                    revenues.
                    When
                    deciding what limit is appropriate for your business, it is sometimes helpful to review current or
                    upcoming
                    contracts. Many contracts require you to carry specific limit levels in order to bid on business.
                    Pricing
                    for higher limits is usually determined on a gradual scale (you don’t pay twice as much for twice
                    the
                    limits), making higher limits an option worth considering.
                    Each of the carriers also has options when it comes to deductibles as well. However, you should know
                    that
                    minor adjustments to the deductible don’t have the same impact on premium as other lines of
                    insurance.
                    Unless you choose to significantly alter the deductible (over $100K, for example), you will likely
                    not
                    see
                    significant premium savings. Your policy may have enhancements that include unique deductibles, and
                    please
                    review your quote. The table below indicates the limits and primary deductibles being offered at
                    this
                    time.
                </div>
                {Flex.LimitsAndDeductiblesTable}
            </div>
            <div class="section single-section">
                <div class="subtitle">
                    Carrier Ratings
                </div>
                <div class="text">
                    In addition to researching and evaluating each and every form and endorsement, UCPM also reviewed
                    each
                    carrier’s strength, stability, and capabilities - in case you are interested in something other than
                    what
                    was offered with your quote. Here, we compared their maximum limits and their AM Best rating.
                </div>
                {Flex.CarrierRatingsTable}
            </div>
            {Flex.RetroDatesTable}
            {Flex.MultipleLocationRetroTable}
            <div class="section single-section">
                <div class="title">
                    Final Thought
                </div>
                <div class="text">
                    The analysis was put together to help you better understand the terms offered by each carrier so
                    that
                    you
                    can make an informed decision about which carrier is right for you. If you have questions regarding
                    a
                    particular issue, please don’t hesitate to ask.
                    Coverage terms are often negotiable. If you would like to see coverage enhancements that are
                    important
                    to
                    you included, let us know, and we will work with the carrier to provide them if possible.
                </div>
            </div>
            {Flex.DcatEndNoteSection}
            <div class="section single-section">
                <div class="title">
                    Forms List
                </div>
                <div class="text">
                    The following forms were considered in the above analysis:
                </div>
                {Flex.Forms}
            </div>
            <div class="section single-section">
                <div class="title">
                    Disclaimer
                </div>
                <div class="text">
                    This Dynamic Coverage Analysis Tool (DCAT) provides an analysis of insurance coverage forms for
                    general
                    description and comparison purposes only. The analysis is not a legal review, and the review does
                    not
                    provide legal advice. The analysis is provided for educational purposes and does not provide a
                    specific
                    recommendation for coverage selection. While we strive to provide information about coverage that is
                    current
                    and accurate, we do not warranty the completeness or accuracy of the information contained in the
                    analysis.
                    The information provided by the DCAT does not alter, amend, modify, or supplement in any way the
                    terms,
                    conditions, coverage, exclusions, premiums, or retentions offered by the carriers’ quotes analyzed
                    by
                    the
                    DCAT.
                </div>
            </div>
        </div>
    </div>
</body>

</html>
    '
WHERE DcatTemplateId = 1

    print 'Successfully Updated Coverage.DcatTemplate'

end else begin
  
    print 'Failed to update Coverage.DcatTemplate with Dcat Template Id 1'
end


-- Update Coverage.DcatTemplate