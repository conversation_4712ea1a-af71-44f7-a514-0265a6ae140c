/*
*  Populate row Pulse.PulsePolicyGenerationStatus
*/

if not exists (
    select 1
    from Pulse.PulsePolicyGenerationStatus
)
begin

    insert into Pulse.PulsePolicyGenerationStatus
    values 
        (0, 'Pending'),
        (1, 'Processing'),
        (2, 'Generated/Completed'),
        (3, 'Error');
    
    print 'Populated Pulse.PulsePolicyGenerationStatus'
end else begin
  
    print 'Pulse.PulsePolicyGenerationStatus. Already Populated'

end

-- 110 - Populate Pulse.PulsePolicyGenerationStatus.sql
