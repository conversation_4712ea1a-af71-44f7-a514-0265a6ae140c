CREATE OR ALTER       view [Yoda].[LocationAGBPairing] as 

with LocationsInQuestion as (
	--Get all of the LocationGuids for the Locations that are in these Zip Codes and/or States. Will also need the AGB's name.
	select 
		Location.LocationGuid,
		case
			when Location.State in ('MS', 'AL', 'TN')
			 then '<PERSON>'
			
			when (left(Location.Zip,5) in (/*This should be South Nevada*/'89074','89113','89114','89117','80791','89118','89119','89121','89134','89183')
		          or Location.State in ('UT', 'CO')
			) then '<PERSON>'
			
			when (left(Location.Zip,5) in (/*[So cal ]Added 13 of them up to '93105'*/'93455', '93301', '93455', '93301', '93463', '93001', '55555', '93455', '93312', '93105', '92231', '93301', '93001', '93105','93412','93402','93405','93409','93408','93407','93403','93406','93401','93421','93420','93454','93453','93441','93460','93254','93102','93120','93121','93130','93140','93150','93160','93190','93101','93103','93108','93224','93067','93014','93013','93268','93252','93276','93022','93002','93005','93006','93007','93023','93024','93035','93003','93222','93009','93041','93043','93311','93044','93036','93030','93031','93032','93034','93004','93225','93033','93060','93010','93309','93061','93313','93066','93011','93304','93012','91320','93016','93241','91319','91360','93020','91361','93220','93021','93243','93015','91358','91359','91362','93203','93307','93062','90265','93065','91376','91377','91301','93094','93040','93099','93064','90263','93063','91307','91302','91383','91372','90264','91384','91304','91381','91367','90290','91365','91303','93531','91313','91364','91305','91308','91309','91311','91355','91371','91310','91306','91396','91326','91380','91324','91354','91357','90272','91356','91335','91385','91327','91328','91337','93532','91322','91382','91330','91394','91325','91316','91344','91350','90402','91416','91426','91436','93560','90401','91406','90406','90407','90408','90409','90410','90411','90403','90049','93536','93561','91343','90404','90405','91346','91395','91403','90291','91351','91321','91411','90294','91345','90077','90073','91386','91412','91392','91413','91404','91407','91408','91409','91410','91470','91482','91499','93581','90084','90292','90296','91405','91402','90025','90095','90293','90295','91341','90024','91340','90066','91423','91401','90064','93518','91333','91331','90094','90210','90067','91387','90245','90212','90045','91390','90267','91607','90266','90230','90034','90209','90213','91342','90254','90231','91605','90232','91604','91606','90277','90035','90211','90069','91603','91609','91610','91614','91615','91616','91617','91618','90261','90048','90056','90278','91601','91353','90046','91602','90275','90301','90304','90016','90260','90302','90503','91608','90306','90307','90308','90309','90310','90312','90505','90251','90311','90036','91505','90008','90250','91352','90507','90508','90509','90510','91522','90019','90274','90043','90068','91523','90506','90504','90305','90303','90028','90038','91504','91521','91506','90717','90249','90018','91040','90501','90010','90020','90005','90732','90004','90062','91502','90047','91503','91507','91508','91510','91501','91041','90247','90710','90029','90006','90502','90044','91201','90733','90734','90027','90037','90089','91043','90248','90007','91393','90749','90057','90731','90061','90003','90070','90015','91202','91203','90017','90026','90748','91204','90039','90744','91207','90745','90011','90746','91210','90134','90747','91334','90079','90071','91209','91221','91222','91224','91225','91226','90014','91208','90099','90001','90189','90002','91214','90059','93551','90030','90050','90051','90052','90053','90054','90055','90060','90072','90074','90075','90076','90078','90080','90081','90082','90083','90086','90087','90088','90093','90009','90013','91205','90220','90012','90021','91046','90222','91042','91020','90065','91021','90810','90223','90224','90895','90031','91206','90802','90255','90058','90033','90096','90221','90041','90813','90262','90831','91011','91012','90023','90806','90844','90042','90280','90801','90809','90832','90842','90846','90847','90848','90853','90063','90270','93510','90805','90032','90807','93502','93501','91103','91105','90201','90755','90723','90833','91123','91030','90140','90091','90040','90202','90022','91031','93534','91754','90712','90804','91803','91102','91109','91110','91114','91115','91116','91117','91118','91121','91124','91125','91126','91129','91182','91184','91185','91189','90814','91188','90242','91101','93539','91001','93584','93586','91801','90711','90714','91804','90239','91003','90803','91106','90706','91802','91896','91899','90241','91714','91715','91716','91104','91756','90840','91199','90240','93599','90822','90707','93590','91108','90815','91755','90808','90713','90640','91778','90661','90662','91776','93550','91107','91775','90660','90743','90671','90702','91770','90651','90652','90701','90715','90740','90650','90716','91771','91772','90721','90742','90610','90703','91023','90606','90720','90670','92649','91733','91025','91780','91007','91024','91731','90623','90630','91066','91077','91006','90607','90608','90609','90602','92845','91734','91735','90601','90605','93552','92648','90639','91732','90637','90604','90620','90638','92647','92684','91016','91017','92615','90680','90622','90624','91746','90603','92605','90621','92683','92685','93504','92655','93505','91745','92841','92804','91009','92844','91008','91706','92646','92833','92801','91010','90631','92728','92809','91747','91749','92708','90632','90633','91790','91793','92842','92846','91744','93543','92843','92663','92658','92659','92840','92832','92628','92627','92802','92899','92704','92803','92812','92814','92815','92816','92817','92825','92850','91702','92799','92805','92703','91722','92835','92626','93535','91748','92661','92834','92836','92837','92838','91792','92831','92662','92822','91723','93553','91791','92821','92868','92706','92660','92707','92702','92711','92806','92625','91788','93519','93523','92871','92735','92701','91789','92867','92856','92857','92859','92863','92864','91724','92870','92865','91740','92866','92617','92697','91741','93524','92657','92811','92614','92698','92616','92619','92623','92612','92780','93591','91765','92781','91773','92861','92885','92823','92782','92705','92606','92603','91768','92604','92807','92886','92869','92607','92652','92654','92651','93563','91766','93544','92620','92650','91750','91769','92602','92808','91767','92618','92637','92656','92887','91709','93554','91711','92653','92609','92677','92629','91763','92862','92630','93528','92690','91710','93516','92691','92878','92624','91786','92693','91784','92610','92675','91758','93596','91785','92692','91708','92694','92397','91701','92372','92673','92882','91762','92880','92674','92688','92676','91764','91729','92678','91743','91761','91737','91730','92329','92877','92860','91759','92679','92371','92358','92879','91752','92672','91739','92301','92505','92883','92881','92337','92335','92336','92509','92331','92503','92334','92055','92504','92392','92344','92530','92502','92513','92514','92516','92517','92519','92522','92316','92407','92377','92376','92049','92051','92052','92501','92054','92058','92506','92018','92394','92521','92342','92068','92531','92411','92570','92345','92322','92507','92508','92313','92008','92403','92532','92405','92347','92402','92406','92413','92423','92427','92340','92410','92011','92395','92028','92401','92418','92023','92056','92325','92393','92415','92368','92057','92010','92404','92518','92038','92039','92562','92007','92324','92013','92350','92357','92595','92557','92014','92408','92075','92587','92169','92354','92088','92024','92553','92037','92081','92083','92152','92009','92085','92107','92093','92590','92109','92106','92378','92092','92161','92391','92318','92572','92599','92551','92369','92147','92564','92317','92084','92135','92122','92121','92003','92110','92130','92586','92117','92140','92067','92571','92352','92584','92091','92118','92178','92585','92375','92078','92132','92374','92103','92101','92346','92079','92111','92321','92155','92069','92096','92554','92112','92137','92138','92142','92143','92149','92150','92159','92160','92163','92165','92166','92167','92168','92170','92171','92172','92174','92175','92176','92177','92179','92186','92187','92191','92192','92193','92195','92196','92197','92373','92589','92593','92108','92134','92308','92126','92563','92123','92029','92382','92104','92129','92385','92127','92116','92136','92145','92113','92307','92102','92555','91932','91933','92556','92026','92567','92548','92591','92552','91951','92124','92105','91950','92030','92033','92046','92131','91909','91912','92153','92199','92120','92128','92341','92198','92182','92359','92311','92115','92596','92059','92545','92398','91921','91910','92114','92320','91911','92139','91945','92074','92119','91946','91908','92025','91943','91944','92312','92582','91942','92064','92399','91902','92071','92592','92223','91941','91976','91979','91977','91913','92154','92027','92082','92543','92072','92546','92020','91915','91914','92022','92581','91978','92333','92583','92061','92315','92158','92544','92356','92305','92019','92021','92040','92327','92339','92220','92386','92065','92060','92536','91935','92314','92230','92549','91903','92070','91917','91901','92539','92365','92310','92282','92086','91916','91963','92256','92262','92268','92561','91931','92258','92066','92263','92240','92264','92285','91906','92235','92234','91962','91948','92286','92036','92284','92270','92260','92255','92261','92276','92210','92241','92211','92247','92248','91905','92252','92253','92203','92201','92202','91934','92338','92309','92236','92004','92278','92323','92366','92274','92259','92275','92254','92304','92277','92273','92281','92251','92243','92244','92233','92227','92249','92239','92364','92257','92250','92332','92283','92225','92226','92280','92363','92222','92242','92267')
			      --or Location.State in () --Trent does not have full states.
			) then 'Trent Gilbert'

			when (Location.State in ('TX')
				  or (Location.CompanyGuid = 'F2E88B20-255C-4AA2-B1F1-6052B4550B3F' and Location.State in ('OK')) --Unity Insurance Partners in OK
			) then 'Corey Stephens'

			when (Location.State in ('AZ')
			) then 'Amber Waldron'

			when (left(Location.Zip,5) in (/*Added North Nevada and North Cal is after the next Comment*/ '89450','89451','89448','89523','89413','89449','89508','89703','89511','89506','89519','89503','89509','89705','89460','89411','89501','89504','89505','89507','89513','89515','89520','89533','89557','89570','89599','89512','89704','89595','89433','89702','89711','89714','89721','89712','89713','89502','89432','89435','89431','89423','89436','89706','89521','89441','89701','89434','89428','89410','89440','89403','89437','89442','89429','89408', /*Added the first one 95242*/'95242','95325','95372','95305','96141','96113','96135','95720','96146','96126','95379','95383','95333','96161','96123','96160','93630','93660','96145','93627','95223','96118','93637','96142','95346','96109','96115','93624','96104','95735','96112','95306','96110','96105','96140','95311','93204','95721','93234','96151','95335','96148','96111','96143','93638','96150','93606','96155','95646','95375','93607','95345','95338','93653','96152','96154','96157','96158','93639','96156','93723','93656','95364','95321','93246','93706','93652','93722','93636','95318','93609','93245','93711','93705','93728','93239','96120','93266','93650','93704','93249','93741','93701','93724','93721','93707','93708','93709','93712','93714','93715','93716','93717','93718','93729','93744','93747','93750','93755','93760','93761','93764','93765','93771','93772','93773','93774','93775','93776','93777','93778','93779','93786','93790','93791','93792','93793','93794','93844','93888','93720','93703','93730','93726','93710','93745','93702','93740','93601','93614','93725','93612','93202','93613','93242','93611','93727','93645','93737','93625','93623','93626','93232','93662','93230','93644','95389','93616','93619','93669','93604','93212','96133','93651','93648','93282','93631','93201','93673','96107','93643','93227','93280','93602','93657','93618','93667','93649','93277','93291','93654','93274','93275','93666','93272','93256','93219','93263','93290','93278','93279','93605','93615','93216','93646','93670','93634','93235','93292','93647','93223','93675','93215','93250','93261','93247','93267','93517','93221','93664','93270','93621','93641','93286','93642','93258','93218','93244','93529','93603','93308','93541','93628','93237','93546','93257','93287','93633','93271','93262','93260','93226','93265','93207','93208','93512','93285','93205','93240','93515','93514','93238','93526','93283','93255','93545','93527','93530','93513','93549','93542','93555','93556','93558','93522','93562','93592','92328','92384','92389','95537','95564','95501','95532','95538','95562','95534','95540','95567','95503','95545','95547','95518','95502','95570','95524','95548','95519','95521','95565','95555','95589','95531','95528','95571','95554','95549','95560','95569','95553','95525','95543','95587','95550','95542','95420','95546','95559','95456','95488','95460','95437','95585','95410','95432','95514','95573','95417','95511','95526','95459','95463','95563','95427','95445','95556','95466','95568','95468','96046','95454','95497','95527','96039','95552','95415','95595','95429','95490','95480','95412','95494','95421','96086','95482','96031','95470','95418','96010','95428','95450','95481','96041','95449','95469','94923','95430','96048','95425','95486','95462','95465','95446','96085','95471','95493','95453','96037','94929','95419','96093','94922','96032','96050','96076','94972','94937','95485','94971','95436','94940','95441','95435','95472','96027','95444','95448','96024','95464','96052','95473','95492','94956','95451','94952','95458','95401','95439','94950','95403','95407','96014','96047','95443','95402','95426','95406','96091','94926','94927','94938','95404','94931','94946','94928','94924','94933','94963','95405','96097','95424','94970','96034','94930','94973','95422','94951','94955','94975','94953','94999','95423','95939','94947','96074','95409','95461','94978','94515','96033','94960','94948','94998','96095','94941','94979','94954','95457','94945','94957','96044','94949','94942','94904','96038','94903','95452','95467','94977','94965','94914','94915','94912','94913','94976','94939','96022','95442','95431','94925','94038','94037','94901','96029','96087','95979','94121','95433','94974','94964','95416','94132','94966','94116','94122','94044','94576','94017','94015','94018','94129','96001','94920','95476','94118','94016','96094','94127','94112','94567','94131','94117','95487','94508','94114','94014','94115','94123','94066','94080','94573','94109','94119','94120','94125','94126','94137','94139','94140','94141','94142','94144','94145','94146','94147','94151','94159','94160','94161','94163','94164','94172','94177','94005','94102','94110','96080','94103','94133','94019','94134','94574','94108','94083','94030','94104','94562','96089','94111','94158','94107','94105','94143','96099','94807','94124','94801','94188','96019','94128','95963','94599','94010','94130','94011','96049','96079','96067','94802','94808','94804','94074','96051','94402','94806','94060','95679','94559','96017','94497','96064','96070','94592','94401','94850','96002','96007','94805','94710','94403','94607','94615','94002','94062','94706','94820','94608','94530','94702','94581','94662','94803','94564','94707','94501','96003','94703','94701','94712','95987','95637','94604','94614','94620','94622','94623','94624','94649','94659','94660','94661','94666','94404','94617','94070','94612','95988','94021','94709','94609','96021','94708','96025','94547','94704','94720','94590','94589','94065','94606','94502','94705','94572','94061','94610','94618','94064','94503','94525','94020','94611','95017','94601','94028','96073','94621','95913','94602','94027','94063','94558','94591','95606','96035','94563','94516','94569','94025','94613','94026','94603','96078','94619','94305','94304','95955','94577','95060','94579','95006','94605','94553','94301','94022','94302','94309','94580','94306','96090','94570','94578','94510','94534','94303','95607','94545','94023','94024','94575','94549','94556','95007','95014','95005','94040','96008','96062','94541','94039','94042','94546','94540','94543','94557','95937','94523','94555','94043','94041','94597','95970','94595','96055','95064','95932','95018','96023','95041','94035','95070','94560','94544','94587','94520','94596','94088','94542','95015','94087','94522','94524','94527','94529','95943','95061','95063','95067','96084','94086','94518','95071','94533','95627','94089','94085','95912','96092','95694','95688','95066','96065','95129','94519','94583','94526','94598','95002','94507','95951','95030','95062','95696','94537','95130','94538','95044','95051','94536','95653','94552','95065','95033','95026','95031','96069','95117','94528','95054','95052','95055','95056','96057','95008','95010','94521','95050','95009','95011','95134','93953','94585','95073','95053','95920','95128','96011','94535','95032','94565','95124','93950','93921','95698','95687','93922','95126','94568','94582','94506','94539','95110','95036','93944','95625','95131','94588','95001','95196','96058','95125','95950','93942','95103','95106','95108','95109','95115','95150','95151','95152','95153','95154','95155','95156','95157','95158','95159','95160','95161','95164','95170','95172','95173','95190','95191','95193','95194','95113','95118','95101','94586','95112','95192','95003','93943','94517','95973','95035','95695','95133','96088','95116','94566','95929','95938','96059','95136','96075','96096','95926','94512','95122','93940','95976','95927','95120','95111','95982','95132','95123','93955','95042','95620','95616','95928','95121','94509','93923','95958','95697','95119','94531','95957','95019','93933','95039','95148','94551','95645','95139','95948','95953','95077','95013','94571','95141','95127','95974','95012','95617','95138','94513','95076','95135','95676','95993','95917','94561','95140','95776','96013','95618','95969','93902','93912','93915','95038','93907','93901','93962','95965','94548','94514','94550','95967','94511','93924','95037','95004','93906','95992','95991','93905','95046','94505','95391','95641','95837','95690','93908','95691','95659','95612','95020','95021','95680','95836','95961','95968','95615','93920','95954','95605','95942','95831','95798','95799','95045','95978','95835','95834','95833','96063','96016','95639','95668','95674','95811','95822','95818','95832','96028','95814','95840','95851','95852','95853','95860','95865','95866','95867','95894','95899','94203','94204','94205','94206','94209','94229','94230','94232','94235','94236','94237','94239','94240','94244','94247','94248','94249','94250','94252','94254','94256','94257','94258','94259','94261','94262','94263','94267','94268','94269','94271','94273','94274','94277','94278','94279','94280','94283','94284','94285','94287','94288','94289','94290','94293','94294','94295','94296','94297','94298','94299','95812','95813','95686','95816','96061','95377','95626','96134','95901','95673','95219','95817','95820','95815','96040','95823','95824','95838','95758','95819','95757','95376','95242','95234','95378','93926','93925','96071','95966','95825','95652','95206','95828','95024','95304','95692','95821','95826','95660','95864','93927','95747','95914','95759','95903','95843','95681','95841','95209','95842','95980','95916','93960','95829','95203','95608','95609','95918','95972','95204','95207','95827','95648','95211','95258','95621','95330','95210','95741','96020','95624','95915','95231','95075','95267','95269','95296','95297','95201','95387','95655','95202','95213','93928','95919','95977','95611','95678','95765','95241','95830','95670','95610','96056','95205','95941','95661','95940','95693','95628','95385','95337','95962','95677','95662','95632','95212','93932','95208','95360','95935','95742','95253','95946','95220','96068','95925','95336','95023','95663','95763','95363','95746','95650','93452','95923','95658','95975','95240','95671','95930','95630','95638','95237','95215','95949','95683','95366','95602','95984','96009','95358','95368','96137','95604','95762','95603','95664','95356','95227','95722','95320','95313','95236','93435','95924','95971','95981','95350','93426','95956','95043','93930','95351','95960','95397','95352','95353','93428','95672','95703','95614','95736','93635','96054','95682','95354','95307','95322','95254','95922','95367','95355','95712','95651','95640','95959','95934','93430','95328','95713','95947','96015','95319','95635','95945','96006','95225','95324','95357','95374','95613','95669','93954','95699','95226','95380','95623','95936','95326','95717','95381','95382','95230','95601','95910','95633','95714','95252','93450','95619','95944','95986','95667','95685','95654','95315','95361','95642','95334','95701','93446','95675','95983','95656','95629','93465','96103','95228','93422','96130','95316','95303','96125','95709','95634','93423','95312','95665','95386','96127','93665','95631','95301','95715','93447','95249','95323','93620','95689','93661','95232','95222','93622','95388','95221','96132','96114','95684','96106','96119','95245','95348','95246','95317','95248','96101','95636','95726','93432','96122','95327','96116','95341','95344','96124','93451','95728','95251','95229','95724','95257','93640','95247','93210','95369','96108','95329','95309','95310','96121','95370','95343','96128','95373','95340','96129','95255','95233','95666','96117','96162','95224','95365','93608','95347','93668','96136','93461','95644','93610')
			) then 'Mason Crawford'

			when (Location.State in ('OR', 'WA', 'ID')
			) then 'Connor Soelle'

			when (left(Location.Zip,5) in /*This should be North Florida*/ ('32648','32680','32608','32628','32693','32359','32669','32607','32601','32147','32610','32641','32612','32611','32627','32635','32178','32602','32604','32614','32603','32605','32131','32177','32606','32145','32138','32666','32329','32320','32185','32631','32619','32609','32140','32086','32653','32160','32007','32080','32616','32033','32694','32328','32356','32643','32615','32457','32655','32656','32456','32658','32044','32042','32323','32085','32322','32348','32084','32008','32092','32622','32038','32091','32697','32043','32410','32066','32079','32346','32465','32095','32054','32071','32403','32335','32024','32025','32058','32050','32259','32068','32260','32083','32347','32003','32061','32030','32006','32081','32082','32062','32358','32258','32065','32408','32223','32402','32406','32411','32412','32417','32355','32401','32073','32067','32327','32326','32256','32056','32404','32013','32360','32094','32257','32405','32214','32407','32212','32234','32222','32244','32444','32004','32217','32221','32336','32334','32060','32357','32063','32210','32449','32216','32250','32040','32224','32246','32321','32207','32064','32205','32461','32413','32362','32204','32266','32202','32235','32201','32229','32231','32232','32236','32238','32239','32240','32241','32245','32247','32255','32211','32409','32203','32099','32305','32254','32220','32507','32508','32206','32225','32361','32562','32087','32233','32459','32561','32209','32311','32059','32096','32466','32277','32228','32310','32550','32055','32506','32227','32540','32208','32548','32563','32541','32512','32430','32424','32549','32511','32344','32502','32314','32520','32399','32437','32544','32513','32516','32521','32522','32523','32524','32559','32591','32009','32501','32301','32307','32566','32219','32302','32315','32569','32306','32316','32313','32331','32340','32505','32304','32503','32341','32547','32317','32308','32509','32337','32226','32504','32579','32542','32218','32343','32052','32439','32438','32526','32580','32351','32588','32421','32578','32303','32534','32514','32345','32462','32318','32053','32583','32330','32309','32312','32560','32011','32353','32350','32333','32530','32332','32533','32034','32463','32435','32572','32041','32428','32352','32097','32455','32420','32324','32571','32035','32448','32046','32422','32432','32427','32442','32564','32577','32434','32537','32447','32536','32460','32539','32570','32431','32433','32425','32464','32446','32531','32568','32443','32565','32440','32567','32423','32426','32535','32445','32538','32452')
		          or Location.State in ('GA', 'SC')
			) then 'Tyler Redick'

			when Location.State = 'PA' and (left(Location.Zip,5) in /*This should be West PA*/ ('16611','16849','16839','16647','16834','16674','16660','16685','17228','16689','17212','16683','16877','16870','15861','16622','16623','16859','16654','16871','17229','16720','17253','16845','17233','17264','17052','17236','16865','16874','16669','17778','17223','16915','16844','17215','16652','16829','16803','17224','17249','17243','17060','16868','16835','17066','17255','16923','16802','16804','16805','17231','17239','17260','16801','17075','17002','17221','16851','17252','16853','17265','17271','16864','17764','17213','16827','16823','16941','17225','17051','16948','17729','16922','17004','17054','17220','17217','16937','17262','16822','17235','17219','17202','16828','17760','17246','17244','17210','17256','17251','17021','16927','17201','16841','16856','17232','17272','17029','17084','16826','17268','17035','16943','17254','17071','17044','17099','17240','17247','16875','17009','16950','17237','17058','16848','17063','16921','16852','17006','17769','17257','17222','17261','17751','17723','16854','17750','17082','16832','17745','16928','17726','17047','17266','17727','17241','16820','17841','17739','17059','16942','17037','17353','17748','17320','16901','17310','17056','17776','16872','17779','16882','16938','17747','17343','17040','17721','16935','17307','17024','17076','16920','17303','17885','17081','17049','17740','17324','17306','17812','17304','17325','17845','17375','17835','17015','17882','17843','17337','17014','17013','17090','17065','17068','17340','16372','15238','15413','15033','15420','15477','15221','15051','15463','15454','15104','15458','15412','15133','15087','15432','15072','16002','16317','15037','15438','15134','15110','15032','16438','15132','15435','15483','16029','16428','15024','16040','15112','15468','15235','16056','15442','15076','16442','15139','15147','15415','15075','16323','15439','15012','15145','15451','15422','16048','16039','15137','15478','15135','15035','15047','15049','15084','16374','16432','15131','15028','16030','15018','15484','15083','15148','15030','15144','15449','16404','15140','15480','15660','15473','16050','15488','15069','15492','15146','16023','16343','16413','15239','16025','15014','15401','15089','16034','16055','15482','15615','16022','15448','15479','15640','15068','16328','15678','15065','15436','15647','15642','15085','15472','15637','16041','16373','15416','16354','15486','16434','15489','15428','15695','15691','15625','16407','16344','15698','15692','16049','15663','16319','15665','15682','16346','15668','16036','15617','15430','15636','15611','15445','15456','16262','16054','16229','15465','15679','15455','16301','15675','16375','16028','15672','15623','15656','15421','15440','16058','15644','15437','15634','15632','15683','15431','15612','15641','15639','15688','15631','15697','16212','16213','15626','16218','15616','15425','16331','15629','16436','15673','15613','16341','15690','16405','16420','16232','15605','15606','16228','15601','15685','16236','16238','15633','16210','15470','15684','16364','16248','15680','16201','16226','15619','15635','15674','15689','15666','15621','15662','15624','16261','15664','15676','16416','15459','16259','16255','16402','15681','15490','15618','16334','16221','16254','15610','15686','15464','16370','16340','15670','16326','15693','15469','15446','15638','16353','15650','16223','15628','15462','16322','16321','16332','15696','16351','16257','15411','15661','15725','16214','15783','16235','16253','15736','16361','15620','16234','15774','16263','16242','15485','16371','15646','15424','15671','15622','16350','16312','16245','15756','16249','16258','15727','16233','15627','15687','16224','15752','16230','16329','16244','16220','16368','16369','15713','15551','16222','15502','15540','15658','16250','16225','15677','15779','15732','16240','15864','15717','16260','15557','15829','16211','16313','15716','15778','16217','15655','15750','15923','16246','15731','15705','15565','15723','15828','15754','15770','16366','16367','15701','15949','16256','15747','15763','15784','15501','15562','15748','15776','15767','16365','15544','15561','15944','15558','15542','15744','16239','15555','15510','15730','16345','15720','15531','15520','15532','15758','15547','15546','15920','15764','15780','15825','15860','16352','15847','15739','16347','15549','15759','15734','15954','15552','15929','15765','15781','15937','15771','15905','15541','15935','15777','15733','15711','15906','15728','15563','15548','15961','15907','15745','15915','15957','15772','15959','15928','15729')
		          or Location.State in ('OH', 'IN', 'IL', 'MI') 
			) then 'Gabriel Chepke'
			
			else 'Not Assigned' 
		end as AGB
	from 
		Crm.Location
	where 1=1
		and Location.IsDeleted = 0
)

--Use the CTE above to join the LocationGuids to the EmployeeGuids

select
	Location.LocationGuid,
	Employee.EmployeeGuid,
	LocationsInQuestion.AGB
from
	Crm.Employee
inner join
	LocationsInQuestion
		on LocationsInQuestion.AGB = Employee.FirstName + ' ' + Employee.LastName --Little rough to join on a string but it should work since we don't have people with exaclty the same name.
inner join
	Crm.Location
		on Location.LocationGuid = LocationsInQuestion.LocationGuid
inner join
	Crm.Company
		on Company.CompanyGuid = Location.CompanyGuid
where 1=1
	and not (LocationsInQuestion.AGB = 'Trent Gilbert' AND Company.CompanyName IN ('Arthur J. Gallagher & Co.','Arthur J. Gallagher Risk Management Services LLC', 'JVRC Insurance Services','C3 Risk & Insurance Services','Gallagher','Arthur J. Gallagher Risk Management Services, LLC '))
	and not (LocationsInQuestion.AGB = 'Corey Stephens' AND Company.CompanyName IN ('Hilb Group Central, LLC'))
	and not (LocationsInQuestion.AGB = 'Mason Crawford' AND Company.CompanyName in ('Arthur J. Gallagher & Co. Insurance Brokers of CA, Inc.', 'Arthur J. Gallagher & Co. of California - Fresno','Arthur J. Gallagher & Co. of California - San Francisco','Arthur J. Gallagher Risk Management Services LLC','Arthur J. Gallagher Risk Management Services, LLC '))
	and LocationsInQuestion.AGB != 'Not Assigned'