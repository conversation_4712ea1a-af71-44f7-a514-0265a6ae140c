/*
*  Drop Crm.ProjectOwner.StateCode
*/

if exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'ProjectOwner'
    and COLUMNS.TABLE_SCHEMA = 'Crm'
    and COLUMNS.COLUMN_NAME = 'StateCode'
)
begin

    alter table Crm.ProjectOwner 
    drop constraint FK_ProjectOwner_State, DF_ProjectOwner_StateCode,
        column StateCode;
    
    print 'Dropped Crm.ProjectOwner.StateCode'
    
end else begin
  
    print 'Crm.ProjectOwner.StateCode Already Dropped'

end

exec util_refreshviews;

-- 144 110 - Drop Crm.ProjectOwner.StateCode.sql
