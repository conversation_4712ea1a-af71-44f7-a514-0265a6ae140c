
/*
*  Create Policy.PendingEndorsement.ProcessingNotes
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'PendingEndorsement'
    and COLUMNS.TABLE_SCHEMA = 'Policy'
    and COLUMNS.COLUMN_NAME = 'ProcessingNotes'
)
begin

    alter table Policy.PendingEndorsement 
    add ProcessingNotes nvarchar(150) null,
        JsonData nvarchar(max) null;
        
    
    print 'Created Policy.PendingEndorsement.ProcessingNotes'
    
end else begin
  
    print 'Policy.PendingEndorsement.ProcessingNotes Already Created'

end

exec util_refreshviews;

-- 100 - Create Policy.PendingEndorsement.ProcessingNotes.sql
