
/*
*  Create Crm.Agent.SurplusLinesAgentGuid
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'Agent'
    and COLUMNS.TABLE_SCHEMA = 'Crm'
    and COLUMNS.COLUMN_NAME = 'SurplusLinesAgentGuid'
)
begin

    alter table Crm.Agent 
    add SurplusLinesAgentGuid uniqueidentifier null -- Set when we migrate Primary Source to the new account type
        constraint FK_Agent_SurplusLinesAgent
        foreign key references SurplusLines.SurplusLinesAgent(SurplusLinesAgentGuid);
    
    print 'Created Crm.Agent.SurplusLinesAgentGuid'
    
end else begin
  
    print 'Crm.Agent.SurplusLinesAgentGuid Already Created'

end

exec util_refreshviews;

-- 190 - Create Crm.Agent.SurplusLinesAgentGuid.sql
