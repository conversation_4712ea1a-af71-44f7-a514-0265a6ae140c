/*
*  Create SurplusLines.SurplusLinesEndorsementType
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'SurplusLinesEndorsementType'
    and TABLES.TABLE_SCHEMA = 'SurplusLines'
)
begin

    create table SurplusLines.SurplusLinesEndorsementType (
        SurplusLinesEndorsementTypeId int not null
            constraint PK_SurplusLinesEndorsementType
            primary key,
        EndorsementType nvarchar(50) not null
    );

    print 'Created SurplusLines.SurplusLinesEndorsementType'
end else begin

    print 'SurplusLines.SurplusLinesEndorsementType Already Created'

end

-- 170 - Create SurplusLines.SurplusLinesEndorsementType.sql
