/*
* Wave 1 dependencies typeID 1. Schema D - L
*/

UPDATE Lookups.SchemaClone
SET SchemaClone.SchemaCloneTypeId = 1, SchemaClone.CloneWaveNumber = 1000
WHERE
    (SchemaClone.SchemaName = 'Dcat' AND SchemaClone.TableName IN (
    'DcatNotification',
    'DcatReviewGroup'
    ))
    OR (SchemaClone.SchemaName = 'Doc' AND SchemaClone.TableName IN (
    'CommonlyShared',
    'DocPerformanceAction',
    'DocType',
    'NeverShared',
    'StorageFolders'
    ))
    OR (SchemaClone.SchemaName = 'Employee' AND SchemaClone.TableName IN (
    'BrokerAutoPrunerPlan',
    'BrokerExpertiseLevel',
    'BrokerPruneMode',
    'Department',
    'EmployeeRenewalHandling',
    'FindRepFactor',
    'GoalFormatType',
    'GoalScopeType',
    'ProductQualification',
    'Setting',
    'Team'
    ))
    OR (SchemaClone.SchemaName = 'Erp' AND SchemaClone.TableName IN (
    'BestManagementPractice',
    'CertificateClass',
    'ErpCustomerSource',
    'ErpDocumentSourceType',
    'ErpEmployeeStatus',
    'ErpMaterial',
    'ErpPhoneType',
    'ErpPreference',
    'KnownCertification'
    ))
    OR (SchemaClone.SchemaName = 'Finder' AND SchemaClone.TableName IN ('SavedQuery'))
    OR (SchemaClone.SchemaName = 'ForTest' AND SchemaClone.TableName IN ('StringEncoding'))
    OR (SchemaClone.SchemaName = 'Help' AND SchemaClone.TableName IN ('ReleaseStatus'))
    OR (SchemaClone.SchemaName = 'Jarvis' AND SchemaClone.TableName IN ('JarvisTask'))
    OR (SchemaClone.SchemaName = 'Kato' AND SchemaClone.TableName IN ('KatoShareableContent'))
    OR (SchemaClone.SchemaName = 'License' AND SchemaClone.TableName IN (
    'LicenseAuthority',
    'LicenseStatus'
    ))
    OR (SchemaClone.SchemaName = 'Lookups' AND SchemaClone.TableName IN (
    'Animal',
    'ApiKey',
    'Application',
    'AreaCode',
    'Color',
    'Country',
    'DesktopSoftware',
    'FeatureGroup',
    'GardenerRegion',
    'LoadingPhraseCategory',
    'Naics',
    'NavigationSource',
    'NodeType',
    'PolicyTrackerControl',
    'PreflightWebTest',
    'ProfileWhiteList',
    'RequirementType',
    'SchemaCloneType',
    'Sic',
    'SiteProject',
    'SoftwareVersion',
    'Sortable',
    'SortOrder',
    'State',
    'TimeZone',
    'TransactionTest'
    ));