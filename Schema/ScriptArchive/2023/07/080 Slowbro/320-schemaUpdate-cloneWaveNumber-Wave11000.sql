/*
* CloneWaveNumber 11. Schema All
*/

UPDATE Lookups.SchemaClone
SET SchemaClone.CloneWaveNumber = 11000
WHERE
    (SchemaClone.SchemaName = 'Cache' AND SchemaClone.TableName IN (
    'FinancialSummaryPolicy',
    'PolicyHistoryPolicy'
    ))
    OR (SchemaClone.SchemaName = 'Carrier' AND SchemaClone.TableName IN (
    'CarrierApproacherEmployee',
    'CarrierPolicyPoolRequest',
    'CarrierSubmissionFootnote',
    'CarrierSubmissionIssueOverride',
    'CarrierSubmissionJunkDrawer',
    'CarrierSubmissionOption',
    'CarrierSubmissionStatusTemporal',
    'CarrierSubmissionSubjectivity',
    'CarrierSubmissionTermsAndConditions'
    ))
    OR (SchemaClone.SchemaName = 'Coverage' AND SchemaClone.TableName IN (
    'DcatHistorySelectedSections',
    'SubmissionForms'
    ))
    OR (SchemaClone.SchemaName = 'Employee' AND SchemaClone.TableName IN (
    'EmployeeWatchSubmission'
    ))
    OR (SchemaClone.SchemaName = 'Flex' AND SchemaClone.TableName IN (
    'CarrierSubmissionFlexResponse'
    ))
    OR (SchemaClone.SchemaName = 'History' AND SchemaClone.TableName IN (
    'PhoneCallRecording'
    ))
    OR (SchemaClone.SchemaName = 'Kato' AND SchemaClone.TableName IN (
    'KatoSelectedContentSelection'
    ))
    OR (SchemaClone.SchemaName = 'Mail' AND SchemaClone.TableName IN (
    'EmailBlastTarget'
    ))
    OR (SchemaClone.SchemaName = 'Ocr' AND SchemaClone.TableName IN (
    'OcrSubmission'
    ))
    OR (SchemaClone.SchemaName = 'Pilot' AND SchemaClone.TableName IN (
    'CarrierSubmissionSparkSnooze'
    ))
    OR (SchemaClone.SchemaName = 'Policy' AND SchemaClone.TableName IN (
    'Claimant',
    'EndorsementAddress',
    'ErpPolicyReviewService',
    'PolicyPostMortemSelectedPostMortemReason',
    'SubmissionDeclineReason',
    'SubmissionStatusHistory',
    'SurplusLinesFilingHistory'
    ))
    OR (SchemaClone.SchemaName = 'Survey' AND SchemaClone.TableName IN (
    'CarrierOptionResponse'
    ))
    OR (SchemaClone.SchemaName = 'Tag' AND SchemaClone.TableName IN (
    'ClaimTag'
    ))
    OR (SchemaClone.SchemaName = 'Touch' AND SchemaClone.TableName IN (
    'TouchSubmissionCost'
    ))