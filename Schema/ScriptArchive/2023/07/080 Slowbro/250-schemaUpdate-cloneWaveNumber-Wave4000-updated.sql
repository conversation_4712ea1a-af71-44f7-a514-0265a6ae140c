/*
* CloneWaveNumber 4. Schema All
*/

UPDATE Lookups.SchemaClone
SET SchemaClone.CloneWaveNumber = 4000
WHERE
    (SchemaClone.SchemaName = 'Acc' AND SchemaClone.TableName IN (
    'AccountBank',
    'AccountingPeriod',
    'AccountTransfer',
    'CarrierContact',
    'ChartPermission',
    'EmployeePayroll',
    'Payment',
    'PaymentFilter',
    'PaymentInProgress',
    'TransactionCompanyContact',
    'TransactionTypeRule',
    'VoucherPaymentRequest'
    ))
    OR (SchemaClone.SchemaName = 'Badge' AND SchemaClone.TableName IN (
    'BadgeRequiresClassOfBusinessCount'
    ))
    OR (SchemaClone.SchemaName = 'Board' AND SchemaClone.TableName IN (
    'Board'
    ))
    OR (SchemaClone.SchemaName = 'Cache' AND SchemaClone.TableName IN (
    'EmployeePilotStats'
    ))
    OR (SchemaClone.SchemaName = 'Calendar' AND SchemaClone.TableName IN (
    'Event', 
    'RoomBooking'
    ))
    OR (SchemaClone.SchemaName = 'Campaign' AND SchemaClone.TableName IN (
    'EmailCampaign'
    ))
    OR (SchemaClone.SchemaName = 'Carrier' AND SchemaClone.TableName IN (
    'CarrierMarketer',
    'CarrierPolicyPoolBatch',
    'CarrierPolicyPoolRecovered',
    'Underwriter'
    ))
    OR (SchemaClone.SchemaName = 'Cerc' AND SchemaClone.TableName IN (
    'IssuedCerc'
    ))
    OR (SchemaClone.SchemaName = 'Cob' AND SchemaClone.TableName IN (
    'ClaimsTemplateClassOfBusiness',
    'ClassOfBusinessBestManagementPractice',
    'ClassOfBusinessDetail',
    'ClassOfBusinessNaics',
    'ClassOfBusinessPollutionPreventionPractice',
    'ClassOfBusinessSic',
    'ClassOfBusinessSynonym',
    'ClassOfBusinessToGroup'
    ))
    OR (SchemaClone.SchemaName = 'Commission' AND SchemaClone.TableName IN (
    'CommissionLevelCommitmentByState',
    'CompanyContingency'    
    ))
    OR (SchemaClone.SchemaName = 'Content' AND SchemaClone.TableName IN (
    'ContentBlockCurrent'
    ))
    OR (SchemaClone.SchemaName = 'Crm' AND SchemaClone.TableName IN (
    'CarrierNonPreferredStatus',
    'Company',
    'EmployeeMailSettings',
    'InsuredClassOfBusiness',
    'InsuredLocationInsurable',
    'InsuredResponsibleRepEmployee',
    'InsuredUserDirectSendTemplate',
    'ProfileClassOfBusiness'
    ))
    OR (SchemaClone.SchemaName = 'Dcat' AND SchemaClone.TableName IN (
    'DcatForm'
    ))
    OR (SchemaClone.SchemaName = 'Doc' AND SchemaClone.TableName IN (
    'DocTag', 
    'FolderSurplusLines',
    'PinnedFolder'    
    ))
    OR (SchemaClone.SchemaName = 'Employee' AND SchemaClone.TableName IN (
    'AutoOpenParent',
    'Bookmark',
    'BrokerPruneMonth',
    'DcatEmployee',
    'EmployeeAchievement',
    'EmployeeAreaCode',
    'EmployeeCover',
    'EmployeeDepartment',
    'EmployeeGardenerRegion',
    'EmployeePhoneImport',
    'EmployeeSetting',
    'EmployeeSortPreference',
    'EmployeeTouchDefinition',
    'PersonalGoal',
    'ReportNote',
    'ReviewGroupEmployee',
    'StateDefaultAssigned',
    'TeamClassOfBusiness',
    'TeamEmployee',
    'TeamGoal'
    ))
    OR (SchemaClone.SchemaName = 'Ero' AND SchemaClone.TableName IN (
    'EnviroRiskOverviewCob'
    ))
    OR (SchemaClone.SchemaName = 'Erp' AND SchemaClone.TableName IN (
    'ErpCustomerClassOfBusiness', 
    'ErpProspect'
    ))
    OR (SchemaClone.SchemaName = 'History' AND SchemaClone.TableName IN (
    'BugReport',
    'DesktopLogin',
    'Edit',
    'ExpirationDate',
    'FinderLog',
    'NodeHistory',
    'SearchRelevanceByUser',
    'SearchRelevanceByUserGrandTotal',
    'StoredNodeSet'
    ))
    OR (SchemaClone.SchemaName = 'Hit' AND SchemaClone.TableName IN (
    'Hit'
    ))
    OR (SchemaClone.SchemaName = 'License' AND SchemaClone.TableName IN (
    'LicenseType'
    ))
    OR (SchemaClone.SchemaName = 'Lookups' AND SchemaClone.TableName IN (
    'DesktopSoftwareVersion',
    'EmployeeAssignmentLookup', 
    'Feature',
    'Preference'
    ))
    OR (SchemaClone.SchemaName = 'Mail' AND SchemaClone.TableName IN (
    'DirectSendLogAttachment',
    'DirectSendLogRepeated',
    'DirectSendLogTo',
    'DirectSendTemplateVersion',
    'OutlookReport',
    'SparkyEmailOverride'
    ))
    OR (SchemaClone.SchemaName = 'Marketing' AND SchemaClone.TableName IN (
    'Assistance',
    'MarketingActivityAudience',
    'MarketingActivityEmployeePerformedRole',
    'WebEventOnDemand'
    ))
    OR (SchemaClone.SchemaName = 'Messaging' AND SchemaClone.TableName IN (
    'Chat', 
    'MessageTargetChannel',
    'ServiceEmployee'
    ))
    OR (SchemaClone.SchemaName = 'News' AND SchemaClone.TableName IN (
    'InsuredStoryClassOfBusiness',
    'OnlineArticle'    
    ))
    OR (SchemaClone.SchemaName = 'Notifications' AND SchemaClone.TableName IN (
    'EmployeeNotificationSubscription'
    ))
    OR (SchemaClone.SchemaName = 'Pathfinder' AND SchemaClone.TableName IN ( 
    'PathfinderClassOfBusinessBlurb',
    'PathfinderClassOfBusinessGroupToCob'
    ))
    OR (SchemaClone.SchemaName = 'Payroll' AND SchemaClone.TableName IN (
    'EmployeeDivision', 
    'ExpenseType', 
    'PayPeriodEmployee', 
    'PeriodEmployee'
    ))
    OR (SchemaClone.SchemaName = 'Pilot' AND SchemaClone.TableName IN (
    'FlowStepMailTemplate'
    ))
    OR (SchemaClone.SchemaName = 'Planning' AND SchemaClone.TableName IN (
    'Backlog'
    ))
    OR (SchemaClone.SchemaName = 'Policy' AND SchemaClone.TableName IN (
    'BordereauSent', 
    'ClaimNumberPool', 
    'ErpServiceCob', 
    'Program', 
    'SurplusLinesFiledByMap', 
    'Tier'
    ))
    OR (SchemaClone.SchemaName = 'Rating' AND SchemaClone.TableName IN (
    'CumulativePremiumFactorLookup'
    ))
    OR (SchemaClone.SchemaName = 'Report' AND SchemaClone.TableName IN (
    'CercQualifiedParams',
    'DashboardUserReportEmployee',
    'PowerReportLog',
    'UserReportFilter',
    'UserReportGroup',
    'UserReportMyColumns',
    'UserReportSort'
    ))
    OR (SchemaClone.SchemaName = 'Request' AND SchemaClone.TableName IN (
    'EmployeeRequest'
    ))
    OR (SchemaClone.SchemaName = 'Security' AND SchemaClone.TableName IN (
    'InviteLinkParam', 
    'InviteLinkUse', 
    'NotificationSetting'
    ))
    OR (SchemaClone.SchemaName = 'Strategy' AND SchemaClone.TableName IN (
    'StrategyRequiresPolicyType'
    ))
    OR (SchemaClone.SchemaName = 'SurplusLines' AND SchemaClone.TableName IN (
    'SurplusLinesState'
    ))
    OR (SchemaClone.SchemaName = 'Survey' AND SchemaClone.TableName IN (
    'SurveyDefinitionVersion',
    'WorkCompCarrierCode'    
    ))
    OR (SchemaClone.SchemaName = 'Tag' AND SchemaClone.TableName IN (
    'CarrierTag', 
    'LawyerContactTag',
    'LenderContactTag'    
    ))
    OR (SchemaClone.SchemaName = 'Task' AND SchemaClone.TableName IN (
    'Activity', 
    'ActivityHistory', 
    'EmployeeQueue', 
    'QueueBranchEmployeeSetting', 
    'TaskDocParent', 
    'UpdateLock'
    ))
    OR (SchemaClone.SchemaName = 'Tfs' AND SchemaClone.TableName IN (
    'CodeReview'
    ))
    OR (SchemaClone.SchemaName = 'Touch' AND SchemaClone.TableName IN (
    'EmployeeTouch'
    ))
    OR (SchemaClone.SchemaName = 'Transcript' AND SchemaClone.TableName IN (
    'EmployeeModule', 
    'EmployeeTraining', 
    'ModuleTraining'
    ))
    OR (SchemaClone.SchemaName = 'Validator' AND SchemaClone.TableName IN (
    'ValidationEmployeeHistory'
    ))
    OR (SchemaClone.SchemaName = 'Vetting' AND SchemaClone.TableName IN (
    'EvaluatorProcess', 
    'EvaluatorSetting', 
    'EvaluatorUrl', 
    'EvaluatorUser'
    ))
    OR (SchemaClone.SchemaName = 'Work' AND SchemaClone.TableName IN (
    'EmployeeWorkload'
    ))


