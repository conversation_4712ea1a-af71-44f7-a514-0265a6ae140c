/*
*  Create Review.TankRatingReviewResult
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'TankRatingReviewResult'
    and TABLES.TABLE_SCHEMA = 'Review'
)
begin

    create table Review.TankRatingReviewResult (
        PolicyProspectGuid uniqueidentifier not null
            constraint FK_TankRatingReviewResult_PolicyProspect
            foreign key references Policy.PolicyProspect(PolicyProspectGuid),
        CarrierMaximumOfferGuid uniqueidentifier not null
            constraint FK_TankRatingReviewResult_CarrierMaximumOffer
            foreign key references Carrier.CarrierMaximumOffer(CarrierMaximumOfferGuid),
        Premium decimal(18,4) not null,
        constraint PK_TankRatingReviewResult
        primary key (PolicyProspectGuid, CarrierMaximumOfferGuid)
    );

    print 'Created Review.TankRatingReviewResult'
end else begin

    print 'Review.TankRatingReviewResult Already Created'

end

-- 130 - Create Review.TankRatingReviewResult.sql
