/*
Create SurplusLinesStateRequirementYear.Note nvarchar(max)
Create SurplusLinesStateRequirementYear.Description nvarchar(150)
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'SurplusLInesStateRequirementYear'
    and COLUMNS.TABLE_SCHEMA = 'SurplusLines'
    and COLUMNS.COLUMN_NAME = 'Note'
)
begin

    alter table SurplusLines.SurplusLInesStateRequirementYear 
    add Note nvarchar(max) not null,
        Description nvarchar(150) not null;
    
    print 'Created SurplusLines.SurplusLInesStateRequirementYear.Note'
    
end else begin
  
    print 'SurplusLines.SurplusLInesStateRequirementYear.Note Already Created'

end

exec util_refreshviews;

-- 180 - Create SurplusLines.SurplusLInesStateRequirementYear.Note.sql
