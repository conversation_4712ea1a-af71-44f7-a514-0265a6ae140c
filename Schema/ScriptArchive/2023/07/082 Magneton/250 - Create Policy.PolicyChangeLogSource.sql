/*
*  Create Policy.PolicyChangeLogSource
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'PolicyChangeLogSource'
    and TABLES.TABLE_SCHEMA = 'Policy'
)
begin

    create table Policy.PolicyChangeLogSource (
        PolicyChangeLogSourceId int not null
            constraint PK_PolicyChangeLogSource
                primary key,
        SourceName nvarchar(100) not null
    );

    print 'Created Policy.PolicyChangeLogSource'
end else begin

    print 'Policy.PolicyChangeLogSource Already Created'

end

-- 250 - Create Policy.PolicyChangeLogSource.sql
