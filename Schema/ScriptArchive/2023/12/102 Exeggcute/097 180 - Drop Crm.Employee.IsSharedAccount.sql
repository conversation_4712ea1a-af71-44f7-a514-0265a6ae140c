
/*
*  Drop Crm.Employee.IsSharedAccount
*/

if exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'Employee'
    and COLUMNS.TABLE_SCHEMA = 'Crm'
    and COLUMNS.COLUMN_NAME = 'IsSharedAccount'
)
begin

    alter table Crm.Employee 
    drop constraint DF_Employee_IsSharedAccount, column IsSharedAccount 
    
    print 'Dropped Crm.Employee.IsSharedAccount'
    
end else begin
  
    print 'Crm.Employee.IsSharedAccount Already Dropped'

end

exec util_refreshviews;

-- 097 180 - Drop Crm.Employee.IsSharedAccount.sql
