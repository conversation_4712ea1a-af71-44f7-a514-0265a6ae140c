/*
*  Create Lookups.SchemaCloneType
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'SchemaCloneType'
    and TABLES.TABLE_SCHEMA = 'Lookups'
)
begin

    create table Lookups.SchemaCloneType (
        SchemaCloneTypeId int not null
            constraint PK_SchemaCloneType
            primary key,
        SchemaCloneTypeName nvarchar(50) not null
    );

    print 'Created Lookups.SchemaCloneType'
end else begin

    print 'Lookups.SchemaCloneType Already Created'

end

-- 160- Create Lookups.SchemaCloneType.sql
