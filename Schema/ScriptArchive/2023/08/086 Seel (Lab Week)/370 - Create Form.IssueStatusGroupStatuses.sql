if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'IssueStatusGroupStatuses'
    and TABLES.TABLE_SCHEMA = 'Form'
)
begin

--new table 6

CREATE TABLE [Form].[IssueStatusGroupStatuses](
	[CoverageIssueStatusGroupCopyGuid] [uniqueidentifier] NOT NULL,
	[CoverageIssueStatusCopyGuid] [uniqueidentifier] NOT NULL
	
	
 CONSTRAINT [PK_IssueStatusGroupStatuses] PRIMARY KEY CLUSTERED 
(
	[CoverageIssueStatusGroupCopyGuid] ASC,
	[CoverageIssueStatusCopyGuid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY];


ALTER TABLE [Form].[IssueStatusGroupStatuses]  WITH CHECK ADD  CONSTRAINT [FK_IssueStatusGroupStatuses_CoverageIssueStatusGroupCopyGuid] FOREIGN KEY([CoverageIssueStatusGroupCopyGuid])
REFERENCES [Form].[CoverageIssueStatusGroupCopy] ([CoverageIssueStatusGroupCopyGuid]);

ALTER TABLE [Form].[IssueStatusGroupStatuses]  WITH CHECK ADD  CONSTRAINT [FK_IssueStatusGroupStatuses_CoverageIssueStatusCopyGuid] FOREIGN KEY([CoverageIssueStatusCopyGuid])
REFERENCES [Form].[CoverageIssueStatusCopy] ([CoverageIssueStatusCopyGuid]);

print 'Created Form.IssueStatusGroupStatuses'

end else begin

print 'Form.IssueStatusGroupStatuses already exists'

end


-- 370 - Create Form.IssueStatusGroupStatuses.sql
