INSERT INTO Help.HelpSourceData (HelpSourceDataGuid, SiteProjectId, ControllerName, ActionName, ElementCssSelector, HelpTitle, HelpData)
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_DetailsTabInsuredName', 'Insured Name', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_DetailsTabInsuredName'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_DetailsTabInsuredAddress1', 'Insured Address 1', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_DetailsTabInsuredAddress1'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_DetailsTabInsuredAddress2', 'Insured Address 2', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_DetailsTabInsuredAddress2'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_DetailsTabRiskCounty', 'Insured Risk County', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_DetailsTabRiskCounty'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_DetailsTabCollaboration', 'Insured Collaboration', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_DetailsTabCollaboration'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_DetailsTabImportantNotes', 'Insured Important Notes', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_DetailsTabImportantNotes'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_DetailsTabFEIN', 'Insured FEIN', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_DetailsTabFEIN'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_DetailsTabDateLastVerified', 'Insured Date Last Verified', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_DetailsTabDateLastVerified'
)
UNION ALL 
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_OverviewTabInsuredAddress', 'Insured Overview Address', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_OverviewTabInsuredAddress'
)
UNION ALL 
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_OverviewTabInsuredAssociatedAgents', 'Insured Overview Associated Agents', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_OverviewTabInsuredAssociatedAgents'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_OverviewTabPoliciesList', 'Insured Overview Policies List', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_OverviewTabPoliciesList'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_OverviewTabPolicyCoverageType', 'Insured Overview Policy Coverage Type', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_OverviewTabPolicyCoverageType'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_OverviewTabPolicyIssuingCompany', 'Insured Overview Policy Issuing Company', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_OverviewTabPolicyIssuingCompany'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_OverviewTabPolicyPremium', 'Insured Overview Policy Premium', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_OverviewTabPolicyPremium'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_OverviewTabPolicyPeriod', 'Insured Overview Policy Period', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_OverviewTabPolicyPeriod'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_OverviewTabTotalUcpmIncome', 'Insured Overview Total UCPM Income', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_OverviewTabTotalUcpmIncome'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_OverviewTabSparkyTasks', 'Insured Overview Spark Tasks', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_OverviewTabSparkyTasks'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_OverviewTabInsuredServicingTasks', 'Insured Overview Servicing Tasks', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_OverviewTabInsuredServicingTasks'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_OverviewTabInsuredNotes', 'Insured Overview Insured Notes', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_OverviewTabInsuredNotes'
)
UNION ALL
SELECT NEWID(), 1, 'Insured', 'InsuredDetails', 'HelpOverlay_DetailsTabInsuredCobs', 'Insured Details Cobs', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_DetailsTabInsuredCobs'
)
UNION ALL
SELECT NEWID(), 1, 'PolicyProspect', 'Details', 'HelpOverlay_PolicyDetailsEffectiveDate', 'Policy Effective Date', 'ERROR - DATA NOT SET'
WHERE NOT EXISTS (
    SELECT 1
    FROM Help.HelpSourceData
    WHERE ElementCssSelector = 'HelpOverlay_PolicyDetailsEffectiveDate'
)
