
/*
*  Create Policy.PolicyStatus.StatusForInsured
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'PolicyStatus'
    and COLUMNS.TABLE_SCHEMA = 'Policy'
    and COLUMNS.COLUMN_NAME = 'StatusForInsured'
)
begin

    alter table Policy.PolicyStatus 
    add StatusForInsured nvarchar(50) not null
        constraint DF_PolicyStatus_StatusForInsured
        default 0;

    
    print 'Created Policy.PolicyStatus.StatusForInsured'
    
end else begin
  
    print 'Policy.PolicyStatus.StatusForInsured Already Created'

end

exec util_refreshviews;

-- 110 - Create Policy.PolicyStatus.StatusForInsured.sql
