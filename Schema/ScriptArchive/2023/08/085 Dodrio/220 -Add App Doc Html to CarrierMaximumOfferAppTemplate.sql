if not exists (select 1 from Carrier.CarrierMaximumOfferAppTemplate
where CarrierMaximumOfferGuid = '1101148f-f517-4c46-a9ba-04a498afd1b6')
begin
	insert into Carrier.CarrierMaximumOfferAppTemplate
values ('1101148f-f517-4c46-a9ba-04a498afd1b6', '<style>
    .page {
        padding: 5px 20px 5px 20px;
        display: flex;
        flex-direction: column;
    }

    img {
        margin-bottom: 11pt;
        width: 200px;
        height: auto;
    }

    table {
        border-collapse: collapse;
        margin-bottom: 11pt;
    }

    table,
    th,
    td {
        border: 1px solid;
        padding: 5pt;
    }

    u,
    p,
    b {
        margin: 0pt 0pt 11pt 0pt;
    }

    th {
        background-color: #bfbfbf;
    }

    .full-table {
        width: 100%;
    }

    .title-tag {
        border-top: 1px solid rgb(0, 0, 0);
        border-bottom: 1px solid rgb(0, 0, 0);
        font-weight: bold;
        color: blue;
        text-align: center;
        padding-top: 16px;
    }

    .header-tag {
        font-weight: bold;
        text-align: center;
        padding: 15px 1px 15px 1px;
        background: lightgray;
        margin: 16px 0;
    }

    .two-column-text {
        width: 50%;
        float: left;
    }

    .warranty-col-1 {
        width: 75%;
        float: left;
    }

    .warranty-col-2 {
        width: 10%;
        float: right;
    }
    
    .small-font{
        font-size:10;
    }

    @media print {
        .break-after {
            page-break-after: always;
        }

        table {
            page-break-inside: avoid;
        }

        .section {
            page-break-inside: avoid;
        }
    }
</style>

<div>
    <section>
        <article>
            <div class="page">
                <div>
                    <img
                        src="https://ucpmpublic.blob.core.windows.net/ucpmpublic/Production%5Cfd%5Cbe%5C11ff%5Ca7564199b082d3ecf045c7c8%5CLogo.png" />
                </div>
                <div class="title-tag">
                    <p>STORAGE TANK POLLUTION LIABILITY APPLICATION</p>
                </div>
                <p class="header-tag">Applicant Information</p>
                <div>
                    <p>Application Number: {Flex.ApplicationNumber}</p>
                    <p>Named Insured: {Flex.InsuredName}</p>
                    <p>Physical Address: {Flex.InsuredFullAddress}</p>
                    <p>Mailing Address: {Flex.InsuredMailedAddress}</p>
                    <div class="two-column-text">
                        <p>Proposed Effective Date: {Flex.EffectiveDate}</p>
                    </div>
                    <div class="two-column-text">
                        <p>Proposed Expiration Date: {Flex.ExpirationDate}</p>
                    </div>
                    <div class="two-column-text">
                        <p>Limits Requested: ________________ / ________________</p>
                    </div>
                    <div class="two-column-text">
                        <p>Deductible Requested: ___________________________</p>
                    </div>

                    <p>
                        Risk Management Contact Information:
                        _______________________________________________________________
                    </p>
                    <div>
                        <p class="two-column-text">Name: ___________________________</p>
                        <p class="two-column-text">
                            Telephone #: ___________________________
                        </p>
                    </div>

                    <div>
                        <p class="two-column-text">Email: ___________________________</p>
                        <p class="two-column-text">FEIN #: _______________________________</p>
                    </div>
                </div>
                <div>
                    <p class="header-tag">Warranty Questions</p>
                </div>
                <div>
                    <p class="warranty-col-1">
                        • Are any tanks used in septic systems, for wastewater or storm
                        water collection, flow-through process, or emergency
                        spill/overfill? *
                    </p>
                    <p class="warranty-col-2">No ☐</p>
                    <p class="warranty-col-2">Yes ☐</p>
                </div>
                <div>
                    <p class="warranty-col-1">
                        • Are any of the tanks residential, portable, bare steel without
                        cathodic protection or above the floor of underground areas such
                        as basements or tunnels? *
                    </p>
                    <p class="warranty-col-2">No ☐</p>
                    <p class="warranty-col-2">Yes ☐</p>
                </div>
                <div>
                    <p class="warranty-col-1">
                        • Are any USTs inactive, closed, temporarily out-of-service or scheduled to be replaced, removed, upgraded, or taken out of service? *
                    </p>
                    <p class="warranty-col-2">No ☐</p>
                    <p class="warranty-col-2">Yes ☐</p>
                </div>
                <div>
                    <p class="warranty-col-1">
                        • Are there any tanks or associated piping to be covered that are out of compliance with applicable EPA or state regulations for construction, tightness testing, monitoring, leak detection or SPCC plans? *
                    </p>
                    <p class="warranty-col-2">No ☐</p>
                    <p class="warranty-col-2">Yes ☐</p>
                </div>
                <div>
                    <p class="warranty-col-1">
                        • Has there ever been a reportable release at this location that has not received closure from the applicable regulatory agency? *
                    </p>
                    <p class="warranty-col-2">No ☐</p>
                    <p class="warranty-col-2">Yes ☐</p>
                </div>
                <div>
                    <p class="warranty-col-1">
                        • Are you aware of any failed tank/piping tightness tests or any other negative monitoring system data regarding the tanks to be covered? *
                    </p>
                    <p class="warranty-col-2">No ☐</p>
                    <p class="warranty-col-2">Yes ☐</p>
                </div>
                <div>
                    <p class="warranty-col-1">
                        • Are you aware of any known environmental losses in the last three years? *
                    </p>
                    <p class="warranty-col-2">No ☐</p>
                    <p class="warranty-col-2">Yes ☐</p>
                </div>
                <div>
                    <p class="warranty-col-1">
                        • At the time of signing this application, are you aware of any circumstances which may reasonably be expected to give rise to a claim under this policy? *
                    </p>
                    <p class="warranty-col-2">No ☐</p>
                    <p class="warranty-col-2">Yes ☐</p>
                </div>
								<div>              
					<p class="header-tag">
						Other Additional Insureds to be listed on the Policy &
						Relationship
					</p>
					
					{Flex.OtherInsureds}
				</div>
				<div>              
					<p class="header-tag">
						Facility Details
					</p>
					
					{Flex.FacilityDetails}
				</div>
				<div>              
					<p class="header-tag">
						Underground Storage Tank (UST) Details
					</p>
					
					{Flex.UstDetails}
				</div>
				<div>              
					<p class="header-tag">
						Aboveground Storage Tank (AST) Details
					</p>
					
					{Flex.AstDetails}
				</div>
				<div class="small-font">
					<p><b>Key:</b> Double Wall = DW, Single Wall = SW</p>
				</div>
            </div>
        </article>
    </section>
</div>')
end

