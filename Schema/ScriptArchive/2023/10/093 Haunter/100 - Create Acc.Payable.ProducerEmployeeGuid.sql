
/*
*  Create Acc.Payable.ProducerEmployeeGuid
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.COLUMNS
    where COLUMNS.TABLE_NAME = 'Payable'
    and COLUMNS.TABLE_SCHEMA = 'Acc'
    and COLUMNS.COLUMN_NAME = 'ProducerEmployeeGuid'
)
begin

    alter table Acc.Payable 
    add ProducerEmployeeGuid uniqueidentifier null
        constraint FK_Payable_Employee
            foreign key references Crm.Employee(EmployeeGuid);

    
    print 'Created Acc.Payable.ProducerEmployeeGuid'
    
end else begin
  
    print 'Acc.Payable.ProducerEmployeeGuid Already Created'

end

exec util_refreshviews;

-- 100 - Create Acc.Payable.ProducerEmployeeGuid.sql
