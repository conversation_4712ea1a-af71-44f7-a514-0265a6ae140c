/*
00.1 Schema CarrierBillingOfficeIssuingCompany

Join table (CarrierBillingOfficeGuid, IssuingCompanyGuid)
*/

/*
*  Create Carrier.CarrierBillingOfficeIssuingCompany
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'CarrierBillingOfficeIssuingCompany'
    and TABLES.TABLE_SCHEMA = 'Carrier'
)
begin

    create table Carrier.CarrierBillingOfficeIssuingCompany (
        CarrierBillingOfficeGuid uniqueidentifier not null
            constraint FK_CarrierBillingOfficeIssuingCompany_CarrierBillingOffice
            foreign key references Carrier.CarrierBillingOffice(CarrierBillingOfficeGuid),
        IssuingCompanyGuid uniqueidentifier not null
            constraint FK_CarrierBillingOfficeIssuingCompany_IssuingCompany
            foreign key references Policy.IssuingCompany(IssuingCompanyGuid),
        constraint PK_CarrierBillingOfficeIssuingCompany
        primary key (CarrierBillingOfficeGuid, IssuingCompanyGuid)
    );

    print 'Created Carrier.CarrierBillingOfficeIssuingCompany'
end else begin

    print 'Carrier.CarrierBillingOfficeIssuingCompany Already Created'

end

-- 110 - Create Carrier.CarrierBillingOfficeIssuingCompany.sql
