/*
*  Create Employee.EmployeeNotificationMethod
*/

if not exists (
    select 1
    from INFORMATION_SCHEMA.TABLES
    where TABLES.TABLE_NAME = 'EmployeeNotificationMethod'
    and TABLES.TABLE_SCHEMA = 'Employee'
)
begin

    create table Employee.EmployeeNotificationMethod (
        EmployeeNotificationMethodId int not null
            constraint PK_EmployeeNotificationMethod
                primary key,
        NotificationMethiod nvarchar(100) not null
    );

    print 'Created Employee.EmployeeNotificationMethod'
end else begin

    print 'Employee.EmployeeNotificationMethod Already Created'

end

-- 100 - Create Employee.EmployeeNotificationMethod.sql
