using Microsoft.AspNetCore.Components;
using PathfinderWeb.Client.Enums;

namespace PathfinderWeb.Client.Components;

public partial class CustomContainer1Base : ComponentBase
{
    /// <summary>
    /// The content to be displayed as the header.
    /// </summary>
    [Parameter] public RenderFragment? HeaderContent { get; set; }

    /// <summary>
    /// The content to be displayed as the body.
    /// </summary>
    [Parameter] public RenderFragment? BodyContent { get; set; }

    /// <summary>
    /// The content to be displayed as the footer.
    /// </summary>
    [Parameter] public RenderFragment? FooterContent { get; set; }

    /// <summary>
    /// Color of the front shape.
    /// </summary>
    [Parameter] public string FrontBackgroundColor { get; set; } = "#EEF3F0";

    /// <summary>
    /// Color of the back shape.
    /// </summary>
    [Parameter] public string BackBackgroundColor { get; set; } = "#137D7C";

    /// <summary>
    /// Color of the front shape's border.
    /// </summary>
    [Parameter] public string FrontBackgroundBorderColor { get; set; } = "#137D7C";

    [Parameter] public string BackgroundColor { get; set; } = "#EEF3F0";

    /// <summary>
    /// Color of the background shape's border.
    /// </summary>
    [Parameter] public string BackgroundBorderColor { get; set; } = "#137D7C";

    /// <summary>
    /// Additional class for the main div.
    /// </summary>
    [Parameter] public string Class { get; set; } = string.Empty;

    [Parameter]
    public QuoteType CurrentQuoteType { get; set; }
}