@inherits RequiredForBindingListBase

<div class="requirements-table-wrapper">
    <div class="requirements-table-inner">
        <table class="requirements-table">
            <thead>
                <tr>
                    <th class="text-start" style="width:50px !important;">Status</th>
                    <th style="width:350px !important;">
                        Requirements<br /><span class="text-muted">(Click to download blank PDF)</span>
                    </th>
                    <th colspan="2">
                        <div class="row justify-content-evenly align-items-center">
                            <div class="col-5 text-center">
                                Uploaded
                            </div>
                            @* <div class="col-auto">
                                or
                            </div>
                            <div class="col-5">
                                Generate
                                <br />E-Sign
                            </div> *@
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody>
                @if (IsLoading)
                {
                    <tr>
                        <td colspan="4" class="text-center py-4">
                            <span class="p-4">
                                <span class="spinner-grow text-success spinner-grow-sm" aria-hidden="true"></span>
                                <span role="status">Loading requirements...</span>
                            </span>
                        </td>
                    </tr>
                }
                else
                {
                    if (CarrierSubmissionSubjectivitiesBySelectedOption.Any())
                    {
                        foreach (var requirement in CarrierSubmissionSubjectivitiesBySelectedOption)
                        {
                            <tr>
                                <td class="text-center">
                                    @if (requirement.IsDocumentBound)
                                    {
                                        <span class="risk-dot dot-fill"></span>
                                    }
                                    else
                                    {
                                        <span class="risk-dot dot-empty"></span>
                                    }
                                </td>
                                <td>
                                    @if (!string.IsNullOrEmpty(requirement.SubjectivityLinkOverride))
                                    {
                                        <a href="@requirement.SubjectivityLinkOverride" target="_blank">
                                        @requirement.SubjectivityTextOverride
                                        </a>
                                    } 
                                    else
                                    {
                                        @requirement.SubjectivityTextOverride
                                        
                                    }
                                    @if (requirement.Required || requirement.RequireSignature)
                                    {
                                        <i class="bi bi-asterisk ms-2 text-danger fs-11"></i>
                                    }
                                </td>
                                <td style="width:130px !important;">
                                    @if (requirement.IsDocumentBound)
                                    {
                                        if (!string.IsNullOrEmpty(requirement.Document.CloudUrl))
                                        {
                                            <a href="@requirement.Document.CloudUrl" target="_blank"
                                               class="btn light-btn">
                                                View
                                            </a>
                                        }
                                        else
                                        {
                                            <button disabled class="btn light-btn">
                                                View
                                            </button>
                                        }
                                    }
                                    else
                                    {
                                        <label disabled
                                               class="@(GetIsSubjectivityLoading(requirement.CarrierSubmissionSubjectivityGuid) ? "" : "text-light btn-sm primary-btn shadow-sm")">
                                            @if (GetIsSubjectivityLoading(requirement.CarrierSubmissionSubjectivityGuid))
                                            {
                                                <span class="spinner-grow text-success spinner-grow-sm"
                                                      aria-hidden="true"></span>
                                                <span role="status">Uploading...</span>
                                            }
                                            else
                                            {
                                                <span>Upload</span>
                                            }
                                            <InputFile OnChange="async (eventArgs) => await ValidateAndUploadFile(eventArgs, requirement)"
                                                       accept="@string.Join(",", AllowedExtensions)" class="d-none" />
                                        </label>
                                    }
                                </td>
                              @*   <td class="text-center">
                                    @if (GetIsApp(requirement))
                                    {
                                        <button class="btn link-btn"
                                                @onclick="() => OpenESignModal(requirement)">
                                            Link
                                        </button>
                                    }
                                    else if (requirement.RequireSignature)
                                    {
                                        @if (requirement is { IsDocumentSigned: false, IsReceived: true })
                                        {
                                            <button class="btn link-btn"
                                                    @onclick="() => OpenESignModal(requirement)">
                                                Link
                                            </button>
                                        }
                                        else
                                        {
                                            <button disabled class="btn link-btn">
                                                Link
                                            </button>
                                        }
                                    }
                                    else
                                    {
                                        <button disabled class="btn link-btn">
                                            Link
                                        </button>
                                    }
                                </td> *@
                            </tr>
                        }
                    }
                    else
                    {
                        <tr>
                            <td colspan="4" class="text-center">
                                <span class="text-muted fs-6">No requirements found for binding.</span>
                            </td>
                        </tr>
                    }
                }
            </tbody>

        </table>
    </div>
</div>
<div class="requirement-footer justify-content-center mt-4 d-flex gap-2">
    @if (!IsLoading)
    {
        <button class="btn primary-btn" disabled="@(!IsAllRequiredSubjectivitiesUploaded || IsBinding)"
                @onclick="SetPolicyStatusToOrdered">
            @if (IsAllRequiredSubjectivitiesUploaded)
            {
                <span>
                    @(AreSubjectivitiesUploading || IsBinding ? "Submitting and Binding..." : "CLICK TO BIND") <i class="bi bi-upload ms"></i>
                </span>
            }
            else
            {
                <span>REQUIREMENTS INCOMPLETE</span>
            }
        </button>
    }
</div>

<GenerateEsignModal @ref="ESignModal" OnSendEsignCarrierSubmissionSubjectivity="HandleGenerateESign" />