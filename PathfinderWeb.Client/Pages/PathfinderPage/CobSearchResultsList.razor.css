.result-list {
    width: 100%;
    max-width: 600px;
    h4{
        color: #007275;
        font-weight: normal !important;
        font-size: 1.3rem !important;
    }
    .bi{
        color: #eeecde  !important;
    }
    p{
        font-family: "Archivo", sans-serif !important;
        color: black;
        font-size: 0.9rem !important;
    }
}

.list-group-item{
    border:0 !important;
    padding-left: 0 !important;
}

.form-check-input:checked + .form-checked-content {
    opacity: .5;
}

.form-check-input-placeholder {
    border-style: dashed;
}
[contenteditable]:focus {
    outline: 0;
}

.list-group-checkable .list-group-item {
    cursor: pointer;
    font-family: 'Roboto Slab', serif !important;
}
.list-group-item-check {
    position: absolute;
    clip: rect(0, 0, 0, 0);
}
.list-group-item-check:hover + .list-group-item {
    background-color: var(--bs-secondary-bg);
}
.list-group-item-check:checked + .list-group-item {
    color: #fff;
    background-color: var(--link-color);
    border-color: var(--color-primary-aria-cloud);
    box-shadow: 0 0 0 2px var(--color-secondary-mint)
}
.list-group-item-check[disabled] + .list-group-item,
.list-group-item-check:disabled + .list-group-item {
    pointer-events: none;
    filter: none;
    opacity: .5;
}

.list-group-radio .list-group-item {
    cursor: pointer;
    border-radius: .5rem;
}
.list-group-radio .form-check-input {
    z-index: 2;
    margin-top: -.5em;
}
.list-group-radio .list-group-item:hover,
.list-group-radio .list-group-item:focus {
    background-color: var(--bs-secondary-bg);
}

.list-group-radio .form-check-input:checked + .list-group-item {
    background-color: var(--bs-body);
    border-color: var(--link-color);
    box-shadow: 0 0 0 2px var(--link-hover-color);
}
.list-group-radio .form-check-input[disabled] + .list-group-item,
.list-group-radio .form-check-input:disabled + .list-group-item {
    pointer-events: none;
    filter: none;
    opacity: .5;
}

.more-results-toggle {
    font-family: 'Roboto Slab', serif !important;
    text-transform: capitalize;
    font-weight: normal !important;
    font-size: 1rem !important;
}