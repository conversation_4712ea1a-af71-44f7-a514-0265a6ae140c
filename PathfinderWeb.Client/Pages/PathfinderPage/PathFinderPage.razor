@page "/"
@page "/index"
@layout Layout
@inherits PathFinderPageBase
<PageTitle>Pathfinder</PageTitle>

<!-- Pathfinder Main Content -->
<div class="container search-container">
    <div class="row">
        <div class="col-md-12 col-lg-6 col-12 mt-5 pt-5">
            <div class=" align-items-center pb-1 justify-content-start">
                <img id="pathfinder-logo"
                     src="https://ucpmpublic.blob.core.windows.net/ucpmpublic/Production/3a/fb/aac1/894043afb8d45be9040c7175/pathfinder-logo-new.png"
                     loading="lazy"
                     width="350" alt="Pathfinder"/>
            </div>
            <div class="mt-4">
                <div class="row align-self-center align-content-center justify-content-start">
                    <div class="col-12 col-md-6 col-lg-10 info-note text-center ">
                        <div class="d-flex align-items-start">
                            <span class="text-black fw-bold search-title">Tell us about your customer. What do they do?</span>
                        </div>
                    </div>
                </div>
                <div class="row align-self-center  align-content-center justify-content-start">
                    <div class="col-11 col-md-8 col-lg-6">
                        <div class="input-group my-3 mb-3">
                            <input type="text" disabled="@(IsLoading || IsFlexVersionCarrierOfferLoading)"
                                   class="form-control border-2 border-offwhite-dark bg-offwhite-dark search-input"
                                   @onkeydown="@OnSearchKeyUp"
                                   @bind="SearchTerm"
                                   @bind:event="oninput"
                                   placeholder="Search by primary operations.." aria-label="Recipient’s username"
                                   aria-describedby="button-addon2">
                            <button class="primary-light-btn" disabled="@(IsLoading || IsFlexVersionCarrierOfferLoading)" type="button"
                                    @onclick="ExecuteCobSearch" id="btn-search">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                        <div class="text-center">
                            @if (IsFlexVersionCarrierOfferLoading)
                            {
                                <div class="spinner-grow spinner-grow-sm primary-text" role="status">
                                    <span class="visually-hidden">checking NXUS...</span>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
            <div class="row search-results min-height-25vh">
                @if (IsLoading)
                {
                    <span class="py-5">
                        <span class="spinner-grow primary-text spinner-grow-sm" aria-hidden="true"></span>
                        <span role="status">Searching primary operations, please wait...</span>
                    </span>
                }
                else
                {
                    <div class="col-11 col-md-8 col-lg-7 ">
                        @if (MatchedAndContainsFullTermList.Any())
                        {
                            @if (!string.IsNullOrEmpty(PreviousSearchTerm))
                            {
                                <div class="row justify-content-start d-none">
                                    <div class="col-12">
                                        <h6 class="dark-text">Search Results for:
                                            <span
                                                class="text-primary">@PreviousSearchTerm</span>
                                        </h6>
                                        <button class="btn-secondary fs-8 mb-3" @onclick="ResetSearch">
                                            <i class="bi bi-x-lg text-danger"></i> Clear
                                        </button>
                                    </div>
                                </div>
                               
                            }

                            <CobSearchResultsList MatchedAndContainsFullTermList="MatchedAndContainsFullTermList"
                                                  OtherPossibleCobList="OtherPossibleCobList"
                                                  OnCobSelected="HandleCobSelected"/>
                        }
                        else
                        {
                            @if (!string.IsNullOrEmpty(PreviousSearchTerm))
                            {
                                <div class="text-start ">
                                    <h6 class="dark-text mt-4">No results found for:
                                        <span
                                            class="text-black">@PreviousSearchTerm</span>
                                    </h6>
                                    <button class="btn-secondary fs-8 mb-3 d-none" @onclick="ResetSearch">
                                        <i class="bi bi-x-lg text-danger"></i> Clear
                                    </button>
                                </div>
                            }
                        }
                    </div>
                }
            </div>
            <div class="row align-self-center  align-content-center justify-content-start">
                <div class="col-11 col-md-6 col-lg-6 ps-2 info-note text-center px-1">
                    <div class="d-flex align-items-start">
                        <i class="bi bi-info-circle text-offwhite-dark me-2 fs-5"></i>
                        <div class="text-start pt-2">
                            @InstructionsInfo
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @if (SelectedCob.CobGuid != Guid.Empty)
        {
            <div class="col-12 mt-5 col-md-8 ps-3 col-lg-6 cob-result-details">
                <SelectedCobDetails SelectedCob="SelectedCob" SearchTerm=@SearchTerm></SelectedCobDetails>
            </div>
        }
    </div>
</div>