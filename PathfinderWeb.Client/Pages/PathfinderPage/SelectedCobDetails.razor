@using PathfinderWeb.Client.Extensions
@inherits SelectedCobDetailsBase

<div class="row col-12 border rounded px-4 py-3 cob-details">
    <div class="col-12 text-start">
        <h4 class="border-dark-subtle dark-text cob-details-border-bottom py-2">
            @SelectedCob.CobName
        </h4>
        <p class="accent-text">
            @((MarkupString)SelectedCob.ClassOfBusinessBlurb.ClassOfBusinessCustomContent.RemoveEmptyHtmlTags())
        </p>
    </div>
    <div class="col-12 text-start mt-3">
        <h4 class="border-dark-subtle dark-text cob-details-border-bottom py-2">
            Risk Classifier
        </h4>
        <div class="row mt-4 px-3">
            @foreach (var risk in SelectedCob.FlexInsuredExposureClassOfBusinessList)
            {
                <div class="col-md-6">
                    <h6>
                        <span class="risk-dot dot-fill"></span>
                        <a class="cursor-pointer text-black"
                           @onclick="() => ShowDescription(risk)">@risk.FlexInsuredExposure.ExposureName</a>
                    </h6>
                    <div
                        class="accent-text mb-3 px-3 py-2 bg-offwhite shadow-sm fs-8 text-wrap  @(risk.IsSelected ? "" : "d-none")">
                        @((MarkupString)risk.FlexInsuredExposure.Description)
                    </div>
                </div>
            }
            <div class="text-end mt-3 px-0 d-none">
                <button type="button" disabled="@IsLoading" class="btn dark-btn">
                    Risk management tools
                </button>
            </div>
        </div>
    </div>
    <div class="col-12 text-start mt-3 px-3 ">
        <h4 class="border-dark-subtle dark-text cob-details-border-bottom py-2">
            Product Options
        </h4>
        <div class="row mx-0 bg-white border rounded clip-path product-options">
            @if (SelectedCob.HasNxusRating)
            {
                <div class="col-12 px-0">
                    <div class="row">
                        <div class="col-7 px-0 align-self-center">
                            <img src="https://ucpmpublic.blob.core.windows.net/ucpmpublic/Production/3a/fb/aac1/894043afb8d45be9040c7175/nxus_cpl_new.png" width="450" class="img-fluid" alt="..."/>
                        </div>
                        <div class="col-auto ps-4 align-self-end text-center mb-0">
                            <h4 class="text-black mb-0">and Multi-carrier</h4>
                        </div>
                    </div>
                </div>

                <div class="col-12 px-0 mt-1">
                    <div class="row">
                        <div class="col-auto accent-text pe-0">
                            <p class="fs-8">
                                Get multiple CPL quotes all in one place. Limitless
                                <br/>
                                combinations, smart solutions, and powerful plans start here.
                            </p>
                        </div>
                        <div class="col-1 text-start align-self-center px-1">
                            <img src="images/yellow_bullet.png" alt=""/>
                        </div>
                        <div class="col-auto align-self-center p-0">
                            <button @onclick="OnQuoteNowClick" disabled="@IsLoading" class="btn dark-btn">
                                @if (IsLoading)
                                {
                                    <span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span>
                                    <span class="fs-9">Processing...</span>
                                }
                                else
                                {
                                    <span>Quote Now</span>
                                }
                            </button>
                        </div>
                    </div>
                </div>
            }
            else
            {
                  <div class="col-12 px-0">
                    <div class="row">
                        <div class="col-7 px-0 align-self-center">
                            <img src="https://ucpmpublic.blob.core.windows.net/ucpmpublic/Production/6e/a3/0df4/076543e382e9e4e8de08488d/Brokerage.png" width="450" class="img-fluid" alt="..."/>
                        </div>
                    </div>
                </div>

                <div class="col-12 px-0 mt-1">
                    <div class="row">
                        <div class="col-auto accent-text pe-0">
                            <p class="fs-8">
                                Online products for this class of business are currently unavailable.
                                <br/>
                                Click "Connect" to get connected with one of our experienced brokers.
                            </p>
                        </div>
                        <div class="col-1 text-start align-self-center px-1">
                            <img src="images/yellow_bullet.png" alt=""/>
                        </div>
                        <div class="col-auto align-self-center p-0">
                            <a href="mailto:<EMAIL>" disabled="@IsLoading" class="btn dark-btn">
                                <span>Connect</span>
                            </a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
    <div class="cob-details-border-bottom mx-2 my-3 d-none"></div>
    <div class="row m-0 px-0 justify-content-center text-center d-flex pb-2 align-items-center d-none">
        <div class="col justify-content-center align-items-center">
            <div class="dropdown ms-0 me-lg-3 me-md-3 me-2">
                <a class="nav-link dark-text text-nowrap" href="#" role="button" data-bs-toggle="dropdown">
                    Marketing Tools
                    <i class="bi bi-chevron-down link-color ms-1"></i>
                </a>
            </div>
        </div>
        <div class="col justify-content-center align-items-center">
            <div class="dropdown mx-2 mx-lg-3 mx-md-3">
                <a class="nav-link dark-text text-nowrap" href="#" role="button"
                   data-bs-toggle="dropdown">
                    Experts
                    <i class="bi bi-chevron-down link-color ms-1"></i>
                </a>
            </div>
        </div>
        <div class="col justify-content-center align-items-center">
            <div class="dropdown mx-2 mx-lg-3 mx-md-3">
                <a class="nav-link dark-text text-nowrap" href="#" role="button"
                   data-bs-toggle="dropdown">
                    Education
                    <i class="bi bi-chevron-down link-color ms-1"></i>
                </a>
            </div>
        </div>
    </div>
</div>