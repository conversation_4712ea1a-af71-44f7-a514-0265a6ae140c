using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using PathfinderWeb.Client.Models;
using UcpmApi.Shared;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Lookups;
using UcpmApi.Shared.Mail;
using UcpmApi.Shared.Pulse;
using UcpmApi.Shared.Security;
using UcpmComponentLibraryEx.Managers;
using UcpmComponentLibraryEx.Services;
using UcpmComponentLibraryEx.Services.Interfaces;
using UcpmComponentLibraryEx.Shared;

namespace PathfinderWeb.Client.Pages.Pulse;

public class BindingTableBase : PathfinderComponentBase
{
    protected List<CarrierSubmissionSubjectivity> CarrierSubmissionSubjectivities { get; set; } = [];

    [Parameter]
    public CarrierSubmission? CarrierSubmission { get; set; }

    [Parameter]
    public EventCallback<bool> OnBindingComplete { get; set; }

    [Inject]
    public DocManager DocManager { get; set; } = null!;

    [Inject]
    public CarrierSubmissionSubjectivityManager CarrierSubmissionSubjectivityManager { get; set; } = null!;

    [Inject]
    public CarrierSubmissionManager CarrierSubmissionManager { get; set; } = null!;

    [Inject]
    public PolicyManager PolicyManager { get; set; } = null!;

    [Inject]
    public DirectSendManager DirectSendManager { get; set; } = null!;

    [Inject]
    public NoteManager NoteManager { get; set; } = null!;

    [Inject]
    protected ActivityHistoryManager ActivityHistoryManager { get; set; } = null!;

    [Inject]
    private StrategyManager StrategyManager { get; set; } = null!;

    [Inject]
    public ILocalStorageService LocalStorageService { get; set; } = null!;

    [Inject]
    public NotificationManager NotificationManager { get; set; } = null!;

    [Inject]
    public EnvironmentManager EnvironmentManager { get; set; } = null!;

    [Inject]
    public SiteUrlManager SiteUrlManager { get; set; } = null!;

    private Agent Agent { get; set; } = new();

    private List<DocModel> SubjectivityDocuments { get; set; } = [];

    private HashSet<Guid> SubjectivityUploadingList { get; set; } = [];

    protected bool AreSubjectivitiesUploading { get; set; }

    protected bool IsBinding { get; set; } = false;

    protected bool IsAllRequiredSubjectivitiesUploaded { get; set; }

    protected static readonly List<string> AllowedExtensions = [".pdf"];

    private const long MaxFileSize = 50 * 1024 * 1024;

    private Guid LastLoadedSubmissionGuid = Guid.Empty;

    protected override async Task OnInitializedAsync()
    {
        DataIsLoading = true;
        await base.OnInitializedAsync();
        await GetAgent();
        if (CarrierSubmission == null || CarrierSubmission.CarrierSubmissionGuid == Guid.Empty)
        {
            ToastService.ShowToast("No submission selected.", ToastLevelEnum.Error, 8000);
            return;
        }

        if (OnBindingComplete.HasDelegate)
        {
            bool bound = await CheckIfBound();
            await OnBindingComplete.InvokeAsync(bound);
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        // Only reload if the Guid has changed
        if (CarrierSubmission != null && LastLoadedSubmissionGuid != CarrierSubmission.CarrierSubmissionGuid)
        {
            DataIsLoading = true;
            StateHasChanged();

            LastLoadedSubmissionGuid = CarrierSubmission.CarrierSubmissionGuid;

            // Reset before loading new subjectivities
            IsAllRequiredSubjectivitiesUploaded = false;

            await LoadSubjectivities(LastLoadedSubmissionGuid).ConfigureAwait(false);

            DataIsLoading = false;
        }

        if (OnBindingComplete.HasDelegate)
        {
            bool bound = await CheckIfBound();
            await OnBindingComplete.InvokeAsync(bound);
        }

        await base.OnParametersSetAsync();
    }

    public async Task LoadSubjectivities(Guid carrierSubmissionGuid)
    {
        CarrierSubmissionSubjectivities.Clear();
        SubjectivityDocuments.Clear();

        IEnumerable<CarrierSubmissionSubjectivity> subjectivities = await CarrierSubmissionSubjectivityManager.GetAllCarrierSubmissionSubjectivityByCarrierSubmissionGuid(carrierSubmissionGuid);

        IOrderedEnumerable<CarrierSubmissionSubjectivity> carrierSubmissionSubjectivities = subjectivities.OrderBy(x => x.RecordCreatedZoned);
        CarrierSubmissionSubjectivities.AddRange(carrierSubmissionSubjectivities);

        // Use a temporary list to avoid multiple UI updates
        var docs = new List<DocModel>();
        foreach (CarrierSubmissionSubjectivity carrierSubmissionSubjectivity in CarrierSubmissionSubjectivities)
        {
            IEnumerable<DocModel> existingDocs = await DocManager.GetAllDocsBySourceGuid(carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid);
            DocModel? firstDoc = existingDocs.FirstOrDefault();
            if (firstDoc != null)
            {
                docs.Add(firstDoc);
            }
        }

        SubjectivityDocuments = docs;

        CheckIsAllRequiredSubjectivitiesUploaded();
    }

    public async Task ValidateAndUploadFile(InputFileChangeEventArgs args, CarrierSubmissionSubjectivity carrierSubmissionSubjectivity)
    {
        IBrowserFile file = args.File;
        string extension = Path.GetExtension(file.Name).ToLowerInvariant();
        if (!AllowedExtensions.Contains(extension))
        {
            ToastService.ShowToast(
                $"Invalid file type selected. Allowed file types are: {string.Join(", ", AllowedExtensions)}.",
                ToastLevelEnum.Error, 8000);
            return;
        }

        if (file.Size > MaxFileSize)
        {
            ToastService.ShowToast($"File size exceeds the maximum of {MaxFileSize / (1024 * 1024)} MB.",
                ToastLevelEnum.Error, 8000);
            return;
        }

        await UploadSubjectivity(args, carrierSubmissionSubjectivity);
    }

    protected bool GetIsSubjectivityLoading(Guid carrierSubmissionSubjectivityGuid)
    {
        return SubjectivityUploadingList.Contains(carrierSubmissionSubjectivityGuid);
    }

    protected async Task Bind()
    {
        IsBinding = true;
        if (CarrierSubmission != null)
        {
            UpdatePolicySubStatusRequest updatePolicySubStatusRequest = new()
            {
                PolicyProspectGuid = CarrierSubmission.PolicyProspectGuid,
                PulseSubStatusId = (int)PulseSubStatusEnum.ItemsReceivedUnderReview,
            };

            bool result = await PolicyManager.UpdatePolicySubStatus(updatePolicySubStatusRequest);


            if (result)
            {
                await CreateActivityHistories();
                await SendPulseEmail();
                await SendPushNotification();

            }

            await OnBindingComplete.InvokeAsync(true);
        }

        IsBinding = false;
        StateHasChanged();
    }

    private async Task CreateActivityHistories()
    {
        if (CarrierSubmission != null)
        {
            foreach (DocModel doc in SubjectivityDocuments)
            {
                CarrierSubmissionSubjectivity? carrierSubmissionSubjectivity = CarrierSubmissionSubjectivities.FirstOrDefault(x => x.CarrierSubmissionSubjectivityGuid == doc.SourceGuid);
                ActivityHistory activityHistory = new()
                {
                    ActivityHistoryGuid = Guid.NewGuid(),
                    ActionByGuid = UserAuth.CurrentUser.AccountGuid,
                    RegardingAndNotes = $"Agent requested to bind for subjectivity {carrierSubmissionSubjectivity?.SubjectivityTextOverride} and attached file {doc.FileName}",
                    LinkingGuid = CarrierSubmission.PolicyProspectGuid,
                    IsAutogenerated = true,
                    StartTimeZoned = DateTimeOffset.Now,
                    RecordCreatedZoned = DateTimeOffset.Now,
                };

                await ActivityHistoryManager.CreateActivityHistory(activityHistory);
            }
        }
    }

    private async Task SendPushNotification()
    {
        if (CarrierSubmission?.PolicyProspect?.Package?.PrimaryRepEmployee != null)
        {
            EnvironmentEnumModel environment = await EnvironmentManager.GetEnvironment();
            SiteUrl siteUrl = await SiteUrlManager.GetSiteUrlByEnvironmentNameAndSiteProjectId(environment.EnvironmentName, (int)SiteProjectEnum.PolicyTrackerBlazor);
            string link = $"{siteUrl.BaseUrl}Pulse/PolicyDetails/{CarrierSubmission.PolicyProspectGuid}";
            string message = $"Changes Requested for: {CarrierSubmission.PolicyProspect.InsuredName}. Agent Requested to Bind {CarrierSubmission.Carrier.CarrierName}";

            Notification notification = new()
            {
                Link = link,
                Message = message,
                AccountGuid = CarrierSubmission.PolicyProspect.Package.PrimaryRepLoginGuid
            };

            await NotificationManager.SendNotification(notification);
        }
    }

    private async Task SendPulseEmail()
    {
        if (CarrierSubmission?.PolicyProspect?.Package?.PrimaryRepEmployee != null)
        {
            DirectSend directSend = new()
            {
                ExtraData = new Dictionary<string, string>() {
                    { "Subject", $"Binding Request for {CarrierSubmission.PolicyProspect.Package.InsuredAgentHistory?.Insured?.Name}" },
                    { "Body", $"Agent {Agent.AgentName} requested to bind for subjectivities" },
                    { "PrimaryRepName", Agent.AgentName },
                    { "PrimaryRepLogin", Agent.AgentEmail },
                },
                NodeGuid = Guid.Empty,
                NodeType = NodeTypeEnum.Agent,
                TemplateId = (int)DirectSendTemplateEnum.PulseCommunicationEmail,
                Targets = [
                    new DirectSendTarget()
                    {
                        TargetEmail = CarrierSubmission.PolicyProspect.Package.PrimaryRepEmployee.Login,
                        TargetMode = RecipientTypeEnum.Normal,
                        NodeTypeId = (int)NodeTypeEnum.Employee,
                        UserGuid = CarrierSubmission.PolicyProspect.Package.PrimaryRepEmployee.EmployeeGuid,
                    }
                ],
                Attachments = SubjectivityDocuments.Select(x => x.DocPath).ToList(),
            };

            DirectSendLog log = await DirectSendManager!.SendEmail(directSend);
        }
    }

    private async Task GetAgent()
    {
        Agent? cachedAgent = await LocalStorageService.GetJsonItem<Agent>(LocalStorageKey.Agent);
        if (cachedAgent == null)
        {
            // store agent in local storage
            Agent = await StrategyManager.GetAgent(UserAuth.CurrentUser.AccountGuid);
            await LocalStorageService.SetJsonItem(LocalStorageKey.Agent, Agent);
        }
        else
        {
            Agent = cachedAgent;
        }
    }

    private async Task<bool> CheckIfBound()
    {
        if (CarrierSubmission == null)
        {
            return false;
        }

        PolicyProspect policyProspect = await PolicyManager.GetPolicyProspectByGuid(CarrierSubmission.PolicyProspectGuid);
        return policyProspect.PulseSubStatusId == (int)PulseSubStatusEnum.ItemsReceivedUnderReview;
    }

    private void CheckIsAllRequiredSubjectivitiesUploaded()
    {
        // check if all Subjectivities with required and required signature is bound
        List<CarrierSubmissionSubjectivity> requiredSubjectivities = CarrierSubmissionSubjectivities
            .Where(s => s.Required || s.RequireSignature)
            .ToList();

        // check the result if all bound
        IsAllRequiredSubjectivitiesUploaded = requiredSubjectivities.All(a => a.IsDocumentBound || !string.IsNullOrWhiteSpace(a.SubjectivityLinkOverride));
    }

    private async Task<DocModel> ProcessUploadedFile(IBrowserFile file, CarrierSubmissionSubjectivity carrierSubmissionSubjectivity)
    {
        DocModel doc = new()
        {
            DocGuid = Guid.Empty,
            FileName = file.Name,
            FolderGuid = carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid,
            SourceGuid = carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid,
            EmployeeGuid = Guid.Empty,
            DocTypeId = (int)DocTypeEnum.Custom
        };

        await using Stream fileStream = file.OpenReadStream(MaxFileSize);
        using MemoryStream stream = new();
        byte[] buffer = new byte[81920];
        int read = 0;

        while ((read = await fileStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
        {
            stream.Write(buffer, 0, read);
        }

        doc.FileData = stream.ToArray();

        return doc;
    }

    private async Task<bool> UpdateSubjectivity(CarrierSubmissionSubjectivity carrierSubmissionSubjectivity)
    {
        CarrierSubmissionSubjectivity subjectivityToUpdate = new()
        {
            CarrierSubmissionSubjectivityGuid = carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid,
            Note = carrierSubmissionSubjectivity.Note,
            SubjectivityTextOverride = carrierSubmissionSubjectivity.SubjectivityTextOverride,
            LinkedDocGuid = carrierSubmissionSubjectivity.LinkedDocGuid,
            SubjectivityStatusId = carrierSubmissionSubjectivity.SubjectivityStatusId,
            SubjectivityLinkOverride = carrierSubmissionSubjectivity.SubjectivityLinkOverride,
        };

        Guid subjectivityGuid = carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid;
        bool result = await CarrierSubmissionSubjectivityManager.UpdateCarrierSubmissionSubjectivity(subjectivityGuid, subjectivityToUpdate);

        return result;
    }

    private async Task UploadSubjectivity(InputFileChangeEventArgs args, CarrierSubmissionSubjectivity carrierSubmissionSubjectivity)
    {
        SubjectivityUploadingList.Add(carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid);
        StateHasChanged();

        IBrowserFile uploadedFile = args.File;
        DocModel newDoc = await ProcessUploadedFile(uploadedFile, carrierSubmissionSubjectivity);

        DocModel? oldDoc = SubjectivityDocuments.Find(d => d.SourceGuid == carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid);
        if (oldDoc != null)
        {
            SubjectivityDocuments.Remove(oldDoc);
        }

        SubjectivityDocuments.Add(newDoc);
        await UploadSubjectivities(newDoc);

        SubjectivityUploadingList.Remove(carrierSubmissionSubjectivity.CarrierSubmissionSubjectivityGuid);
        CheckIsAllRequiredSubjectivitiesUploaded();
        StateHasChanged();
    }

    private async Task UploadSubjectivities(DocModel subjectivityDocument)
    {
        AreSubjectivitiesUploading = true;

        CarrierSubmissionSubjectivity? carrierSubmissionSubjectivity =
            CarrierSubmissionSubjectivities.Find(s => s.CarrierSubmissionSubjectivityGuid == subjectivityDocument.SourceGuid);
        DocModel existingDoc = await DocManager.GetDocBySourceAndName(subjectivityDocument.SourceGuid, subjectivityDocument.FileName);
        if (existingDoc.DocGuid != Guid.Empty)
        {
            await DocManager.ArchiveDoc(existingDoc);
        }

        existingDoc = await DocManager.UploadDoc(subjectivityDocument);
        if (existingDoc.DocGuid == Guid.Empty)
        {
            ToastService.ShowToast("A problem occurred while uploading the document. The document was not uploaded.", ToastLevelEnum.Error);
            return;
        }

        if (carrierSubmissionSubjectivity != null)
        {
            carrierSubmissionSubjectivity.SubjectivityStatusId = (int)SubjectivityStatusEnum.Received;
            carrierSubmissionSubjectivity.LinkedDocGuid = existingDoc.DocGuid;
            carrierSubmissionSubjectivity.Document = existingDoc;
            bool result = await UpdateSubjectivity(carrierSubmissionSubjectivity);
            if (result)
            {
                ToastService.ShowToast("Subjectivity has been uploaded!", ToastLevelEnum.Success);
            }
            else
            {
                ToastService.ShowToast(
                    "A problem occurred while updating the subjectivity. The document was not uploaded.",
                    ToastLevelEnum.Error, 8000);
            }
        }

        AreSubjectivitiesUploading = false;
    }
}

