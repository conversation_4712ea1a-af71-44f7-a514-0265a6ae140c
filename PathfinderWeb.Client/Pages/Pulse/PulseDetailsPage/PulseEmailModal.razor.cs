using BlazorStrap.V5;
using Microsoft.AspNetCore.Components;
using PathfinderWeb.Client.Shared;
using System.Web.Helpers;
using UcpmApi.Shared;

namespace PolicyTrackerBlazor.Client.Pages.Pulse.PulseDetailsPage;

public class PulseEmailModalBase : PathfinderBase
{
    protected BSModal PulseEmailModal = new BSModal();

    [Parameter]
    public required DirectSendLogEmail Email { get; set; }

    public async Task ShowAsync(DirectSendLogEmail email)
    {
        Email = email;
        await PulseEmailModal.ShowAsync();
    }

    public async Task HideModal()
    {
        await PulseEmailModal.HideAsync();
    }
}

