@using PathfinderWeb.Client.Components
@using UcpmApi.Shared.Extensions
@inherits CarrierSubmissionCardComponentBase

<CustomContainer1 CurrentQuoteType="@UcpmRenewableType" Class="w-auto">
    <HeaderContent>
        <div class="text-center">
            @if (string.IsNullOrEmpty(NxusLogo))
            {
                <div>@NxusName</div>
            }
            else
            {
                <img width="200" class="img-fluid align-self-center" src="@NxusLogo" alt="" />
            }
        </div>
    </HeaderContent>
    <BodyContent>
        <div style="min-width: 200px;">
            <hr class="m-2" />
            <div class="text-center py-2 h4">
                @if (NxusName == NxusTypeEnum.NXUSMatch.ToString() && IsMatching)
                {
                    <LoadingComponent Message="Matching coverage..." MessageClass="fs-8" />
                }
                else
                {
                    @if (DataIsLoading)
                    {
                        <LoadingComponent Message="Loading price..." />
                    }
                    else
                    {
                        @Price.ToCurrencyFormat()
                    }
                }
            </div>
            <hr class="m-2" />
            <div class="mt-3 fs-8">
                <table>
                    @foreach (string content in Contents)
                    {
                        <tr>
                            <td class="text-end pe-1">
                                <i class="icon-pathfinder fs-5 bi bi-check-lg"></i>
                            </td>
                            <td class="">
                                @content
                            </td>
                        </tr>
                    }
                </table>
            </div>
        </div>
    </BodyContent>
</CustomContainer1>
