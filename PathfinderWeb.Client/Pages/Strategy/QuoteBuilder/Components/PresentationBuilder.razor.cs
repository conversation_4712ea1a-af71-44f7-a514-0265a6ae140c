using FlexLibrary;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using UcpmApi.Shared;
using UcpmApi.Shared.Enums;
using UcpmComponentLibraryEx.Managers;
using UcpmComponentLibraryEx.Shared;

namespace PathfinderWeb.Client.Pages.Strategy.QuoteBuilder.Components
{
    public class PresentationBuilderBase : PathfinderComponentBase
    {
        [Inject]
        public FlexRatingManager FlexRatingManager { get; set; } = null!;

        [Inject]
        public FlexResponseSaveStateManager FlexResponseSaveStateManager { get; set; } = null!;

        [Inject]
        public DocManager DocManager { get; set; } = null!;

        [Inject]
        protected NavigationManager NavigationManager { get; set; } = null!;
        [Parameter]
        public required Guid FlexResponseGuid { get; set; }
        [Parameter]
        public required Guid InsuredGuid { get; set; }

        [Parameter]
        public required List<CarrierSubmission> CarrierSubmissions { get; set; }

        public bool IsDownloading { get; set; } = false;

        /// <summary>
        /// PrintMultipleQuote - gets all checked carriersubmissionoptions and its carriersubmission nxustype then prints quote document for multiple carriersubmissionoptions 
        /// </summary>
        protected async Task PrintMultipleQuote()
        {
            try
            {
                IsDownloading = true;

                List<NxusTypeEnum> nxusTypes = CarrierSubmissions
                .Where(cs => cs.CarrierSubmissionOption.Any(cso => !cso.IsDeleted))
                .Select(cs => (NxusTypeEnum)cs.NxusTypeId)
                .ToList();

                List<CarrierSubmissionOption>? selectedCarrierSubmissionOptions = CarrierSubmissions
                    .SelectMany(cs => cs.CarrierSubmissionOption)
                    .Where(cso => !cso.IsDeleted)
                    .ToList();

                await PrintQuotes(nxusTypes, selectedCarrierSubmissionOptions);
            }
            finally
            {
                IsDownloading = false;
            }
        }
        
        /// <summary>
        /// Go to the next page
        /// </summary>
        public void GoToNextPage()
        {
            NavigationManager.NavigateTo($"InsuredDashboard/{InsuredGuid}");
        }

        /// <summary>
        /// PrintQuotes - print the quote document of the selected carriersubmissionoptions
        /// <param name="nxusTypes">List of nxus types of the selected carriersubmissionoptions</param>
        /// <param name="selectedCarrierSubmissionOptions">List of the selected carriersubmissionoptions</param>
        /// </summary>
        private async Task PrintQuotes(List<NxusTypeEnum> nxusTypes, List<CarrierSubmissionOption> selectedCarrierSubmissionOptions)
        {
            try
            {
                if (!nxusTypes.Any() || !selectedCarrierSubmissionOptions.Any())
                {
                    ToastService.ShowToast($"Download Failed, No Selected Options", UcpmComponentLibraryEx.Services.ToastLevelEnum.Error);
                    return;
                }

                GenerateNxusQuoteModel model = new()
                {
                    FlexResponseGuid = FlexResponseGuid,
                    NxusTypes = nxusTypes,
                    SelectedCarrierSubmissionOptions = selectedCarrierSubmissionOptions
                };

                DocInfo result = await FlexRatingManager.GenerateNxusQuote(model);

                if (result != null)
                {
                    await DownloadQuote(result);
                }
            }
            catch (Exception)
            {
                ToastService.ShowToast($"An error occurred while downloading the quote..", UcpmComponentLibraryEx.Services.ToastLevelEnum.Error);
            }
        }

        /// <summary>
        /// DownloadQuote - Downloads the quote
        /// <param name="docUrlInfo">docUrlInfo object containing the document to be downloaded</param>
        /// </summary>
        private async Task DownloadQuote(DocInfo docUrlInfo)
        {
            FlexResponseSaveState saveState = await FlexResponseSaveStateManager.GetFlexResponseSaveState(FlexResponseGuid);
            FlexAnswer? insuredName = saveState?.Answers?.FlexAnswers?.FirstOrDefault(f => f.QuestionDataName == "InsuredName");
            string fileName = $"{insuredName?.Answer}{DateTimeOffset.Now.ToString("MM/dd/yyyy")}-Quote.pdf";
            DocModel doc = await DocManager.GetDocAsync(docUrlInfo.DocGuid);

            await JSRuntime.InvokeVoidAsync("downloadFile", doc.FileData, fileName);

            ToastService.ShowToast($"Success, please check your 'Downloads' folder.", UcpmComponentLibraryEx.Services.ToastLevelEnum.Success);
        }
    }
}
