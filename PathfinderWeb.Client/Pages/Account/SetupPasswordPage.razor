@page "/Authentication/PasswordSetup/{resetHash:guid}/{email}"
@using PathfinderWeb.Client.Components
@inherits SetupPasswordPageBase
@attribute [AllowAnonymous]
@layout FullWidthLayout

@if (IsLoading)
{
    <LoadingComponent IsPage="true" IsTopDown="true" />
}
else
{
    @if (ErrorMessage != null)
    {
        <PathfinderPageMessage Message="@ErrorMessage" />
    }
    else
    {
        <div class="row">
            <div class="col-md-6 col-sm-8 mx-auto">
                @if (IsSuccess)
                {
                    <PathfinderPageMessage Message="Password has been set!" />
                }
                else
                {
                    <PasswordReset AgentEmail="@AgentModel.AgentEmail"
                                   AgentGuid="AgentModel.AgentGuid"
                                   FormText="Setup your password below:"
                                   OnSubmit="HandleSubmit" />
                }
            </div>
        </div>
    }
}

