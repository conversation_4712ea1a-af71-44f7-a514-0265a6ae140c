@inherits PriorPathsBase
@page "/PreviousInquiries"
@using PathfinderWeb.Client.Components

<PageTitle>Previous Inquiries</PageTitle>

<div class="row justify-content-center">
    <div class="col-sm-8 col-md-6">
        <h1>Previous Inquiries</h1>
        @if (DataIsLoading)
        {
            <LoadingComponent IsPage="true" IsTopDown="true" />
        }
        else
        {
            <BSTable IsStriped="false" IsHoverable="true" IsResponsive="true" IsSmall="true" >
                <BSTHead Class="table-light">
                    <BSTR>
                        <BSTD>
                            Insured
                        </BSTD>
                        <BSTD>
                            Class Of Business
                        </BSTD>
                        <BSTD>
                            Prior Paths
                        </BSTD>
                        <BSTD>
                            Archive
                        </BSTD>
                    </BSTR>
                </BSTHead>
                <BSTBody>
                    @if (PagedPriorPaths.Count > 0)
                    {
                        foreach (var path in PagedPriorPaths)
                        {
                            <BSTR >
                                <BSTD >
                                    @path.InsuredName
                                </BSTD>
                                <BSTD>
                                    @path.ResolvedClassOfBusinessName
                                </BSTD>
                                <BSTD>
                                    <button class="btn btn-secondary btn-sm shadow-sm" @onclick="() => GoToPriorPath(path)">
                                        Continue
                                        <i class="bi bi-arrow-right ms-1"></i>
                                    </button>
                                </BSTD>
                                <BSTD>
                                    <button class="btn btn-danger btn-sm shadow-sm" @onclick="(e => ArchivePath(e, path))">
                                        Archive
                                        <i class="bi bi-archive ms-1"></i>
                                    </button>
                                </BSTD>
                            </BSTR>
                        }
                    }
                    else
                    {
                        <BSTR>
                            <BSTD ColSpan="4">
                                <div class="text-center">
                                    <p>There are no previous inquiries to display.</p>
                                </div>
                            </BSTD>
                        </BSTR>
                    }
                </BSTBody>
            </BSTable>
            <div class="d-flex justify-content-between align-items-center">
                <button class="btn btn-primary" style="margin-top: -18px;" @onclick="GoBack">Back</button>
                <PaginationComponent PageItemCount="@PageItemCount"
                                     ItemsCount="@TotalItemsCount"
                                     OnChange="@HandlePageChange"
                                     ActivePageNumber="@ActivePageNumber"
                                     PageButtonCount="10"
                                     PeekAheadCount="1"
                                     HideShowLastButton
                                     HideShowFirstButton />
            </div>

        }
    </div>
</div>
