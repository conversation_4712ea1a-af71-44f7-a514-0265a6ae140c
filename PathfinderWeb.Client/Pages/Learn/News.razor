@page "/News"
@inherits NewsBase
@attribute [AllowAnonymous]

<div class="container">
    <div class="container-fluid">
        <div class="row">
            <div class="col">
                <h3 class="text-start">News</h3>
                <p class="mt-2">
                    Here's what's new!
                </p>
            </div>
        </div>
        <div class="row mb-5">
            <div class="col-12">
                <div class="tab-content news-tab-content" id="newsTabContent">
                    @foreach (var article in Articles)
                    {
                        <div class="card news-article-card-rev shadow-sm border-light-subtle my-2" id="article">
                            <div class="row no-gutters g-0 align-items-center">
                                @if (article.HeroImageUrl == "")
                                {
                                    <div class="col-md-12">
                                        <div class="card-body searchable">
                                            <h5 class="card-text">@article.ArticleTitle</h5>
                                            <span class="d-none searchable">@article.SearchTags</span>
                                            <div class="card-text">
                                                <div>
                                                    @((MarkupString)article.AbbreviatedArticleHtml)
                                                </div>
                                                <div class="d-flex justify-content-end">
                                                    <NavLink class="rev-hyperlink-b" href="@($"/NewsArticle/{article.OnlineArticleGuid}")">Read More</NavLink>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <div class="col-md-4">
                                        <img class="article-featured-image" src="@article.HeroImageUrl" alt="article image" />
                                    </div>
                                    <div class="col-md-8">
                                        <div class="card-body searchable">
                                            <h5 class="card-text">@article.ArticleTitle</h5>
                                            <span class="d-none searchable">@article.SearchTags</span>
                                            <div class="card-text">
                                                <div>
                                                    @((MarkupString)article.AbbreviatedArticleHtml)
                                                </div>
                                                <div class="d-flex justify-content-end">
                                                    <NavLink href="@($"/NewsArticle/{article.OnlineArticleGuid}")">Read More</NavLink>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
