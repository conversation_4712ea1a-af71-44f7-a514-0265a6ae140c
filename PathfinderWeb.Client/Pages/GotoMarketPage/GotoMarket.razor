@using PathfinderWeb.Client.Components;
@page "/GotoMarket/{FlexResponseGuid:guid}"
@inherits GotoMarketBase

<div class="container">
    <h4>Multi Carrier Options</h4>
    <div class="container">
        <div class="row mb-4 justify-content-between align-items-center">
            <div class="col-auto">
                <div class="align-items-center text-center ms-4">
                    <!-- Company Logo Placeholder -->
                    @if (!string.IsNullOrEmpty(CompanyLogo))
                    {
                        <div class="logo me-3">
                            <img src="@CompanyLogo" alt="Company Image">
                        </div>
                    }
                    else
                    {
                        <div>
                            <h4 class="my-2">@CompanyName</h4>
                        </div>
                    }
                </div>
            </div>
            <div class="col-auto agent-details col-auto agent-details d-flex justify-content-between align-items-center">
                <!-- Agent Details -->
                <div class="me-2">
                    <p class="m-0"><strong>@AgentName</strong></p>
                    <p class="m-0 text-muted">@PositionTitle</p>
                </div>
                <!-- Agent Profile Placeholder -->
                <div class="profile-placeholder img-thumbnail mb-2 ">
                    <img src="@ImageUrl" alt="Profile Image">
                </div>
            </div>
        </div>
        <div class="row mb-4 ms-5 justify-content-between align-items-start">
            <span class="mb-3 fw-bold">@InsuredName</span>
            <br />
            <div class="mb-2">
                <button type="button" onclick="@GoToInsuredDashboard" class="back-to-dashboard">
                    <span class="icon">←</span>
                    Back to Insured Dashboard
                </button>
            </div>
            <br />
            <div class="row mb-3 justify-content-around align-items-center">
                <PathfinderFlexComponent @ref="PathfinderFlexComponent"
                                         SinglePageNumber="12"
                                         FlexResponseGuid="@FlexResponseGuid"
                                         PathResponseActionLogEvent="LogPathResponseAction"
                                         SourceOfContactEnum="SourceOfContactEnum.Pathfinder">
                    <LoadingComponent>
                        <LoadingComponent Message="@PathfinderFlexComponent.LoadingMessage" />
                    </LoadingComponent>
                </PathfinderFlexComponent>
            </div>
        </div>
    </div>
</div>
