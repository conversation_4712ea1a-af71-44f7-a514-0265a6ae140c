@inherits MarketSummaryBase
@page "/MarketSummary/{ResponseGuid:guid}"
@using UcpmApi.Shared.Flex
@layout InsuredUserLayout
@attribute [AllowAnonymous]

<div class="container">
    <div class="row pt-5">
        <div class="col-12">
            <h1>Estimate</h1>
        </div>
        <div class="col-12">
            @if (Loading)
            {
                <h4>
                    Loading...
                </h4>
            }
            else
            {
                <h4>
                    Range: @MinPrice.ToString("c2") ~ @MaxPrice.ToString("c2")
                </h4>
            }

            @if (IsReferral)
            {
                <h5>Could not generate pricing from given information. Please contact your agent for referral options.</h5>
            }

        </div>
    </div>
</div>


<div class="container">
    <div class="row pt-5">

        <div class="col-12">
            <h4>Options:</h4>
        </div>
        <div class="col-12">
            <table class="table table-bordered" id="">
                <tr>
                    <th>Carrier</th>
                    <th>Limit Occurrence</th>
                    <th>Limit Aggregate</th>
                    <th>Deductible</th>
                    <th>Estimated Cost</th>
                    <th>Select Option</th>
                </tr>
                @foreach (FlexOption option in Options) 
                {
                    <tr>
                        <td>@option.CarrierName</td>
                        <td>@option.LimitOccurrence.ToString("c2")</td>
                        <td>@option.LimitAggregate.ToString("c2")</td>
                        <td>@option.Deductible.ToString("c2")</td>
                        <td>@option.OptionPremium.ToString("c2")</td>
                        <td><input type="checkbox" @bind="option.IsSelected"/></td>
                    </tr>
                }
            </table>
        </div>
    </div>
</div>

@if (!IsReferral)
{
    <div class="container">
        <BSButton Class="cv-blue-btn float-end" OnClick="(() => SendSelectedOptionsToAgent())">Send Selected Options To Agent</BSButton>
    </div>
}
