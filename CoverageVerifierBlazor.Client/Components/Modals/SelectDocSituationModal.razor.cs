using BlazorStrap.V5;
using CoverageVerifierBlazor.Client.Shared;
using CoverageVerifierBlazor.Client.Shared.Enums;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using System.Text;
using System.Text.RegularExpressions;
using UcpmApi.Shared;
using UcpmApi.Shared.Enums;
using UcpmApi.Shared.Vetting;
using UcpmComponentLibraryEx.Managers;
using UcpmComponentLibraryEx.Services;

namespace CoverageVerifierBlazor.Client.Components.Modals
{
    public class SelectDocSituationModalBase : CoverageVerifierBase
    {
        private long _maxFileSize = 1024 * 5000;
        private int _maxFileCount = 5;

        [Parameter]
        public Guid ProcessInstanceGuid { get; set; }
        [Parameter]
        public Guid AssessedGuid { get; set; }
        [Parameter]
        public EventCallback<bool> OnDocumentUploaded { get; set; }

        [Inject]
        public ProcessInstanceManager ProcessInstanceManager { get; set; }
        [Inject]
        public ProcessInstanceDocManager ProcessInstanceDocManager { get; set; }
        [Inject]
        public DocManager DocManager { get; set; }
        [Inject]
        AgentManager AgentManager { get; set; }
        [Inject]
        DirectSendManager DirectSendManager { get; set; }
        [Inject]
        MessageManager MessageManager { get; set; }
        [Inject]
        AssessedManager AssessedManager { get; set; }

        protected DocumentActionEnum _currentAction = DocumentActionEnum.None;
        protected string InputFieldId = Guid.NewGuid().ToString();
        protected string DeclarationsInputFieldId = Guid.NewGuid().ToString();
        protected string FormsInputFieldId = Guid.NewGuid().ToString();

        public bool IsLoading { get; set; }
        public IBrowserFile UploadedFile { get; set; }
        public List<IBrowserFile> DeclarationsPages { get; set; }
        public List<IBrowserFile> FormsPages { get; set; }
        public string DecPageRange { get; set; }
        public string FormPageRange { get; set; }
        public BSModal SelectDocSituationModal;

        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();
        }

        public async Task ShowModal(DocumentActionEnum documentAction = DocumentActionEnum.None)
        {
            _currentAction = documentAction;
            UploadedFile = null;
            DeclarationsPages = null;
            FormsPages = null;
            DecPageRange = "";
            FormPageRange = "";
            StateHasChanged();
            await SelectDocSituationModal.ShowAsync();
        }

        public async Task CloseModal()
        {
            _currentAction = DocumentActionEnum.None;
            StateHasChanged();
            await SelectDocSituationModal.HideAsync();
        }

        public async Task ShowAddSingleDoc()
        {
            _currentAction = DocumentActionEnum.SingleDoc;
            StateHasChanged();
        }

        public async Task ShowAddMultipleDoc()
        {
            _currentAction = DocumentActionEnum.MultipleDoc;
            StateHasChanged();
        }

        public async Task LoadSingleDoc(InputFileChangeEventArgs args)
        {
            if (args.File != null)
            {
                if (args.File.Size <= _maxFileSize)
                {
                    UploadedFile = args.File;
                }
                else
                {
                    ToastService.ShowToast($"The selected file is over the maximum file size of {_maxFileSize} bytes. Please select a smaller file", ToastLevelEnum.Warning, 7000);
                }
            }
            else
            {
                ToastService.ShowToast("No file was selected.", ToastLevelEnum.Warning);
            }
        }

        public async Task LoadDeclarationsPages(InputFileChangeEventArgs args)
        {
            if (args.File != null)
            {
                int failedCount = 0;
                StringBuilder failedFiles = new();
                DeclarationsPages = [];

                if (args.FileCount > _maxFileCount)
                {
                    ToastService.ShowToast($"You have added too many files. The maximum number of dec files is {_maxFileCount}.", ToastLevelEnum.Warning, 8000);
                    return;
                }

                foreach (IBrowserFile file in args.GetMultipleFiles(_maxFileCount))
                {
                    if (file != null)
                    {
                        if (file.Size <= _maxFileSize)
                        {
                            DeclarationsPages.Add(file);
                        }
                        else
                        {
                            failedCount++;
                            failedFiles.Append($"{file.Name}, ");
                        }
                    }
                }

                if (failedCount > 0)
                {
                    ToastService.ShowToast($"The following dec files were too large: {failedFiles} and cannot be uploaded. The maximum file size is {_maxFileSize} bytes.", ToastLevelEnum.Warning, 8000);
                }
            }
            else
            {
                ToastService.ShowToast("No files were selected.", ToastLevelEnum.Warning);
            }
        }

        public async Task LoadFormsPages(InputFileChangeEventArgs args)
        {
            if (args.File != null)
            {
                int failedCount = 0;
                StringBuilder failedFiles = new();
                FormsPages = [];

                if (args.FileCount > _maxFileCount)
                {
                    ToastService.ShowToast($"You have added too many files. The maximum number of forms files is {_maxFileCount}.", ToastLevelEnum.Warning, 8000);
                    return;
                }

                foreach (IBrowserFile file in args.GetMultipleFiles(_maxFileCount))
                {
                    if (file != null)
                    {
                        if (file.Size <= _maxFileSize)
                        {
                            FormsPages.Add(file);
                        }
                        else
                        {
                            failedCount++;
                            failedFiles.Append($"{file.Name}, ");
                        }
                    }
                }

                if (failedCount > 0)
                {
                    ToastService.ShowToast($"The following forms files were too large: {failedFiles} and cannot be uploaded. The maximum file size is {_maxFileSize} bytes.", ToastLevelEnum.Warning, 8000);
                }
            }
            else
            {
                ToastService.ShowToast("No files were selected.", ToastLevelEnum.Warning);
            }
        }

        public async Task SaveUploadedDocs()
        {
            if (_currentAction == DocumentActionEnum.SingleDoc)
            {
                await SaveSingleDoc();
            }
            else if (_currentAction == DocumentActionEnum.MultipleDoc)
            {
                await SaveMultipleDocs();
            }
        }

        private bool ValidPageRanges()
        {
            bool valid = true;
            int outVal = 0;

            if (string.IsNullOrEmpty(DecPageRange) || string.IsNullOrEmpty(FormPageRange))
            {
                valid = false;
            }

            if (valid && !DecPageRange.Contains("-"))
            {
                if (!int.TryParse(DecPageRange, out outVal))
                {
                    valid = false;
                }
            }
            else if (valid && DecPageRange.Contains("-"))
            {
                string[] parts = DecPageRange.Split("-");
                if (!int.TryParse(parts[0], out outVal))
                {
                    valid = false;
                }

                if (valid && !int.TryParse(parts[1], out outVal))
                {
                    valid = false;
                }
            }

            if (valid && !FormPageRange.Contains("-"))
            {
                if (!int.TryParse(FormPageRange, out outVal))
                {
                    valid = false;
                }
            }
            else if (valid && FormPageRange.Contains("-"))
            {
                string[] parts = FormPageRange.Split("-");
                if (!int.TryParse(parts[0], out outVal))
                {
                    valid = false;
                }

                if (valid && !int.TryParse(parts[1], out outVal))
                {
                    valid = false;
                }
            }

            return valid;
        }

        private async Task SaveSingleDoc()
        {
            IsLoading = true;

            if (UploadedFile == null || string.IsNullOrEmpty(UploadedFile.Name))
            {
                ToastService.ShowToast("No document was selected to upload.", ToastLevelEnum.Warning, 7000);
                IsLoading = false;
                return;
            }

            if (!ValidPageRanges())
            {
                ToastService.ShowToast("The Dec and Form page ranges need to both be set with a page number or range of page numbers. Example: 1 or 1-3.", ToastLevelEnum.Warning, 7000);
                IsLoading = false;
                return;
            }

            DocModel doc = await ProcessUploadedFile(UploadedFile);

            DocModel existingDoc = await DocManager.GetDocBySourceAndName(AssessedGuid, doc.FileName);
            if (existingDoc == null || existingDoc.DocGuid == Guid.Empty)
            {
                doc = await DocManager.UploadDoc(doc);
                if (doc == null || doc.DocGuid == Guid.Empty)
                {
                    ToastService.ShowToast("A problem occurred uploading the document. The document was not uploaded.", ToastLevelEnum.Error, 8000);
                    IsLoading = false;
                    return;
                }
            }
            else
            {
                doc = existingDoc;
            }

            bool newProcessInstanceDoc = false;
            ProcessInstanceDoc processInstanceDoc = await ProcessInstanceDocManager.GetProcessInstanceDocByGuids(ProcessInstanceGuid, doc.DocGuid);
            if (processInstanceDoc == null || processInstanceDoc.DocGuid == Guid.Empty)
            {
                newProcessInstanceDoc = true;
                processInstanceDoc = new()
                {
                    DocGuid = doc.DocGuid,
                    ProcessInstanceGuid = ProcessInstanceGuid
                };
            }


            processInstanceDoc.ProcessDocStatusId = (int)ProcessDocStatusEnum.NotProcessed;

            bool success = newProcessInstanceDoc ? await ProcessInstanceDocManager.AddProcessInstanceDoc(processInstanceDoc) : await ProcessInstanceDocManager.UpdateProcessInstanceDoc(processInstanceDoc);
            if (success)
            {
                int newStatus = (int)ProcessStatusIdEnum.PendingPolicyProcessing;
                ProcessInstance processInstance = new()
                {
                    ProcessInstanceGuid = processInstanceDoc.ProcessInstanceGuid,
                    ProcessStatusId = newStatus
                };

                success = await ProcessInstanceManager.UpdateProcessInstanceStatusAndAddHistory(processInstance);
                if (success)
                {
                    ToastService.ShowToast("The document was saved successfully.", ToastLevelEnum.Success);
                    await OnDocumentUploaded.InvokeAsync(success);
                    ResetData();
                    await CloseModal();
                }
                else
                {
                    ToastService.ShowToast("A problem occurred updating the process instance status and updating history. The process instance was not updated.", ToastLevelEnum.Error, 8000);
                }
            }
            else
            {
                ToastService.ShowToast("A problem occurred adding/updating the process instance document. The document was not saved.", ToastLevelEnum.Error, 8000);
            }

            IsLoading = false;
        }

        private async Task SaveMultipleDocs()
        {
            DocModel doc = new();
            IsLoading = true;

            if (DeclarationsPages == null || DeclarationsPages.Count == 0 ||
                FormsPages == null || FormsPages.Count == 0)
            {
                ToastService.ShowToast("Declarations and Forms documents are both required for the upload process.", ToastLevelEnum.Warning, 7000);
                IsLoading = false;
                return;
            }

            StringBuilder failedDocs = new();
            int failedDocsCount = 0;
            ProcessInstanceDocCollection processInstanceDocCollection = new();

            foreach (IBrowserFile file in FormsPages)
            {
                doc = await ProcessUploadedFile(file);

                DocModel existingDoc = await DocManager.GetDocBySourceAndName(AssessedGuid, doc.FileName);
                if (existingDoc == null || existingDoc.DocGuid == Guid.Empty)
                {
                    doc = await DocManager.UploadDoc(doc);
                    if (doc == null || doc.DocGuid == Guid.Empty)
                    {
                        failedDocsCount++;
                        failedDocs.Append($"{file.Name} ");
                        continue;
                    }
                }
                else
                {
                    doc = existingDoc;
                }

                DocSetupAndSave(processInstanceDocCollection, file, doc, true);
            }

            foreach (IBrowserFile file in DeclarationsPages)
            {
                doc = await ProcessUploadedFile(file);

                DocModel existingDoc = await DocManager.GetDocBySourceAndName(AssessedGuid, doc.FileName);
                if (existingDoc == null || existingDoc.DocGuid == Guid.Empty)
                {
                    doc = await DocManager.UploadDoc(doc);
                    if (doc == null || doc.DocGuid == Guid.Empty)
                    {
                        failedDocsCount++;
                        failedDocs.Append($"{file.Name} ");
                        continue;
                    }
                }
                else
                {
                    doc = existingDoc;
                }

                DocSetupAndSave(processInstanceDocCollection, file, doc, false);
            }

            if (await ProcessInstanceDocManager.AddProcessInstanceDocs(processInstanceDocCollection))
            {

                ProcessInstance processInstance = new()
                {
                    ProcessInstanceGuid = ProcessInstanceGuid,
                    ProcessStatusId = (int)ProcessStatusIdEnum.PendingPolicyProcessing
                };

                if (await ProcessInstanceManager.UpdateProcessInstanceStatusAndAddHistory(processInstance))
                {
                    ToastService.ShowToast("The documents were uploaded and saved successfully.", ToastLevelEnum.Success);
                    ResetData();
                    await OnDocumentUploaded.InvokeAsync(true);
                    await CloseModal();
                }
                else
                {
                    ToastService.ShowToast("A problem occurred updating the process instance status and updating history. The process instance was not updated.", ToastLevelEnum.Error, 8000);
                }
            }
            else
            {
                ToastService.ShowToast("A problem occurred with call to AddProcessInstanceDocs. The uploaded documents were not saved.", ToastLevelEnum.Error, 8000);
            }

            IsLoading = false;
        }

        private void ResetData()
        {
            UploadedFile = null;
            DeclarationsPages = [];
            FormsPages = [];
            DeclarationsInputFieldId = Guid.NewGuid().ToString();
            FormsInputFieldId = Guid.NewGuid().ToString();
        }

        private async Task<int> GetPageCount(IBrowserFile file)
        {
            int pageCount = 0;

            using (StreamReader reader = new(file.OpenReadStream(_maxFileSize)))
            {
                Regex regex = new(@"/Type\s*/Page[^s]");
                string? contents = await reader.ReadToEndAsync();
                MatchCollection matches = regex.Matches(contents);
                pageCount = matches.Count;
            }

            return pageCount;
        }

        private async Task<DocModel> ProcessUploadedFile(IBrowserFile file)
        {
            DocModel doc = new()
            {
                DocGuid = Guid.Empty,
                FileName = file.Name,
                FolderGuid = AssessedGuid,
                EmployeeGuid = Guid.Empty,
                DocTypeId = 13
            };

            using (Stream fileStream = file.OpenReadStream(_maxFileSize))
            {
                using MemoryStream stream = new();
                byte[] buffer = new byte[4096];
                int read = 0;

                while ((read = await fileStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    stream.Write(buffer, 0, read);
                }

                doc.FileData = stream.ToArray();
            }

            return doc;
        }

        private async void DocSetupAndSave(ProcessInstanceDocCollection processInstanceDocCollection, IBrowserFile file, DocModel doc, bool isForms)
        {
            int stop = file.Name.ToLower().EndsWith(".pdf") ? await GetPageCount(file) : 1;
            ProcessInstanceDoc processInstanceDoc = new()
            {
                DocGuid = doc.DocGuid,
                ProcessInstanceGuid = ProcessInstanceGuid,
                ProcessDocStatusId = (int)ProcessDocStatusEnum.NotProcessed
            };



            processInstanceDocCollection.ProcessInstanceDocs.Add(processInstanceDoc);
        }
    }
}
