using UcpmComponentLibraryEx.Services;

namespace CoverageVerifierBlazor.Client.Shared
{
    public class ToastBase : CoverageVerifierBase, IDisposable
    {
        protected string? _heading;
        protected string? _message;
        protected string? _backgroundCssClass;
        protected string? _iconCssClass;
        protected bool _isVisible;

        protected override async Task OnInitializedAsync()
        {
            ToastService.OnShow += ShowToast;
            ToastService.OnHide += HideToast;

            await base.OnInitializedAsync();
        }

        private void ShowToast(string message, ToastLevelEnum level, int duration = 5000)
        {
            BuildToastSettings(level, message);
            _isVisible = true;
            StateHasChanged();
        }

        private void HideToast()
        {
            _isVisible = false;
            StateHasChanged();
        }

        private void BuildToastSettings(ToastLevelEnum level, string message)
        {
            switch (level)
            {
                case ToastLevelEnum.Information:
                    _backgroundCssClass = $"bg-info";
                    _iconCssClass = "info";
                    _heading = "Information";
                    break;

                case ToastLevelEnum.Success:
                    _backgroundCssClass = $"bg-success";
                    _iconCssClass = "check";
                    _heading = "Success";
                    break;

                case ToastLevelEnum.Warning:
                    _backgroundCssClass = $"bg-warning";
                    _iconCssClass = "exclamation";
                    _heading = "Warning!";
                    break;

                case ToastLevelEnum.Error:
                    _backgroundCssClass = $"bg-danger";
                    _iconCssClass = "times";
                    _heading = "Error!";
                    break;
            }

            _message = message;
        }

        void IDisposable.Dispose()
        {
            ToastService.OnShow -= ShowToast;
            ToastService.OnHide -= HideToast;
        }
    }
}
