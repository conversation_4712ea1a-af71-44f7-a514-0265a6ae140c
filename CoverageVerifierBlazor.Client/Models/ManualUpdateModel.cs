using System.ComponentModel.DataAnnotations;

namespace CoverageVerifierBlazor.Client.Models
{
    public class ManualUpdateModel
    {
        public string? PolicyNumber { get; set; }
        
        [Required]
        [Display(Name = "Effective Date")]
        public DateTime EffectiveDate { get; set; }
        
        [Required]
        [Display(Name = "Expiration Date")]
        public DateTime ExpirationDate { get; set; }
        public decimal OccurrenceLimit { get; set; }
        public decimal AggregateLimit { get; set; }
        public decimal Deductible { get; set; }
        public List<FormEntryModel> FormData { get; set; } = new List<FormEntryModel>();

        public ManualUpdateModel() 
        {
            EffectiveDate = System.DateTime.Now;
            ExpirationDate = System.DateTime.Now.AddYears(1);
            OccurrenceLimit = -1;
            AggregateLimit = -1;
            Deductible = -1;
        }
        public class FormEntryModel
        {
            public string FormNumber { get; set; }
            public string FormDescription { get; set; }
        }
    }
}
